<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"
        tools:replace="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        tools:node="remove" />

    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        tools:node="remove" />
    <!-- 注意：不能申请这个权限，因为太敏感，谷歌商店通不过 -->
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.TYPE_APPLICATION_OVERLAY" /> <!-- No address associated with hostname -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE " />
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission
        android:name="android.permission.WRITE_MEDIA_STORAGE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />

    <uses-feature android:name="android.hardware.camera" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_CHANGED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REPLACED" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Android 11 使用相机，需要再AndroidManifest.xm 添加如下代码： -->

    <!--人脸识别需要使用-->
    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>

        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />

        <package android:name="com.facebook.katana" />
        <package android:name="com.facebook.orca" />
    </queries>

    <application
        android:name=".common.application.VauApplication"
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning,UnusedAttribute,DiscouragedApi,LockedOrientationActivity"
        tools:replace="android:allowBackup"
        tools:targetApi="m">
        <!-- android:networkSecurityConfig="@xml/network_security_config" 用于测试抓包，提交正式包需要删除掉 -->
        <activity
            android:name=".trade.st.activity.StStrategyOrdersActivity"
            android:exported="false" />
        <activity
            android:name=".page.user.openAccoGuide.result.OpenAccountLvResultActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <service
            android:name=".common.push.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- Firebase意见征求模式：设置所有参数默认为均已征得用户同意 -->
        <meta-data
            android:name="google_analytics_default_allow_analytics_storage"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_storage"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_user_data"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_personalization_signals"
            android:value="true" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/img_source_notification_logo" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" /> <!-- 华为海外应用市场 -->
        <!-- <meta-data android:name="CHANNEL" android:value="huawei" /> -->
        <!-- <meta-data android:name="AF_STORE" android:value="${CHANNEL_VALUE}"/> -->
        <!-- <meta-data android:name="AF_STORE" android:value="Xiaomi_Store"/> -->
        <!-- <meta-data android:name="AF_STORE" android:value="OPPO_Store"/> -->
        <!-- <meta-data android:name="AF_STORE" android:value="Samsung_Store"/> -->
        <!-- <meta-data android:name="AF_STORE" android:value="vivo_Store"/> -->
        <!-- <meta-data android:name="AF_STORE" android:value="Huawei_Store"/> -->
        <!-- Adjust -->
        <!--查看性能事件的日志消息-->
        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="true" />
        <receiver
            android:name="com.adjust.sdk.AdjustReferrerReceiver"
            android:exported="true"
            android:permission="android.permission.INSTALL_PACKAGES">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="android.max_aspect"
            android:value="2.2" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.installapkdemo"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data
            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
            android:value="true" />
        <meta-data
            android:name="com.facebook.sdk.AutoInitEnabled"
            android:value="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <!-- Firebase 埋点，深入链接 -->
            <meta-data
                android:name="cn.com.vau.common.utils.initializer.FirebaseInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="@string/fb_login_protocol_scheme"
                    tools:ignore="ManifestResource" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider262879427825763"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />

        <activity
            android:name="cn.com.vau.page.start.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- 放这里 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefx.onelink.me"
                    android:pathPrefix="/Sqk2"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantageapp.onelink.me"
                    android:pathPrefix="/qaPD"
                    android:scheme="https" />
            </intent-filter>
            <!-- FireBase 深度链接 ( 为深层链接添加一个 intent 过滤器 ) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefx.onelink.me"
                    android:pathPrefix="/Sqk2"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="example.com"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/H5.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/Deposit.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/Open_Live.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vfxapp.onelink.me"
                    android:pathPrefix="/UxDH"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vfxapp.onelink.me"
                    android:pathPrefix="/UxDH"
                    android:scheme="http" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefxapp.onelink.me"
                    android:pathPrefix="/Qgw0"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <!-- 神策 -->
        <activity
            android:name="com.sensorsdata.analytics.android.sdk.dialog.SchemeActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="${Sensors_Schema}" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.com.vau.page.start.WelcomeActivity"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name="cn.com.vau.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            android:theme="@style/MainTheme"
            tools:ignore="LockedOrientationActivity"> <!-- 登陆 -->
            <!-- 放这里 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefx.onelink.me"
                    android:pathPrefix="/Sqk2"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantageapp.onelink.me"
                    android:pathPrefix="/qaPD"
                    android:scheme="https" />
            </intent-filter>
            <!-- FireBase 深度链接 ( 为深层链接添加一个 intent 过滤器 ) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefx.onelink.me"
                    android:pathPrefix="/Sqk2"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="example.com"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="vantage" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/H5.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/Deposit.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagemarketapp.com"
                    android:pathPrefix="/web/h5/app/Open_Live.html"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vfxapp.onelink.me"
                    android:pathPrefix="/UxDH"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vfxapp.onelink.me"
                    android:pathPrefix="/UxDH"
                    android:scheme="http" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="vantagefxapp.onelink.me"
                    android:pathPrefix="/Qgw0"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.com.vau.page.user.login.LoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.login.VerificationActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi,LockedOrientationActivity" />
        <activity
            android:name="cn.com.vau.home.activity.MainNewComerEventActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="cn.com.vau.home.activity.FcmTransitActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/TranslucentStyle" />
        <activity
            android:name="cn.com.vau.signals.live.LivingPLayerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.live.HistoryPlayerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.ui.common.activity.ChooseYourThemeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.ui.common.activity.AccountErrorDialogActivity"
            android:exported="true"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.ui.common.activity.LanguageActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" />

        <activity
            android:name="cn.com.vau.ui.mine.activity.FeedbackFormActivity"
            android:screenOrientation="portrait" />

        <!--    账户活动    -->
        <activity
            android:name="cn.com.vau.ui.mine.activity.AccountActivityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.ui.deal.activity.LossOrderActivity"
            android:screenOrientation="portrait" /> <!-- 解锁设置 -->
        <activity
            android:name="cn.com.vau.page.setting.activity.SecurityCodeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.loginPwd.LoginPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 注册引导页 -->
        <activity
            android:name="cn.com.vau.page.user.register.RegisterFirstActivity"
            android:screenOrientation="portrait" /> <!-- NewsLetter -->
        <!-- 自定义关注 -->
        <activity
            android:name="cn.com.vau.trade.activity.ManageSymbolsActivity"
            android:screenOrientation="portrait" />
        <!-- 账户管理列表 -->
        <activity
            android:name="cn.com.vau.page.user.accountManager.AccountManagerActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" /> <!-- 选择区号 -->
        <activity
            android:name="cn.com.vau.page.common.selectArea.SelectAreaCodeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" /> <!-- 搜索 -->
        <activity
            android:name="cn.com.vau.trade.activity.SearchProductActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" />

        <!-- 筛选 -->
        <activity
            android:name="cn.com.vau.signals.activity.FiltersActivity"
            android:screenOrientation="portrait" /> <!-- 财经日历详情 -->
        <activity
            android:name="cn.com.vau.signals.activity.EconomicCalendarActivity"
            android:screenOrientation="portrait" />

        <!-- 修改杠杆 -->
        <activity android:name="cn.com.vau.page.user.leverage.LeverageActivity" />
        <activity android:name="cn.com.vau.page.user.leverage.st.StLeverageActivity" />

        <!-- 图片预览 -->
        <activity android:name="cn.com.vau.page.photopreview.PhotoActivity" /> <!-- 视频详情 -->
        <activity
            android:name="cn.com.vau.signals.activity.VideoDetailsActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait" /> <!-- 用户摘要 我的历程 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StProviderToPublicTradeActivity"
            android:screenOrientation="portrait" /> <!-- 资金管理 -->
        <activity
            android:name="cn.com.vau.profile.activity.manageFunds.FundsActivity"
            android:screenOrientation="portrait" /> <!-- Html5详情页面 -->
        <activity
            android:name="cn.com.vau.page.html.HtmlActivity"
            android:screenOrientation="portrait" />

        <!-- 资金管理明细 入金详情 -->
        <activity
            android:name="cn.com.vau.profile.activity.manageFundsDetails.FundsDetailsActivity"
            android:screenOrientation="portrait" />

        <!-- 请输入密码 -->
        <activity
            android:name="cn.com.vau.profile.activity.inputPWD.InputPWDActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!-- 选择国籍页面 -->
        <activity
            android:name="cn.com.vau.page.common.selectNation.SelectNationalityActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" />

        <!-- 开通真实账户第一步 -->
        <activity
            android:name="cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name="cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstSecondActivity"
            android:screenOrientation="portrait" />

        <!-- 开通真实账户第二步 -->
        <activity
            android:name="cn.com.vau.page.user.openAccountSecond.OpenAccountSecondActivity"
            android:screenOrientation="portrait" />

        <!-- 开通真实账户第三步 -->
        <activity
            android:name="cn.com.vau.page.user.openAccountThird.OpenAccountThirdActivity"
            android:screenOrientation="portrait" />

        <!-- 开通真实账户第四步 -->
        <activity
            android:name="cn.com.vau.page.user.openAccountForth.OpenAccountForthActivity"
            android:screenOrientation="portrait" />

        <!-- 开通真实账户第五步 -->
        <activity
            android:name="cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFifthActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.OpenFifthAddressActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.OpenFifthIdentifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.OpenFifthIdentifyExampleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.OpenFifthAddressExampleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccountFifth.UploadingActivity"
            android:screenOrientation="portrait" />

        <!-- 开通同名账户 -->
        <activity
            android:name="cn.com.vau.page.user.openSameNameAccount.OpenSameNameAccountActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="cn.com.vau.page.user.openAccount.OpenAccountSuccessAsicActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 安全设置 -->
        <activity
            android:name="cn.com.vau.profile.activity.changeLoginPWD.ChangeLoginPWDActivity"
            android:screenOrientation="portrait" />

        <!-- 修改资金安全密码 -->
        <activity
            android:name="cn.com.vau.profile.activity.changeSecurityPWD.ChangeSecurityPWDActivity"
            android:screenOrientation="portrait" />

        <!-- 选择地区 -->
        <activity
            android:name="cn.com.vau.page.common.selectResidence.activity.SelectResidenceActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" /> <!-- 选择省份 -->
        <activity
            android:name="cn.com.vau.page.common.selectResidence.activity.SelectProvinceActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" /> <!-- 选择城市 -->
        <activity
            android:name="cn.com.vau.page.common.selectResidence.activity.SelectCityActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" /> <!-- 新增/忘记资金安全密码 -->
        <activity
            android:name="cn.com.vau.profile.activity.addOrForgotSecurityPWD.AddOrForgotSecurityPWDActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <!-- 下单页面 -->
        <activity
            android:name="cn.com.vau.trade.activity.OrderActivity"
            android:screenOrientation="portrait" />

        <!-- 设置页面 -->
        <activity
            android:name="cn.com.vau.page.setting.activity.SettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.trade.activity.HKLineChartActivity"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:screenOrientation="landscape" /> <!-- 横屏K线图 -->
        <activity
            android:name="cn.com.vau.trade.kchart.tradingview.ChartCandleLandscapeActivity"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:screenOrientation="landscape" />

        <!-- 改单页面 -->
        <activity
            android:name="cn.com.vau.trade.activity.NewModifyOrderActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.page.depositNew.DepositDetailsActivity"
            android:screenOrientation="portrait" />
        <!-- 问卷页面 -->
        <activity
            android:name="cn.com.vau.page.user.question.AsicQuestionnaireActivity"
            android:screenOrientation="portrait" /> <!-- 电汇AUD -->
        <activity
            android:name="cn.com.vau.page.user.bindEmail.BindEmailActivity"
            android:screenOrientation="portrait" />

        <!-- 帮助中心 -->
        <activity
            android:name="cn.com.vau.page.customerservice.HelpCenterActivity"
            android:screenOrientation="portrait" />

        <!-- 帮助中心 - 联系我们 -->
        <activity
            android:name="cn.com.vau.page.customerservice.support.ContactUsActivity"
            android:screenOrientation="portrait" />

        <!-- 帮助中心 - FAQs问答 -->
        <activity
            android:name="cn.com.vau.page.customerservice.help.FAQsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.profile.stfund.StFundsActivity"
            android:screenOrientation="portrait" /> <!-- 用来打开pdf的页面 -->

        <!--    跟随策略增加资金    -->
        <activity
            android:name="cn.com.vau.trade.st.activity.StStrategyAddOrRemoveFundsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!--    跟随策略更新设置    -->
        <activity
            android:name="cn.com.vau.trade.st.activity.StStrategyUpdateSettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.signals.stsignal.activity.PersonalDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.lv1.OpenAccoGuideLv1Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.lv2.OpenAccoGuideLv2Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.lv3.OpenAccoGuideLv3Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.lv1.SelectCountryResidenceActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" />
        <activity
            android:name="cn.com.vau.profile.activity.authentication.AuthenticationActivity"
            android:screenOrientation="portrait" />

        <!-- 安全设置 -->
        <activity
            android:name="cn.com.vau.page.security.SecurityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.trade.activity.TradeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.updateMobileNumber.UpdateMobileNumberActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.setting.activity.DeviceHistoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.openAccoGuide.lv2.OpenUoloadPreviewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.activity.H5DebugActivity"
            android:exported="false" />
        <activity
            android:name="cn.com.vau.page.setting.activity.ConfigAndUnlockActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFABindActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFAResetActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.DisclaimerActivity"
            android:screenOrientation="portrait" />
        <!-- 信号源详情 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StSignalDetailsActivity"
            android:screenOrientation="portrait" />
        <!-- 策略详情 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StStrategyCopyActivity"
            android:screenOrientation="portrait" />
        <!-- 信号源中心 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.center.activity.StSignalCenterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StSignalSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing|stateHidden" />
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StCreateAndEditStrategyActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.signals.stsignal.activity.EditPersonalInfoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.EditNicknameActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StEditStrategyMinActivity"
            android:screenOrientation="portrait" />
        <!-- 粉丝列表 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StFansListActivity"
            android:screenOrientation="portrait" />

        <!-- 收藏的策略列表 -->
        <activity
            android:name="cn.com.vau.signals.stsignal.activity.StFollowListActivity"
            android:screenOrientation="portrait" />

        <!--   订单详情页 - 跟随策略持仓订单  -->
        <activity
            android:name="cn.com.vau.trade.activity.StStrategyPositionDetailsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!--   订单详情页 - 历史订单  -->
        <activity
            android:name=".history.ui.HistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".history.ui.HistoryPositionDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!--   订单详情页 - 跟单自主历史订单  -->
        <activity
            android:name="cn.com.vau.trade.activity.StStrategyHistoryDetailsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!--   持仓详情页  -->
        <activity
            android:name="cn.com.vau.trade.activity.TradesPositionDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!--   切换线路（仅限测试环境）  -->
        <activity
            android:name="cn.com.vau.page.setting.activity.SwitchLineActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.user.sumsub.SumsubPromptActivity"
            android:screenOrientation="portrait" />
        <!-- 互抵平仓 -->
        <activity
            android:name="cn.com.vau.trade.activity.CloseByOrderActivity"
            android:exported="false" />

        <!-- 批量平仓 - 筛选订单 页 -->
        <activity
            android:name="cn.com.vau.trade.activity.ModifiedCloseConfigurationActivity"
            android:exported="false" />

        <!-- 批量平仓 - 筛选订单 End 页 -->
        <activity
            android:name="cn.com.vau.trade.activity.ModifiedCloseConfigurationEndActivity"
            android:exported="false" />

        <activity
            android:name="cn.com.vau.profile.activity.passkey.PasskeySettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.profile.activity.passkey.PasskeyAuthVerificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.passkey.PasskeyCreatingActivity"
            android:screenOrientation="portrait" />
        <!-- 创建价格提醒 -->
        <activity
            android:name="cn.com.vau.profile.activity.pricealert.activity.CreatePriceAlertActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <!-- 产品价格提醒列表 -->
        <activity
            android:name="cn.com.vau.profile.activity.pricealert.activity.PriceAlterListActivity"
            android:screenOrientation="portrait" />
        <!-- 价格提醒管理页面 -->
        <activity
            android:name="cn.com.vau.profile.activity.pricealert.activity.PriceAlertsManageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.trade.activity.CloseHistoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.trade.activity.BatchCloseDisclaimerActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="cn.com.vau.trade.activity.KLineActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboardHidden"  />
        <!-- 2fa的分页设置页面，已经设置了2fa以后会再次设置2fa跳转的页面，可以调整修改或重置2fa的页面 -->
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFASettingActivity"
            android:screenOrientation="portrait" />
        <!-- 2fa修改页面 的提示页面 -->
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFAChangePromptActivity"
            android:screenOrientation="portrait" />
        <!-- 2fa修改页面 -->
        <activity
            android:name="cn.com.vau.profile.activity.twoFactorAuth.activity.TFAChangeActivity"
            android:screenOrientation="portrait" />
        <!-- 消息列表页面的activity -->
        <activity
            android:name="cn.com.vau.page.notice.activity.NoticeActivity"
            android:screenOrientation="portrait" />
        <!-- 消息列表的设置页面 -->
        <activity
            android:name="cn.com.vau.page.notice.activity.NoticeSettingActivity"
            android:screenOrientation="portrait" />
        <!-- 输入密码 -->
        <activity
            android:name="cn.com.vau.page.user.login.EnterPasswordActivity"
            android:screenOrientation="portrait" />
        <!-- 新的html 页面 -->
        <activity
            android:name="cn.com.vau.page.html.NewHtmlActivity"
            android:screenOrientation="portrait" />

     <!-- Demo开户页引导 -->
        <activity
            android:name=".page.user.openAccoGuide.demo.OpenDemoGuideActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.page.coupon.CouponManagerActivity"
            android:screenOrientation="portrait" />
        <!-- 满减券使用 -->
        <activity
            android:name="cn.com.vau.page.coupon.couponUse.CouponDetailActivity"
            android:screenOrientation="portrait" />
        <!-- 注册第一步 -> 选择居住地 -->
        <activity
            android:name="cn.com.vau.page.login.activity.SignUpActivity"
            android:screenOrientation="portrait" />
        <!-- 非ASIC：注册第二步 -> 输入手机号/邮箱、密码 -->
        <activity
            android:name="cn.com.vau.page.login.activity.SignUpPwdActivity"
            android:screenOrientation="portrait" />
        <!-- ASIC：注册第二步 -> 输入手机号 -->
        <activity
            android:name="cn.com.vau.page.login.activity.SignUpAsicActivity"
            android:screenOrientation="portrait" />
        <!-- ASIC：注册第四步 -> 输入姓、名、邮箱、密码 -->
        <activity
            android:name="cn.com.vau.page.login.activity.SignUpAsicPwdActivity"
            android:screenOrientation="portrait" />
        <!-- 输入验证码页面 -->
        <activity
            android:name="cn.com.vau.page.login.activity.InputCodeActivity"
            android:screenOrientation="portrait" />
        <!-- 绑定邮箱，包括三方登录方式绑定 -->
        <activity
            android:name="cn.com.vau.page.login.activity.BindEmailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="cn.com.vau.profile.activity.kycAuth.KycAuthActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- kyc-绑定邮箱 -->
        <activity
            android:name="cn.com.vau.profile.activity.kycLink.BindEmailKycActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- kyc-绑定手机号 -->
        <activity
            android:name="cn.com.vau.profile.activity.kycLink.BindPhoneKycActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- kyc-修改登录密码 -->
        <activity
            android:name="cn.com.vau.profile.activity.kycLink.ChangeLoginPwdKycActivity"
            android:screenOrientation="portrait" />
        <!-- kyc-修改邮箱 -->
        <activity
            android:name="cn.com.vau.profile.activity.kycLink.UpdateEmailKycActivity"
            android:screenOrientation="portrait" />
        <!-- 验证邮箱验证码 -->
        <activity
            android:name="cn.com.vau.page.login.activity.VerifyEmailCodeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!-- 验证sms验证码 -->
        <activity
            android:name="cn.com.vau.page.login.activity.VerifySmsCodeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!-- kyc-设置/修改资金密码 -->
        <activity
            android:name="cn.com.vau.profile.activity.addOrForgotSecurityPWD.SetFundsPwdKycActivity"
            android:screenOrientation="portrait" />
        <!-- kyc-修改手机号 -->
        <activity
            android:name="cn.com.vau.profile.activity.kycLink.UpdatePhoneKycActivity"
            android:screenOrientation="portrait" />
        <!-- kyc-修改手机号 -->
        <activity
            android:name=".demo.DashedTextDemoActivity"
            android:screenOrientation="portrait" />

    </application>

</manifest>