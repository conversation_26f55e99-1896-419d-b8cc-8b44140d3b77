package cn.com.vau.page.user.forgotPwdSecond

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.profile.activity.changeLoginPWD.ChangePwdContract
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class ForgetPwdSecondModel : ForgetPwdSecondContract.Model, ChangePwdContract.Model {
//    override fun getMobileApi(map: java.util.HashMap<String, Any>, baseObserver: BaseObserver<GetMobileBean>): Disposable {
//        HttpUtils.loadData(
//                RetrofitHelper.getHttpService().getPhoneNumApi(map),
//                baseObserver
//        )
//        return baseObserver.disposable
//    }

    override fun goEditPwdApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().forgetpwdForgetUpPwdApi(map),
                baseObserver
        )
        return baseObserver.disposable
    }

    override fun getVerificationCodeApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().forgetpwdGetVerificationCodeApi(map),
                baseObserver
        )
        return baseObserver.disposable
    }

    override fun smsValidateSmsForgetPwdCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().smsValidateSmsForgetPwdCodeApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun validateEmailForgetPwdCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().forgetpwdOtpVerificationApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

//    override fun checkVerificationCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
//        HttpUtils.loadData(
//                RetrofitHelper.getHttpService().smsValidateSmsForgetPwdCodeApi(map),
//                baseObserver
//        )
//        return baseObserver.disposable
//    }

    override fun getWithdrawRestrictionMsgApi(
        map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().fundWithdrawRestrictionMessageApi(map), baseObserver)
        return baseObserver.disposable
    }

    /**
     * 校验邮箱验证码
     */
    override fun validateEmailFirstLoginCodeApi(
        map: HashMap<String, Any?>,
        baseObserver: BaseObserver<BaseBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().emailPreValidateEmailCodeApi(map), baseObserver)
        return baseObserver.disposable
    }

    /**
     * 发送邮箱验证码
     */
    override fun emailSendEmailCodeApi(
        map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().emailSendEmailCodeApi(map), baseObserver)
        return baseObserver.disposable
    }
}
