package cn.com.vau.page.user.openSameNameAccount

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.NDBStatusBean
import cn.com.vau.data.account.OpenWalletBean
import cn.com.vau.data.account.PlatFormAccountData
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 */
interface OpenSameNameAccountContract {
    interface Model : BaseModel {
        fun getPlatFormAccountTypeCurrency(
            map: HashMap<String, String>, baseObserver: BaseObserver<PlatFormAccountData>
        ): Disposable

        fun openSameAccount(
            map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>
        ): Disposable

        fun checkNdbPromoApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<NDBStatusBean>
        ): Disposable

        fun exitNdbPromoApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>
        ): Disposable

        fun accountOpenAccountValidate(baseObserver: BaseObserver<OpenWalletBean>): Disposable
    }

    interface View : BaseView {
        fun refreshPlatFormView()
        fun refreshAccountTypeView()
        fun refreshAccountCurrencyView()
        fun showOpenSuccessDialog()
        fun showAccountTypeDialog()
        fun showCurrencyDialog()
        fun changeButtonState(isClickable: Boolean)
        fun checkNdbPromoResult(status: Int?) //status: 1：正在参加活动 ；0：未参加活动
        fun exitNdbPromoSuc() //退出NDB活动成功
        fun showWallet() // 显示钱包相关
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getPlatFormAccountTypeCurrency()
        abstract fun openSameAccount()
        abstract fun checkNdbPromoApi() // 检查是否有NDB活动
        abstract fun exitNdbPromoApi() // 退出NDB活动
        abstract fun accountOpenAccountValidate() // 是否可以开通钱包
    }

}
