package cn.com.vau.page

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * 输入描述
 * Created by THINKPAD on 2020/7/30.
 */
@Keep
@Parcelize
data class ScanCreditData(
    var bank_name: String?,//"中国银行",      # 图片中没有的话，返回""
    var card_num: String?,//"****************",
    var valid_date: String?,//"03/17",             # 图片中没有的话，返回""；如有多个，则逗号分隔开"03/17,04/05"
    var card_type: String?,//"DC",       #DC(借记卡),  CC(贷记卡),  SCC(准贷记卡), DCC(存贷合一卡), PC(预付卡)
    var is_fake: String?,// false,                   #是否是复印件
    var request_id: String?,//"20190806163620_e8b744b981620b764c4abb9537533287",
    var success: String?//true
) : Parcelable
