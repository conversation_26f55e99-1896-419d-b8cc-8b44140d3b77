package cn.com.vau.page.user.openAccountFirst

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.EmploymentBean
import cn.com.vau.data.account.MoreAboutYouBean
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.data.account.RealAccountCacheBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 *
 */
open class OpenAccountCacheModel : OpenAccountCacheContract.Model {
    override fun getRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getData(map), baseObserver)
        return baseObserver.disposable
    }

    override fun saveRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().process(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getSelectData(map: HashMap<String, String>, baseObserver: BaseObserver<MoreAboutYouBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getAccountSelect(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getEmploymentData(map: HashMap<String, String>, baseObserver: BaseObserver<EmploymentBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().employmentFinance(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getFinanceData(map: HashMap<String, String>, baseObserver: BaseObserver<EmploymentBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().trading(map), baseObserver)
        return baseObserver.disposable
    }

    override fun checkEmail(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().emailIsExist(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getPlatFormAccountTypeCurrency(map: HashMap<String, String>, baseObserver: BaseObserver<PlatFormAccountData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getPlatFormAccountTypeCurrency(map), baseObserver)
        return baseObserver.disposable
    }
}
