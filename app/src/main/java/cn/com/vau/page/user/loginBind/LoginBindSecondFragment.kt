package cn.com.vau.page.user.loginBind

import android.annotation.SuppressLint
import android.os.*
import android.text.TextUtils
import android.view.*
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.FragmentLoginBindSecondBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.CountDownTextHelper
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoWithIconListDialog
import com.netease.nis.captcha.*
import kotlinx.coroutines.delay

/**
 * 绑定手机号
 */
@SuppressLint("SetTextI18n")
class LoginBindSecondFragment : BaseFrameFragment<LoginBindSecondPresenter, LoginBindModel>(), LoginBindContract.View, PasswordView.PasswordListener {

    private val mBinding: FragmentLoginBindSecondBinding by lazy { FragmentLoginBindSecondBinding.inflate(layoutInflater) }

    var captcha: Captcha? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { requireContext().getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    override fun initParam() {
        super.initParam()
        arguments?.let {
            mPresenter.areaCodeData = it.getSerializable("bind_data_bean") as SelectCountryNumberObjDetail
            mPresenter.smsSendType = it.getString(VerificationActivity.KEY_SEND_TYPE) ?: VerificationActivity.TYPE_SEND_SMS
        }

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()

        mBinding.passwordView.setPasswordListener(this)

        mBinding.tvPhoneNumber.text = "+${mPresenter.areaCodeData?.countryNum} ${mPresenter.areaCodeData?.mobile}"

        mBinding.tvLoginType.text = "${getString(R.string.the_verification_code_has_been_sent_to)}:"

        checkTitleTextViewShow()

        lifecycleScope.launchWhenResumed {
            delay(500)
            mBinding.passwordView.showSoftInput()
        }
    }

    override fun passwordComplete() {
        val code = mBinding.passwordView.getPassword()
        if (code.length == 6) {
            mPresenter.bindEmailApi(mPresenter.areaCodeData?.mobile, code)
            mBinding.passwordView.hiddenSoftInputFromWindow()
        }
    }

    override fun initListener() {
        mBinding.mHeaderBar.setStartBackIconClickListener {
            back()
        }.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        mBinding.tvReSendEms.setOnClickListener(this)
        mBinding.tvSendEms.setOnClickListener(this)
        mBinding.llWhatsApp.setOnClickListener(this)
        // 收不到验证码提示文案
        mBinding.tvNotReceiveCodeTips.setOnClickListener {
            showNotReceiveCodeDialog()
        }

        mPresenter.initSendCodeUtil(object : SendCodeUtil.SendCodeListener {
            override fun onFinish() {
                if (activity == null || activity?.isFinishing == true || activity?.isDestroyed == true || !isAdded) return
                mBinding.tvReSendEms.text = ac.getString(R.string.resend)
                mBinding.tvReSendEms.isEnabled = true
                mBinding.tvReSendEms.setTextColor(ce35728)

                mBinding.tvSendEms.isEnabled = true
                mBinding.llWhatsApp.isEnabled = true
                if (mBinding.tvSendEms.isVisible) {
                    mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                    mBinding.tvSendEms.setTextColor(
                        AttrResourceUtil.getColor(requireActivity(), R.attr.color_cebffffff_c1e1e1e)
                    )
                }
                if (mBinding.llWhatsApp.isVisible)
                    mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            }

            override fun onTick(millisUntilFinished: Int) {
                if (activity == null || activity?.isFinishing == true || activity?.isDestroyed == true || !isAdded) return
                val second = millisUntilFinished.toString() // 倒计时秒数
                val fullText = getString(R.string.resend_code_in_x_seconds, second)
                mBinding.tvReSendEms.setTextColor(color_c1e1e1e_cebffffff)
                mBinding.tvReSendEms.text = countDownTextHelper.updateCountDownText(fullText, second)
                mBinding.tvReSendEms.isEnabled = false

                mBinding.tvSendEms.isEnabled = false
                mBinding.llWhatsApp.isEnabled = false
                if (mPresenter.smsSendType == VerificationActivity.TYPE_SEND_SMS) {
                    mBinding.llWhatsApp.isVisible = true
                    mBinding.tvSendEms.isVisible = false
                } else {
                    mBinding.tvSendEms.isVisible = true
                    mBinding.llWhatsApp.isVisible = false
                }
                if (mBinding.tvSendEms.isVisible) {
                    mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
                    mBinding.tvSendEms.setTextColor(
                        AttrResourceUtil.getColor(requireActivity(), R.attr.color_c731e1e1e_c61ffffff)
                    )
                }
                if (mBinding.llWhatsApp.isVisible)
                    mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            }

        })
        if (mPresenter.isFirstCount) {
            mPresenter.startSendCodeUtil()
            mPresenter.isFirstCount = false
        }

    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getCodeApi(
                        mPresenter.areaCodeData?.mobile?.trim()?.ifEmpty { mPresenter.areaCodeData?.mobile }, validate
                    )
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
    }

    private fun checkTitleTextViewShow() {
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvReSendEms -> {
                mPresenter.phoneIsUsedApi(mPresenter.areaCodeData?.mobile)
            }

            R.id.tvSendEms -> {
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_SMS
                mBinding.passwordView.clearInput()
                mPresenter.phoneIsUsedApi(mPresenter.areaCodeData?.mobile)
            }

            R.id.llWhatsApp -> {
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_WA
                mBinding.passwordView.clearInput()
                mPresenter.phoneIsUsedApi(mPresenter.areaCodeData?.mobile)
            }
        }
    }

    override fun back() {
        if (!NavHostFragment.findNavController(this).popBackStack()) activity?.finish()
    }

    override fun goSecond() {

    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    @SuppressLint("SetTextI18n")
    override fun showLocalAreaInfo() {

    }

    companion object {

        @JvmStatic
        fun newInstance() =
            LoginBindSecondFragment()
    }

    override fun passwordChange(changeText: String?) {
    }

    override fun keyEnterPress(password: String?, isComplete: Boolean) {
    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        mBinding.passwordView.hiddenSoftInputFromWindow()
        val data = arrayListOf(
            HintLocalData(
                getString(R.string.double_check_your_phone_number),
                getString(R.string.ensure_you_have_it_correctly),
                AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips1)
            ),
            HintLocalData(
                getString(R.string.wait_a_few_minutes),
                getString(R.string.delays_can_happen_occasionally),
                AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips2)
            ),
            HintLocalData(
                getString(R.string.try_another_verification_method),
                getString(R.string.switch_verification_methods_your_otp),
                AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips3)
            )
        )
        BottomInfoWithIconListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }

    override fun onDetach() {
        super.onDetach()
        mPresenter.stopSendCodeUtil()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (captcha != null) captcha?.destroy()
    }
}