package cn.com.vau.page.html

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.util.AESUtil
import cn.com.vau.util.AppUtil
import cn.com.vau.util.IPUtil
import cn.com.vau.util.MD5Util
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.UtilApp
import cn.com.vau.util.ifNull
import cn.com.vau.util.language.LanguageHelper

/**
 * Filename: NewHtmlViewModel
 * Author: GG
 * Date: 2025/1/8
 * Description:
 */
class NewHtmlViewModel : BaseViewModel() {

    var appTitle: String = ""
    var title: String = ""
    var oldTitle: String = ""

    // 是否是入金三方页面
    var isDeposit3: Boolean = false
    var isUseAppTitle = false

    var url: String = ""
        set(value) {
            if (value.startsWith("http") && !isDeposit3) {
                field = setDefaultUrl(value)
                field = setUserInfo(field)
                if (dataMap.isNotEmpty())
                    field = setOtherData(field)
                field = setDebugConfig(field)
            } else {
                field = value
            }
        }

    val dataMap: MutableMap<String, String?> by lazy { mutableMapOf() }

    /**
     * 是否可以返回 ,是否有上一级页面， 由h5 传来参数 控制 不用h5控制 直接关闭
     */
    var htmlIsCanBack: Boolean = false

    /**
     * 是否可以直接关闭 ,由h5 传来参数 控制 ，默认 false ，不用h5控制 直接关闭
     */
    var htmlIsCanClose: Boolean = false

    var isPDFOnly = false

    /**
     * 是否使用h5传来的icon列表
     */
    var isUserH5Icon = false

    /**
     * 是否监听到了kyc关闭的通知，默认为false，只有监听到了 才设置true ，用于拦截切换账户时的页面刷新，kyc关闭时 有自己的刷新
     */
    var isListenKycClose = false

    /**
     * 获取sign参数
     */
    fun getSign(): String {
        val appToken = UserDataUtil.loginToken()
        val appUserId = SpManager.getCrmUserId()
        val sign = appToken + appToken.takeLast(5) + appUserId + appUserId.reversed()
        return MD5Util.sha256Hex(sign).uppercase()
    }

    private fun getAESStr(string: String): String {
        return AESUtil.encryptAES(string, AESUtil.PWD_AES_KEY).replace("+", "-")
            .replace("/", "_")
            .replace("=", "")
    }

    /**
     * 设置url的基础传参
     */
    private fun setDefaultUrl(baseUrl: String): String {
        return buildString {
            append(baseUrl)
            if (!baseUrl.contains("?"))
                append("?")
            if (!this.contains("theme=")) {
                if (this.endsWith("?")) {
                    append("theme=${SpManager.getStyleState(0)}")
                } else {
                    append("&theme=${SpManager.getStyleState(0)}")
                }
            }
            if (!this.contains("lang="))
                append("&lang=${LanguageHelper.getHtmlLang()}")
            if (!this.contains("deviceVersion="))
                append("&deviceVersion=${AppUtil.getSdkVersion()}")

            if (!this.contains("appVersion="))
                append("&appVersion=${AppUtil.getVersionName()}")
            if (!this.contains("device="))
                append("&device=android")
            if (!this.contains("uuid="))
                append("&uuid=${getAESStr(AppUtil.uniqueID)}")
            if (!this.contains("ip="))
                append("&ip=${getAESStr(IPUtil.getIPAddress(UtilApp.getApp()).ifNull())}")
        }
    }

    /**
     * 设置url的用户信息传参
     */
    private fun setUserInfo(urlField: String): String {
        return buildString {
            append(urlField)
            if (!urlField.contains("?"))
                append("?")

            if (!this.contains("token=") && UserDataUtil.loginToken().isNotBlank())
                append("&token=${UserDataUtil.loginToken()}")
            if (!this.contains("userId=") && UserDataUtil.userId().isNotBlank())
                append("&userId=" + UserDataUtil.userId())
            val accountId = if (UserDataUtil.isStLogin()) UserDataUtil.stAccountId() else UserDataUtil.accountCd()
            if (!this.contains("accountId=") && accountId.isNotBlank())
                append("&accountId=${accountId}")
            if (!this.contains("serverAccountId=") && UserDataUtil.accountCd().isNotBlank())
                append("&serverAccountId=${UserDataUtil.accountCd()}")
            if (!this.contains("mt4=") && UserDataUtil.accountCd().isNotBlank())
                append("&mt4=${UserDataUtil.accountCd()}")
            if (!this.contains("xtoken="))
                append("&xtoken=${UserDataUtil.xToken()}")
            val sign = getSign()
            if (!this.contains("sign=") && sign.isNotBlank())
                append("&sign=${sign}")
            if (!this.contains("crmUserId=") && SpManager.getCrmUserId().isNotBlank())
                append("&crmUserId=${SpManager.getCrmUserId()}")
        }
    }

    /**
     * 设置url的其他传参
     */
    private fun setOtherData(urlField: String): String {
        return buildString {
            append(urlField)
            if (!urlField.contains("?"))
                append("?")
            dataMap.forEach { (key, value) ->
                if (!this.contains(key) && !value.isNullOrEmpty())
                    append("&$key=$value")
            }
        }
    }

    /**
     * 设置debug下才添加的入参
     */
    private fun setDebugConfig(string: String): String {
        return buildString {
            append(string)
            if (!HttpUrl.official) {
                if (!this.contains("servertype="))
                    append("&servertype=${HttpUrl.BaseUrl}")
                if (!this.contains("stServer="))
                    append("&stServer=${HttpUrl.BaseStTradingUrl}")
            }
        }
    }

    /**
     * 需要显示进度的接口
     * 目前需要进度条的页面有 钱包、转账
     */
    fun isNeedProgress(): Boolean {
        return isWalletUrl(url) || isTransferUrl(url) || isDepositUrl(url)
    }

    /**
     * 包含了以下字符串的url 不显示默认的标题
     */
    fun hideDefaultTitleUrl(): Boolean {
        return url.contains("/shadowAccount")
    }

    /**
     * 是否是pdf的url
     */
    fun isPdfUrl(checkString: String = url) = checkString.contains(".pdf") || checkString.contains(".PDF")

    fun getIbpUrl() {
        requestNet({
            baseService.getIbpUrl(token = UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            it.data?.obj?.crmUserId?.let {
                SpManager.putCrmUserId(it)
            }
            sendEvent(DataEvent(NoticeConstants.JS.HTML_IB_URL, it.data?.obj?.url))
        }, onError = {
            hideLoading()
        })
    }

    companion object {

        /**
         * 是否是钱包的url
         */
        fun isWalletUrl(url: String): Boolean {
            return url.lowercase().contains("appwallet/wallet")
        }

        /**
         * 是否是转账的url
         */
        fun isTransferUrl(url: String): Boolean {
            return url.lowercase().contains("funds/active/transfer")
        }

        /**
         * 是否是入金的url
         */
        fun isDepositUrl(url: String): Boolean {
            return url.lowercase().contains("funds/active/deposit")
        }

        /**
         * 要跳转的url是否需要登录
         */
        fun isNeedLogin(url: String): Boolean {
            return isWalletUrl(url) || isTransferUrl(url) || isDepositUrl(url)
        }

        /**
         * 跳转新h5页面的url
         */
        fun isJumpNewHtml(url: String): Boolean {
            return url.contains("nativeTitle") || url.contains("announcement") || url.contains("shadowAccount") || isWalletUrl(url) || isTransferUrl(url) || isDepositUrl(url)
        }
    }
}