package cn.com.vau.page

import androidx.annotation.Keep

@Keep
data class AWSMessageData(
    val Attributes: MessageAttributes,
    val Content: String,
    val Id: String,
    val RequestId: String,
    val SendTime: String,
    val Sender: Sender,
    val Type: String,
    val EventName: String?, //aws:DELETE_MESSAGE
    var messageType : Int = 0
)
@Keep
data class Sender(
    val Attributes: Attributes,
    val UserId: String
)
@Keep
data class Attributes(
    val photo: String,
    val userName: String
)