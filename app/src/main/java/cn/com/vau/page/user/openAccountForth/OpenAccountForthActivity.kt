package cn.com.vau.page.user.openAccountForth

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.RealAccountCacheObj
import cn.com.vau.databinding.ActivityOpenAcountForthWhiteBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstSecondActivity
import cn.com.vau.page.user.openAccount.OpenAccountSuccessAsicActivity
import cn.com.vau.page.user.openAccountFirst.*
import cn.com.vau.page.user.openAccountSecond.OpenAccountSecondActivity
import cn.com.vau.page.user.openAccountThird.OpenAccountThirdActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by Haipeng.
 * 开通真实账户 第四步
 */
class OpenAccountForthActivity :
    BaseFrameActivity<OpenAccountForthPresenter, OpenAccountCacheModel>(),
    OpenAccountCacheContract.View {

    private var urlTextColor: Int = 0
    private val binding by lazy { ActivityOpenAcountForthWhiteBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
    }

    override fun initParam() {
        super.initParam()
        mPresenter.currency = intent?.extras?.getString("currency") ?: ""
        mPresenter.accountType = intent?.extras?.getInt("accountType") ?: 1
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initView() {
        super.initView()
        urlTextColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        mPresenter.getRealInfo()

        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrack()
    }

    override fun initListener() {
        super.initListener()
        binding.tvNext.setOnClickListener(this)
        binding.mHeaderBar.setStartBackIconClickListener {
            if (mPresenter.rejectOpenAccount) {
                finish()
            } else {
                goBack()
            }
        }
        binding.mHeaderBar.setEndIconClickListener {
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstSecondActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountSecondActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountThirdActivity::class.java)
            finish()
        }
        binding.mHeaderBar.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        binding.cbAgreement.setOnCheckedChangeListener { _, b ->
            binding.tvNext.setBackgroundResource(
                if (b)
                    R.drawable.bitmap_icon2_next_active
                else
                    R.drawable.bitmap_icon2_next_inactive
            )
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> {
                if (!binding.cbAgreement.isChecked) return
                mPresenter.isChecked = binding.cbAgreement.isChecked
                mPresenter.saveRealInfo()

                // 神策自定义埋点(v3500)，点击事件
                sensorsTrackClick()
            }
        }
    }

    private var isStAccountType = false

    override fun showRealInfo(data: RealAccountCacheObj?) {
        isStAccountType = data?.isStAccountType ?: false
        showAgreement()
        val supervisionType = SpManager.getSuperviseNum("")
        if (supervisionType != "1") {
            binding.stepView.setStepNum(6)
            binding.stepView.setStepNumTotal(6)
        }
        binding.cbAgreement.isChecked = (data?.readingProtocol ?: 0) == 1
    }

    // 根据监管显示不同文案
    @SuppressLint("SetTextI18n")
    private fun showAgreement() {
        val supervisionType = SpManager.getSuperviseNum("")
        if (supervisionType == "1") {
            binding.tvDeclaration6.post {
                showAgreementAsic()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun goBack() {
        finish()
        val secondActivity = ActivityManagerUtil.getInstance().secondActivity
        if (secondActivity !is OpenAccountThirdActivity) {
            val bundle = Bundle()
            openActivity(OpenAccountThirdActivity::class.java, bundle)
        }
    }

    override fun refreshOpenGuide() {
        super.refreshOpenGuide()
        EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
    }

    override fun showSecondStepDialog(
        resultCode: String?,
        msgInfo: String?,
        url: String?,
        isFrom: Int,
        cacheData: RealAccountCacheObj?
    ) {

    }

    override fun registerRealAccountSuccess(msgInfo: String?) {

        if (mPresenter.skipType == 0) mPresenter.skipType = 3
        openActivity(OpenAccountSuccessAsicActivity::class.java, Bundle().apply {
            putInt("type", mPresenter.skipType)
            putBoolean("isAppraisal", mPresenter.isAppraisal ?: true)
        })

        // 刷新账户列表
        EventBus.getDefault().post(NoticeConstants.REFRESH_ACCOUNT_MANAGER)
        finish()
    }

    @SuppressLint("SetTextI18n")
    private fun showAgreementAsic() {
        binding.tvDeclaration2
            .set("Vantage Global Prime Pty Ltd's Financial Services Guide", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Financial_Services_Guide.pdf?v=********", -3)
            }
            .set("Product Disclosure Statement", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Product_Disclosure_Statement_Retail_Client.pdf", -3)
            }
            .set("Terms and Conditions", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Retail_Client_Terms_and_Conditions.pdf", -3)
            }
            .set("Privacy Policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Global_Prime_Pty_Ltd_Privacy_Policy.pdf?v=********", -3)
            }
            .set("Vantage FX Pty Ltd's Privacy Policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_FX_Pty_Ltd_Privacy_Policy.pdf?v=********", -3)
            }
        // ASIC监管注册流程更新--应对25年合规审查 去掉该文案和链接 by Chelsea
//            .set("Target Market Determination", urlTextColor) {
//                goWebView("https://www.vantagemarkets.com/au-resources/pdf/Vantage_Target_Market_Determination.pdf?v=********", -3)
//            }
        binding.tvDeclaration8
            .set("Vantage's deposits and withdrawals policy", urlTextColor) {
                goWebView("https://www.vantagemarkets.com/en-au/deposit-withdrawals-policy/", -2)
            }
        binding.tvDeclaration9.isVisible = isStAccountType
        if (isStAccountType) {
            binding.tvDeclaration9.text = "8.${getString(R.string.st_agreements_text)}"
            binding.tvDeclaration9
                .set(getString(R.string.copy_trading_terms_and_conditions), urlTextColor) {
                    openActivity(HtmlActivity::class.java, Bundle().apply { putInt("tradeType", 16) })
                }
        }
    }

    private fun goWebView(url: String, tradeType: Int) {
        val bundle = Bundle()
        bundle.putString("title", getString(R.string.app_name_upper))
        bundle.putString("url", url)
        bundle.putInt("tradeType", tradeType)
        openActivity(HtmlActivity::class.java, bundle)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "ASIC") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "ASIC-Next") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }
}
