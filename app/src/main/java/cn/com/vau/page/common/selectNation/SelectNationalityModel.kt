package cn.com.vau.page.common.selectNation

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.SelectNationalityBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class SelectNationalityModel : SelectNationalityContract.Model {
    override fun getNationalityData(baseObserver: BaseObserver<SelectNationalityBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().queryNationalitys,
                baseObserver
        )
        return baseObserver.disposable
    }
}
