package cn.com.vau.page.common.selectResidence.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.TextView;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.data.account.ResidenceObj;
import cn.com.vau.data.account.ResidenceObjList;

/**
 * Created by zhy on 2018/12/20.
 */

public class ResidenceAdapter extends BaseExpandableListAdapter {

    private Context mContext;
    private List<ResidenceObj> mList;
    private OnNationSelectedListener onNationSelectedListener;
    private int mType = 0;//0国家；1省份；2城市

    public ResidenceAdapter(Context mContext, List<ResidenceObj> mList, int type) {
        this.mContext = mContext;
        this.mList = mList;
        this.mType = type;
    }

    @Override
    public int getGroupCount() {
        return mList != null ? mList.size() : 0;
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        return mList.get(groupPosition).list != null ? mList.get(groupPosition).list.size() : 0;
    }

    @Override
    public Object getGroup(int groupPosition) {
        return mList.get(groupPosition);
    }

    @Override
    public Object getChild(int groupPosition, int childPosition) {
        return mList.get(groupPosition).list.get(childPosition);
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return true;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView, ViewGroup parent) {
        GroupViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.item_select_country_number_letter, null);
            holder = new GroupViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (GroupViewHolder) convertView.getTag();
        }
        if(mList.get(groupPosition).lettername.equals("HOT")) {
            holder.letterView.setText(mContext.getString(R.string.popular));
        } else {
            holder.letterView.setText(mList.get(groupPosition).lettername);
        }

        if (groupPosition == 0) {
            holder.viewTopLine.setVisibility(View.GONE);
        } else {
            holder.viewTopLine.setVisibility(View.VISIBLE);
        }

        return convertView;
    }

    @Override
    public View getChildView(final int groupPosition, final int childPosition, boolean isLastChild, View convertView, ViewGroup parent) {
        ChildViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.item_select_nation, null);
            holder = new ChildViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ChildViewHolder) convertView.getTag();
        }
        ResidenceObjList bean = mList.get(groupPosition).list.get(childPosition);
        if (mType == 0) {
            holder.tvNationName.setText(bean.countryNameEn);
        } else if (mType == 1) {
            holder.tvNationName.setText(bean.provinceNameEn);
        } else {
            holder.tvNationName.setText(bean.cityNameEn);
        }
        holder.viewNationName.setVisibility(isLastChild ? View.GONE : View.VISIBLE);

        holder.tvNationName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onNationSelectedListener.onSelected(groupPosition, childPosition);
            }
        });
        return convertView;
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return false;
    }

    class GroupViewHolder {
        TextView letterView;
        View viewTopLine;

        public GroupViewHolder(View convertView) {
            letterView = convertView.findViewById(R.id.tvItemLetter);
            viewTopLine = convertView.findViewById(R.id.viewTopLine);
        }
    }

    class ChildViewHolder {
        TextView tvNationName;
        View viewNationName;

        public ChildViewHolder(View convertView) {
            tvNationName = convertView.findViewById(R.id.tvNationName);
            viewNationName = convertView.findViewById(R.id.viewNationName);
        }
    }

    //===================================interface==================================================

    public void setOnNationSelectedListener(OnNationSelectedListener onNationSelectedListener) {
        this.onNationSelectedListener = onNationSelectedListener;
    }

    public interface OnNationSelectedListener {
        void onSelected(int groupPosition, int childPosition);
    }

}
