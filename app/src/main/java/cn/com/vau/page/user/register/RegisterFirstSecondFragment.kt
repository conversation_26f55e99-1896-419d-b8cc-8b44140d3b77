package cn.com.vau.page.user.register

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.view.PasswordView
import cn.com.vau.databinding.FragmentRegisterFirstSecondBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.RegisterRequestBean
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.delay

@SuppressLint("SetTextI18n")
class RegisterFirstSecondFragment : BaseFrameFragment<RegisterFirstSecondPresenter, RegisterModel>(), RegistestContract.View, PasswordView.PasswordListener {

    private val mBinding: FragmentRegisterFirstSecondBinding by lazy { FragmentRegisterFirstSecondBinding.inflate(layoutInflater) }

    private val mViewModel by activityViewModels<RegisterViewModel>()

    private var mCaptcha: Captcha? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            mPresenter.registerRequestBean = it.getSerializable("registerRequestBean") as RegisterRequestBean
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    override fun initView() {
        super.initView()
        mBinding.tvLoginType.text = "${getString(R.string.the_verification_code_has_been_sent_to)}:"
        mBinding.tvPhoneNumber.text = "+${mPresenter.registerRequestBean.countryNum} ${mPresenter.registerRequestBean.mobile}"

        if (mViewModel.smsSendType == VerificationActivity.TYPE_SEND_SMS) {
            mBinding.llWhatsApp.isVisible = true
            mBinding.tvSendEms.isVisible = false
        } else {
            mBinding.llWhatsApp.isVisible = false
            mBinding.tvSendEms.isVisible = true
        }

        lifecycleScope.launchWhenResumed {
            delay(500)
            mBinding.passwordView.showSoftInput()
        }
    }

    override fun initData() {
        // 倒计时开始
        if (mPresenter.isFirstCount) {
            mPresenter.startSendCodeUtil()
            mPresenter.isFirstCount = false
        }
    }

    override fun passwordChange(changeText: String?) {
    }

    override fun passwordComplete() {
        val code = mBinding.passwordView.getPassword()
        if (code.length == 6) {
            mPresenter.checkSmsCodeApi(code)
            mBinding.passwordView.hiddenSoftInputFromWindow()
        }
    }

    override fun keyEnterPress(password: String?, isComplete: Boolean) {
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setStartBackIconClickListener {
            if (!NavHostFragment.findNavController(this).popBackStack())
                activity?.finish()
        }.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        mBinding.passwordView.setPasswordListener(this)
        // 重新发送
        mBinding.tvReSendEms.clickNoRepeat {
            mPresenter.getCodeApi("", mViewModel.smsSendType)
        }
        //
        mBinding.tvSendEms.clickNoRepeat {
            mViewModel.smsSendType = VerificationActivity.TYPE_SEND_SMS
            mBinding.passwordView.clearInput()
            mPresenter.getCodeApi("", mViewModel.smsSendType)
        }
        mBinding.llWhatsApp.clickNoRepeat {
            mViewModel.smsSendType = VerificationActivity.TYPE_SEND_WA
            mBinding.passwordView.clearInput()
            mPresenter.getCodeApi("", mViewModel.smsSendType)
        }

        mPresenter.initSendCodeUtil(object : SendCodeUtil.SendCodeListener {
            override fun onTick(millisUntilFinished: Int) {
                mBinding.tvReSendEms.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
                mBinding.tvReSendEms.text = getString(R.string.resend_code_in_x_seconds, millisUntilFinished.toString())
                mBinding.tvReSendEms.isEnabled = false
                mBinding.tvSendEms.isEnabled = false
                if (mBinding.llWhatsApp.isVisible) {
                    mBinding.llWhatsApp.isEnabled = false
                    mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
                }
            }

            override fun onFinish() {
                mBinding.tvReSendEms.setTextColor(ContextCompat.getColor(requireContext(), R.color.ce35728))
                mBinding.tvReSendEms.text = ac.getString(R.string.resend)
                mBinding.tvReSendEms.isEnabled = true
                mBinding.tvSendEms.isEnabled = true
                if (mBinding.llWhatsApp.isVisible) {
                    mBinding.llWhatsApp.isEnabled = true
                    mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
                }
            }
        })
    }

    override fun jumpToLogin(handleType: Int, msg: String?) {
        ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
        val bundle = Bundle()
        bundle.putInt(Constants.HANDLE_TYPE, 99)
//        bundle.putString(Constants.DATA_MSG, msg)
        openActivity(LoginPwdActivity::class.java, bundle)
        activity?.finish()
    }

    override fun goSecond() {
        val bundle = Bundle()
        bundle.putSerializable("registerRequestBean", mPresenter.registerRequestBean)
        NavHostFragment.findNavController(this).navigate(R.id.actionRegisterFirstToSecond, bundle)
    }

    override fun onDetach() {
        super.onDetach()
        mPresenter.stopSendCodeUtil()
    }

    companion object {
        @JvmStatic
        fun newInstance() = RegisterFirstSecondFragment()
    }

    override fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(requireContext()) {
            mPresenter.getCodeApi(it, mViewModel.smsSendType)
        }
        mCaptcha?.validate()
    }

    override fun onDestroy() {
        super.onDestroy()
        mCaptcha?.destroy()
    }
}