package cn.com.vau.page.user.openAccountSecond

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.page.user.openAccountFirst.OpenAccountCacheContract
import cn.com.vau.data.account.RealAccountCacheBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface OpenAccountSecondContract {
    interface Model : BaseModel {
        fun getRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable
        fun saveRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
    }

    interface View : BaseView, OpenAccountCacheContract.View {
    }

    abstract class Presenter : OpenAccountCacheContract.Presenter() {
        open fun saveAddressInfo() {}
    }

}
