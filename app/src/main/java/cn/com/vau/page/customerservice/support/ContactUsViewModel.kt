package cn.com.vau.page.customerservice.support

import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 24 09:34
 * @updateUser:
 * @updateDate: 2025 4月 24 09:34
 */
class ContactUsViewModel : BaseViewModel() {

    init {
        consultContactus()
    }

    fun consultContactus() {
        requestNet({
            baseService.consultContactus(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(it.data)
        }, isShowDialog = true)
    }
}