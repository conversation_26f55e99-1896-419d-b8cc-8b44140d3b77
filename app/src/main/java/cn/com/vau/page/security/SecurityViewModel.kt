package cn.com.vau.page.security

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SecurityStatusData
import cn.com.vau.data.init.BasicMyProfileObj
import cn.com.vau.data.profile.AuthConfigObjBean
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * Filename: SecurityViewModel
 * Author: GG
 * Date: 2025/3/24
 * Description:
 */
class SecurityViewModel : BaseViewModel() {

    init {
        requestData()
    }

    var securityStatusData: SecurityStatusData.Obj? = null
    private val _basicMyProfileObjLiveData: MutableLiveData<BasicMyProfileObj> by lazy { MutableLiveData() }
    val basicMyProfileObjLiveData: LiveData<BasicMyProfileObj> get() = _basicMyProfileObjLiveData

    // 获取安全中心配置信息成功
    private val _getAuthConfigSuccessLiveData = MutableLiveData<Pair<String, AuthConfigObjBean?>>()
    val getAuthConfigSuccessLiveData: LiveData<Pair<String, AuthConfigObjBean?>> = _getAuthConfigSuccessLiveData

    // 获取安全中心配置信息失败（自己处理的loading在接口报错无法关闭时使用这个监听）
    private val _getAuthConfigErrorLiveData = MutableLiveData<String>()
    val getAuthConfigErrorLiveData: LiveData<String> = _getAuthConfigErrorLiveData

    // 获取kyc认证等级成功（兜底方案，预防 kyc 认证等级没有请求成功）
    private val _queryUserLevelSuccessLiveData = MutableLiveData<Int>()
    val queryUserLevelSuccessLiveData: LiveData<Int> = _queryUserLevelSuccessLiveData

    /**
     * 是否是kyc验证的账户
     */
    fun isKycAccount() = SpManager.isV1V2()

    /**
     * kyc验证等级
     */
    fun kycLevel() = UserDataUtil.kycLevel().toIntCatching(-1)

    fun needShowLock(): Boolean = (UserDataUtil.isDemoAccount() && !isKycAccount()) || (isKycAccount() && kycLevel() < 1)

    fun requestData() {
        queryAccountListOnSendEmailApi()
        twoFactorStatusApi()
        userCollectDataDisplayApi()
        basicMyProfileInfoApi()
    }

    /**
     * 获取用户账户列表用于发送邮件
     */
    fun queryAccountListOnSendEmailApi() {
        requestNet({
            baseService.queryAccountListOnSendEmailApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            sendEvent(DataEvent(EVENT_GET_EMAIL_LIST, it.data?.obj))
            usersetHasfundpwdApi()
        })
    }

    /**
     * 判断用户是否设置过资金安全密码
     */
    fun usersetHasfundpwdApi() {
        requestNet({
            baseService.usersetHasfundpwdApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            sendEvent(DataEvent(EVENT_FUND_PWD, it.data?.obj))
        })
    }

    /**
     * 发送账户邮件
     */
    fun sendAccountEmailToCrmApi(accountId: String?) {
        requestNet({
            baseService.sendAccountEmailToCrmApi(UserDataUtil.loginToken(), accountId)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                sendEvent(DataEvent(EVENT_SEND_EMAIL_RESULT, false))
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_SEND_EMAIL_RESULT, true))
        }, isShowDialog = true)
    }

    /**
     * 安全等级判断
     */
    fun twoFactorStatusApi() {
        requestNet({
            baseService.twoFactorStatusApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
            }
            securityStatusData = it.data?.obj
            sendEvent(DataEvent(EVENT_TWO_FACTOR_STATUS, it.data?.obj))
        }, isShowDialog = true)
    }

    /**
     * 获取用户是否同意收集信息
     */
    fun userCollectDataDisplayApi() {
        requestNet({
            baseService.userCollectDataDisplayApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_COLLECT_DATA_DISPLAY, it.data?.obj))
        }
        )
    }

    /**
     * 获取用户的基本信息， 邮箱、手机号等以及验证状态
     */
    fun basicMyProfileInfoApi() {
        requestNet({
            baseService.basicMyProfileInfoApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            setAreaData(it.data?.obj)
            _basicMyProfileObjLiveData.value = it.data?.obj
        })
    }

    /**
     * 设置用户国家区号等缓存数据
     */
    private fun setAreaData(bean: BasicMyProfileObj?) {
        // 如果是邮箱注册登录，没有手机号，会把之前设置的缓存覆盖为空，所以判断不为空才覆盖
        if (!bean?.emailPlainText.isNullOrBlank()) {
            UserDataUtil.setEmail(bean.emailPlainText)
        }
        if (!bean?.phonePlainText.isNullOrBlank()) {
            UserDataUtil.setUserTel(bean.phonePlainText)
            SpManager.putUserTel(bean.phonePlainText.ifNull())
        }
        if (!bean?.phoneCode.isNullOrBlank()) {
            UserDataUtil.setAreaCode(bean.phoneCode)
            SpManager.putCountryNum(bean.phoneCode.ifNull())
        }
        if (!bean?.phoneCountryCode.isNullOrBlank()) {
            UserDataUtil.setCountryCode(bean.phoneCountryCode)
            SpManager.putCountryCode(bean.phoneCountryCode.ifNull())
        }
    }

    /**
     * 用户同意开启Firebase数据收集状态变更
     */
    fun userCollectDataSwitchApi() {
        requestNet({
            baseService.userCollectDataSwitchApi(UserDataUtil.loginToken())
        }, {})
    }

    /**
     * kyc-获取安全中心配置
     *
     * @param functionCode modify-email = 修改邮箱；enable-auth-2fa = 启用2FA；modify-auth-2fa = 修改2FA；modify-password = 修改密码；
     *                     modify-phone = 修改手机号；add-fund-code = 添加资金安全码；modify-fund-code = 修改资金安全码；
     *                     reset-fund-code = 忘记资金安全码
     */
    fun getAuthConfigApi(functionCode: String) {
        val map = hashMapOf<String, Any?>()
        map["functionCode"] = functionCode
        requestNet({ baseService.getAuthConfigApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                _getAuthConfigErrorLiveData.value = ""
                return@requestNet
            }
            _getAuthConfigSuccessLiveData.value = Pair(functionCode, it.data?.obj)
        }, isShowDialog = true)
    }

    fun userQueryUserLevelApi(eventTag: String) {
        requestNet({ baseService.userQueryUserLevelApi() }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            UserDataUtil.setKycLevel(it.data?.obj?.level.ifNull(0).toString())
            sendEvent(DataEvent(eventTag, it.data?.obj?.level.ifNull(0)))
            _queryUserLevelSuccessLiveData.value = it.data?.obj?.level.ifNull(0)
        })
    }

    /**
     * 神策自定义埋点(v3700)
     */
    fun sensorsTrack(buttonName: String?, bindType: Int?) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        properties.put(
            SensorsConstant.Key.FUNCTION_NAME, when (bindType) {
                0 -> "Bind"
                1 -> "Verify"
                else -> "Edit"
            }
        ) // 方法名
        properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "SecurityCenter")
        SensorsDataUtil.track(SensorsConstant.V3700.OTP_VERIFY_CLICK, properties)
    }

    companion object {
        const val EVENT_GET_EMAIL_LIST = "event_get_email_list"
        const val EVENT_SEND_EMAIL_RESULT = "event_send_email_result"
        const val EVENT_TWO_FACTOR_STATUS = "event_two_factor_status"
        const val EVENT_FUND_PWD = "event_fund_pwd"
        const val EVENT_COLLECT_DATA_DISPLAY = "event_collect_data_display"
        const val EVENT_CLICK_PASSKEY = "EVENT_CLICK_PASSKEY"
        const val EVENT_CLICK_FUNDS_PASSWORD = "EVENT_CLICK_FUNDS_PASSWORD"
    }
}