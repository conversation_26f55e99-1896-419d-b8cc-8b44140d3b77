package cn.com.vau.page.html

import android.annotation.*
import android.content.*
import android.graphics.Canvas
import android.net.Uri
import android.net.http.SslError
import android.os.*
import android.text.TextUtils
import android.util.Base64
import android.view.*
import android.view.inputmethod.InputMethodManager
import android.webkit.*
import androidx.activity.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.os.bundleOf
import androidx.core.view.*
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OpenUploadToH5Util
import cn.com.vau.common.view.pdfview.DefaultLinkHandler
import cn.com.vau.common.view.share.ShareUtils
import cn.com.vau.data.BaseBean
import cn.com.vau.databinding.ActivityHtmlBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.*
import com.github.barteksc.pdfviewer.listener.*
import com.github.barteksc.pdfviewer.util.FitPolicy
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.*
import java.io.*
import java.lang.reflect.Field
import java.net.URL

class HtmlActivity : BaseActivity(), OnLoadCompleteListener, OnErrorListener, OnPageErrorListener, OnRenderListener, OnDrawListener {

    private val mBinding by lazy { ActivityHtmlBinding.inflate(layoutInflater) }
    private val sensorsHelper = HtmlSensorsHelper()
    private val mViewModel: NewHtmlViewModel by viewModels()
    private val csDrawable by lazy {
        AttrResourceUtil.getDrawable(this, R.attr.icon1Cs)
    }
    private var type: Int = 0
    private var bundle: Bundle? = null
    private var needClearHistory: Boolean = false

    private var isNeedBack: Boolean = true

    private var isNeedFresh: Boolean = true
    private var isNeedCS: Boolean = false
    private var isNeedRightClose: Boolean = false
    private var isNoTitle = false
    private var isProgressEnable = false

    // 没有title bar 全屏模式下，点击返回键，是否可以返回上一页 ，默认false 目前只有出金页面做了传这个入参，解除了全屏模式下点击返回键的限制
    private var isNoTitleCanBack = false

    private val openUploadToH5Util: OpenUploadToH5Util = OpenUploadToH5Util(this)

    var startCurrentTimeMillis = 0L
    var endCurrentTimeMillis = 0L
    var leftResourceType = 0
    var titleName = ""

    var isPDFOnly = false
    var ruleId = ""
    var from = ""

    private var isCanBack = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        startCurrentTimeMillis = System.currentTimeMillis()
    }

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
        bundle = intent.extras
        type = bundle?.getInt("tradeType") ?: 0
        leftResourceType = bundle?.getInt("leftResourceType") ?: 0
        isPDFOnly = bundle?.getBoolean("isPDFOnly", false).ifNull(false)
        isNeedBack = bundle?.getBoolean("isNeedBack", true) ?: true   // 是否需要逐级返回
        isNeedFresh = bundle?.getBoolean("isNeedFresh", true) ?: true
        isProgressEnable = bundle?.getBoolean("isProgress", false) ?: false
        isNeedCS = bundle?.getBoolean("isNeedCS", false) ?: false
        isNeedRightClose = bundle?.getBoolean("isNeedRightClose", false) ?: false
        isNoTitleCanBack = bundle?.getBoolean("isNoTitleCanBack", false) ?: false
        ruleId = intent?.extras?.getString("ruleId") ?: ""
        from = intent?.extras?.getString("from") ?: ""
    }

    @SuppressLint("SetJavaScriptEnabled", "ObsoleteSdkInt")
    override fun initView() {
        super.initView()
        // 代码中设置webView背景色，解决夜间模式闪白屏现象
        mBinding.mWebView.setBackgroundColor(AttrResourceUtil.getColor(this, R.attr.mainLayoutBg))

        if (isProgressEnable) {
            mBinding.progressBar.visibility = View.VISIBLE
        }
        //右边icon可能是一个也可能是两个，欢哥建议先都设置上icon，再动态隐藏
        mBinding.mHeaderBar.setEndIconResource(R.drawable.draw_bitmap2_close16x16_c731e1e1e_c61ffffff)
            .setEndIcon1Resource(csDrawable)
            .setEndIconVisible(isNeedRightClose)
            .setEndIcon1Visible(isNeedCS)

        if (leftResourceType != 0)
            mBinding.mHeaderBar.setStartBackIconResource(R.drawable.draw_bitmap2_close16x16_c731e1e1e_c61ffffff)

        mBinding.mHeaderBar.setStartBackIconClickListener {
            if ("deposit" == from) {
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V343.DEPOSIT_LVL3_RETURN_BUTTON_CLICK, bundleOf(
                        "Payment_method" to titleName
                    )
                )
            }
            onBackPressed()
        }.setEndIcon1ClickListener {
            when {
                //需要刷新
                isNeedFresh -> {
                    showLoadingDialog()
                    if (mBinding.mWebView.visibility == View.VISIBLE)
                        mBinding.mWebView.reload()
                    else
                        mBinding.pdfView.loadPages()
                }
                //跳转到帮助中心页面
                isNeedCS -> {
                    openActivity(HelpCenterActivity::class.java)

                }
            }
        }.setEndIconClickListener {
            finish()
        }

        val webSettings = mBinding.mWebView.settings
        // 自适应屏幕
        webSettings.layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        webSettings.loadWithOverviewMode = true
        // 自适应可不设置，但是华为P10有问题
        webSettings.textZoom = 100
        //允许与js进行交互
        webSettings.javaScriptEnabled = true
        //允许锚点
        webSettings.domStorageEnabled = true
        webSettings.databaseEnabled = true
        // 允许多窗口，telegram授权时会调用window.open()，加上这个原生才会触发onCreateWindow()方法
        webSettings.setSupportMultipleWindows(true)
        webSettings.userAgentString = WebSettings.getDefaultUserAgent(this) + "/${Constants.PRODUCT_NAME}_android"

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
        openUploadToH5Util.setWebView(mBinding.mWebView)
        mBinding.mWebView.addJavascriptInterface(JavaScriptInterface(mContext = this, mViewModel = mViewModel, sensorsHelper = sensorsHelper), "vfx_android")

        // pdf使用
        webSettings.allowFileAccess = true
        webSettings.allowFileAccessFromFileURLs = true
        webSettings.allowUniversalAccessFromFileURLs = true
        webSettings.javaScriptCanOpenWindowsAutomatically = true

        mBinding.mWebView.webViewClient = object : WebViewClient() {

            // 通过重写WebViewClient的onReceivedSslError方法来接受所有网站的证书，忽略SSL错误。
            override fun onReceivedSslError(
                view: WebView,
                handler: SslErrorHandler,
                error: SslError,
            ) {
                handler.cancel()
                super.onReceivedSslError(view, handler, error)
                // h5流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view.url)
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                // h5流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view?.url)
            }

            override fun onReceivedHttpError(view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?) {
                super.onReceivedHttpError(view, request, errorResponse)
                // h5流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view?.url)
            }

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {

                try {
                    showLoadingDialog()
                    // 全拦截
//                    LogUtil.w("shouldOverrideUrlLoading: url:$url")
//                    return true
                    if (!TextUtils.isEmpty(url) && url.contains(".pdf")) {
                        loadPdfFile(url)
                    } else if (
                        url.contains("item.m.jd.com") ||
                        url.contains("play.google.com") ||
                        url.startsWith("scheme://") ||
                        url.startsWith("market://")
                    ) { // 跳三方 app
                        startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                    } else {
                        if (mBinding.mWebView.isGone) {
                            mBinding.mWebView.isVisible = true
                            mBinding.pdfView.isGone = true
                        }
                        view.loadUrl(url)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                return true
            }

            override fun doUpdateVisitedHistory(view: WebView, url: String, isReload: Boolean) {
                super.doUpdateVisitedHistory(view, url, isReload)
                if (needClearHistory) {
                    needClearHistory = false
                    view.clearHistory() // 清除历史记录
                }
            }

            // url加载完成
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                endCurrentTimeMillis = System.currentTimeMillis()
                hideLoadingDialog()
                // h5流程神策埋点 -> 加载成功
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_SUCCESS, url = url)
            }
        }

        mBinding.mWebView.webChromeClient = object : WebChromeClient() {
            // For Android >= 5.0
            override fun onShowFileChooser(
                webView: WebView,
                filePathCallback: ValueCallback<Array<Uri>>,
                fileChooserParams: FileChooserParams,
            ): Boolean {
                uploadMessageAboveL = filePathCallback
                openImageChooserActivity()
                return true
            }

            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (isProgressEnable) {
                    mBinding.progressBar.progress = newProgress
                    if (newProgress == 100) {
                        mBinding.progressBar.visibility = View.GONE
                        if ("deposit" == from) {
                            hideDepositLoading()
                        }
                    } else {
                        mBinding.progressBar.visibility = View.VISIBLE
                        if ("deposit" == from) {
                            showDepositLoading()
                        }
                    }
                }
            }

            // 设置webSettings.setSupportMultipleWindows(true)才会回调这里
            override fun onCreateWindow(view: WebView?, isDialog: Boolean, isUserGesture: Boolean, resultMsg: Message?): Boolean {
                LogUtil.e("onCreateWindow")
                val newWebView = WebView(this@HtmlActivity).apply {
                    settings.javaScriptEnabled = true
                    showNetDialog()
                    webViewClient = object : WebViewClient() {
                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            hideNetDialog()
                        }
                    }
//                    webChromeClient = this@webChromeClient
                }

                val layoutParams = ConstraintLayout.LayoutParams(0, 0)
                layoutParams.topToBottom = mBinding.mHeaderBar.id
                layoutParams.startToStart = mBinding.mWebView.id
                layoutParams.endToEnd = mBinding.mWebView.id
                layoutParams.bottomToBottom = mBinding.mWebView.id
                (view?.parent as? ViewGroup)?.addView(newWebView, layoutParams)

                val transport = resultMsg?.obj as? WebView.WebViewTransport
                transport?.webView = newWebView
                resultMsg?.sendToTarget()
                return true
            }

            override fun onCloseWindow(window: WebView?) {
                super.onCloseWindow(window)
                LogUtil.e("onCloseWindow")
            }
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPressed()
            }
        })
    }

    override fun initData() {
        super.initData()

        showLoadingDialog()
        if (ruleId.isNotEmpty()) notificationMarketingClickCntUpdate()

        val loginToken = UserDataUtil.loginToken()

        if (type == -1) {
            mBinding.mHeaderBar.setTitleText(bundle?.getString("title") ?: getString(R.string.app_name))
            var content = bundle?.getString("url") ?: "url-1=null"
            content = Base64.encodeToString(content.toByteArray(), Base64.DEFAULT)
            mBinding.mWebView.loadData(content, "text/html; charset=utf-8", "base64")
            // h5流程神策埋点 -> 打开页面
            sensorsHelper.init(content)
            sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.OPEN_PAGE, url = content)
            return
        }

        if (type == -2) {
            mBinding.mHeaderBar.setTitleText(bundle?.getString("title") ?: getString(R.string.app_name))
            mBinding.mWebView.loadUrl(bundle?.getString("url") ?: "url-1=null")
            // h5流程神策埋点 -> 打开页面
            sensorsHelper.init(bundle?.getString("url") ?: "url-1=null")
            sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.OPEN_PAGE, url = bundle?.getString("url") ?: "url-1=null")
            return
        }

        if (type == -3) {
            mBinding.mHeaderBar.setTitleText(bundle?.getString("title") ?: getString(R.string.app_name))
            isPDFOnly = true
            val htmlUrl = bundle?.getString("url") ?: "url-1=null"
            mBinding.mWebView.visibility = View.GONE
            mBinding.pdfView.visibility = View.VISIBLE
            val thread = Thread {
                try {
                    val input: InputStream = URL(htmlUrl).openStream()
                    loadPdfFile(input)
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            thread.start()
            return
        }

        var htmlUrl = ""
        when (type) {
            // 公告
            1 -> {
                titleName = getString(R.string.announcements)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/notice/${bundle?.getString("id")}"
            }

            3 -> {
                titleName = bundle?.getString("title") ?: getString(R.string.app_name)
                htmlUrl = bundle?.getString("url") ?: ""
            }

            4 -> {
                titleName = getString(R.string.newsletter)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/newsletter/${bundle?.getString("id")}"
            }

            // 活动条款
            7 -> { // 用不到了，与iOS同步
//                titleName = getString(R.string.tcs)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/commandOrder/index"
            }

            9 -> {
                titleName = bundle?.getString("title") ?: getString(R.string.app_name)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/regPro"
            }

            11 -> {
                titleName = getString(R.string.market_analysis)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/analysis/${bundle?.getString("id")}"
            }

            12 -> { // 产品介绍
                titleName = getString(R.string.about_us)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/productIntro"
            }

            // 隐私条款
            13 -> {
                titleName = getString(R.string.privacy_policy)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/privacyClause"
            }

            14 -> { // 免责声明
                titleName = getString(R.string.disclaimer)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/disclaimer"
            }

            15 -> {
                mBinding.mHeaderBar.setTitleText(getString(R.string.privacy_policy))
                isPDFOnly = true
                val superviseStr = SpManager.getSuperviseNum("")
                htmlUrl = when (superviseStr) {
                    "1" -> "https://www.vantagemarkets.com/en-au/company-profile/legal-documentation/VGP_PrivacyPolicy"
                    "13" -> "https://www.vantagemarkets.co.uk/privacy_policy"
                    else -> "https://www.vantagemarkets.com/company-profile/legal-documentation/vantageglobal-privacypolicy"
                }
                mBinding.mWebView.visibility = View.GONE
                mBinding.pdfView.visibility = View.VISIBLE
                val thread = Thread {
                    try {
                        val input: InputStream = URL(htmlUrl).openStream()
                        loadPdfFile(input)
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
                thread.start()
                return
            }
            // 风险提示
            16 -> {
                titleName = getString(R.string.terms_and_conditions)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/copyTrading"
            }

            17 -> {
                mBinding.mHeaderBar.visibility = View.GONE
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/socialTradingAccount/copyTrading?token=$loginToken"
            }

            // 分析师观点
            20 -> {
                val id = bundle?.getString("id")
                titleName = getString(R.string.market_analysis)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/fxStreet/$id"
            }

            21 -> {
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/noTitle/pro/index"
            }

            22 -> {
                titleName = getString(R.string.copy_trading)
                htmlUrl = HttpUrl.BaseHtmlUrl + "web/h5/active/socialTrading/index.html"    // yunyu已确认废弃不用了
            }

            // 入金银行验证
            23 -> {
                htmlUrl = HttpUrl.BaseHtmlUrl +
                        "web/h5/active/bank_channel_auth/financialWorkInformation.html?from=deposit&token=${        // H5说有特殊原因，需要改回用原来的地址
                            UserDataUtil.loginToken()
                        }&userID=${
                            UserDataUtil.userId()
                        }&lang=${LanguageHelper.getHtmlLang()}&theme=${
                            SpManager.getStyleState(0)
                        }"
            }

            // 分润示例页
            24 -> {
                titleName = getString(R.string.more_illustrations)
                htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/socialTrading/profitSharingCalculation"
            }

            25 -> {
                titleName = "IB"
                isNeedBack = false
                showNetDialog()
                mViewModel.getIbpUrl()
                return
            }
        }
        if (htmlUrl.endsWith(".pdf") || htmlUrl.endsWith(".PDF")) {
            loadPdfFile(htmlUrl)
            return
        }
        loadUrl(htmlUrl)
    }

    private fun loadUrl(url: String): String {
        val loginToken = UserDataUtil.loginToken()
        var htmlUrl = url
        htmlUrl += if (htmlUrl.contains("?")) "" else "?"
        htmlUrl = "$htmlUrl&appsflyerid=${SpManager.getAppsFlyerId()}"
        if (htmlUrl.contains("?&appsflyerid")) {
            htmlUrl = htmlUrl.replace("?&appsflyerid", "?appsflyerid")
        }
        htmlUrl =
            "$htmlUrl&promoteId=${SpManager.getGoogleAdvertisingId()}"
        htmlUrl = "$htmlUrl&uuid=${AppUtil.getUUID()}"
        if (!htmlUrl.contains("lang=")) htmlUrl =
            htmlUrl + "&lang=" + LanguageHelper.getHtmlLang()
        if (!htmlUrl.contains("timeZone=")) htmlUrl =
            htmlUrl + "&timeZone=" + AppUtil.getTimeZoneRawOffsetToHour()
        if (!htmlUrl.contains("zone=")) htmlUrl =
            htmlUrl + "&zone=" + AppUtil.getTimeZoneRawOffsetToHour()
        if (!htmlUrl.contains("appVersion=")) htmlUrl =
            htmlUrl + "&appVersion=" + AppUtil.getVersionName()
        if (!htmlUrl.contains("device=")) htmlUrl = "$htmlUrl&device=android"
        if (!htmlUrl.contains("theme=")) htmlUrl = "$htmlUrl&theme=${SpManager.getStyleState(0)}"
        // zheng quan 11.29日 让添加的
        if (!htmlUrl.contains("ip=")) htmlUrl = "$htmlUrl&ip=${IPUtil.getIPAddress(this)}"
        if (UserDataUtil.isLogin()) {
            if (!htmlUrl.contains("token=")) htmlUrl = "$htmlUrl&token=$loginToken"
            if (!htmlUrl.contains("xtoken=")) htmlUrl = "$htmlUrl&xtoken=${UserDataUtil.xToken()}"
            if (!htmlUrl.contains("userToken=")) htmlUrl = "$htmlUrl&userToken=$loginToken"
            if (!htmlUrl.contains("userAccount=")) htmlUrl = htmlUrl + "&userAccount=" + UserDataUtil.accountCd()
            if (!htmlUrl.contains("mt4=")) htmlUrl = htmlUrl + "&mt4=" + UserDataUtil.accountCd()
            if (!htmlUrl.contains("userId=")) htmlUrl = htmlUrl + "&userId=" + UserDataUtil.userId()
            if (!htmlUrl.contains("userid=")) htmlUrl = htmlUrl + "&userid=" + UserDataUtil.userId()
            if (!htmlUrl.contains("stUserId=")) htmlUrl = htmlUrl + "&stUserId=" + UserDataUtil.stUserId()
            if (!htmlUrl.contains("type=")) htmlUrl = htmlUrl + "&type=" + SpManager.getSuperviseNum("")
            if (!htmlUrl.contains("sign=")) {
                htmlUrl = "$htmlUrl&sign=${mViewModel.getSign()}"
            }
            if (!htmlUrl.contains("crmUserId=")) htmlUrl = "$htmlUrl&crmUserId=${
                SpManager.getCrmUserId()
            }"
        }
        // h5 需要，后期去除（h5没搞定多套地址，后期和BaseUrl一样，对应多套h5链接）
        if (!HttpUrl.official) htmlUrl = htmlUrl + "&servertype=" + HttpUrl.BaseUrl
        // 隐藏标题栏
        if (htmlUrl.contains("/noTitle/")) {
            mBinding.mHeaderBar.visibility = View.GONE
            isNoTitle = true
        }

        mBinding.mHeaderBar.setTitleText(titleName)

        // 老拉新活动隐藏标题栏
        if (htmlUrl.contains("h5/active/invite")) {
            mBinding.mHeaderBar.visibility = View.GONE
            isNoTitle = true
        }
        LogUtil.w("HTML >>" + htmlUrl)
        mBinding.mWebView.loadUrl(htmlUrl)
        // h5流程神策埋点 -> 打开页面
        sensorsHelper.init(htmlUrl)
        sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.OPEN_PAGE, url = htmlUrl)
        return htmlUrl
    }

    private fun notificationMarketingClickCntUpdate() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("ruleId", ruleId ?: "")
        val requestBody = jsonObject.toString()
            .toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().notificationMarketingClickCntUpdate(requestBody),
            object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {
                }

                override fun onHandleSubscribe(d: Disposable?) {
                }
            })
    }

    override fun initListener() {
        super.initListener()
        mBinding.mWebView.setDownloadListener(WebDownLoadListener(this))
        mBinding.mWebView.setOnLongClickListener { true }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle).collectLatest {
                if (it !is DataEvent) {
                    hideNetDialog()
                    return@collectLatest
                }
                when (it.tag) {
                    NoticeConstants.JS.HTML_IB_URL -> {
                        loadUrl(it.data.toString())
                    }

                    NoticeConstants.JS.HTML_CONTROL_LOADING -> {
                        if (it.data == true) {
                            showNetDialog()
                            if (mViewModel.isNeedProgress()) {
                                mBinding.progressBar.isVisible = true
                            }
                        } else {
                            hideNetDialog()
                            if (mViewModel.isNeedProgress()) {
                                mBinding.progressBar.isVisible = false
                            }
                        }
                    }
                }
            }
        }

    }

    @Deprecated("Deprecated in Java")
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        KeyboardUtil.hideSoftInput(this)
        // 225通知：表示此时H5页面逻辑不允许逐级返回，要求返回时直接退出页面
        if (!isCanBack) {
            finish()
            return
        }
        // 如果是PDF页面，先执行隐藏PDF组件
        if (mBinding.pdfView.visibility == View.VISIBLE && !isPDFOnly) {
            mBinding.pdfView.visibility = View.GONE
            mBinding.pdfView.recycle()
            mBinding.mWebView.visibility = View.VISIBLE
            return
        }
        // 页面传入 isNeedBack：是否需要逐级返回
        if (!isNeedBack) {
            finish()
            return
        }
        // 如果页面没有TitleBar，即H5全屏显示时，不允许物理返回关闭
        // 2024.12.22 添加 全屏模式下出金页面取消返回按钮的拦截
        if (isNoTitle && !isNoTitleCanBack) {
            return
        }
        // 逐级返回逻辑
        if (mBinding.mWebView.canGoBack()) {
            mBinding.mWebView.settings.cacheMode = WebSettings.LOAD_NO_CACHE
            mBinding.mWebView.goBack()
            return
        }
        finish()
    }

    private fun loadPdfFile(input: InputStream) {  // pdfFile: File?
        mBinding.pdfView.fromStream(input)
            .enableSwipe(true) // allows to block changing pages using swipe
            .swipeHorizontal(false)
            .enableDoubletap(true)
            .defaultPage(0)
            // allows to draw something on the current page, usually visible in the middle of the screen
            //                .onDraw(this)
            // allows to draw something on all pages, separately for every page. Called only for visible pages
            //                .onDrawAll(this)
            .onLoad(this) // called after document is loaded and starts to be rendered
            //                .onPageChange(this)
            //                .onPageScroll(this)
            .onError(this)
            .onPageError(this)
            .onRender(this) // called after document is rendered for the first time
            .onDrawAll(this)
            // called on single tap, return true if handled, false to toggle scroll handle visibility
            //                .onTap(this)
            //                .onLongPress(this)
            .enableAnnotationRendering(false) // render annotations (such as comments, colors or forms)
            .password(null)
            .scrollHandle(null)
            .enableAntialiasing(true) // improve rendering a little bit on low-res screens
            // spacing between pages in dp. To define spacing color, set view background
            .spacing(0)
            .autoSpacing(false) // add dynamic spacing to fit each page on its own on the screen
            .linkHandler(DefaultLinkHandler(mBinding.pdfView))
            .pageFitPolicy(FitPolicy.WIDTH)
            .pageSnap(true) // snap pages to screen boundaries
            .pageFling(false) // make a fling change only a single page like ViewPager
            .nightMode(false) // toggle night mode
            .load()
    }

    private fun loadPdfFile(htmlUrl: String) {
        mBinding.mWebView.isGone = true
        mBinding.pdfView.isVisible = true
        val thread = Thread {
            try {
                val input: InputStream = URL(htmlUrl).openStream()
                loadPdfFile(input)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        thread.start()
        return
    }

    //    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
    //        if (keyCode == KeyEvent.KEYCODE_BACK) {
    //            if (binding.pdfView.visibility == View.VISIBLE && !isPDFOnly) {
    //                binding.pdfView.visibility = View.GONE
    //                binding.pdfView.recycle()
    //                binding.mWebView.visibility = View.VISIBLE
    //                return false
    //            }
    //            if (isNoTitle) {
    //                // mWebView.loadUrl("javascript:testAndroidFunction(${"data from android"})")
    //                return true
    //            }
    //        }
    //        return super.onKeyDown(keyCode, event)
    //    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 登陆账户
            NoticeConstants.SWITCH_ACCOUNT -> {
                needClearHistory = true
                initData()
            }
            // 开通真实账户 提交
            NoticeConstants.REFRESH_ACCOUNT_MANAGER, NoticeConstants.JS.INTERFACE_104 -> {
                finish()
            }

            NoticeConstants.JS.INTERFACE_50, NoticeConstants.JS.OPEN_ACCOUNT_OPEN_APP -> {
                showLoadingDialog()
            }

            "html_dialog_net_finish" -> {
                hideLoadingDialog()
            }

            NoticeConstants.HTML_HIDE_TITLE_BAR -> {
                //8.7新通知，不隐藏标题栏了，但是需要点击返回按钮直接退出
                //                loginTitleView.isVisible(false)
                //                LogUtils.w("----------")
                isCanBack = false
            }

            // 通知h5账户类型
            NoticeConstants.NOTICE_H5_ACCOUNT_TYPE -> {
                val json = JsonObject()
                json.addProperty("accountId", UserDataUtil.accountCd())
                json.addProperty("isDemo", UserDataUtil.isDemoAccount())
                mBinding.mWebView.loadUrl("javascript:responseCallback(${json})")
            }

            NoticeConstants.NOTICE_H5_KYC_REFRESH -> {
                loadVFXMethod("refreshPageState")
            }

        }
    }

    /**
     * 调用 H5 的 JS 方法。
     *
     * @param methodName 要调用的 JS 方法名。
     * @param map 传递给 JS 方法的参数，可选。
     */
    fun loadVFXMethod(methodName: String, map: Map<String, Any>? = null) {

        val method = if (map != null) {
            val json = JsonObject()
            map.forEach {
                when (it.value) {
                    is Boolean -> json.addProperty(it.key, it.value as Boolean?)
                    is Number -> json.addProperty(it.key, it.value as Number?)
                    is CharSequence -> json.addProperty(it.key, it.value as String?)
                    else -> {
                        json.addProperty(it.key, it.value.toString())
                    }
                }
            }
            "window.vfx_android.${methodName}(${json})"
        } else {
            "window.vfx_android.${methodName}()"
        }
        LogUtil.w(method)
        mBinding.mWebView.evaluateJavascript(method) {}
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(data: DataEvent) {
        when (data.tag) {
            // 出金-验证2fa 回传结果
            NoticeConstants.TFA_VERIFY_BACK -> {
                mBinding.mWebView.loadUrl("javascript:retrieve2FAVerificationBack()")
            }

            NoticeConstants.TFA_VERIFY_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("code", data.data as String)
                json.addProperty("appVersion", AppUtil.getVersionName())
                json.addProperty("isSuccess", true)
                mBinding.mWebView.loadUrl("javascript:retrieve2FAVerificationStatus(${json})")
                //                LogUtil.d("wj", "[出金-验证2fa 回传结果]: $json")
            }

            // 出金-绑定2fa 回传结果
            NoticeConstants.TFA_BIND_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data == true)
                mBinding.mWebView.loadUrl("javascript:retrieve2FASettingStatus(${json})")
                //                LogUtil.d("wj", "[出金-绑定2fa 回传结果]: $json")
            }

            // 出金-重置2fa 回传结果
            NoticeConstants.TFA_RESET_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data == true)
                mBinding.mWebView.loadUrl("javascript:retrieve2FAResetStatus(${json})")
                //                LogUtil.d("wj", "[出金-重置2fa 回传结果]: $json")
            }
            // 通知h5人脸识别结果
            NoticeConstants.NOTICE_H5_SUMSUB_RESULT -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data as Boolean)
                mBinding.mWebView.loadUrl("javascript:retrieveSumsubStatus(${json})")
            }
        }
    }

    override fun onDestroy() {
        fixInputMethodManagerLeak(this)
        if (mBinding.mWebView != null) {
            try {
                val parent = mBinding.mWebView.parent
                if (parent != null) {
                    (mBinding.mWebView.parent as ViewGroup).removeView(mBinding.mWebView)
                }
                mBinding.mWebView.stopLoading()
                mBinding.mWebView.settings.javaScriptEnabled = false
                mBinding.mWebView.clearHistory()
                mBinding.mWebView.clearView()
                mBinding.mWebView.removeAllViews()
                mBinding.mWebView.destroy()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        // h5流程神策埋点 -> 关闭页面
        sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.CLOSE_PAGE, url = mBinding.mWebView.url)
    }

    /**
     * @param destContext 上下文对象
     * 用于解决输入法内存泄露
     * 参考：http://blog.csdn.net/sodino/article/details/32188809
     */
    fun fixInputMethodManagerLeak(destContext: Context?) {
        if (destContext == null) {
            return
        }
        val imm = destContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager ?: return
        val arr = arrayOf("mCurRootView", "mServedView", "mNextServedView")
        var f: Field? = null
        var obj_get: Any? = null
        for (i in arr.indices) {
            val param = arr[i]
            try {
                f = imm.javaClass.getDeclaredField(param)
                if (!f.isAccessible) {
                    f.isAccessible = true
                } // author: sodino mail:<EMAIL>
                obj_get = f[imm]
                if (obj_get != null && obj_get is View) {
                    if (obj_get.context === destContext) { // 被InputMethodManager持有引用的context是想要目标销毁的
                        f[imm] = null // 置空，破坏掉path to gc节点
                    } else {
                        // 不是想要目标销毁的，即为又进了另一层界面了，不要处理，避免影响原逻辑,也就不用继续for循环了
                        //                        if (QLog.isColorLevel()) {
                        //                            QLog.d(ReflecterHelper.class.getSimpleName(), QLog.CLR, "fixInputMethodManagerLeak break, context is not suitable, get_context=" + v_get.getContext()+" dest_context=" + destContext);
                        //                        }
                        break
                    }
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
    }

    private fun showDepositLoading() {
        if (this.isFinishing || this.isDestroyed || mBinding.clDepositLoading.isVisible) {
            return
        }

        if (!mBinding.lottieView.isAnimating) {
            mBinding.lottieView.playAnimation()
        }
        mBinding.clDepositLoading.isVisible = true
    }

    private fun hideDepositLoading() {
        if (this.isFinishing || this.isDestroyed || mBinding.clDepositLoading.isGone) {
            return
        }

        mBinding.lottieView.cancelAnimation()
        mBinding.clDepositLoading.isGone = true
    }

    fun showUploadBottomDialog() {
        openUploadToH5Util.showUploadBottomDialog()
    }

    /**
     * h5调用截屏功能
     */
    fun screenShot() {
        if (!this.isFinishing && !this.isDestroyed)
            ShareUtils.save(this, ImageUtil.view2Bitmap(mBinding.mWebView))
    }

    override fun onPause() {
        super.onPause()
        hideLoadingDialog()
    }

    companion object {
        private const val FILE_CHOOSER_RESULT_CODE = 10000
    }

    private var uploadMessage: ValueCallback<Uri?>? = null
    private var uploadMessageAboveL: ValueCallback<Array<Uri>>? = null

    private fun openImageChooserActivity() {
        val i = Intent(Intent.ACTION_OPEN_DOCUMENT)
        i.addCategory(Intent.CATEGORY_OPENABLE)
        i.type = "*/*" //文件上传
        startActivityForResult(Intent.createChooser(i, "Image Chooser"), FILE_CHOOSER_RESULT_CODE)
    }

    private fun showLoadingDialog() {
        // 入金H5单独loading样式
        if ("deposit" != from) {
            showNetDialog()
        }
    }

    private fun hideLoadingDialog() {
        // 入金H5单独loading样式
        if ("deposit" != from) {
            hideNetDialog()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == FILE_CHOOSER_RESULT_CODE) {
            if (null == uploadMessage && null == uploadMessageAboveL) return
            val result = if (data == null || resultCode != RESULT_OK) null else data.data
            // Uri result = (((data == null) || (resultCode != RESULT_OK)) ? null : data.getData());
            if (uploadMessageAboveL != null) {
                onActivityResultAboveL(requestCode, resultCode, data)
            } else if (uploadMessage != null) {
                uploadMessage?.onReceiveValue(result)
                uploadMessage = null
            }
        } else {
            //这里uploadMessage跟uploadMessageAboveL在不同系统版本下分别持有了
            //WebView对象，在用户取消文件选择器的情况下，需给onReceiveValue传null返回值
            //否则WebView在未收到返回值的情况下，无法进行任何操作，文件选择器会失效
            if (uploadMessage != null) {
                uploadMessage?.onReceiveValue(null)
                uploadMessage = null
            } else if (uploadMessageAboveL != null) {
                uploadMessageAboveL?.onReceiveValue(null)
                uploadMessageAboveL = null
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private fun onActivityResultAboveL(requestCode: Int, resultCode: Int, intent: Intent?) {
        if (requestCode != FILE_CHOOSER_RESULT_CODE || uploadMessageAboveL == null) return
        var results: Array<Uri>? = null
        var resultList: ArrayList<Uri>? = null
        if (resultCode == RESULT_OK) {
            if (intent != null) {
                val dataString = intent.dataString
                try {
                    val clipData = intent.clipData
                    if (clipData != null) {
                        resultList = ArrayList()
                        for (i in 0 until clipData.itemCount) {
                            val item = clipData.getItemAt(i)
                            resultList.add(item.uri)
                        }
                        results = resultList.toArray() as Array<Uri>?
                    }
                    if (dataString != null) results = arrayOf(Uri.parse(dataString))
                } catch (e: Exception) {
                    e.printStackTrace()
                    if (dataString != null) results = arrayOf(Uri.parse(dataString))
                }
            }
        }
        uploadMessageAboveL?.onReceiveValue(results)
        uploadMessageAboveL = null
    }

    //PDF
    override fun loadComplete(nbPages: Int) {
        hideLoadingDialog()
    }

    override fun onError(t: Throwable?) {
        hideLoadingDialog()
    }

    override fun onPageError(page: Int, t: Throwable?) {
        hideLoadingDialog()
    }

    override fun onInitiallyRendered(nbPages: Int) {
        hideLoadingDialog()
    }

    override fun onLayerDrawn(
        canvas: Canvas?,
        pageWidth: Float,
        pageHeight: Float,
        displayedPage: Int
    ) {
        hideLoadingDialog()
    }
}
