package cn.com.vau.page.user.bindEmail


import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.account.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface BindEmailContract {

    interface Model : BaseModel {
        fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BindEmailBean>): Disposable
        fun bindRegisterEmailUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable
    }

    interface View : BaseView {
        fun dealLoginData(email: String?, pwd: String?, data: LoginBean)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun pwdLoginApi(email: String?, pwd: String?)
        abstract fun bindEmailPhoneApi(email: String?, pwd: String?)
    }

}
