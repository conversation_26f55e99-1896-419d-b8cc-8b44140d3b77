package cn.com.vau.page.user.openAccoGuide

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.*
import android.os.Bundle
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.fragment.app.Fragment
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivityOpenAccoGuideBaseBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsDataUtil

abstract class OpenAccoGuideBaseActivity<VM : BaseViewModel> : BaseActivity() {

    private var tabIcons: Array<Array<Int>>? = null
    private var tabTexts: Array<String>? = null
    private var fragments: Array<Fragment>? = null
    private val binding: ActivityOpenAccoGuideBaseBinding by lazy { ActivityOpenAccoGuideBaseBinding.inflate(layoutInflater) }
    lateinit var mViewModel: VM
    private var items = -1
    var page = 0
    val buryPointMsg by lazy {
        when (SpManager.getSuperviseNum("")) {
            "8" -> "VFSC"
            "13" -> "FCA"
            else -> "VFSC2"
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        mViewModel = initViewModels()
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
    }

    override fun initView() {
        super.initView()
        binding.tvTitle.text = setTitle()
        binding.tvTitleTip.text = setTitleTip()

        tabIcons = getTabIconSelector()
        tabTexts = getTabText()
        fragments = getFragmentList()
        if (tabIcons?.size == 2 && tabTexts?.size == 2 && fragments?.size == 2) {
            items = 2
            binding.ivTab1.setImageDrawable(createDrawableSelector(this, tabIcons?.getOrNull(0)))
            binding.ivTab2.setImageDrawable(createDrawableSelector(this, tabIcons?.getOrNull(1)))
            binding.tvTab1.text = tabTexts?.getOrNull(0)
            binding.tvTab2.text = tabTexts?.getOrNull(1)
            binding.mViewPager.init(
                fragments?.toMutableList() ?: arrayListOf(), arrayListOf(), supportFragmentManager, this
            )
            binding.mViewPager.isUserInputEnabled = false
        } else if (tabIcons?.size == 3 && tabTexts?.size == 3 && fragments?.size == 3) {
            items = 3
            binding.ivTab1.setImageDrawable(createDrawableSelector(this, tabIcons?.getOrNull(0)))
            binding.ivTab2.setImageDrawable(createDrawableSelector(this, tabIcons?.getOrNull(1)))
            binding.ivTab3.setImageDrawable(createDrawableSelector(this, tabIcons?.getOrNull(2)))
            binding.tvTab1.text = tabTexts?.getOrNull(0)
            binding.tvTab2.text = tabTexts?.getOrNull(1)
            binding.tvTab3.text = tabTexts?.getOrNull(2)
            binding.mViewPager.init(
                fragments?.toMutableList() ?: arrayListOf(), arrayListOf(), supportFragmentManager, this
            )
            binding.mViewPager.isUserInputEnabled = false
        } else if ((tabIcons?.size ?: 0) < 2 || (tabTexts?.size ?: 0) < 2 || (fragments?.size ?: 0) < 2) {
            throw IllegalArgumentException("The tab at least 2 items")
        }

        when (items) {
            2 -> {
                binding.llTab3.visibility = View.GONE
//                binding.llTablayout.setPadding(
//                    0.dp2px(),
//                    20.dp2px(),
//                    0.dp2px(),
//                    20.dp2px()
//                )
                tabSelectedRander()
            }

            3 -> {
                binding.llTab3.visibility = View.VISIBLE
//                binding.llTablayout.setPadding(
//                    0,
//                    20.dp2px(),
//                    0,
//                    20.dp2px()
//                )
                tabSelectedRander()
            }
        }
    }

    override fun initListener() {
        super.initListener()
        binding.mHeaderBar.setStartBackIconClickListener {
            backOrExit()
        }
        binding.mHeaderBar.setEndIconClickListener {
            finish()
        }
        binding.mHeaderBar.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        mViewModel.showLoadingData.observe(this) {
            if (it) showNetDialog()
            else hideNetDialog()
        }
    }

    private fun createDrawableSelector(context: Context, resArray: Array<Int>?): Drawable? {
        if (!resArray.isNullOrEmpty() && resArray.size == 2) {
            val stateDrawable = StateListDrawable()
            // selected drawable
            stateDrawable.addState(
                intArrayOf(android.R.attr.state_selected),
                AppCompatResources.getDrawable(this@OpenAccoGuideBaseActivity, AttrResourceUtil.getDrawable(context, resArray[0]))
            )
            // unselected drawable
            stateDrawable.addState(
                intArrayOf(-android.R.attr.state_selected),
                AppCompatResources.getDrawable(this@OpenAccoGuideBaseActivity, AttrResourceUtil.getDrawable(context, resArray[1]))
            )
            return stateDrawable
        }
        return null
    }

    abstract fun initViewModels(): VM

    fun tabSelected(tabIndex: Int) {
        if (tabIndex < (fragments?.size ?: 0)) {
            page = tabIndex
            tabSelectedRander()
        }
    }

    private fun backOrExit() {
        if (canScrollBack()) {
            scrollBack()
        } else {
            //退出神策登录
            SensorsDataUtil.logout()
            finish()
        }
    }

    private fun canScrollBack(): Boolean {
        val pageShowing = binding.mViewPager.currentItem
        return pageShowing > 0
    }

    open fun scrollBack() {
        page = binding.mViewPager.currentItem - 1
        tabSelectedRander()
    }

    private fun tabSelectedRander() {
        binding.ivTab1.isSelected = page == 0
        binding.tvTab1.isSelected = page == 0
        binding.ivTab2.isSelected = page == 1
        binding.tvTab2.isSelected = page == 1
        binding.ivTab3.isSelected = page == 2
        binding.tvTab3.isSelected = page == 2
        scrollToPage()
    }

    private fun scrollToPage() {
        val pageShowing = binding.mViewPager.currentItem
        if (page != pageShowing) {
            binding.mViewPager.setCurrentItem(page, false)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        backOrExit()
    }

    abstract fun setTitle(): String

    abstract fun getTabIconSelector(): Array<Array<Int>>

    abstract fun getTabText(): Array<String>

    abstract fun getFragmentList(): Array<Fragment>

    open fun setTitleTip(): String = ""
}