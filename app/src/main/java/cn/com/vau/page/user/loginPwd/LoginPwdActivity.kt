package cn.com.vau.page.user.loginPwd

import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import androidx.navigation.*
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.tracking.*
import org.greenrobot.eventbus.EventBus

/**
 * Created by Haipeng.
 */

class LoginPwdActivity : BaseActivity() {

    private val navController: NavController by lazy {
        Navigation.findNavController(this, R.id.loginFragment)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login_pwd)

        //神策自定义埋点(v3500)
        //登录页面浏览 -> 登录页面加载完成时触发
        SensorsDataUtil.track(SensorsConstant.V3500.LOGIN_PAGE_VIEW)
    }

    override fun onSupportNavigateUp(): Boolean {
        return navController.navigateUp()
    }

    override fun initParam() {
        super.initParam()
        jump(intent)
    }

    private fun jump(intent: Intent?) {
        when (intent?.extras?.getInt("isFrom", 0)) {
            1 -> {
                val bundle = Bundle()
                bundle.putBoolean("isShowEmail", false)
                bundle.putInt("isFrom", 1)
                navController.navigate(R.id.forgetPwdFirstFragment, bundle)
            }

            2 -> {
                navController.navigate(R.id.action_loginFragment_to_forgetPwdSecondFragment, intent.extras)
            }
        }
        EventBus.getDefault().post(NoticeConstants.LOGOUT_GUIDE_CLEAR)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        jump(intent)
    }

    // 改到 ForgetPwdFirstFragment 处理物理back事件了
//    override fun onBackPressed() {
//        val currentDestination = navController.currentDestination  // 得到当前目的地
//        if (currentDestination is FragmentNavigator.Destination) {  //对Destination进行强制转换
//            when (currentDestination.className) {
//                //如果当前页是SplashFragment或StartFragment或MainFragment则直接退出Activity
//                ForgetPwdFirstFragment::class.java.name ->
//                    if (intent.extras?.getInt("isFrom", 0) == 1) finish()
//                    else super.onBackPressed()
//
//                else -> super.onBackPressed()
//            }
//        } else {
//            super.onBackPressed()
//        }
//    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView ?: findViewById(R.id.clRoot), event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }
}