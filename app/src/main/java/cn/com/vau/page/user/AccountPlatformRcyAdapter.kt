package cn.com.vau.page.user

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.util.ImageLoaderUtil
import java.util.Locale

/**
 * Created by roy on 2018/10/25.
 */
class AccountPlatformRcyAdapter(
    var mContext: Context,
    var dataList: ArrayList<PlatFormAccountData.Obj>
) : RecyclerView.Adapter<AccountPlatformRcyAdapter.ViewHolder>() {

    var selectIndex = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_rcy_account_platform, parent, false)
        )
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val dataBean = dataList.elementAtOrNull(position)
        ImageLoaderUtil.loadImage(
            mContext,
            dataBean?.listImage?.elementAtOrNull(0)?.imgUrl,
            holder.ivIcon
        )

        holder.tvName.text = dataBean?.displayPlatFormName

        when (dataBean?.platFormName?.lowercase(Locale.getDefault())) {
            "mt4" -> {
                holder.iv_new.isVisible = false
                holder.tvLabel.isInvisible = true
                holder.tvDesc.text = mContext.getString(R.string.basic_trading_account)
            }

            "mt5" -> {
                holder.iv_new.isVisible = false
                holder.tvLabel.isInvisible = false
                holder.tvLabel.text = mContext.getString(R.string.recommended)
                holder.tvLabel.setBackgroundResource(R.drawable.shape_solid_ce35728_top_r10)
                holder.tvDesc.text = mContext.getString(R.string.advanced_trading_account)
            }

            "mts" -> {
                holder.iv_new.isVisible = true
                holder.tvLabel.isInvisible = false
                holder.tvLabel.text = mContext.getString(R.string.popular)
                holder.tvLabel.setBackgroundResource(R.drawable.shape_solid_c034854_top_r10)
                holder.tvDesc.text = mContext.getString(R.string.copy_trading_account)
            }
        }

        if (selectIndex == position) {
            holder.llAccountType.setBackgroundResource(
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
            )
        } else {
            holder.llAccountType.setBackgroundResource(
                R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
            )
        }

        holder.itemView.setOnClickListener {
            if (position == selectIndex) return@setOnClickListener
            mOnItemClickListener?.onItemClick(position)
            selectIndex = position
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val ivIcon = view.findViewById<ImageView>(R.id.ivIcon)
        val tvName = view.findViewById<TextView>(R.id.tvName)
        val iv_new = view.findViewById<ImageView>(R.id.iv_new)
        val llAccountType = view.findViewById<LinearLayout>(R.id.llAccountType)
        val tvLabel = view.findViewById<TextView>(R.id.tvLabel)
        val tvDesc = view.findViewById<TextView>(R.id.tvDesc)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}