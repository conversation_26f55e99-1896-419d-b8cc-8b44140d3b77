package cn.com.vau.page.common.selectResidence.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.data.account.ResidenceObj;
import cn.com.vau.util.AttrResourceUtil;
import cn.com.vau.util.DistKt;

/**
 * Created by zhy on 2018/12/20.
 */

public class BigLetterAdapter extends RecyclerView.Adapter<BigLetterAdapter.BigLetterViewHolder> {

    private Context mContext;
    private List<ResidenceObj> mList;
    private OnItemClickListener onItemClickListener;
    private String selectedName = "";

    public BigLetterAdapter(Context mContext, List<ResidenceObj> mList) {
        this.mContext = mContext;
        this.mList = mList;
    }

    @Override
    public BigLetterViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_nav_letter, parent, false);
        return new BigLetterViewHolder(view);
    }

    @SuppressLint ("RecyclerView")
    @Override
    public void onBindViewHolder(final BigLetterViewHolder holder, final int position) {
        ViewGroup.LayoutParams layoutParams = holder.tvNavLetterRoot.getLayoutParams();
        layoutParams.height = (DistKt.getScreenHeight() - DistKt.dp2px(100) - DistKt.getStatusHeight(mContext)) / 27;
        holder.tvNavLetterRoot.setLayoutParams(layoutParams);
        if(mList.get(position).lettername.equals("HOT") || mList.get(position).lettername.equals("Popular") || position == 0) {
            holder.tvNavLetterRoot.setText("");
        } else {
            holder.tvNavLetterRoot.setText(mList.get(position).lettername);
            holder.tvNavLetterRoot.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        }

        if(holder.tvNavLetterRoot.getText().equals(selectedName)) {
            holder.tvNavLetterRoot.setTextColor(mContext.getResources().getColor(R.color.ce35728));
        } else {
            holder.tvNavLetterRoot.setTextColor(AttrResourceUtil.getColor(mContext, R.attr.color_c731e1e1e_c61ffffff));
        }

        if(onItemClickListener != null) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClickListener.onItemClick(holder.itemView, position);
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    public class BigLetterViewHolder extends RecyclerView.ViewHolder {
        TextView tvNavLetterRoot;

        public BigLetterViewHolder(View itemView) {
            super(itemView);
            tvNavLetterRoot = itemView.findViewById(R.id.tvNavLetterRoot);
        }
    }

    public void showSelectedName(String name) {

        if(!name.equals(selectedName)) {
            selectedName = name;
            for (int i = 0; i < mList.size(); i++) {
                if(mList.get(i).lettername.equals(selectedName)) {
                    notifyItemRangeChanged(i - 1, 3);
                    return;
                }
            }
            //            notifyDataSetChanged();
        }
    }

    //===================================interface==================================================

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }
}
