package cn.com.vau.page.photopreview

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.ActivityPhotoBinding
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.setNbOnItemClickListener

/**
 * 图片预览页面
 */
class PhotoActivity : BaseMvvmBindingActivity<ActivityPhotoBinding>() {

    private val urlList: ArrayList<String> by lazy { intent.getStringArrayListExtra("imageslist") ?: arrayListOf() }
    private val imagePosition by lazy { intent.getIntExtra("images_position", 1) }

    private var lastPosition = 0
    private val photoAdapter: PhotoAdapter by lazy {
        PhotoAdapter().apply {
            setList(urlList)
        }
    }

    private var size = 1

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        size = if (urlList.isEmpty()) 1 else urlList.size
    }

    override fun initView() {
        mBinding.ivBack.clickNoRepeat {
            finish()
        }

        mBinding.tvNum.text = buildString {
            append(imagePosition % size)
            append("/")
            append(size)
        }

        val layoutManager = WrapContentLinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        mBinding.rvView.layoutManager = layoutManager
        mBinding.rvView.adapter = photoAdapter
        mBinding.rvView.smoothScrollToPosition(imagePosition)
        val snapHelper = PagerSnapHelper()
        snapHelper.attachToRecyclerView(mBinding.rvView)
        mBinding.rvView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                lastPosition = layoutManager.findLastVisibleItemPosition()
                mBinding.tvNum.text = buildString {
                    append(lastPosition % size + 1)
                    append("/")
                    append(size)
                }
            }
        })
        photoAdapter.setNbOnItemClickListener { _, _, _ ->
            finish()
        }
    }

}
