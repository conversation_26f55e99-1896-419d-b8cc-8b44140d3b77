package cn.com.vau.page.user.leverage.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.util.ToastUtil

class LeverageViewModel : BaseViewModel() {
    var oldLeverage: String? = null
    var accountCd: String? = null
    var accountServer: String? = null
    var leverageList: MutableList<SelectBean> = arrayListOf()
    var selectIndex = 0

    val oldLeverageLiveData by lazy { MutableLiveData<String>() }
    val queryProClientLiveData by lazy { MutableLiveData<Boolean>() }

    fun getLeverageApi() {
        val map = hashMapOf<String, Any>().apply {
            put("loginUserId", UserDataUtil.userId())
            put("mt4AccountId", accountCd ?: "")
            put("accountServer", accountServer ?: "")
        }
        requestNet({
            baseService.crmLeverageApi(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            oldLeverage = it.data?.obj?.leverage
            leverageList.clear()
            val supervisionType = SpManager.getSuperviseNum("")

            it.data?.obj?.list?.forEach {
                if (supervisionType == "1" && (it <= 25)) return@forEach // 不顯示 25一下的杠桿
                leverageList.add(SelectBean("$it:1"))
            }
            selectIndex = (leverageList.size) - 1
            oldLeverageLiveData.postValue("$oldLeverage:1")
        }, isShowDialog = true)
    }

    fun saveCurrentLeverageApi(selectLeverage: String, checked: Boolean) {
        if (oldLeverage == null) {
            getLeverageApi()
            return
        }
        if (!checked) {
            // 请阅读并同意相关条款.
            ToastUtil.showToast(getString(R.string.please_read_the_conditions))
            return
        }
        val map = hashMapOf<String, String>().apply {
            put("mt4AccountId", accountCd ?: "")
            put("loginUserId", UserDataUtil.userId())
            put("leverage", selectLeverage)
            put("oldLeverage", oldLeverage ?: "")
            put("currencyName", UserDataUtil.currencyType())
        }
        requestNet({
            baseService.crmLeveragemodifyApi(map)
        }, onSuccess = {
            ToastUtil.showToast(it.getResponseMsg())
        }, isShowDialog = true)
    }

    fun queryProClientApi() {
        val paramMap = hashMapOf<String, Any>().apply {
            put("userId", UserDataUtil.userId())
        }
        requestNet({
            baseService.queryUserIsProclientApi(paramMap)
        }, onSuccess = {
            if (it.data?.obj?.proclient != true) {
                queryProClientLiveData.postValue(true)
            } else {
                queryProClientLiveData.postValue(false)
            }
        }, isShowDialog = true)
    }
}