package cn.com.vau.page.user.login.presenter

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.page.WithdrawalBundleBean
import cn.com.vau.util.SendCodeUtil
import io.reactivex.disposables.Disposable

class VerificationContract {

    interface Model : BaseModel {

        fun getBindingTelSMSApi(
            map: HashMap<String, Any?>,
            baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
        ): Disposable

        fun pwdLoginApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable

        fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable

        fun updateTelApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable

        fun checkVerificationCodeApi(map: java.util.HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable

        fun goEditPwdApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable

        fun getVerificationCodeApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable

        fun insertFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>): Disposable

        fun forgotFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>): Disposable

        fun withdrawal(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>): Disposable
    }

    interface View : BaseView {

        fun showCaptcha(type: Int)

        fun finish()

        fun refreshTel(logoutInfo: ChangeUserInfoData?)

        fun sendCodeSuccess()

        fun refreshFundPWD(state: Int)

        fun refreshWithdrawal(number: String?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {

        abstract fun startSendCodeUtil()

        abstract fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener)

        /**
         * 获取验证码
         */
        abstract fun getBindingTelSMSApi(
            userTel: String,
            userPassword: String,
            phoneCountryCode: String,
            code: String,
            type: String,
            validateCode: String?,
            smsSendType: String
        )

//        /**
//         * 密码登录
//         */
//        abstract fun pwdLogin(
//            handleType: Int,
//            nextType: Int,
//            veriCode: String?,
//            countryCode: String?,
//            code: String?,
//            mobile: String?,
//            pwd: String?,
//            recaptcha: String?
//        )

        /**
         * 修改手机号
         */
        abstract fun updateTelApi(
            userToken: String?, phone: String?, password: String?,
            validateCode: String?, code: String?, countryCode: String?
        )

        /**
         * 修改密码
         */
        abstract fun goEditPwdApi(pwd: String?, pwdAgain: String?, verificationCode: String?)

        /**
         * 忘记密码的获取验证码接口
         */
        abstract fun getVerificationCodeApi(telNum: String, userPassword: String, validateCode: String?, type: Int, smsSendType: String)

        /**
         * 校验验证码  成功以后 调用修改密码的接口
         */
        abstract fun checkVerificationCodeApi(
            pwd: String?,
            pwdAgain: String?,
            verificationCode: String?,
            phoneNo: String?,
            areaCode: String?
        )

        /**
         * 首次设置资金安全码
         */
        abstract fun insertFundPWDApi(
            userToken: String?, optType: String?, phone: String?, validateCode: String?,
            fundPwd: String?, confirmPwd: String?, code: String?
        )

        /**
         * 忘记资金安全码
         */
        abstract fun forgotFundPWDApi(
            userToken: String?, phone: String?, validateCode: String?,
            fundPwd: String?, confirmPwd: String?, code: String?
        )

        /**
         * 出金
         */
        abstract fun withdrawal(userToken: String?, accountId: String?, currency: String?, data: WithdrawalBundleBean, fundSafePwd: String?, state: Int)
    }
}