package cn.com.vau.page.user.openAccoGuide.lv1.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.util.ImageLoaderUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class OpenAccoTypeAdapter :
    BaseQuickAdapter<PlatFormAccountData.PlatFormAccountType, BaseViewHolder>(R.layout.item_open_lv1_acco_type) {

    var selectIndex = 0

    override fun convert(holder: BaseViewHolder, item: PlatFormAccountData.PlatFormAccountType) {
        ImageLoaderUtil.loadImage(
            context,
            item.listImage?.elementAtOrNull(0)?.imgUrl,
            holder.getView(R.id.iv_acco_type)
        )
        holder.itemView.background = ContextCompat.getDrawable(
            context,
            if (selectIndex == holder.bindingAdapterPosition)
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
            else
                R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
        )
    }
}