package cn.com.vau.page.common.selectResidence.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.data.account.ResidenceObjList;

public class SelectRegionAdapter extends RecyclerView.Adapter<SelectRegionAdapter.SelectRegionViewHolder> {

    private Context mContext;
    private List<ResidenceObjList> mList;
    private int mType = 0;//0国家；1省份；2城市

    public SelectRegionAdapter(Context mContext, List<ResidenceObjList> list, int type) {
        this.mContext = mContext;
        this.mList = list;
        this.mType = type;
    }

    @NonNull
    @Override
    public SelectRegionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_select_nation, parent, false);
        return new SelectRegionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SelectRegionViewHolder holder, @SuppressLint("RecyclerView") int position) {
        ResidenceObjList bean = mList.get(position);
        if (mType == 0) {
            holder.tvNationName.setText(bean.countryNameEn);
        } else if (mType == 1) {
            holder.tvNationName.setText(bean.provinceNameEn);
        } else {
            holder.tvNationName.setText(bean.cityNameEn);
        }

        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClickListener.onItemClick(holder.itemView, position);
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return mList != null ? mList.size() : 0;
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }

    public class SelectRegionViewHolder extends RecyclerView.ViewHolder {
        TextView tvNationName;

        public SelectRegionViewHolder(View itemView) {
            super(itemView);
            tvNationName = itemView.findViewById(R.id.tvNationName);
        }
    }

}
