package cn.com.vau.page.coupon.couponUse

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.*
import androidx.core.view.isInvisible
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication.Companion.context
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.depositcoupon.*
import cn.com.vau.databinding.ActivityVoucherDetailsBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 14 14:48
 * @updateUser:
 * @updateDate: 2025 4月 14 14:48
 */
class CouponDetailActivity : BaseMvvmActivity<ActivityVoucherDetailsBinding, CouponDetailsViewModel>() {

    private val typeAdapter: SelectAccountAdapter<TransferAccountInfo> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }

    private val tradeTypePopup: BottomListDialog? by lazy {
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.switch_account))
            .setAdapter(typeAdapter)
            .build()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        if (intent?.extras?.containsKey("currentCoupon") == true && intent?.extras?.getSerializable("currentCoupon") != null)
            mViewModel.currentCoupon = intent?.extras?.getSerializable("currentCoupon") as DepositCouponDetail
    }

    override fun initView() {
        mBinding.ctlCoupon.tvCouponType.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        mBinding.ctlCoupon.tvCouponContent.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))

        typeAdapter.setOnItemClickListener { _, _, position ->
            if (typeAdapter.selectTitle == typeAdapter.data.getOrNull(position)?.getShowItemValue()) return@setOnItemClickListener
            typeAdapter.selectTitle = typeAdapter.data.getOrNull(position)?.getShowItemValue()
            typeAdapter.notifyDataSetChanged()
            val selectAccount = typeAdapter.data.getOrNull(position) as? TransferAccountInfo
            mViewModel.currentCurrency = selectAccount?.currency.ifNull()
            mViewModel.currentAccount = selectAccount?.code
            mViewModel.currentAccountName = selectAccount?.name
            mBinding.tvAccountManager.text = mViewModel.currentAccount
            mBinding.tvCurrency.text = mViewModel.currentCurrency
            mViewModel.currencyTransform()
            tradeTypePopup?.dismiss()
        }

        mViewModel.crmMemberMt4AccountIdList()

        initCouponView()
        mViewModel.sensorsTrack(SensorsConstant.V3710.COUPONDETAILPAGE_VIEW)
    }

    override fun initListener() {
        super.initListener()
        mBinding.ctlCoupon.tvDetails.clickNoRepeat {
            NewHtmlActivity.openActivity(this, url = mViewModel.currentCoupon?.infoUrl.ifNull(), title = getString(R.string.deposit_coupon))
        }
        mBinding.tvNext.clickNoRepeat {
            mViewModel.inviteWithdraw()
            mViewModel.sensorsTrack(SensorsConstant.V3710.COUPONDETAILPAGE_SUBMIT)
        }
        mBinding.tvAccountManagerDesc.clickNoRepeat {
            showAccountListDialog()
        }
    }

    override fun createObserver() {
        super.createObserver()

        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    CouponDetailsViewModel.EVENT_COUPON_MONEY -> {
                        showMoney(it.data.toString())
                    }

                    CouponDetailsViewModel.EVENT_COUPON_FINISH -> {
                        ToastUtil.showToast(getString(R.string.successfully_redeemed))
                        finish()
                    }

                    CouponDetailsViewModel.EVENT_ACCOUNT_LIST -> {
                        it.data as MutableList<TransferAccountInfo>
                        showSelectAccount(it.data)
                    }
                }
            }
        }
    }

    private fun initCouponView() {
        if (mViewModel.currentCoupon?.couponType == "7") {
            mBinding.tvTransferCountDesc.visibility = View.GONE
            mBinding.tvCurrency.visibility = View.GONE
            mBinding.tvTransferCount.visibility = View.GONE
        }

        mViewModel.currentCoupon?.let {
            mBinding.ctlCoupon.tvCouponType.text = context.getString(
                when (it.couponType) {
                    "1", "6" -> R.string.deposit_coupon
                    "3" -> R.string.cash_voucher
                    "4" -> R.string.credit_voucher
                    "5" -> R.string.trade_loss_voucher
                    "7" -> R.string.cfd_voucher
                    "9" -> R.string.loss_protection
                    "10" -> R.string.deposit_rebate
                    "11" -> R.string.commission_fee
                    "12" -> R.string.profit_booster
                    else -> R.string.voucher
                }
            )

            if (mViewModel.currentCoupon?.userCouponStatus == "0") {
                mBinding.ctlCoupon.tvEndTime.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
                mBinding.ctlCoupon.root.alpha = 0.3f
            } else {
                mBinding.ctlCoupon.tvEndTime.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
                mBinding.ctlCoupon.root.alpha = 1f
            }

            // 如果给了H5地址就显示能跳转，否则不显示不跳转  https://hytechc.atlassian.net/browse/VJP01-399
            mBinding.ctlCoupon.tvDetails.isInvisible = mViewModel.currentCoupon?.infoUrl.isNullOrBlank()
            mBinding.ctlCoupon.tvDetails.text = buildSpannedString { underline { append(context.getString(R.string.terms_conditions)) } }
            mBinding.ctlCoupon.tvCouponAmount.text = it.amountDes ?: ""
            mBinding.ctlCoupon.tvCouponAmount.setTextColor(ContextCompat.getColor(context, R.color.ce35728))
            mBinding.ctlCoupon.tvCouponContent.text = it.couponDes ?: ""
            mBinding.ctlCoupon.tvButton.isInvisible = true
            mBinding.ctlCoupon.tvEndTime.visibility = if (TextUtils.isEmpty(it.remainDays)) View.GONE else View.VISIBLE
            mBinding.ctlCoupon.tvEndTime.text = getString(R.string.x_days_remaining, it.remainDays ?: " ")
        }
    }

    private fun showAccountListDialog() {
        if (typeAdapter.data.isEmpty()) {
            ToastUtil.showToast(getString(R.string.no_account_list))
            return
        }

        tradeTypePopup?.show()
    }

    private fun showSelectAccount(accountList: MutableList<TransferAccountInfo>) {
        mBinding.tvAccountManager.text = mViewModel.currentAccount
        mBinding.tvCurrency.text = mViewModel.currentCurrency
        typeAdapter.selectTitle = accountList.firstOrNull { it.name == mViewModel.currentAccountName }?.getShowItemValue()
        typeAdapter.setList(accountList)

        mViewModel.currencyTransform()
    }

    private fun showMoney(couponMoney: String?) {
        mBinding.tvTransferCount.text = couponMoney
    }
}