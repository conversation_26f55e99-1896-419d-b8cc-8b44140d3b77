package cn.com.vau.page.customerservice.help

import android.annotation.SuppressLint
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.msg.CSAnswerData
import cn.com.vau.data.msg.CSQuestsData
import cn.com.vau.data.msg.CSQuestsObj
import cn.com.vau.databinding.ActivityFaqsBinding
import cn.com.vau.util.dp2px
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class FAQsActivity : BaseMvvmActivity<ActivityFaqsBinding, FAQViewModel>() {

    private var dataList = arrayListOf<CSQuestsObj>()
    private var mAdapter: CustomServiceFAQAdapter? = null

    @SuppressLint("WrongConstant")
    override fun initView() {
        mAdapter = CustomServiceFAQAdapter(this, dataList)
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(16.dp2px()))
        mBinding.mRecyclerView.adapter = mAdapter
    }

    override fun initData() {
        super.initData()
        mViewModel.getCSQuests()
    }
    override fun initListener() {
        super.initListener()
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }
        mAdapter?.setOnItemClickListener(object : CustomServiceFAQAdapter.onItemClickListener {
            override fun onItemClick(position: Int) {
                val bean = dataList.getOrNull(position) as CSQuestsObj
                val answer = mViewModel.answerMap[bean.id]
                if (answer != null) {
                    answerByAsk(position, answer)
                } else {
                    mViewModel.consultAnswer(bean.id ?: "")
                }
            }
        })

        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    FAQViewModel.EVENT_QUESTION_LIST -> {
                        mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
                        renderQuestionList(it.data as? CSQuestsData)
                    }

                    FAQViewModel.EVENT_ANSWER_ITEM -> {
                        mergeAnswerList(it.data as? CSAnswerData)
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun renderQuestionList(bean: CSQuestsData?) {
        try {
            if ((bean?.obj?.size ?: 0) > 0) {
                dataList.clear()
                dataList.addAll(bean?.obj ?: arrayListOf())
                mAdapter?.notifyDataSetChanged()
//                autoScrollEnd()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun mergeAnswerList(bean: CSAnswerData?) {
        val faqItemNum = dataList.indexOfFirst { bean?.obj?.quest == it.quest }
        answerByAsk(faqItemNum, bean?.obj?.answer ?: "")
    }

    private fun answerByAsk(index: Int, answer: String) {
        dataList.getOrNull(index)?.let { bean ->
            bean.answer = answer
            bean.expanded = bean.expanded.not()
            mAdapter?.notifyItemChanged(index)
        }
    }

}