package cn.com.vau.page.user.openAccoGuide.lv1.vm

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.*
import cn.com.vau.page.user.openAccoGuide.OpenAccoRepository
import cn.com.vau.page.user.openAccoGuide.lv1.SelectCountryResidenceActivity

class OpenLv1ViewModel : BaseViewModel() {

    private val repository by lazy { OpenAccoRepository() }

    val openAccountData : MutableLiveData<PlatFormAccountData.OpenAccountData> by lazy { MutableLiveData(PlatFormAccountData.OpenAccountData()) }

    var residenceLiveData = MutableLiveData<ResidenceBean>()
    var getProcessLiveData1 = MutableLiveData<GetProcessData>()
    var processParam1 = hashMapOf<String, Any>()
    var tempParam = hashMapOf<String, Any>()
    var persInfoSaveCompleted = true    // 默认为true

    var getProcessLiveData4 = MutableLiveData<GetProcessData>()
    var accountSelectLiveData = MutableLiveData<AccoSelectData>()
    var saveProcessLiveData1 = MutableLiveData<SaveProcessData>()
    var saveProcessLiveData4 = MutableLiveData<SaveProcessData>()
    var platTypeCurrencyLiveData = MutableLiveData<PlatFormAccountData>()
    var platTitleLiveData = MutableLiveData<PlatformTypeTitleData>()
    var checkEmailLiveData = MutableLiveData<CheckEmailData>()
    var auditStatusLiveData = MutableLiveData<AuditStatusData>()
    var openWalletLiveData = MutableLiveData<OpenWalletBean>()

    var confirmDialog = false
    var fromWallet = false  // 是否来自于钱包
    var withWallet = false  // 是否是钱包开户

    fun checkEmail(email: String) {
        repository.checkEmail(hashMapOf(
            "token" to UserDataUtil.loginToken(),
            "email" to email
        )).bindTLiveData(checkEmailLiveData)
    }

    fun checkParamSame(param: HashMap<String, Any>): Boolean {
        if (param.isNotEmpty() && processParam1.isNotEmpty()) {
            var emailSame = false
            if (param.containsKey("email") && processParam1.containsKey("email")) {
                emailSame = param["email"] == processParam1["email"]
            }
            var firstNameSame = false
            if (param.containsKey("firstName") && processParam1.containsKey("firstName")) {
                firstNameSame = param["firstName"] == processParam1["firstName"]
            }
            var middleNameSame = false
            if (param.containsKey("middleName") && processParam1.containsKey("middleName")) {
                middleNameSame = param["middleName"] == processParam1["middleName"]
            }
            var lastNameSame = false
            if (param.containsKey("lastName") && processParam1.containsKey("lastName")) {
                lastNameSame = param["lastName"] == processParam1["lastName"]
            }
            var nationalityIdSame = false
            if (param.containsKey("nationalityId") && processParam1.containsKey("nationalityId")) {
                nationalityIdSame = param["nationalityId"] == processParam1["nationalityId"]
            }
            var countryIdSame = false
            if (param.containsKey("countryId") && processParam1.containsKey("countryId")) {
                countryIdSame = param["countryId"] == processParam1["countryId"]
            }
            var dobSame = false
            if (param.containsKey("dob") && processParam1.containsKey("dob")) {
                dobSame = param["dob"] == processParam1["dob"]
            }
            var genderSame = false
            if (param.containsKey("gender") && processParam1.containsKey("gender")) {
                genderSame = param["gender"] == processParam1["gender"]
            }

            return emailSame && firstNameSame && middleNameSame && lastNameSame && nationalityIdSame && countryIdSame && dobSame && genderSame
        }
        return false
    }

    fun queryResidence(fromPage: String?) {
        val map = hashMapOf<String, Any>()
        if (fromPage == SelectCountryResidenceActivity.TYPE_FORM_SIGN_UP) { // 注册页面不需要传监管，需要显示所有国家
            map["query"] = ""
        } else {
            map["query"] = ""
            map["regulator"] = "CIMA"
        }
        repository.queryResidence(map).compose(applyLoading(true))
            .bindTLiveData(residenceLiveData)
    }

    fun getProcess(step: String, isLoading: Boolean = false) {
        repository.getProcess(hashMapOf(
            "token" to UserDataUtil.loginToken(),
            "step" to step,
            "openAccountMethod" to "1"
        )).compose(applyLoading(isLoading))
            .bindTLiveData(if (step == "1") getProcessLiveData1 else getProcessLiveData4)
    }

    fun getAccountSelect() {
        repository.getAccountSelect()
            .compose(applyLoading(true))
            .bindTLiveData(accountSelectLiveData)
    }

    fun saveProcess(step: Int, param: HashMap<String, Any>) {
        tempParam = param
        repository.saveProcess(param)
            .bindTLiveData(when (step) {
                1 -> saveProcessLiveData1
                4 -> saveProcessLiveData4
                else -> MutableLiveData()
            })
    }

    fun getPlatFormAccountTypeCurrency(map: HashMap<String, String>) {
        repository.getPlatFormAccountTypeCurrency(map)
            .bindTLiveData(platTypeCurrencyLiveData)
    }

    fun getAccountTypeTitle() {
        repository.getAccountTypeTitle()
            .bindTLiveData(platTitleLiveData)
    }

    fun getAuditStatus() {
        repository.getAuditStatus()
            .bindTLiveData(auditStatusLiveData)
    }

    fun accountOpenAccountValidate() {
        repository.accountOpenAccountValidate()
            .bindTLiveData(openWalletLiveData)
    }
}