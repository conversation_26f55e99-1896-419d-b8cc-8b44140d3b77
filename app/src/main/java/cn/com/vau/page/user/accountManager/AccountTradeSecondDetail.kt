package cn.com.vau.page.user.accountManager

data class AccountTradeSecondDetail(
        var acountCd: String? = "",
        var currencyType: String? = "",
        var guarantee: String? = "",
        var equity: String? = "",
        var profit: String? = "",
        var profitRate: String? = "",
        var payRate: String? = "",
        var readyOnlyAccount: Boolean? = false,
        var lastLoginDate: String? = "",
        var isArchive: Boolean? = false,
        var accountTypeName: String? = "", //ECN账户类型
        var leverage: String? = "", //杠杆
        var ecnUpgrade: Boolean? = false, //是否显示ECN升级按钮
)