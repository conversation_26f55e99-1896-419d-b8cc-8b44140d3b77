package cn.com.vau.page.user.loginBind

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.*
import cn.com.vau.util.SendCodeUtil
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface LoginBindContract {
    interface Model : BaseModel {
        fun phoneIsUsedApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun getCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<VerificationCodeData>): Disposable
        fun bindEmailApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<EmailBindBean>): Disposable
        fun thirdpartyBindPhoneApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<EmailBindBean>): Disposable
    }

    interface View : BaseView {
        fun showLocalAreaInfo()
        fun back()
        fun goSecond()
        fun showCaptcha()
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener)
        abstract fun startSendCodeUtil()
        abstract fun stopSendCodeUtil()
        abstract fun phoneIsUsedApi(mobile: String?)
        abstract fun getCodeApi(mobile: String?, validateCode: String)
        abstract fun bindEmailApi(
            userTel: String?, randStr: String?
        )

        abstract fun setSelectAreaData(areaCodeData: SelectCountryNumberObjDetail)
        abstract fun getLocalAreaInfo()
        abstract fun dealData(resUserInfoModel: EmailBindBean, userTel: String, userPassword: String)
    }

}
