package cn.com.vau.page.setting.activity

import android.content.Intent
import android.text.TextUtils
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication.Companion.context
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.GetDiskCacheSizeTaskUtil
import cn.com.vau.databinding.ActivitySettingBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.setting.viewmodel.SettingViewModel
import cn.com.vau.signals.activity.H5DebugActivity
import cn.com.vau.ui.common.activity.ChooseYourThemeActivity
import cn.com.vau.ui.common.activity.LanguageActivity
import cn.com.vau.ui.mine.activity.AccountActivityActivity
import cn.com.vau.ui.mine.activity.FeedbackFormActivity
import cn.com.vau.util.AppUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterInputDialog
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.cache.DiskCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import java.io.File

class SettingActivity : BaseMvvmActivity<ActivitySettingBinding, SettingViewModel>() {

    override fun initView() {
        mBinding.run {
            // 语言
            layoutLanguage.setDetailStr(SpManager.getLanguageShowName())
            // 主题
            layoutTheme.setDetailStr(getString(if (AppUtil.isLightTheme()) R.string.light_theme else R.string.dark_theme))
            sbScreenshotShare.setState(SpManager.getScreenshotShareEnable())
            // 登录 设置按钮有loading状态，未登录时 直接本地操作，不用loading
            sbScreenshotShare.setIsCanLoading(UserDataUtil.isLogin())
            layoutAccountActivity.isVisible = UserDataUtil.isLogin()
            layoutFeedback.isVisible = UserDataUtil.isLogin()
            layoutClearCache.isVisible = UserDataUtil.isLogin()
            // 版本号
            layoutVersion.setDetailStr("V${AppUtil.getVersionName()}")

            // 清除缓存
            GetDiskCacheSizeTaskUtil(layoutClearCache.getDetailStr()).execute(
                File(context.cacheDir, DiskCache.Factory.DEFAULT_DISK_CACHE_DIR)
            )
            // 登录 并且 是跟单账号 才显示tnc 的安妮
            layoutTnc.isVisible = UserDataUtil.isLogin() && UserDataUtil.isStLogin()
            // 切换线路 -> 测试环境可见
            layoutSwitchNoTradeLine.isVisible = !HttpUrl.official
            layoutSwitchTradeLine.isVisible = !HttpUrl.official
            layoutSwitchStTradeLine.isVisible = !HttpUrl.official
            layoutSwitchWsLine.isVisible = !HttpUrl.official
            layoutSwitchStWsLine.isVisible = !HttpUrl.official
            layoutSwitchH5Line.isVisible = !HttpUrl.official
            layoutJumpH5Url.isVisible = !HttpUrl.official
            tvLogout.isVisible = UserDataUtil.isLogin()
            tvDeleteAccount.isVisible = UserDataUtil.isLogin()
        }
        if (!UserDataUtil.isLogin()) {
            SensorsDataUtil.track(SensorsConstant.V3550.NLIPROFILESETTINGPAGE_VIEW)
        }
    }

    override fun createObserver() {
        // 检查版本号成功
        mViewModel.checkVerLiveData.observe(this) {
            showUpdateFlag()
        }
        // 解绑/删除账户成功
        mViewModel.unbindSrcLiveData.observe(this) {
            setLogoutState()
            // 通知主页面，是解绑手机号退出的，需要跳转到登录页
            EventBus.getDefault().post(NoticeConstants.UNBIND_ACCOUNT)
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle).collect {
                if (it !is DataEvent) return@collect
                when (it.tag) {
                    SettingViewModel.EVENT_SCREENSHOT -> {
                        mBinding.sbScreenshotShare.setState(it.data == true)
                    }
                }
            }
        }
    }

    override fun initListener() {
        mBinding.run {
            // 语言
            layoutLanguage.clickNoRepeat {
                openActivity(LanguageActivity::class.java)
            }
            // 主题
            layoutTheme.clickNoRepeat {
                openActivity(ChooseYourThemeActivity::class.java)
            }
            tvScreenshotShare.clickNoRepeat {
                showScreenPromptDialog()
            }
            sbScreenshotShare.setOnLoadingListener {
                // 未登录时，不应该有loading状态，直接操作本地数据
                if (!UserDataUtil.isLogin()) {
                    return@setOnLoadingListener
                }

                // 登录状态，会回调loading 状态，此时请求网络
                val enable = SpManager.getScreenshotShareEnable()
                mViewModel.userSetItemSet(!enable)
            }
            sbScreenshotShare.setStateChangeListener { state ->
                // 登录时 不使用回调的状态
                if (UserDataUtil.isLogin()) {
                    return@setStateChangeListener
                }
                // 未登录时，设置不用请求网络直接操作本地数据
                SpManager.putScreenshotShareEnable(state)
            }
            // 账户活动
            layoutAccountActivity.clickNoRepeat {
                openActivity(AccountActivityActivity::class.java)
            }
            // 反馈信息
            layoutFeedback.clickNoRepeat {
                openActivity(FeedbackFormActivity::class.java)
            }
            // 关于我们
            layoutAboutUs.clickNoRepeat {
                val htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/support/productIntro"
                NewHtmlActivity.openActivity(this@SettingActivity, url = htmlUrl)
                if (!UserDataUtil.isLogin()) {
                    SensorsDataUtil.track(SensorsConstant.V3550.NLIPROFILESETTINGPAGE_ABOUTUS_CLICK)
                }
            }
            // 清除缓存
            layoutClearCache.setOnClickListener {
                lifecycleScope.launch(Dispatchers.IO) {
                    Glide.get(context).clearDiskCache()
                    withContext(Dispatchers.Main) {
                        Glide.get(context).clearMemory()
                        GetDiskCacheSizeTaskUtil(mBinding.layoutClearCache.getDetailStr()).execute(
                            File(
                                context.cacheDir,
                                DiskCache.Factory.DEFAULT_DISK_CACHE_DIR
                            )
                        )
                        ToastUtil.showToast(getString(R.string.clear_cache))
                    }
                }
            }
            // 检查版本
            layoutVersion.clickNoRepeat {
                if (!TextUtils.isEmpty(mViewModel.checkVerLiveData.value?.dlPath)) {
                    val intent = Intent()
                    intent.action = "android.intent.action.VIEW"
                    // val uri = Uri.parse("https://vau-usa.oss-accelerate.aliyuncs.com//apk/au/Vantage.apk")
                    val uri = UrlConstants.APK_UPDATE_URL.toUri()
                    intent.data = uri
                    startActivity(intent)
                } else if (!TextUtils.isEmpty(mViewModel.checkVersionMsgInfo)) {
                    ToastUtil.showToast(mViewModel.checkVersionMsgInfo.ifNull())
                }
            }
            layoutTnc.clickNoRepeat {
                NewHtmlActivity.openActivity(this@SettingActivity, title = getString(R.string.account_terms_and_conditions), url = UrlConstants.TNC_URL)
            }

            // 退出登录
            tvLogout.clickNoRepeat {
                loginOut()
            }
            // 删除账户
            tvDeleteAccount.clickNoRepeat {
                showDeleteAccountPpw()
            }
        }
        configSwitchTestUrl()
    }

    private fun configSwitchTestUrl() {
        if (HttpUrl.official) {
            return
        }
        mBinding.run {
            // 切换非交易线路
            layoutSwitchNoTradeLine.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_NO_TRADE)
            }
            // 切换交易线路
            layoutSwitchTradeLine.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_TRADE)
            }
            // 切换跟单线路
            layoutSwitchStTradeLine.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_ST_TRADE)
            }
            // 切换ws线路
            layoutSwitchWsLine.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_WS)
            }
            // 切换跟单ws线路
            layoutSwitchStWsLine.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_ST_WS)
            }
            // 切换h5线路
            layoutSwitchH5Line.clickNoRepeat {
                SwitchLineActivity.open(this@SettingActivity, SwitchLineActivity.TYPE_H5)
            }
            // 跳转输入的h5地址
            layoutJumpH5Url.clickNoRepeat {
                H5DebugActivity.openActivity(this@SettingActivity)
            }
        }
    }

    private fun loginOut() {
        CenterActionDialog.Builder(this)
            .setContent(getString(R.string.are_you_sure_log_out)) // 设置内容   是否确定退出登录?
            .setStartText(getString(R.string.cancel)) // 设置左侧按钮文本
            .setEndText(getString(R.string.ok)) // 设置右侧按钮文本
            .setOnEndListener {
                setLogoutState()
                // 通知到MainActivity做退出登录操作
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }
            .build()
            .showDialog()
    }

    private fun showScreenPromptDialog() {
        BottomContentDialog.Builder(this)
            .setTitle(getString(R.string.screenshot_share_popup))
            .setContent(getString(R.string.when_enabled_a_offering_sharing_options))
            .build()
            .showDialog()
    }

    private fun setLogoutState() {
        // 刷新fcm的状态
        EventBus.getDefault().post(NoticeConstants.SUBSCRIBE_TOPIC)
        // 设置退出状态为true
        SpManager.putExitStatus(true)
    }

    private fun showUpdateFlag() {
        val drawable = ContextCompat.getDrawable(context, R.drawable.img_new)
        drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
        mBinding.layoutVersion.getDetailStr().compoundDrawablePadding = 4.dp2px()
        mBinding.layoutVersion.getDetailStr().setCompoundDrawables(drawable, null, null, null)
    }

    /**
     * 删除账户弹框
     */
    private fun showDeleteAccountPpw() {
        val content = "${getString(R.string.account_cancellation_will_not_be_restored)}.${getString(R.string.if_you_are_you_delete_input_box)}"
        CenterInputDialog.Builder(this)
            .setContent(content) // 设置内容
            .setStartText(getString(R.string.cancel))
            .setEndText(getString(R.string.confirm))
            .setOnEndListener { dialog, text ->
                if (text.toString().lowercase() != "delete") {
                    // 请输入正确的内容
                    ToastUtil.showToast(getString(R.string.please_enter_the_correct_content))
                    return@setOnEndListener
                }
                mViewModel.unbindPhoneApi()
                dialog.dismissDialog()
            }.build().showDialog()
    }
}