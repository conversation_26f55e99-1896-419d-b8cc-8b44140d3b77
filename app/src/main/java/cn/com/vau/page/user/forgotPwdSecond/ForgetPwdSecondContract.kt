package cn.com.vau.page.user.forgotPwdSecond

import android.os.Bundle
import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.util.SendCodeUtil
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface ForgetPwdSecondContract {

    interface Model : BaseModel {
        fun goEditPwdApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable
        fun getVerificationCodeApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
        ): Disposable

        fun smsValidateSmsForgetPwdCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun validateEmailForgetPwdCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
//        fun getMobileApi(map: java.util.HashMap<String, Any>, baseObserver: BaseObserver<GetMobileBean>): Disposable
        fun getWithdrawRestrictionMsgApi(
            map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>
        ): Disposable //获取出金限制横幅文案

        /**
         * 发送邮箱验证码
         */
        fun emailSendEmailCodeApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
        ): Disposable

        /**
         * 校验邮箱验证码
         */
        fun validateEmailFirstLoginCodeApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>
        ): Disposable
    }

    interface View : BaseView {
        fun back()
        fun showCaptcha()
        fun goThird(validateCode: String?)
        fun showWithdrawRestrictionMsg(msg: String?) //出金限制横幅

        /**
         * 跳转到绑定手机号页面
         */
        fun goBindPhone(bundle: Bundle)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun goEditPwdApi(pwd: String?, pwdAgain: String?, verificationCode: String?)
        abstract fun getVerificationCodeApi(validateCode: String)
        abstract fun validateSmsForgetPwdCodeApi(validateCode: String)
        abstract fun validateEmailForgetPwdCodeApi(validateCode: String)
        abstract fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener)
        abstract fun startSendCodeUtil()
        abstract fun stopSendCodeUtil()
        abstract fun getWithdrawRestrictionMsgApi(type: Int) //获取出金限制横幅文案

        /**
         * 校验邮箱验证码
         */
        abstract fun validateEmailFirstLoginCodeApi(validateCode: String)

        /**
         * 发送邮箱验证码
         */
        abstract fun emailSendEmailCodeApi()
    }

}
