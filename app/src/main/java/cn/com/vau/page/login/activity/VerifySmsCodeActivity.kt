package cn.com.vau.page.login.activity

import android.annotation.SuppressLint
import android.content.*
import android.os.*
import androidx.activity.viewModels
import androidx.annotation.Keep
import androidx.core.os.bundleOf
import androidx.core.text.*
import androidx.core.view.isVisible
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.ActivityVerifySmsCodeBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.viewmodel.*
import cn.com.vau.page.login.viewmodel.activity.VerifySmsCodeViewModel
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.profile.activity.addOrForgotSecurityPWD.SetFundsPwdKycActivity
import cn.com.vau.profile.activity.kycLink.*
import cn.com.vau.profile.activity.twoFactorAuth.activity.*
import cn.com.vau.util.*
import cn.com.vau.util.SendCodeUtil.SendCodeListener
import cn.com.vau.util.widget.dialog.*
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.*
import kotlinx.parcelize.Parcelize
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2025/04/01
 * desc：验证sms验证码
 */
@SuppressLint("SetTextI18n")
class VerifySmsCodeActivity : BaseMvvmActivity<ActivityVerifySmsCodeBinding, VerifySmsCodeViewModel>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private val loginViewModel by viewModels<LoginViewModel>()
    private var mCaptcha: Captcha? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean") ?: SignUpRequestBean()
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        loginViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        sendCodeViewModel.stopSendCodeUtil()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        mViewModel.signUpRequestBean = intent?.getParcelableExtra("signUpRequestBean") ?: SignUpRequestBean()
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        loginViewModel.signUpRequestBean = mViewModel.signUpRequestBean
        // 重置状态和数据
        mBinding.passwordView.clearInput()
        sendCodeViewModel.stopSendCodeUtil()
        initView()
        initData()
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel, loginViewModel)
        mBinding.tvLoginType.text = "${getString(R.string.the_verification_code_has_been_sent_to)}:"
        mBinding.tvAccount.text = "+${mViewModel.signUpRequestBean?.countryNum} ${mViewModel.signUpRequestBean?.mobile}"
        // 按钮默认不可点击
        mBinding.tvSendEms.isEnabled = false
        mBinding.llWhatsApp.isEnabled = false
        // 根据发送方式显示不同按钮
        showWhichBtn()

        when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
            VerifySmsCodeType.BIND_PHONE_KYC, VerifySmsCodeType.VERIFY_PHONE_KYC -> {
                mBinding.tvChange.isVisible = true
                mBinding.tvChange.text = buildSpannedString { // Change文字加下划线
                    underline { append(getString(R.string.kyc_change)) }
                }
            }

            else -> {
                mBinding.tvChange.isVisible = false // 不需要展示 Change 文案
            }
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                delay(500)
                mBinding.passwordView.showSoftInput()
            }
        }
    }

    override fun initData() {
        if (mViewModel.signUpRequestBean?.verifySmsCodeType == VerifySmsCodeType.VERIFY_PHONE_KYC) {
            sendCode()
        } else {
            sendCodeViewModel.startSendCodeUtil()
        }
    }

    override fun createObserver() {
        // 发送验证码时触发滑块验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            sendCodeViewModel.startSendCodeUtil()
        }
        // 验证sms验证码成功
        sendCodeViewModel.validateSmsCodeSuccessLiveData.observe(this) {
            validateCodeSuccess()
        }
        // 登录时触发滑块验证
        loginViewModel.loginShowCaptchaLiveData.observe(this) {
            loginShowCaptcha()
        }
        // 是否绑定了2fa
        loginViewModel.isBind2Fa.observe(this) {
            if (it) { // 跳转账户列表页面
                openActivity(AccountManagerActivity::class.java, bundleOf().apply {
                    putInt(Constants.IS_FROM, 1)
                })
            } else {  // 未绑定 走绑定流程
                TFABindActivity.open(this, TFAVerifyActivity.FROM_LOGIN, mViewModel.signUpRequestBean)
            }
            ActivityManagerUtil.getInstance().finishActivity(TFAVerifyActivity::class.java)
            finish()
        }
        // kyc认证、绑定、修改手机号成功
        mViewModel.updateUserPhoneSuccessLiveData.observe(this) {
            updateUserPhoneSuccess()
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 验证码输入框回调
        mBinding.passwordView.setPasswordListener(passwordListener)
        // 发送验证码回调
        sendCodeViewModel.initSendCodeUtil(sendCodeListener)
        // 修改手机号
        mBinding.tvChange.clickNoRepeat {
            BindPhoneKycActivity.open(this, mViewModel.signUpRequestBean)
        }
        // 重新发送
        mBinding.tvReSend.clickNoRepeat {
            sendCode()
        }
        // 收不到验证码提示文案
        mBinding.tvNotReceiveCodeTips.setOnClickListener {
            showNotReceiveCodeDialog()
        }
        // sms
        mBinding.tvSendEms.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
            sendCode()
        }
        // whatsApp
        mBinding.llWhatsApp.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.WHATSAPP
            sendCode()
        }
    }

    /**
     * 验证码输入框回调
     */
    private val passwordListener = object : PasswordView.PasswordListener {
        override fun passwordChange(changeText: String?) {

        }

        override fun passwordComplete() {
            val code = mBinding.passwordView.getPassword()
            if (code.length == 6) {
                mViewModel.signUpRequestBean?.smsCode = code
                validateCode()
                mBinding.passwordView.hiddenSoftInputFromWindow()
            }
        }

        override fun keyEnterPress(password: String?, isComplete: Boolean) {

        }
    }

    /**
     * 发送验证码回调
     */
    private val sendCodeListener = object : SendCodeListener {
        override fun onTick(millisUntilFinished: Int) {
            val second = millisUntilFinished.toString() // 倒计时秒数
            val fullText = getString(R.string.resend_code_in_x_seconds, second)
            mBinding.tvReSend.setTextColor(color_c1e1e1e_cebffffff)
            mBinding.tvReSend.text = countDownTextHelper.updateCountDownText(fullText, second)
            mBinding.tvReSend.isEnabled = false
            showWhichBtn()
            if (mBinding.tvSendEms.isVisible) {
                mBinding.tvSendEms.isEnabled = false
            }
            if (mBinding.llWhatsApp.isVisible) {
                mBinding.llWhatsApp.isEnabled = false
                mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            }
        }

        override fun onFinish() {
            mBinding.tvReSend.setTextColor(ce35728)
            mBinding.tvReSend.text = getString(R.string.resend)
            mBinding.tvReSend.isEnabled = true
            if (mBinding.tvSendEms.isVisible) {
                mBinding.tvSendEms.isEnabled = true
            }
            if (mBinding.llWhatsApp.isVisible) {
                mBinding.llWhatsApp.isEnabled = true
                mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            }
        }
    }

    /**
     * 根据发送方式显示不同按钮
     */
    private fun showWhichBtn() {
        if (mViewModel.signUpRequestBean?.sendCodeType == SendCodeType.PHONE) {
            mBinding.tvSendEms.isVisible = false
            mBinding.llWhatsApp.isVisible = true
        } else {
            mBinding.tvSendEms.isVisible = true
            mBinding.llWhatsApp.isVisible = false
        }
    }

    /**
     * 发送验证码时触发滑块验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCode(it)
        }
        mCaptcha?.validate()
    }

    /**
     * 登录时触发滑块验证
     */
    private fun loginShowCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
                VerifySmsCodeType.LOGIN_CHANGE_DEVICE -> { // 登录时用户切换设备触发邮箱OTP
                    if (mViewModel.signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
                        if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_2FA) {
                            loginViewModel.thirdLoginApi("10")
                        } else {
                            // 后续复用这个类时再完善这里逻辑
                        }
                    } else {
                        // 手机号验证后登录接口type传10，跟OTP的type没关系
                        if (mViewModel.signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
                            loginViewModel.loginEmailApi("10")
                        } else {
                            loginViewModel.loginPhoneApi("10")
                        }
                    }
                }

                else -> {}
            }
        }
        mCaptcha?.validate()
    }


    /**
     * 发送验证码
     */
    private fun sendCode(validate: String? = null) {
        mBinding.passwordView.clearInput()
        when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
            VerifySmsCodeType.LOGIN_CHANGE_DEVICE -> { // 登录时用户切换设备触发邮箱OTP type=10
                sendCodeViewModel.sendPhoneCodeApi("10", validate)
            }

            VerifySmsCodeType.CHANGE_LOGIN_PWD -> { // 修改登录密码 type=1
                sendCodeViewModel.sendPhoneCodeApi("1", validate)
            }

            VerifySmsCodeType.BIND_PHONE_KYC -> { // kyc绑定手机号 type=26
                sendCodeViewModel.sendPhoneCodeApi("26", validate)
            }

            VerifySmsCodeType.VERIFY_PHONE_KYC -> { // kyc认证手机号 type=25
                sendCodeViewModel.sendPhoneCodeApi("25", validate)
            }

            VerifySmsCodeType.FUNDS_PWD_ADD -> { // 新增资金密码  type=5
                sendCodeViewModel.sendPhoneCodeApi("5", validate)
            }

            VerifySmsCodeType.FUNDS_PWD_FORGET -> { // 忘记（重置）资金密码  type=6
                sendCodeViewModel.sendPhoneCodeApi("6", validate)
            }

            VerifySmsCodeType.CHANGE_TOTP -> { // 修改TOTP  type=18
                sendCodeViewModel.sendPhoneCodeApi("18", validate)
            }

            VerifySmsCodeType.UPDATE_PHONE_OLD_KYC -> { // kyc修改手机号验证旧手机OTP type=3
                sendCodeViewModel.sendPhoneCodeApi("3", validate)
            }

            VerifySmsCodeType.UPDATE_PHONE_NEW_KYC -> { // kyc修改手机号验证新手机OTP type=4
                sendCodeViewModel.sendPhoneCodeApi("4", validate)
            }

            else -> {}
        }
    }

    /**
     * 验证验证码
     */
    private fun validateCode() {
        when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
            VerifySmsCodeType.LOGIN_CHANGE_DEVICE -> { // 登录时用户切换设备触发邮箱OTP type=10
                if (mViewModel.signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
                    if (mViewModel.signUpRequestBean?.fromPage == FromPageType.FROM_PAGE_2FA) {
                        loginViewModel.thirdLoginApi("10")
                    } else {
                        // 后续复用这个类时再完善这里逻辑
                    }
                } else {
                    // 手机号验证后登录接口type传10，跟OTP的type没关系
                    if (mViewModel.signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
                        loginViewModel.loginEmailApi("10")
                    } else {
                        loginViewModel.loginPhoneApi("10")
                    }
                }
            }

            VerifySmsCodeType.CHANGE_LOGIN_PWD -> { // 修改登录密码 type=1
                sendCodeViewModel.validateSmsCodeApi("1")
            }

            VerifySmsCodeType.BIND_PHONE_KYC -> { // kyc绑定手机号 type=26
                mViewModel.updateUserPhoneApi("26")
            }

            VerifySmsCodeType.VERIFY_PHONE_KYC -> { // kyc认证手机号 type=25
                mViewModel.updateUserPhoneApi("25")
            }

            VerifySmsCodeType.FUNDS_PWD_ADD -> { // 新增资金密码  type=5
                sendCodeViewModel.validateSmsCodeApi("5")
            }

            VerifySmsCodeType.FUNDS_PWD_FORGET -> { // 忘记（重置）资金密码  type=6
                sendCodeViewModel.validateSmsCodeApi("6")
            }

            VerifySmsCodeType.CHANGE_TOTP -> { // 修改TOTP  type=18
                sendCodeViewModel.validateSmsCodeApi("18")
            }

            VerifySmsCodeType.UPDATE_PHONE_OLD_KYC -> { // kyc修改手机号验证旧手机OTP type=3
                sendCodeViewModel.validateSmsCodeApi("3")
            }

            VerifySmsCodeType.UPDATE_PHONE_NEW_KYC -> { // kyc修改手机号验证新手机OTP type=4
                sendCodeViewModel.validateSmsCodeApi("4", isAutoDismissDialog = false)
            }

            else -> {}
        }
    }

    /**
     * 验证sms验证码成功
     */
    private fun validateCodeSuccess() {
        when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
            VerifySmsCodeType.CHANGE_TOTP -> { // 修改TOTP  type=18
                mViewModel.signUpRequestBean?.fromPage = FromPageType.FROM_PAGE_KYC_OTP
                TFAChangeActivity.open(this, mViewModel.signUpRequestBean?.tfaCode, mViewModel.signUpRequestBean)
            }

            VerifySmsCodeType.CHANGE_LOGIN_PWD -> { // 修改登录密码 type=1
                ChangeLoginPwdKycActivity.open(this, mViewModel.signUpRequestBean)
            }

            VerifySmsCodeType.UPDATE_PHONE_OLD_KYC -> { // kyc修改手机号验证旧手机OTP type=3
                UpdatePhoneKycActivity.open(this, mViewModel.signUpRequestBean)
            }

            VerifySmsCodeType.FUNDS_PWD_ADD, VerifySmsCodeType.FUNDS_PWD_FORGET -> { // 新增资金密码  type=5 // 忘记（重置）资金密码  type=6
                SetFundsPwdKycActivity.open(this, mViewModel.signUpRequestBean)
            }

            VerifySmsCodeType.UPDATE_PHONE_NEW_KYC -> { // kyc修改手机号验证新手机OTP type=4
                mViewModel.updateUserPhoneApi("4")
            }

            else -> {}
        }
    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        mBinding.passwordView.hiddenSoftInputFromWindow()
        val data = arrayListOf(
            HintLocalData(
                getString(R.string.double_check_your_phone_number),
                getString(R.string.ensure_you_have_it_correctly),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips1)
            ),
            HintLocalData(
                getString(R.string.wait_a_few_minutes),
                getString(R.string.delays_can_happen_occasionally),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips2)
            ),
            HintLocalData(
                getString(R.string.try_another_verification_method),
                getString(R.string.switch_verification_methods_your_otp),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips3)
            )
        )
        BottomInfoWithIconListDialog.Builder(this)
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }

    /**
     * kyc认证、绑定手机号成功弹框
     */
    private fun updateUserPhoneSuccess() {
        when (mViewModel.signUpRequestBean?.verifySmsCodeType) {
            VerifySmsCodeType.UPDATE_PHONE_NEW_KYC -> { // kyc修改手机号验证新手机OTP type=4
                EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
                ActivityManagerUtil.getInstance().finishActivity(UpdatePhoneKycActivity::class.java)
                ActivityManagerUtil.getInstance().finishAllSameActivity(VerifySmsCodeActivity::class.java)
            }

            else -> {
                CenterActionWithIconDialog.Builder(this)
                    .setLottieIcon(R.raw.lottie_dialog_ok)
                    .setTitle(getString(R.string.phone_number_successfully_verified))
                    .setContent(getString(R.string.your_phone_number_this_important_updates))
                    .setSingleButton(true)
                    .setSingleButtonText(getString(R.string.ok))
                    .setDismissOnBackPressed(false)
                    .setOnSingleButtonListener { // 点击按钮后跳转到来源页面
                        EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
                        // 关闭页面
                        ActivityManagerUtil.getInstance().finishAllSameActivity(BindPhoneKycActivity::class.java)
                        ActivityManagerUtil.getInstance().finishAllSameActivity(VerifySmsCodeActivity::class.java)
                    }.build().showDialog()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        sendCodeViewModel.stopSendCodeUtil()
        mCaptcha?.destroy()
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, VerifySmsCodeActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }

    /**
     * 验证sms的类型
     */
    @Parcelize
    @Keep
    enum class VerifySmsCodeType : Parcelable {
        CHANGE_LOGIN_PWD, // 修改登录密码 type=1
        FUNDS_PWD_ADD, // 新增资金密码  type=5
        FUNDS_PWD_FORGET, // 忘记（重置）资金密码  type=6
        LOGIN_CHANGE_DEVICE, // 登录时用户切换设备触发邮箱OTP type=10
        CHANGE_TOTP, // 修改TOTP  type=18
        VERIFY_PHONE_KYC, // kyc认证手机号 type=25
        BIND_PHONE_KYC, // kyc绑定手机号 type=26
        UPDATE_PHONE_OLD_KYC, // kyc修改手机号验证旧手机OTP type=3
        UPDATE_PHONE_NEW_KYC, // kyc修改手机号验证新手机OTP type=4
    }
}