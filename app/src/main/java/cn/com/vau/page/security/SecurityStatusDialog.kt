package cn.com.vau.page.security

import android.content.Context
import cn.com.vau.R
import cn.com.vau.databinding.DialogSecurityStatusBinding
import cn.com.vau.util.AttrResourceUtil
import com.lxj.xpopup.core.CenterPopupView

class SecurityStatusDialog(
    context: Context,
    private var password: Boolean = false,
    private var passKey: Boolean = false,
    private var twoFA: Boolean = false,
    private var lock: Boolean = false
) : CenterPopupView(context) {

    private var binding: DialogSecurityStatusBinding? = null
    private val availableRes: Int by lazy { R.drawable.icon2_cb_tick_circle_c15b374 }
    private val unavailableRes: Int by lazy { R.drawable.draw_bitmap2_info_2_12x12_c1e1e1e_cebffffff }

    override fun getImplLayoutId(): Int = R.layout.dialog_security_status

    override fun onCreate() {
        binding = DialogSecurityStatusBinding.bind(popupImplView)
//        binding?.ivStatusPassword?.setImageResource(if (password) availableRes else unavailableRes)
//        binding?.tvPassword?.setTextColor(AttrResourceUtil.getColor(context, if (password) R.attr.color_c1e1e1e_cebffffff else R.attr.color_ca61e1e1e_c99ffffff))
        binding?.tvPasskey?.setTextColor(AttrResourceUtil.getColor(context, if (passKey) R.attr.color_c1e1e1e_cebffffff else R.attr.color_ca61e1e1e_c99ffffff))
        binding?.ivPasskey?.setImageResource(if (passKey) availableRes else unavailableRes)
        binding?.ivStatus2FA?.setImageResource(if (twoFA) availableRes else unavailableRes)
         binding?.ivStatusSatefyLocks?.setImageResource(if (lock) availableRes else unavailableRes)
        binding?.tv2FA?.setTextColor(AttrResourceUtil.getColor(context, if (twoFA) R.attr.color_c1e1e1e_cebffffff else R.attr.color_ca61e1e1e_c99ffffff))
        binding?.tvSatefyLocks?.setTextColor(AttrResourceUtil.getColor(context, if (lock) R.attr.color_c1e1e1e_cebffffff else R.attr.color_ca61e1e1e_c99ffffff))
        binding?.tvButton?.setOnClickListener {
            dismiss()
        }
    }

}