 package cn.com.vau.page.depositNew

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.depositcoupon.ManageFundsDetailsObj
import cn.com.vau.databinding.ActivityDepositDetailsBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.depositNew.vm.DepositDetailsViewModel
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil

/**
 * 入金详情
 */
class DepositDetailsActivity : BaseMvvmActivity<ActivityDepositDetailsBinding, DepositDetailsViewModel>() {

    private var detailData: ManageFundsDetailsObj? = null
    // type: 00入金，01出金，10转账，20收益

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.orderNo = intent?.extras?.getString("orderNo", "").ifNull()
        mViewModel.accountID = intent?.extras?.getString("accountID", "").ifNull(UserDataUtil.accountCd())
    }

    override fun initView() {
    }

    override fun initData() {
        mViewModel.fundMoneyInDetail()
    }

    override fun initListener() {
        mBinding.onClickListener = this
        mBinding.mHeaderBar.setStartBackIconClickListener {
            finish()
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
    }

    override fun createObserver() {
        mViewModel.fundDetailLiveData.observe(this) {
            detailData = it
            mBinding.run {
                val currency = detailData?.currency.ifNull()
                ImageLoaderUtil.loadImage(
                    this@DepositDetailsActivity,
                    AttrResourceUtil.getDrawable(
                        this@DepositDetailsActivity,
                        when (detailData?.transCode.toIntCatching()) {
                            1, 11, 60 -> R.attr.imgAlertWrong
                            else -> R.attr.imgAlertOk
                        }
                    ),
                    ivStatus,
                )
                ivStatus.isVisible = true
                tvOrderStatus.text = detailData?.tranStatus.ifNull()
                tvOrderStatus.setTextColor(setOrderStatusColorByCode(detailData?.transCode.ifNull()))
                tvOrderAmount.text = "${(detailData?.amount.ifNull()).numCurrencyFormat(currency)} $currency"
                tvOrderExpired.text = detailData?.proccessNote.ifNull()
                tvOrderExpiredTime.text = detailData?.createdTime.ifNull()
                ImageLoaderUtil.loadImage(this@DepositDetailsActivity, detailData?.iconUrl, ivMethodIcon)
                tvMethod.text = detailData?.payType.ifNull()
                tvOrderNumber.text = detailData?.orderNo.ifNull()
                tvUserNeme.text = detailData?.userName.ifNull()
                tvAccount.text = detailData?.accountId.ifNull()
                tvNext.isVisible = true == detailData?.isShowRetry
            }
        }
        mViewModel.fundRetryLiveData.observe(this) {
            NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
            finish()
        }
    }

    private fun setOrderStatusColorByCode(statusCode: String): Int {
        return when (statusCode) {
            "15", "40" -> ContextCompat.getColor(this, R.color.c00c79c)
            else -> ContextCompat.getColor(this, R.color.cf44040)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvNext -> {
                mViewModel.fundDetailsRetry()
                // 埋点
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V343.DEPOSIT_LVL3_RETRY_BUTTON_CLICK, bundleOf(
                        "Payment_method" to detailData?.payType.ifNull(), "Order_number" to detailData?.orderNo.ifNull()
                    )
                )
            }
        }
    }
}