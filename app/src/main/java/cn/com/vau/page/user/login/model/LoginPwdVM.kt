package cn.com.vau.page.user.login.model

import androidx.annotation.ColorInt
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.mvvm.base.BaseViewModel

/**
 * author：lvy
 * date：2024/7/4
 * desc：
 */
class LoginPwdVM : BaseViewModel() {

    //手机号
    val mobileLiveData = MutableLiveData<String?>()
    val areaCodeLiveData = MutableLiveData<String?>()

    //邮箱
    val hintStrLiveData = MutableLiveData<String?>()
    val hintStrColorLiveData = MutableLiveData<Int?>()

    //公共
    val nextStrLiveData = MutableLiveData<String?>()
    val hideBottomViewLiveData = MutableLiveData<Boolean>()

    fun setMobile(mobile: String?) {
        mobileLiveData.value = mobile
    }

    fun setAreaCode(areaNum: String?) {
        areaCodeLiveData.value = areaNum
    }

    fun setHintStr(hintStr: String?) {
        hintStrLiveData.value = hintStr
    }

    fun setHintStrColor(@ColorInt hintStrColor: Int?) {
        hintStrColorLiveData.value = hintStrColor
    }

    fun setNextStr(nextStr: String?) {
        nextStrLiveData.value = nextStr
    }

    /**
     * 如果为三方登录，需要隐藏底部布局
     */
    fun hideBottomView() {
        hideBottomViewLiveData.value = true
    }
}