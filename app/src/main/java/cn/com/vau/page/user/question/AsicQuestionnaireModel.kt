package cn.com.vau.page.user.question

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.AuditQuestionData
import io.reactivex.disposables.Disposable


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class AsicQuestionnaireModel : AsicQuestionnaireContract.Model {
    override fun getQustionList(map: HashMap<String, Any>, baseObserver: BaseObserver<AuditQuestionData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().
                appraisalList(map), baseObserver)
        return baseObserver.disposable
    }

    override fun submitAnswer(map: HashMap<String, Any>, baseObserver: BaseObserver<AuditQuestionData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().
                auditRightAnswer(map), baseObserver)
        return baseObserver.disposable
    }
}
