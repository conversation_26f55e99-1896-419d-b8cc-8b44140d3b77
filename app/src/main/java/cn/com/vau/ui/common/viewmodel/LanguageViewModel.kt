package cn.com.vau.ui.common.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.SelectCountryCodeBean
import cn.com.vau.data.profile.LanguageBean
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.language.LanguageHelper
import java.util.Locale

class LanguageViewModel : BaseViewModel() {

    val languageListLiveData by lazy { MutableLiveData<List<LanguageBean>>() } // 语言列表获取成功
    val setLanguageLiveData = MutableLiveData<Locale>() // 设置语言成功
    val selectCountryByPhoneCodeLiveData = MutableLiveData<Boolean>() // 通过区号查询国家名称

    /**
     * 获取语言列表
     */
    fun getLanguageListApi() {
        requestNet({ baseService.usersetLanguageListApi() }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val list = it.data?.obj?.availableLanguages?.filter { !it.sourceLanguageName.isNullOrBlank() } // 过滤空数据
            list?.find { it.languageCode == LanguageHelper.getAppLanguageCode() }?.isSelected = true // 默认选择的item
            languageListLiveData.value = list
        })
    }

    /**
     * 语言设置保存到后台
     *
     * PS：安卓端语言全是本地保存的，用不到这个接口，请求这个接口的目的是 用户在安卓端设置语言了再登录iOS端，iOS需要适配处理 20250113
     *    且后端推送也用到了 value 参数的配置
     */
    fun userSetItemSet(bean: LanguageBean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = "us0001" // 设置项代码，参考用户设置代码表
        paramMap["value"] = bean.appLanguageName.ifNull()
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                hideLoading()
                return@requestNet
            }
            setLanguage(bean)
        }, isShowDialog = true, isAutoDismissDialog = false)
    }

    /**
     * 设置语言（未登录状态也要直接调用这个方法刷新）
     */
    fun setLanguage(bean: LanguageBean) {
        val list = bean.languageCode?.split("_")
        if (!list.isNullOrEmpty()) {
            if (list.size > 1) {
                setLanguageLiveData.value = Locale(list.first(), list.last())
            } else {
                setLanguageLiveData.value = Locale(list.first())
            }
        } else {
            setLanguageLiveData.value = LanguageHelper.getAppLocale()
        }
        SpManager.putLanguageShowName(bean.sourceLanguageName.ifNull())
        selectCountryByPhoneCodeApi()
    }

    /**
     * 通过区号查询国家名称
     */
    // 与iOS统一逻辑，不管这个接口成不成功都要能正常切换语言 20250514修改
    private fun selectCountryByPhoneCodeApi() {
        val paramMap = hashMapOf<String, Any>()
        paramMap["code"] = SpManager.getCountryNum(Constants.defaultCountryNum)
        requestNet({ baseService.selectCountryByPhoneCodeApi(paramMap) }, {
            if (it.isSuccess()) {
                setAreaData(it.data?.obj)
            }
            selectCountryByPhoneCodeLiveData.value = true
        }, {
            selectCountryByPhoneCodeLiveData.value = true
        })
    }

    /**
     * 设置国家区号缓存
     */
    private fun setAreaData(bean: SelectCountryCodeBean?) {
        SpManager.putCountryCode(bean?.countryCode.ifNull()) // 国家代码 "AU"
        SpManager.putCountryNum(bean?.phoneNum.ifNull()) // 国家区号 "61"
        SpManager.putCountryName(bean?.countryName.ifNull()) // 国家名称 "澳大利亚"
        if (UserDataUtil.isLogin()) {
            UserDataUtil.setCountryCode(bean?.countryCode)
            UserDataUtil.setAreaCode(bean?.phoneNum)
        }
    }
}