package cn.com.vau.ui.order

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.databinding.FragmentOrderThemeStBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.trade.ext.changeTouchSlop
import cn.com.vau.trade.perform.StTradeStrategyPerformance
import cn.com.vau.trade.perform.StTradeTitlePerformance
import cn.com.vau.trade.st.fragment.StCopyTradingFragment
import cn.com.vau.trade.st.fragment.StManualTradingFragment
import cn.com.vau.util.TabType
import cn.com.vau.util.init
import cn.com.vau.util.setVp
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * order
 */
class StOrderThemeFragment : BaseMvvmBindingFragment<FragmentOrderThemeStBinding>() {

    private var currentPosition = -1

    private val fragmentList by lazy {
        ArrayList<Fragment>().apply {
            add(StCopyTradingFragment())
            add(StManualTradingFragment())
        }
    }

    private val titleList = ArrayList<String>()

    private val performManager by lazy {
        PerformManager(this)
    }

    /** 标题 */
    private var titlePerformance: StTradeTitlePerformance? = null

    /** 策略管理 */
    private var strategyPerformance: StTradeStrategyPerformance? = null

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        titleList.add(getString(R.string.copy_trading))
        titleList.add(getString(R.string.manual_trading))
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        lazyInitView()
        addPerformance()
        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Orders") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    private fun addPerformance() {
        titlePerformance = StTradeTitlePerformance(this, mBinding)
        titlePerformance?.run { performManager.addPerformance(this) }
        strategyPerformance = StTradeStrategyPerformance(this, mBinding)
        strategyPerformance?.run { performManager.addPerformance(this) }
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun lazyInitView() {

        mBinding.mViewPager2.init(fragmentList, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR)
        mBinding.mViewPager2.changeTouchSlop()

        // 首次打开 app 直接首页交易，接收 MAIN_SHOW_ORDERS_ITEM_OPEN_ORDER 通知后切换问题
        if (currentPosition != -1) {
            mBinding.mViewPager2.setCurrentItem(currentPosition, false)
            currentPosition = -1
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_OPEN,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_PENDING_REVIEW,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_REJECTED -> {
                mBinding.mViewPager2.post {
                    mBinding.mViewPager2.currentItem = titleList.indexOfFirst {
                        it == getString(R.string.copy_trading)
                    }
                }
            }

            NoticeConstants.OpenOrder.OPEN_ORDER,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_HISTORY -> {
                mBinding.mViewPager2.post {
                    currentPosition = titleList.indexOfFirst {
                        it == context?.getString(R.string.manual_trading)
                    }
                    if (mBinding.mViewPager2.adapter != null) {
                        mBinding.mViewPager2.setCurrentItem(currentPosition, false)
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}