package cn.com.vau.ui.deal.activity

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.data.depositcoupon.UserAccountData
import cn.com.vau.databinding.ActivityLossOrderBinding
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.ui.deal.adapter.LossOrderRcyAdapter
import cn.com.vau.ui.deal.adapter.StLossOrderRcyAdapter
import cn.com.vau.ui.deal.viewmodel.LossOrderViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.widget.dialog.CenterActionDialog

/**
 * 亏损订单
 */
class LossOrderActivity : BaseMvvmActivity<ActivityLossOrderBinding,LossOrderViewModel>() {

    private var loadNetDialog: CommonProcessDialog? = null
    private var adapter: LossOrderRcyAdapter? = null
    private var stAdapter: StLossOrderRcyAdapter? = null

    private val tradeTypePopup: BottomSelectPopup? by lazy {
        val typeAdapter: SelectAccountAdapter<UserAccountData.Account> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }
        typeAdapter.setNewInstance(mViewModel.accountList)
        typeAdapter.selectTitle = typeAdapter.data.getOrNull(mViewModel.currentSelect)?.getShowItemValue()
        typeAdapter.setOnItemClickListener { _, _, position ->
            if (position == mViewModel.currentSelect) return@setOnItemClickListener
            mViewModel.currentSelect = position
            typeAdapter.selectTitle = typeAdapter.data.getOrNull(position)?.getShowItemValue()
            typeAdapter.notifyDataSetChanged()
            val accountBean = mViewModel.accountList.elementAtOrNull(position) as? UserAccountData.Account
            mBinding.mHeaderBar.setTitleText(accountBean?.name.ifNull())
            accountBean?.let {
                mViewModel.currentAccount = it.accountId ?: ""
                mViewModel.currentAccountServiceId = it.serverId ?: ""
                mViewModel.currentAccountName = it.name ?: ""
                mViewModel.currentCurrency = it.currencyType.ifNull()
                if (accountBean.mtsAccount.isNullOrEmpty()) {
                    showNetDialog()
                    mViewModel.isSt = false
                    mViewModel.onRefreshLossOrders()
                } else {
                    mViewModel.isSt = true
                    mViewModel.getTradeLossOrder(accountBean.mtsAccount.ifNull())
                }
            }
            tradeTypePopup?.dismiss()
        }
        BottomSelectPopup.build(this, getString(R.string.switch_account), typeAdapter)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        if (intent?.extras?.containsKey("currentCoupon") == true)
            mViewModel.couponBean = intent?.extras?.getSerializable("currentCoupon") as DepositCouponDetail
    }

    override fun initView() {
        mBinding.mHeaderBar.setEndIconVisible(false).setEndIconClickListener {
            tradeTypePopup?.show()
        }

        mBinding.mVsNoData.visibility = View.VISIBLE

        mViewModel.socialTradingAccountText = getString(R.string.copy_trading_account)
        mViewModel.initData()

        mBinding.mHeaderBar.setTitleText(mViewModel.currentAccountName)

        mBinding.mSmartRefreshLayout.setNoMoreData(true)
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        adapter = LossOrderRcyAdapter(this, mViewModel.dataList, mViewModel.currentCurrency)
        stAdapter = StLossOrderRcyAdapter(this, mViewModel.tradeDataList)
        mBinding.mRecyclerView.adapter = adapter

        observe()
    }

    override fun initData() {
        super.initData()
        mViewModel.initAccountList()
    }

    private fun initLiveAdapter() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        adapter = LossOrderRcyAdapter(this, mViewModel.dataList, mViewModel.currentCurrency)
        mBinding.mRecyclerView.adapter = adapter
        adapter?.setOnItemClickListener(object : LossOrderRcyAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {

                if (mViewModel.dataList.getOrNull(position)?.checkState == true) return

                val indexOfFirst = mViewModel.dataList.indexOfFirst { it.checkState }
                mViewModel.dataList.getOrNull(indexOfFirst)?.let {
                    it.checkState = false
                }
                mViewModel.dataList.getOrNull(position)?.let {
                    it.checkState = true
                }
                adapter?.notifyItemChanged(indexOfFirst)
                adapter?.notifyItemChanged(position)

            }

        })
    }

    private fun initStAdapter() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        stAdapter = StLossOrderRcyAdapter(this, mViewModel.tradeDataList)
        mBinding.mRecyclerView.adapter = stAdapter

        stAdapter?.setOnItemClickListener(object : StLossOrderRcyAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {

                if (mViewModel.tradeDataList.getOrNull(position)?.checkState == true) return

                val indexOfFirst = mViewModel.tradeDataList.indexOfFirst { it.checkState }
                mViewModel.tradeDataList.getOrNull(indexOfFirst)?.let {
                    it.checkState = false
                }
                mViewModel.tradeDataList.getOrNull(position)?.let {
                    it.checkState = true
                }
                stAdapter?.notifyItemChanged(indexOfFirst)
                stAdapter?.notifyItemChanged(position)

            }

        })
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvSubmit.setOnClickListener(this)
        mBinding.mSmartRefreshLayout.setOnRefreshListener { mViewModel.onRefreshLossOrders() }
    }

    private fun observe() {
        mViewModel.accountsLiveData.observe(this, Observer { result ->
            if (result.isFailure) {
                hideNetDialog()
                return@Observer
            }
            val dataBean = result.getOrNull() ?: return@Observer
            if ("V00000" != dataBean.resultCode) return@Observer
            val currentAccount = UserDataUtil.accountCd()
            val listAccount = dataBean.data?.obj?.listAccount
            mViewModel.accountList.clear()
            listAccount?.forEach {
                if (TextUtils.isEmpty(it.mtsAccount)) {
                    it.name = it.accountId
                } else {
                    it.name = getString(R.string.copy_trading_account)
                }
                mViewModel.accountList.add(it)
            }
            mViewModel.currentSelect = mViewModel.accountList.indexOfFirst {
                it.accountId == currentAccount
            }

            // 模拟
            if (UserDataUtil.isDemoAccount()) {
                if (mViewModel.accountList.size <= 0) {
                    CenterActionDialog.Builder(this)
                        .setContent(getString(R.string.account_information_is_please_again_later))
                        .setSingleButton(true)
                        .setSingleButtonText(getString(R.string.confirm))
                        .setOnSingleButtonListener {
                            finish()
                        }
                        .build()
                        .showDialog()
                    return@Observer
                }
                mViewModel.accountList.getOrNull(0)?.let {
                    mViewModel.currentAccount = it.accountId ?: ""
                    mBinding.mHeaderBar.setTitleText(it.getShowItemValue())
                    mViewModel.currentAccountServiceId = it.serverId ?: ""
                    mViewModel.currentAccountName = it.name ?: ""
                    mViewModel.currentCurrency = it.currencyType.ifNull()
                }
            }
            // 这个接口只适用于非跟单，跟单账户列表数据的获取在 viewModel.initData() 中已经请求了，这里要判断一下
            mViewModel.onRefreshLossOrders()
            // 数据请求成功才显示切换账户按钮
            mBinding.mHeaderBar.setEndIconVisible(true)
        })

        mViewModel.lossOrdersLiveData.observe(this, Observer { result ->
            hideNetDialog()
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            if (result.isFailure) return@Observer
            val dataBean = result.getOrNull() ?: return@Observer
            if ("200" != dataBean.code) return@Observer
            val objDataList = dataBean.obj
            mViewModel.dataList.clear()
            mViewModel.dataList.addAll(objDataList ?: arrayListOf())
            // mBinding.layoutNoData.ctlNoData.visibility = if (objDataList?.isEmpty() == true) View.VISIBLE else View.GONE
            mBinding.mVsNoData.isVisible = objDataList.isNullOrEmpty()
            mBinding.tvSubmit.isVisible = !objDataList.isNullOrEmpty()

            if (mViewModel.currentAccountName != getString(R.string.copy_trading_account)) {
                initLiveAdapter()
            }
        })

        mViewModel.tradeLossOrder.observe(this, Observer {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            mViewModel.tradeDataList.clear()
            mViewModel.tradeDataList.addAll(it ?: arrayListOf())
            mBinding.mVsNoData.isVisible = it.isNullOrEmpty()
            mBinding.tvSubmit.isVisible = !it.isNullOrEmpty()
            if (mViewModel.currentAccountName == getString(R.string.copy_trading_account)) {
                initStAdapter()
            }
        })

        mViewModel.userCouponLiveData.observe(this, Observer { result ->
            hideNetDialog()
            if (result.isFailure) return@Observer
            val dataBean = result.getOrNull() ?: return@Observer
            if ("********" != dataBean.resultCode) {
                ToastUtil.showToast(dataBean.msgInfo)
                return@Observer
            }

            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.rescue_your_losses_successfully))
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.confirm))
                .setOnSingleButtonListener {
                    setResult(5)
                    finish()
                }
                .build()
                .showDialog()
        })

        mViewModel.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showNetDialog()
            } else { //关闭弹窗
                hideNetDialog()
            }
        }

    }

    override fun onClick(v: View?) {
        super.onClick(v)
        when (v?.id) {
            R.id.tvSubmit -> {
                if (mViewModel.isSt) {
                    val dataBean = mViewModel.tradeDataList.firstOrNull {
                        it.checkState
                    }
                    if (dataBean == null) {
                        ToastUtil.showToast(getString(R.string.please_select_the_order))
                        return
                    }
                    showNetDialog()
                    mViewModel.useLossCoupon(dataBean.positionId, dataBean.profit)
                } else {
                    val dataBean = mViewModel.dataList.firstOrNull {
                        it.checkState
                    }
                    if (dataBean == null) {
                        ToastUtil.showToast(getString(R.string.please_select_the_order))
                        return
                    }
                    showNetDialog()
                    mViewModel.useLossCoupon(dataBean.orderNo, dataBean.profit)
                }

            }
        }
    }

    fun showNetDialog() {
        if (loadNetDialog == null) {
            loadNetDialog = CommonProcessDialog(this)
        }
        if (loadNetDialog?.isShowing == true || this.isFinishing || this.isDestroyed) {
            return
        }
        if (loadNetDialog?.isShowing == false) {
            loadNetDialog?.show()
        }
    }

    fun hideNetDialog() {
        if (loadNetDialog != null && loadNetDialog?.isShowing == true){
            loadNetDialog?.dismiss()
        }
    }

}