package cn.com.vau.ui.order.util

import androidx.fragment.app.FragmentActivity
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity

object LoadingHelper {
    fun showLoading(activity: FragmentActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> activity.showLoadDialog()
            is BaseDataBindingActivity<*> -> activity.showLoadDialog()
            is BaseActivity -> activity.showNetDialog()
        }
    }

    fun hideLoading(activity: FragmentActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> activity.hideLoadDialog()
            is BaseDataBindingActivity<*> -> activity.hideLoadDialog()
            is BaseActivity -> activity.hideNetDialog()
        }
    }
}