package cn.com.vau

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.*
import android.text.TextUtils
import android.view.*
import android.widget.Toast
import androidx.activity.*
import androidx.core.os.bundleOf
import androidx.core.text.*
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import cn.com.vau.common.application.*
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.event.*
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.ws.*
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.utils.VAUStartUtil.dispatchOpenAccount
import cn.com.vau.common.utils.VAUStartUtil.openAccountToKyc
import cn.com.vau.common.utils.inApp.*
import cn.com.vau.common.utils.network.NetworkConnectMonitor
import cn.com.vau.common.view.dialog.*
import cn.com.vau.common.view.popup.*
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.common.vm.*
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.enums.*
import cn.com.vau.data.init.*
import cn.com.vau.data.msg.*
import cn.com.vau.databinding.*
import cn.com.vau.home.activity.*
import cn.com.vau.home.model.MainModel
import cn.com.vau.home.presenter.*
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.coupon.CouponManagerActivity
import cn.com.vau.page.html.*
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.security.SecurityActivity
import cn.com.vau.page.setting.activity.*
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.profile.*
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.signals.*
import cn.com.vau.signals.activity.EconomicCalendarActivity
import cn.com.vau.signals.fragment.PromoFragment
import cn.com.vau.signals.stsignal.center.activity.StSignalCenterActivity
import cn.com.vau.trade.HomepageFragment
import cn.com.vau.trade.st.StHomepageFragment
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.ui.common.activity.AccountErrorDialogActivity
import cn.com.vau.ui.order.*
import cn.com.vau.util.*
import cn.com.vau.util.opt.AbUtil
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.opt.PerfTraceUtil.StartTrace.Perf_v6_Start_MainCreate_MainFirst
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.*
import cn.com.vau.util.widget.dialog.*
import cn.com.vau.util.widget.dialog.business.CenterMainEventDialog
import cn.com.vau.util.widget.webview.preload.PreloadWebViewFactory
import com.facebook.FacebookSdk
import com.facebook.applinks.AppLinkData
import com.google.android.gms.common.*
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.ktx.messaging
import com.neovisionaries.ws.client.WebSocketState
import kotlinx.coroutines.*
import org.greenrobot.eventbus.*
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.*

class MainActivity : BaseFrameActivity<MainPresenter, MainModel>(), MainContract.View {

    private val mBinding: ActivityMainBinding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    private val fragments = arrayListOf<Fragment>()
    private val viewModel: MainViewModel by viewModels()
    private val mainAndroidViewModel: MainAndroidViewModel by lazy { MainAndroidViewModel() }
    private var lastPosition: Int = 0
    private var mPosition: Int = 0

    private var currentFragment: Fragment? = null
    private var hideFragment: Fragment? = null

    private var securityTypeView = 0

    private val mainNewComerEventPopupWindow: MainNewComerEventPopupWindow by lazy {
        MainNewComerEventPopupWindow(
            this
        )
    }

    /**
     * 服务器维护
     */
    private var wrFullScreenMaintenanceDialog: WeakReference<FullScreenMaintenanceDialog>? = null

    private val mCenterMainEventDialog by lazy { CenterMainEventDialog.Builder(this).build() }

    private val fullScreenUpdateDialog: FullScreenUpdateDialog by lazy { FullScreenUpdateDialog.Builder(this).build() }

    private var isResume = false
    private var isRequestInAppData = false

    private val mHandler = MyHandler(this)

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    internal class MyHandler(activity: MainActivity) : Handler() {

        private var mActivityReference: WeakReference<MainActivity> = WeakReference(activity)

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                60 -> {
                    // Log.i("ApplicationInit", "handleMessage: 放置后台一分钟, 断开WebSocket")
                    removeCallbacksAndMessages(null)
                    InitHelper.breakInit()
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // activity重建时保存页面的位置
        outState.putInt("last_position", lastPosition)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        lastPosition = savedInstanceState.getInt("last_position")
        setSelectedFragment(lastPosition)
        mBinding.lottieNavView.selectItem(mBinding.lottieNavView.getSelect())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 如果未登录状态，就清除全部用户数据
        // 有一种场景：登录后进入账户列表后杀死app进程，导致再进入app时仍然保存着第一次登录时的信息(userId，loginToken等)，而初始化接口和请求header中都会传递loginToken等信息
        if (!UserDataUtil.isLogin()) {
            UserDataUtil.clearUserData()
            FirebaseManager.userLogout()
        } else {
            FirebaseManager.userLogin()
        }

        EventBus.getDefault().register(this)
        securityTypeView = SpManager.getSecuritySetState(0)
        if (securityTypeView != 0 && !SpManager.getInternationalizationSwitch(false)) {
            SpManager.putAppLockOrder(true)
        }
        if (SpManager.getInternationalizationSwitch(false)) {
            SpManager.putInternationalizationSwitch(false)
        }
        super.onCreate(savedInstanceState)
        // FACEBOOK_APP_ID
        if (PerfTraceUtil.isTraceMain() && !PerfTraceUtil.isStartUpFromFcmOrDeepLink(intent)) {
            PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Start_StartMain_MainCreate, intent)
        }

        PerfTraceUtil.startTrace(Perf_v6_Start_MainCreate_MainFirst)

        setContentView(mBinding.root)

        if (PerfTraceUtil.isTraceMain() && !PerfTraceUtil.isStartUpFromFcmOrDeepLink(intent)) {
            PerfTraceUtil.firstFrameTrace(
                window.decorView,
                PerfTraceUtil.StartTrace.Perf_v6_Start_MainCreate_MainFirst,
                PerfTraceUtil.StartTrace.Perf_v6_Start_MainFirst_TradeFirst
            )
        }

        // 在Activity中处理返回手势
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                exitApp()
            }
        })

        mPresenter.viewModel = viewModel
        FirebaseManager.updateAppInstanceId()
        // 注册网络状态监听器
        NetworkConnectMonitor.register()

        if (!UserDataUtil.isStLogin() || !UserDataUtil.isLogin()) {
            fragments.apply {
                add(HomepageFragment())
                add(OrderThemeFragment())
                add(DiscoverFragment())
                add(PromoFragment())
                add(ProfileFragment())
            }
        } else {
            fragments.apply {
                add(StHomepageFragment())
                add(StOrderThemeFragment())
                add(DiscoverFragment())
                add(PromoFragment())
                add(ProfileFragment())
            }
        }

        handleFcmPushEvent(intent)
        if (savedInstanceState == null && lastPosition == 0) setSelectedFragment(0)
        addPerformance()
    }

    override fun initParam() {
        super.initParam()

        if (securityTypeView == 1 && SpManager.getAppLockOrder(false)) {
            VAUStartUtil.checkLockPass(this)
            return
        }
        // 查询APP是否维护
        mPresenter.checkMaintain(13, true)
        if (intent?.extras?.containsKey("dynamic_links") == true) {
            mPresenter.deepSymbolName = intent.getStringExtra("dynamic_links") ?: ""
            initDynamicLinkStr(mPresenter.deepSymbolName)
        }
        // 切换账户
        if (intent?.extras?.containsKey("is_switch_account") == true) {
            mPresenter.updateAccountLoginTime()
        }
        // 跳转LoginActivity（登出的ReStart方法需要跳）
        if (intent?.extras?.getBoolean("isLaunchLogin", false) == true) {
            val type = intent?.extras?.getString("type", "") ?: ""
            val isContainsKey = intent?.extras?.containsKey("data_token_error") == true
            openActivity(LoginActivity::class.java, bundleOf().apply {
                if (type.isNotEmpty()) {
                    putBoolean(type, true)
                }
                if (isContainsKey) {
                    putSerializable(
                        "data_token_error",
                        intent?.extras?.getSerializable("data_token_error")
                    )
                }
            })
        }

        if (!TextUtils.isEmpty(SpManager.getLivestream(""))) {
            EventBus.getDefault().postSticky(
                StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE)
            )
        }
        if (intent?.extras?.containsKey("app_link_type") == true) {
            mPresenter.appLinkType = intent.getIntExtra("app_link_type", -1)
            if (mPresenter.appLinkType == -1) return
            initAppLink()
        }
        if (intent?.data != null)
            appFlyerLinks(intent?.data)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handleFcmPushEvent(intent)
    }

    private fun handleFcmPushEvent(intent: Intent?) {

        // 获取用户同意
        FacebookSdk.setAutoInitEnabled(true)
        FacebookSdk.fullyInitialize()
        AppLinkData.fetchDeferredAppLinkData(this) { appLinkData ->
            appLinkData?.let {
                val url = it.argumentBundle?.getString("target_url") ?: ""
                if (url.startsWith("vantage://")) {
                    initDynamicLinkStr(url.substring("vantage://".length))
                }
            }
        }

        if (intent?.extras?.containsKey(Constants.FCM_TYPE) == true && intent.extras?.containsKey(Constants.FCM_DATA_P) == true) {
            val viewType = intent.extras?.getString(Constants.FCM_TYPE, "") ?: ""
            val parcelableData = intent.extras?.getParcelable<PushParam>(Constants.FCM_DATA_P)
            when (viewType) {
                /**
                 * 外汇 Forex
                 * 数字货币 Crypto
                 * 股票 Share CFDs
                 * 指数 Indices
                 * 金属 Metals
                 * 大宗商品 Commodities
                 * 债卷 Bond
                 * 如果跳转的产品类型不存在则跳转Watchlist
                 */
                "118" -> {
                    setSelectedFragment(0)
                    EventBus.getDefault().post(NoticeConstants.FIREBASE_JUMP_TREAD_GROUP_NAME + parcelableData?.product.ifNull())
                }

                else -> {
                    VAUStartUtil.redirect(this, viewType, parcelableData)
                }
            }
            mPresenter.notificationMarketingClickCntUpdate(parcelableData)
        } else if (intent?.extras?.containsKey(Constants.FCM_TYPE) == false && intent.extras?.containsKey(Constants.FCM_DATA_P) == true) {
            val parcelableData = intent.extras?.getParcelable<PushParam>(Constants.FCM_DATA_P)
            mPresenter.notificationMarketingClickCntUpdate(parcelableData)
        } else if (intent?.data?.toString()?.startsWith("vantage://") == true) {
            intent.data?.let { uri ->
                if (uri.toString().startsWith("vantage://")) {
                    initDynamicLinkStr(uri.toString().substring("vantage://".length))
                }
            }
        } else if (intent?.data != null) {
            // 获取deeplink跳转过来的数据
            appFlyerLinks(intent.data)
        }
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
        tradePermissionPerformance.requestTradePermission()
    }

    override fun initView() {
        super.initView()
        if (SpManager.getPointRemindPromoShow(false)) {
            mBinding.lottieNavView.setRedDotState(3, true)
        }

        if (SpManager.getInstallApkFirst(true)) {
            mBinding.lottieNavView.setRedDotState(3, true)
        }
        initWebView()
    }

    private fun initWebView() {
        PreloadWebViewFactory.getInstance().preload()
    }

    override fun initData() {
        super.initData()
        if (!VauApplication.abOptNetParallel) {
            //以前时因为在Splash中也做了start()，在Application中没有调用这个方法。所以这里之前是有必要在这里start，因为像推送的场景可能不会进入Splash，而是直接跳转到MainActivity的。
            //但是如果开关开启了（现在的逻辑），那么就不需要再调用start()了。因为在Application中就start()
            //因此开关开启时，下掉这段代码
            if (InitHelper.stepNum() == -1) InitHelper.start()
        }
        mPresenter.tradeSeason()
        if (!UserDataUtil.isStLogin()) mPresenter.isShowSt()
        mPresenter.promoTab()
        mPresenter.userActionHasChanges()
        mPresenter.getServerBaseUrl()
        // TODO
        mPresenter.imgAdvertInfoApi()
        mHandler.removeCallbacksAndMessages(null)

        lifecycleScope.launch(Dispatchers.Main) {
            delay(1000)
            subscribeTopic()
        }
        if (UserDataUtil.isLogin()) {
            // TODO
            mPresenter.requestInAppInfo()
            mPresenter.fundCheckDepositStatusApi()     // 用户是否入过金
        }
        // 风控，进入主页面和退出登录都需要调用一次获取sessionID方法
        TMXHelper.generateSessionID("MainActivity")
        addIdleHandlerFlow()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        super.initListener()

        mBinding.ivNewcomerEvent.setOnClickListener {
            val newerData = mPresenter.popWindowBean?.newerGiftActivityV2
            if ("1" == newerData?.isDirectJump) {
                // jumpType=1（内部链接）根据tcLink跳转tc页面  jumpType=2（外部链接）jumpUrl跳转对应的外部链接地址
                val targetUrl = if ("1" == newerData.jumpType) newerData.tcLink ?: ""
                else newerData.jumpUrl ?: ""
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString("url", targetUrl)
                    putString("title", "")
                    putInt("tradeType", 3)
                })
            } else {
                openActivity(MainNewComerEventActivity::class.java)
            }
        }

        mBinding.lottieNavView.setTabClickListener { position ->
            when (position) {
                0 -> {
                    sensorsTrack("Trades") //神策埋点，tab点击事件
                    setSelectedFragment(0)
                    return@setTabClickListener true
                }

                1 -> {
                    sensorsTrack("Orders") //神策埋点，tab点击事件
                    if (!UserDataUtil.isLogin()) { // 未登录
                        openActivity(LoginActivity::class.java)
                        return@setTabClickListener false
                    }

                    if (securityTypeView == 2 && (SpManager.getAppLockOrder(false))
                        && !VAUStartUtil.checkLockPass(
                            this,
                            NoticeConstants.Unlock.UNLOCK_TO_ORDER_LIST
                        )
                    ) {
                        return@setTabClickListener false
                    }
                    if (SpManager.getSecuritySetState() != 2)
                        SpManager.putInternationalizationSwitch(false)

                    tradePermissionPerformance.run {
                        if (handleTradeBlockType { setSelectedFragment(1) }) return@setTabClickListener false
                    }

                    setSelectedFragment(1)
                    return@setTabClickListener true
                }

                2 -> {
                    sensorsTrack("Discover") //神策埋点，tab点击事件
                    setSelectedFragment(2)
                    return@setTabClickListener true
                }

                3 -> {
                    sensorsTrack("Promo") //神策埋点，tab点击事件
                    setSelectedFragment(3)
                    return@setTabClickListener true
                }

                4 -> {
                    sensorsTrack("Profile") //神策埋点，tab点击事件
                    setSelectedFragment(4)
                    return@setTabClickListener true
                }

                else -> {
                    return@setTabClickListener false
                }
            }

        }
    }

    /**
     * 添加需要在IdleHandler里执行的操作
     */
    private fun addIdleHandlerFlow() {
        Looper.myQueue().addIdleHandler {
            try {
                // 需要登录才需要请求分享数据的接口
                if (UserDataUtil.isLogin())
                    ShareHelper.prepareData(this)
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            false
        }
    }

    private fun setSelectedFragment(position: Int): Fragment? {
        viewModel.showFragmentIndex = position
        mBinding.ivNewcomerEvent.isVisible = position == 0 && "1" == mPresenter.popWindowBean?.newerGiftActivityV2?.showSide && (mPresenter.isEEACountry.not() || mPresenter.firebaseDataDisplay.not())

        if (position != 0 && mainNewComerEventPopupWindow.isShowing) {
            mainNewComerEventPopupWindow.dismiss()
        }

        if (position == 3) {
            SpManager.putPointRemindPromoShow(false)
            SpManager.putInstallApkFirst(false)
            mBinding.lottieNavView.setRedDotState(3, false)
            if (UserDataUtil.isLogin()) mPresenter.updateLastActionTime()
        }

        val fm = supportFragmentManager
        val fb = fm.beginTransaction()
        // 要显示的fragment ( 解决 activity 重建时新建实例的问题 )
        currentFragment = fm.findFragmentByTag("fragment$position")
        // 要隐藏的fragment ( 解决 activity 重建时新建实例的问题 )
        hideFragment = fm.findFragmentByTag("fragment$lastPosition")
        // 位置不同
        if (position != lastPosition) {
            hideFragment?.let { fb.hide(it) }
            if (currentFragment == null) {
                currentFragment = fragments.elementAtOrNull(position)
                currentFragment?.let {
                    if (!it.isAdded) {
                        fb.remove(it)
                        fb.add(R.id.containerFrameLayout, it, "fragment$position")
                    }
                }
            } else {
                currentFragment?.let {
                    if (it.isAdded) fb.show(it)
                }
            }
        }

        // 位置相同
        if (position == lastPosition) {
            // 第一次启动应用不存在
            if (currentFragment == null) {
                currentFragment = fragments.elementAtOrNull(position)
                currentFragment?.let {
                    if (!it.isAdded) {
                        fb.remove(it)
                        fb.add(R.id.containerFrameLayout, it, "fragment$position")
                    }
                }
            }
            // 如果位置相同，且fragment存在，则不作任何操作
        }

        // 提交事务
        fb.commitAllowingStateLoss()
        // 更新要隐藏的fragment的位置
        lastPosition = position
        mPosition = position

        // TODO
        showInApp()
        mBinding.lottieNavView.selectItem(position)

        return currentFragment
    }

    // 1 版本更新
    override fun showUpdateDialog() {
        // 不需要更新
        if (mPresenter.updateFlag == -1 || mPresenter.updateFlag == 2) {
            showAccountOverdue()
            return
        }
        if (fullScreenUpdateDialog.isShowDialog()) return

        fullScreenUpdateDialog.setInfo(
            mPresenter?.updateVersionName, mPresenter?.updateContent, mPresenter.updateFlag
        )
        fullScreenUpdateDialog.setConfirmListener {
            if (mPresenter.popWindowBean?.isID == "1") {
                jumpWeb()
            } else {
                updateApk()
            }
        }
        fullScreenUpdateDialog.setCancelListener {
            // 抹亏优惠弹出
            showAccountOverdue()
        }
        fullScreenUpdateDialog.show()
    }

    // 2 归档/demo过期 模拟账号过期 || 真实账户归档
    override fun showAccountOverdue() {

        // 没有登陆没有异常
        if (!UserDataUtil.isLogin()) {
            showNewComerGifDialog()
            return
        }

        // Live归档
        if (true == mPresenter.popWindowBean?.realAccount) {
            openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
                putInt("type_account_error", 2)
            })
            return
        }

        if ("********" == mPresenter.popWindowBean?.demoAccount?.demoCode) {
            openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
                putInt("type_account_error", 1)
            })
            return
        }

        // EEA国家判断 影响Firebase数据收集
        if (mPresenter.isEEACountry) {
            if (mPresenter.firebaseDataDisplay) {
                updateFirebaseStatus(false)
                CenterActionWithIconDialog.Builder(this)
                    // 重要的提醒
                    .setTitle(getString(R.string.important_notice))
                    // 通过选择“接受 Cookie”，您允许我们的应用程序使用 Cookie 来增强您的体验、我们的服务和其他营销目的。 如果您不同意，请选择“管理 cookie”来更改您的设置。
                    .setContent(getString(R.string.by_selecting_accept_your_our_if_yoursetting))
                    // 接受 Cookie
                    .setSingleButtonText(getString(R.string.accept_cookies))
                    .setOnSingleButtonListener {
                        updateFirebaseStatus(true)
                        mPresenter.userCollectDataSwitch()
                        // 与Kaidi确认 此接口后可以无新手礼包活动
                    }
                    .setSingleButton(true)
                    .setLinkText(getString(R.string.manage_cookies))
                    .setLinkListener {
                        if (!UserDataUtil.isLogin()) {
                            openActivity(LoginActivity::class.java)
                            return@setLinkListener
                        }
                        openActivity(
                            SecurityActivity::class.java,
                            bundleOf(Constants.IS_FROM to "main")
                        )
                        // 与Kaidi确认 此跳转后可以无新手礼包活动
                    }
                    .build()
                    .showDialog()

            } else {
                updateFirebaseStatus(mPresenter.currentSwitch)
                showNewComerGifDialog()
            }
        } else {
            updateFirebaseStatus(true)
            showNewComerGifDialog()
        }
    }

    private fun updateFirebaseStatus(status: Boolean) {
        // 操作Firebase API
        FirebaseManager.allowGoogleAnalytics(status)
    }

    // 3 新手礼包弹窗浮窗
    private fun showNewComerGifDialog() {

        val newerData = mPresenter.popWindowBean?.newerGiftActivityV2

        ImageLoaderUtil.loadImage(context, newerData?.sidePicUrl ?: "", mBinding.ivNewcomerEvent)

        if ("1" == newerData?.showBottom) {
            mainNewComerEventPopupWindow.setData(newerData, context)
            mainNewComerEventPopupWindow.showAsDropDown(
                mBinding.popView, 0, -(80.dp2px()), Gravity.TOP
            )
            mainNewComerEventPopupWindow.setOnPopClickListener(object :
                MainNewComerEventPopupWindow.OnPopClickListener {
                override fun onItemClick() {

                    // 直接跳转 & 内部跳转 & 活动跳转地址不为空
                    if ("1" == newerData.isDirectJump) {
                        // jumpType=1（内部链接）根据tcLink跳转tc页面  jumpType=2（外部链接）jumpUrl跳转对应的外部链接地址
                        val targetUrl = if ("1" == newerData.jumpType)
                            newerData.tcLink ?: ""
                        else
                            newerData.jumpUrl ?: ""

                        openActivity(HtmlActivity::class.java, Bundle().apply {
                            putString("url", targetUrl)
                            putString("title", "")
                            putInt("tradeType", 3)
                        })
                    } else {
                        openActivity(MainNewComerEventActivity::class.java)
                    }
                }
            })
        } else {
            if ("1" == newerData?.showSide && lastPosition == 0) {
                mBinding.ivNewcomerEvent.visibility = View.VISIBLE
                initNewUserDialog()
            } else {
                mBinding.ivNewcomerEvent.visibility = View.GONE
                showEventDialog()
            }
        }

        mainNewComerEventPopupWindow.setOnDismissListener {
            lifecycleScope.launch {
                delay(100)
                mBinding.ivNewcomerEvent.isVisible = ("1" == newerData?.showSide && mPosition == 0)
                initNewUserDialog()
            }
        }

    }

    // 4 活动Dialog
    override fun showEventDialog() {

        if (TextUtils.isEmpty(mPresenter.eventImgUrl)) {
            initNewUserDialog()
            return
        }

        mCenterMainEventDialog
            .setImageUrl(mPresenter.eventImgUrl)
            .setOnImageListener {
                VAUStartUtil.openActivity(this, mPresenter.appEventPushBean)
                initNewUserDialog()

                mPresenter.eventsAddClicksCount(
                    mPresenter.popWindowBean?.appEvent?.eventId.ifNull()
                )
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V330.PROMO_TRAFFIC_BUTTON_CLICK, hashMapOf(
                        "Promoted_Page_Name" to mPresenter.popWindowBean?.appEvent?.eventId.ifNull(),
                        "Position" to "Pop-up"
                    )
                )
                // 弹窗点击事件
                mPresenter.sensorsTrackWhenPopupClick(mPresenter.popWindowBean?.appEvent, "redirect")
            }.setOnCloseListener {
                initNewUserDialog()
                // 弹窗点击事件
                mPresenter.sensorsTrackWhenPopupClick(mPresenter.popWindowBean?.appEvent, "close")
            }.showDialog()
        // 弹窗曝光事件
        mPresenter.sensorsTrackWhenPopupShow(mPresenter.popWindowBean?.appEvent)
    }

    /**
     * 是否正在展示kyc的弹窗
     */
    private var isShowKycDialog = false

    /**
     * 新用户引导弹窗，仅展示一次，本地存储
     */
    private fun initNewUserDialog() {
        if (!isShowKycDialog && UserDataUtil.isLogin()
            && !SpManager.getNewUserDialog(false)
            && !mPresenter.bottomDialogBean?.imgList.isNullOrEmpty()
        ) {
            BottomNewVersionGuideDialog.Builder(this)
                .setDataList(mPresenter.bottomDialogBean?.imgList)
                .setCallback {
                    LogEventUtil.setLogEvent(BuryPointConstant.V3474.GENERAL_USER_GUIDE_EXPLORE_NOW_BUTTON_CLICK)
                }
                .setOnDismissListener {
                    SpManager.putNewUserDialog(true)
                }
                .build()
                .showDialog()
            isShowKycDialog = false
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            exitApp()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private var isAcceptAccountErrorMsg = false

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {

            NoticeConstants.MAIN_SHOW_POSITION_FIRST -> {
                setSelectedFragment(0)
                EventBus.getDefault().removeStickyEvent(event)
            }
            // 显示我的
            NoticeConstants.MAIN_SHOW_POSITION_LAST -> {
                setSelectedFragment(4)
                EventBus.getDefault().removeStickyEvent(event)
            }
            // 订单
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_HISTORY,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_FREE,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_OPEN,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_HISTORY,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_PENDING_REVIEW,
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_REJECTED -> {
                setSelectedFragment(1)
            }
            // 显示有料
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_ANALYSES,
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY,
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_CALENDAR,
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE,
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COPY_TRADE,
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM -> {
                setSelectedFragment(2)
            }

            NoticeConstants.CHECK_APP_VERSION -> {
                mPresenter.checkAppVersion(false)
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.VIEWTYPE_30_TO_MAIN_GET_ACCOUNT_STATUS -> {
                if (SpManager.isV1V2()) {
                    openAccountToKyc(this)
                } else {
                    mPresenter?.queryMT4AccountState(30)
                }
                EventBus.getDefault().removeStickyEvent(event)
            }

            //下单
            NoticeConstants.OpenOrder.OPEN_ORDER -> {
                setSelectedFragment(1)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // app进入前台
            NoticeConstants.APP_ON_RESUME -> {
                // Log.i("ApplicationInit", "APP_ON_RESUME: app进入前台")
                if (UserDataUtil.isStLogin()) {
                    if (StWsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                        EventBus.getDefault().post(NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M)
                    }
                } else {
                    if (WsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                        EventBus.getDefault().post(NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M)
                        // app 进入前台是否校验系统维护（退后台一分钟主动断开ws后再次进入前台需要校验，ws没有通知补发）
                        mPresenter?.checkMaintain(13)
                    }
                }

                val localLockTime = SpManager.getSecurityCodeTime(-1L)
                securityTypeView = SpManager.getSecuritySetState(0)
                if (securityTypeView != 0 && localLockTime != -1L) {
                    if ((System.currentTimeMillis() - localLockTime) / 1000 >= SpManager.getUnlockTime(10) * 60
                    ) {
                        SpManager.putAppLockOrder(true)
                    }
                }
                if ((securityTypeView == 2 && SpManager.getAppLockOrder(false) && lastPosition == 1) || (securityTypeView == 1 && SpManager.getAppLockOrder(false))) {
                    VAUStartUtil.checkLockPass(this)
                }
                mHandler.removeCallbacksAndMessages(null)
                getDeepLink()
                // 本地日志
                if (UserDataUtil.isLogin()) {
                    DbManager.getInstance().saveDealLog(DealLogInfo().apply {
                        timeStamp = System.currentTimeMillis().toString()
                        date = PickerDateUtil.currentTimeMillisToString("dd/MM/yyyy HH:mm:ss")
                        val timeZone = AppUtil.getTimeZoneRawOffsetToHour()
                        log =
                            "model:${AppUtil.getSystemModel()}  os:${AppUtil.getSystemVersion()}  versions:${
                                AppUtil.getVersionName()
                            }  time zone:${if (timeZone > 0) "+" else ""}$timeZone"
                        tel = UserDataUtil.userTel()
                    })
                }
            }
            // app进入后台
            NoticeConstants.APP_ON_PAUSE -> {
                // Log.i("ApplicationInit", "APP_ON_PAUSE: app进入后台")
                mHandler.sendEmptyMessageDelayed(60, 60 * 1000L)
                SpManager.putSecurityCodeTime(-1L)
                // 倒计时十分钟恢复解锁
                if (securityTypeView != 0) {
                    SpManager.putSecurityCodeTime(System.currentTimeMillis())
                }
            }
            // 网络可用监听
            NoticeConstants.NETWORK_AVAILABLE -> {
                // 优先检测强更 -> 其次检测主页维护 -> 其次检测行情维护
                mPresenter.checkAppVersion(true)
            }
            // 退出登陆
            NoticeConstants.LOGOUT_ACCOUNT -> {
                loginOut()
            }
            //删除账户/解绑手机号后需要跳转到登录页面
            NoticeConstants.UNBIND_ACCOUNT -> {
                if (!UserDataUtil.isLogin()) return
                val toastContent = getString(R.string.your_account_has_please_using_it)
                val bundle = Bundle()
                bundle.putString("toast_content", toastContent)
                openActivity(LoginActivity::class.java, bundle)
                loginOut()
            }
            // 切换账号 (断开WebSocket,获取产品列表...)  并切换到首页
            NoticeConstants.SWITCH_ACCOUNT -> {
                if (!UserDataUtil.isStLogin()) mPresenter.isShowSt()
                // 双域名配置方案应用
                HttpUrl.applyBaseUrl()
                // 重新获取AppsFlyer埋点必传参数
                AppsFlyerBuryPoint.requestRequireParam()
                SpManager.putInstallApkFirst(false)
                setSelectedFragment(0)
                subscribeTopic()
                // 后端记录登陆时间
                mPresenter.updateAccountLoginTime()
                // 切换账号时清除上个账号的产品数据
                VAUSdkUtil.shareGoodList().clear()
                VAUSdkUtil.symbolList().clear()
                if (AbUtil.isClearOrderList()) {
                    VAUSdkUtil.shareOrderList().clear()
                }
                if (AbUtil.isClearAccount()) {
                    // 清除账户资产等数据
                    VAUSdkUtil.stShareStrategyList().clear()
                    VAUSdkUtil.shareAccountBean().clear()
                    VAUSdkUtil.stShareAccountBean().clear()
                }
                // 初始化数据
                InitHelper.start()
                mBinding.ivNewcomerEvent.visibility = View.GONE
                dismissMainPopWindow()
                mPresenter.promoTab()
                mPresenter.checkMaintain(13, true)       //是否需要展示上线维护页面
                mPresenter.userActionHasChanges()
                mPresenter.requestInAppInfo()
                mPresenter.fundCheckDepositStatusApi()     // 用户是否入过金
                mPresenter.imgAdvertInfoApi()      // 行情首页广告位接口
                // 请求是否有未读消息
                checkNoticeCount()
                SpManager.switchAccountClear()
                //更新神策动态属性
                SensorsDataUtil.updateDynamicProperties(true)
                tradePermissionPerformance.requestTradePermission()
            }
            // 用户变组
            NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP -> InitHelper.start()
            // 刷新持仓
            NoticeConstants.WS.CHANGE_OF_OPEN_ORDER -> {
                InitHelper.initialize(EnumInitStep.ORDER)
            }
            // 出入金变化通知 接口切入不对，应该刷账户信息，后续修改
            NoticeConstants.WS.CHANGE_OF_FUNDS -> {
                InitHelper.initialize(EnumInitStep.ORDER)
            }
            // 跟随策略列表包括所有跟随持仓订单有变化
            NoticeConstants.STStrategy.CHANGE_OF_ST_COPY_TRADING_ORDERS -> {
                InitHelper.initialize(EnumInitStep.SIGNAL_SOURCE)
            }

            NoticeConstants.MAIN_SHOW_POSITION_FIRST -> {
                setSelectedFragment(0)
            }

            NoticeConstants.MAIN_SHOW_PAGE_EVENT -> {
                setSelectedFragment(3)
            }
            // 显示我的
            NoticeConstants.MAIN_SHOW_POSITION_LAST -> {
                setSelectedFragment(4)
            }
            // 产品获取成功
            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                if (securityTypeView == 1 && SpManager.getAppLockOrder(false)) return
                if (!TextUtils.isEmpty(mPresenter.deepSymbolName)) {
                    startDeepSymbolName(mPresenter.deepSymbolName)
                }
            }

            // 语言切换
            NoticeConstants.CHANGE_OF_LANGUAGE -> {
                //更新神策动态属性
                SensorsDataUtil.updateDynamicProperties(true)
                SpManager.putInternationalizationSwitch(true)
                WsManager.getInstance().breakSocket()
                InitHelper.start()
                finish()
            }

            // 初始化结束
            NoticeConstants.Init.APPLICATION_END -> {
                // FCM首页行情Tab跳转
                val fm = supportFragmentManager
                val firstFragment = fm.findFragmentByTag("fragment0")
                if (!UserDataUtil.isStLogin() || !UserDataUtil.isLogin()) {  // 非跟单
                    if (firstFragment != null) {
                        (firstFragment as? HomepageFragment)?.fcmPushTradeToTab()
                    }
                } else {
                    if (firstFragment != null) {
                        (firstFragment as? StHomepageFragment)?.fcmPushTradeToTab()
                    }
                }
            }

            // 账户归档弹窗
            NoticeConstants.Init.ACCOUNT_ERROR_OVERDUE -> {
                if (isAcceptAccountErrorMsg) return
                isAcceptAccountErrorMsg = true
                openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
                    putInt("type_account_error", 2)
                })
            }

            NoticeConstants.ACCOUNT_ERROR_STATE_INIT -> {
                isAcceptAccountErrorMsg = false
            }
            // 禁用
            NoticeConstants.Init.ACCOUNT_ERROR_FORBIDDEN -> {
                if (isAcceptAccountErrorMsg) return
                isAcceptAccountErrorMsg = true
                openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
                    putInt("type_account_error", 3)
                })
            }
            // 过期
            NoticeConstants.Init.ACCOUNT_ERROR_DEMO_EXPIRES -> {
                if (isAcceptAccountErrorMsg) return
                isAcceptAccountErrorMsg = true
                openActivity(AccountErrorDialogActivity::class.java, Bundle().apply {
                    putInt("type_account_error", 1)
                })
            }
            // 用户进入注册登陆页面，清理本地未登录导航
            NoticeConstants.LOGOUT_GUIDE_CLEAR -> {
                SpManager.putLogoutGuideDay(Calendar.getInstance().get(Calendar.DAY_OF_MONTH))
                SpManager.putLogoutGuideDay(-1)
            }
            // 高频 IP
            NoticeConstants.Init.LOGIN_ERROR_HIGH_FREQUENCY_IP -> {
                loginOut(NoticeConstants.Init.LOGIN_ERROR_HIGH_FREQUENCY_IP)
            }
            // 刷新fcm的状态
            NoticeConstants.SUBSCRIBE_TOPIC -> {
                FirebaseManager.bindFcmToken()
                subscribeTopic()
            }
            // 公开交易设置成功
            NoticeConstants.PROVIDER_TO_PUBLIC_TRADE_SUCCESS -> {
                // 跳转至信号源中心
                StSignalCenterActivity.open(this, isPublicTrading = true)
            }

            NoticeConstants.JS.INTERFACE_50 -> {
                if (SpManager.isV1V2()) {
                    openAccountToKyc(this)
                } else {
                    mPresenter.queryMT4AccountState(EnumLinkSkipState.GOLDEN)
                }
            }

            NoticeConstants.JS.OPEN_ACCOUNT_GUIDE -> {
                if (SpManager.isV1V2()) {
                    openAccountToKyc(this)
                } else {
                    mPresenter.queryMT4AccountState(EnumLinkSkipState.DEFAULT)
                }
            }

            NoticeConstants.JS.OPEN_ACCOUNT_OPEN_APP -> {
                skipDepositOrOpenAccount(EnumLinkSkipState.OPEN_APP)
            }

            NoticeConstants.WS.POINT_REMIND_PROMO_SHOW -> {
                SpManager.putPointRemindPromoShow(true)
                mBinding.lottieNavView.setRedDotState(3, true)
            }

            NoticeConstants.VIEWTYPE_30_TO_MAIN_GET_ACCOUNT_STATUS -> {
                if (SpManager.isV1V2()) {
                    openAccountToKyc(this)
                } else {
                    mPresenter?.queryMT4AccountState(30)
                }
            }

            NoticeConstants.CHECK_APP_VERSION -> {
                mPresenter.checkAppVersion(false)
            }

            NoticeConstants.REFRESH_EVENT_IMAGE -> {
                mPresenter.imgAdvertInfoApi()      // 行情首页广告位接口
            }

            NoticeConstants.Unlock.UNLOCK_TO_ORDER_LIST -> {
                SpManager.putAppLockOrder(false)
                setSelectedFragment(1)
            }

            NoticeConstants.Unlock.UNLOCK_SUCCESS -> {
                SpManager.putAppLockOrder(false)
                initParam()
            }

            NoticeConstants.WS.SOCKET_MAINTENANCE_SYSTEM -> mPresenter.checkMaintain(13)
            // 接收到了kyc提升的通知，需要刷新一下接口,并保存数据
            NoticeConstants.WS.SOCKET_KYC_LEVEL_CHANGED -> {
                tradePermissionPerformance.requestTradePermission()
                mPresenter.userQueryUserLevel()
            }

            /**
             * 黄金开户 注册成功
             */
            NoticeConstants.GOLD_REGISTER_SUCCESS -> {
                BottomVerifyDialog.Builder(this)
                    .setIcon(R.raw.lottie_dialog_ok)
                    .setTitle(getString(R.string.welcome_to_vantage))
                    .setContent(getString(R.string.complete_your_account_trading_journey))
                    .setButtonStr(getString(R.string.verify_now))
                    .setLinkStr(buildSpannedString { underline { append(getString(R.string.explore_the_app)) } })
                    .setCustomView(LayoutKycWelcomeBinding.inflate(layoutInflater).root)
                    .setOnCreateListener {
                        // 这里设置true ，让 kyc验证弹窗弹出时， 不弹出新版本引导弹窗 ， 点击下面的maybe later 按钮时， 弹出新版本引导弹窗
                        isShowKycDialog = true
                    }
                    .setButtonClick {
                        KycVerifyHelper.showKycDialog(this, mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC))
                    }
                    .setLinkClick {
                        isShowKycDialog = false
                        initNewUserDialog()
                    }
                    .build()
                    .showDialog()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            // token 异常
            NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN -> {
                // 关闭页面
                ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)

                if (event.data is TokenErrorData?) {
                    val data: TokenErrorData? = event.data

                    loginOut("", data)
                    viewModel.loginTokenExpire()
                } else {
                    loginOut("")
                    viewModel.loginTokenExpire()
                }

            }
            // ws给推送开仓 挂单的通知 将数据推送到main activity 进行弹窗
            NoticeConstants.WS.INAPP_CHANGE_OF_ORDER -> {
                if (event.data !is SocketRecord) return
                val title = when (event.data.type) {
                    1, 6001 -> getString(R.string.order_opened)
                    2, 6007 -> getString(R.string.pending_order_placed)
                    else -> ""
                }
                val content = getString(
                    R.string.x_x_lots_x_at_x,
                    getString(
                        when (event.data.cmd.toIntCatching()) {
                            0 -> R.string.buy
                            1 -> R.string.sell
                            2 -> R.string.buy_limit
                            3 -> R.string.sell_limit
                            4 -> R.string.buy_stop
                            5 -> R.string.sell_stop
                            6 -> R.string.buy_stop_limit
                            else -> R.string.sell_stop_limit
                        }
                    ), event.data.volume, event.data.symbol, event.data.price
                )
                TopInAppPopupUtils.showTopPopupWindow(this, title = title, content = content, icon = AttrResourceUtil.getDrawable(this, R.attr.imgCircleRight), duration = 3000)
            }
            // KYC双端数据同步
            NoticeConstants.WS.SOCKET_KYC_DATA_SYNCHRONIZATION -> {
                if (event.data is KycSyncData) {
                    // 业务场景更新手机号和邮箱是分开的，所以只会有一种进行更新
                    val data: KycSyncData = event.data
                    if (data.email?.isNotEmpty() == true) {
                        // 有邮箱变更
                        UserDataUtil.setEmail(data.email)
                    } else if (data.phoneCountryCode?.isNotEmpty() == true && data.phone?.isNotEmpty() == true && data.phoneCode?.isNotEmpty() == true) {
                        // 有手机号变更
                        UserDataUtil.setUserTel(data.phone)
                        UserDataUtil.setAreaCode(data.phoneCode)
                        UserDataUtil.setCountryCode(data.phoneCountryCode)
                    }
                }
            }
        }
    }

    /**
     * 检测用户未读消息数量
     */
    private fun checkNoticeCount() {
        Looper.myQueue().addIdleHandler {
            // 登录以后 再进行接口请求
            if (UserDataUtil.isLogin()) {
                mainAndroidViewModel.msgInAppType()
            }
            false
        }
    }

    override fun showUpdateView(bean: AppVersionObj?) {
        if (0 == bean?.id) {    // 当前版本不是最新版本
            if (bean.dlPath?.isNotEmpty() == true) {    // 这里对dlPath 是之前的判断
                fullScreenUpdateDialog.setInfo(
                    bean.versionName.ifNull(), bean.introduction.ifNull(), bean.forceFlag.ifNull()
                )
                mBinding.root.post {
                    fullScreenUpdateDialog.show()
                    fullScreenUpdateDialog.setConfirmListener {
                        updateApk()
                    }
                }
            }
        }
    }

    override fun forceUpdate(data: AppVersionObj?) {
        if (fullScreenUpdateDialog.isShowDialog()) return
        fullScreenUpdateDialog.setInfo(
            data?.versionName.ifNull(), data?.introduction.ifNull(), 1
        )
        mBinding.root.post {
            fullScreenUpdateDialog.show()
            fullScreenUpdateDialog.setConfirmListener {
                updateApk()
            }
        }
    }

    override fun showInApp() {
        isRequestInAppData = true
        whenResumeShowInApp()
    }

    private fun whenResumeShowInApp() {
        if (UserDataUtil.isLogin() && isRequestInAppData && isResume) {
            InAppViewUtil.show(this, mBinding.vsInApp, mPosition) { eventId ->
                eventId?.let {
                    mPresenter.eventsAddClicksCount(it)
                    mPresenter.inAppDismissReport(it)
                }
            }
        }
    }

    override fun handlePopEventAccount(viewType: Int, objData: MT4AccountTypeObj?) {
        val objStatus = objData?.applyTpe
        when (viewType) {
            //完全開戶流程
            30 -> {
                //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑
                if ("1" == objData?.regulator) {
                    if (objStatus == 0 || objStatus == 2 || objStatus == 4 || objStatus == 5 || objStatus == 7) {
                        val intent = Intent(context, AccountManagerActivity::class.java)
                        context.startActivity(intent)
                        return
                    }
                }

                //开户跳转公共方法
                VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            }

        }
    }

    private fun dismissMainPopWindow() {
        mainNewComerEventPopupWindow.dismiss()
        mCenterMainEventDialog.dismiss()
    }

    // 退出登陆
    private fun loginOut(type: String? = "", data: Any? = null) {
        // Fcm解绑推送设备号
        FirebaseManager.unbindFcmToken()
        // 清除订单信息
        VAUSdkUtil.shareOrderList().clear()
        // fcm绑定主题
        subscribeTopic()
        SpManager.putRedPointState(false)
        //清除缓存信息
        InAppDataUtil.clearData()
        // 登出Zendesk
        ZendeskUtil.logout()
        // 隐藏小红点
        EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_HIDE)

        SpManager.putSearchHistoryKey("")
        VAUSdkUtil.collectSymbolList.clear()

        StWsManager.getInstance().breakSocket()
        WsManager.getInstance().breakSocket()
        if (UserDataUtil.isStLogin()) {
            //從跟單帳號登出 需做db清空 & 重啟app
            UserDataUtil.clearUserData()
            FirebaseManager.userLogout()
            SpManager.logoutClear()
            VAUSdkUtil.shareOrderList().clear()
            VAUSdkUtil.stShareStrategyList().clear()
            reset()
            EventBus.getDefault().post(NoticeConstants.AFTER_LOGOUT_RESET)
            reStartApp(type, data)
        } else {
            UserDataUtil.clearUserData()
            FirebaseManager.userLogout()
            reset()
            SpManager.logoutClear()
            EventBus.getDefault().post(NoticeConstants.AFTER_LOGOUT_RESET)
            clearTopToMain()

            val bundle = Bundle()
            bundle.putString(type, "")
            if (data is TokenErrorData) {
                bundle.putSerializable("data_token_error", data)
            }
            openActivity(LoginActivity::class.java, bundle)
        }
    }

    fun reset() {
        if (!UserDataUtil.isStLogin()) mPresenter.isShowSt()
        //退出登录，清空ib账户下保存的上次选中账户字段
        SpManager.putInvitationLastSelectAccount("")
        //清除 创建策略的草稿
        SpManager.clearStrategyListDraftKey()
        // 双域名配置方案应用
        HttpUrl.applyBaseUrl()
        setSelectedFragment(0)
        WsManager.getInstance().breakSocket()
        // 初始化
        InitHelper.start()
        ConfigAndUnlockActivity.clearLockData()
        mPresenter.promoTab()
        mBinding.ivNewcomerEvent.visibility = View.GONE
        mPresenter.checkMaintain(13, true)
        // 查询是否有新活动 显示红点提醒
        mPresenter.userActionHasChanges()
        InAppDataUtil.clearData()
        mPresenter.imgAdvertInfoApi()      // 行情首页广告位接口
        AppsFlyerBuryPoint.requestRequireParam()
        // 退出神策登录
        SensorsDataUtil.logout()
        // 风控，进入主页面和退出登录都需要调用一次获取sessionID方法
        TMXHelper.generateSessionID("logout")
    }

    // 修改跳转双页面的方法 由 startActivities(intentArray) 改为 startActivity(intent) 先启动MainActivity，再由MainActivity跳转到其他页面
    // 这样做的好处在于MainActivity一定会启动并注册EventBus，避免EventBus未注册导致后续通知无法接收到而未执行其他操作
    // 解决应用场景：跟单登出 -> 发logout_account通知 执行ReStart -> 弹出登录页(不点跳过直接登录) -> 登录成功跳转账户列表(跟单) -> 发switch_account通知 执行ReStart
    // 解决问题：1.因MainActivity未注册EventBus而未执行InitHelper.start()未初始化
    //         2.为补救问题1在MainActivity的initView中初始化，但会两次执行InitHelper.start()而导致接口被Cancel而发送DATA_ERROR_GOODS通知界面显示异常
    private fun reStartApp(type: String?, data: Any?) {
        SDKIntervalUtil.instance.removeAllCallBack()    // 强制清除所有页面订阅
        val intent: Intent = Intent(context, MainActivity::class.java)
        intent.addFlags(
            Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                    or Intent.FLAG_ACTIVITY_CLEAR_TASK
        )
        val bundle = Bundle()
        bundle.putBoolean("is_switch_account", true)

        bundle.putBoolean("isLaunchLogin", true)
        // 以下参数给LoginActivity传递
        bundle.putString("type", type.ifNull())
        if (data is TokenErrorData) {
            bundle.putSerializable("data_token_error", data)
        }
        intent.putExtras(bundle)
        startActivity(intent)
        finish()
    }

    override fun showMaintenanceDialog(objData: MaintenanceObj?) {
        if (this.isFinishing) return
        mBinding.consParent.post {
            if (null == wrFullScreenMaintenanceDialog || null == wrFullScreenMaintenanceDialog?.get()) {
                val fullScreenMaintenanceDialog = FullScreenMaintenanceDialog.Builder(this).build()
                wrFullScreenMaintenanceDialog = WeakReference<FullScreenMaintenanceDialog>(fullScreenMaintenanceDialog)
            }

            wrFullScreenMaintenanceDialog?.get()?.setInfo(objData?.maintenanceMessage)
            wrFullScreenMaintenanceDialog?.get()?.show()
        }
    }

    override fun hideMaintenanceDialog() {
        if (this.isFinishing) return
        mBinding.consParent.post {
            if (null == wrFullScreenMaintenanceDialog || null == wrFullScreenMaintenanceDialog?.get()) return@post
            wrFullScreenMaintenanceDialog?.get()?.dismiss()
        }
    }

    override fun initPromoTabTxt(state: Boolean) {
        mBinding.lottieNavView.changeItemData(getString(if (state) R.string.promo else R.string.info))
    }

    override fun initPromoTabRemind(state: Boolean) {
        if (SpManager.getInstallApkFirst(true)) return
        SpManager.putPointRemindPromoShow(state)
        mBinding.lottieNavView.setRedDotState(3, state)
    }

    override fun onResume() {
        super.onResume()
        securityTypeView = SpManager.getSecuritySetState(0)
        isResume = true
        whenResumeShowInApp()
        if (mPresenter.firstInto) {
            mPresenter.firstInto = false
            return
        }
    }

    private fun getDeepLink() {
        Firebase.dynamicLinks.getDynamicLink(intent)
            .addOnSuccessListener(this) { pendingDynamicLinkData ->
                var deepLink: Uri? = null
                if (pendingDynamicLinkData != null) {
                    deepLink = pendingDynamicLinkData.link
                    if (deepLink != null) initDynamicLinkStr(deepLink.toString().trim())
                }
            }.addOnFailureListener(this) { e ->
                e.printStackTrace()
            }
    }

    private fun initDynamicLinkStr(linkString: String) {
        // 设置交易解锁，进入下单页面，需要验证解锁
        if (linkString.contains("http://Trade_", true) || linkString.contains("trade-", true)) {
            if (securityTypeView == 2 && SpManager.getAppLockOrder(false)) {
                VAUStartUtil.checkLockPass(this)
                return
            }
        }

        // 清理
        mPresenter.deepSymbolName = ""
        when {
            linkString.contains("https://Home/", true) || linkString.contains("http://Home/", true) -> {
                setSelectedFragment(0)
            }

            TextUtils.equals(linkString, "https://economic_calendar/") || TextUtils.equals(linkString, "http://economic_calendar/") -> { // 财经日历
                // todo gold 这里应该是需要传id的 但是现在没有id ， 节后 需要找马东或者 淑漩排查一下这里的id怎么获取
                openActivity(EconomicCalendarActivity::class.java)
            }

            linkString.contains("http://Trade_", true) || linkString.contains("https://Trade_", true) -> { // 产品
                val splitList = linkString.split("Trade_")
                val deepSymbolName = splitList.elementAtOrElse(1) { "" }
                deepLinkStartTrade(deepSymbolName)
            }

            linkString.contains("Trade_", true) -> { // 产品
                val splitList = linkString.split("Trade_")
                val deepSymbolName = splitList.elementAtOrElse(1) { "" }
                deepLinkStartTrade(deepSymbolName)
            }

            linkString.contains("http://Open_Live", true) || linkString.contains("https://Open_Live", true) -> { // 催用户开户
                skipDepositOrOpenAccount(EnumLinkSkipState.OPEN_APP)
            }

            linkString.contains("http://Deposit", true) || linkString.contains("https://Deposit", true) -> {
                skipDepositOrOpenAccount(EnumLinkSkipState.GOLDEN)
            }

            linkString.contains("http://Coupon", true) || linkString.contains("https://Coupon", true) -> { // 优惠券
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }

                val supervise = SpManager.getSuperviseNum("")

                if (supervise == "1") {
                    mPresenter.queryUserIsProclient()
                } else {
                    openActivity(CouponManagerActivity::class.java)
                    return
                }
            }

            linkString.contains("https://TopTrader", true) || linkString.contains("http://TopTrader", true) -> { // 发现页面信号源列表
                EventBus.getDefault().postSticky(
                    StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY)
                )
            }

            linkString.contains("http://Livestream", true) || linkString.contains("https://Livestream", true) -> { // 跳转直播
                SpManager.getLivestream("firebase")
                EventBus.getDefault().postSticky(
                    StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE)
                )
            }
            /**
             * 跳转到Promo页面
             * 未登录/已登录 -点击后跳转Promo页面
             */
            linkString.contains("http://promo", true) || linkString.contains("https://promo", true) -> {
                setSelectedFragment(3)
            }
            /**
             * 跳转到Discover页面
             * 未登录/已登录 -点击后跳转Discover
             */
            linkString.contains("http://discover", true) || linkString.contains("https://discover", true) -> {
                setSelectedFragment(2)
                hideNetDialog()
            }
            /**
             * 跳转到Copy Trading tag页面
             * 未登录 -点击后跳转默认登录页
             * 已登录 -点击后跳转Copy Trading tag页面
             * 非VFSC和VFSC2的账户 - 跳转到Trade首页
             */
            linkString.contains("http://copytrading", true) || linkString.contains("https://copytrading", true) -> {
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                val supervise = SpManager.getSuperviseNum("")
                if (supervise == "14" || supervise == "8") {
                    lifecycleScope.launchWhenResumed {
                        EventBus.getDefault().postSticky(
                            StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COPY_TRADE)
                        )
                    }
                } else {
                    setSelectedFragment(0)
                }
                hideNetDialog()
            }
            /**
             * 跳转到Login登录页面
             * 未登录 -点击后跳转默认登录页
             * 已登录 -点击后行情页 Trade
             */
            linkString.contains("http://login", true) || linkString.contains("https://login", true) -> {
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
            }

            else -> {
                VAUStartUtil.openActivity(this@MainActivity, PushBean().apply {
                    openType = "url"
                    titles = PushTitle("", "", "", "")
                    title = ""
                    urls = PushUrl(
                        linkString, linkString, linkString, linkString, linkString, linkString
                    )
                })
            }
        }
    }

    /*
    深度链接，跳入金，跳开户
    h5 跳入金，跳开户
    推送 跳入金
     */
    private fun skipDepositOrOpenAccount(linkState: EnumLinkSkipState) {
        if (!UserDataUtil.isLogin()) {
            openActivity(LoginActivity::class.java)
            return
        }

        // 真实账户
        if (linkState == EnumLinkSkipState.GOLDEN) {
            if (UserDataUtil.isLiveAccount()) {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                return
            }
        }
        if (SpManager.isV1V2()) {
            openAccountToKyc(this)
        } else {
            mPresenter.queryMT4AccountState(linkState)
        }
    }

    override fun skipOpenAccountActivity(
        linkSkipState: EnumLinkSkipState,
        objData: MT4AccountTypeObj?
    ) {

        // 更新数据库状态，以前逻辑有问题，暂时补 bug
        UserDataUtil.setOpenLiveAccountState(if (objData?.applyTpe == 2) "1" else "0")

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_OPEN_ACCOUNT) {
            if (objData?.status == 2) {
                ToastUtil.showToast(getString(R.string.you_have_an_existing_processed))
                return
            }
            if (objData?.applyTpe == 2) {
                //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑

                if (objData.regulator != "1") {
                    VAUStartUtil.openAccountGuide(this, objData)
                    return
                }

                openActivity(AccountManagerActivity::class.java)
                return
            }
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }
        val objStatus = objData?.status
        val applyTpe = objData?.applyTpe
        /**
         * 深度链接http://Open_Live
         * 安装app的情况下，如果未登录则跳转到默认登录页
         * 如果已登录如果未开通成功真实账户，则跳转到开户页面（根据首页那个开户流程弹窗跳转，比如已经完成1-1，那就跳转到1-2）
         * 如果是审核中，则只是打开app
         * 如果是已经开户的，则只是打开app
         *
         */
        if (linkSkipState == EnumLinkSkipState.OPEN_APP) {
            //0 账户审核中
            if (applyTpe == 0) {
                return
            }

            //applyTpe == 2 能开同名账户  objStatus == 5  账户审核已通过， 两个条件都满足说明已经开户成功
            if (applyTpe == 2 && objStatus == 5) {
                return
            }

            // 跳开户第几步
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
        }

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_DEPOSIT) {
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }


        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_REFER) {
            if (objData?.status == 2) {
                ToastUtil.showToast(getString(R.string.you_have_an_existing_processed))
                return
            }

            if (objData?.status == 5) {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString("url", mPresenter.wbpDataBean?.refer?.activityUrl ?: "")
                    putString("title", mPresenter.wbpDataBean?.refer?.activityName ?: "")
                    putInt("tradeType", 3)
                })
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        // 默认正常跳转，直接走公共跳转
        if (linkSkipState == EnumLinkSkipState.DEFAULT) {

            if (objData?.applyTpe == 2) {
                //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑

                if (objData.regulator != "1") {
                    VAUStartUtil.openAccountGuide(this, objData)
                    return
                }

                openActivity(AccountManagerActivity::class.java)
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        // 成功开户 模拟/返佣 进入金
        if (objStatus == 5) {
            if (linkSkipState == EnumLinkSkipState.GOLDEN) {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
            }
            return
        }

        if (objStatus == 3) {
            dispatchOpenAccount(this)
            return
        }

        /*
         开户审核中,只打开app
         1：账户未提交 (跳步数)
         2：账户审核，
         3：账户被挂起 {跳第一步}
         4：账户被拒绝(被拒绝，不存在这种情况，被拒绝时被弹出登陆，不能登陆)
         5：账户已通过
         6：账户待审核且身份证明未提交(跳身份证明)  数据不对，也是跳步数
         */
        if (objStatus == 2) {
            ToastUtil.showToast(getString(R.string.you_have_an_existing_processed))
            return
        }

        // 跳开户第几步
        VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
    }

    private fun exitApp() {
        if (System.currentTimeMillis() - mPresenter.lastClickBackMilli < 2000) {
            moveTaskToBack(true)
        } else {
            // 再按一次退出
            Toast.makeText(this, getString(R.string.tap_again_to_close), Toast.LENGTH_SHORT).show()
        }
        mPresenter.lastClickBackMilli = System.currentTimeMillis()
    }

    override fun onDestroy() {
        super.onDestroy()
        HttpUrl.applyBaseUrl()
        mCenterMainEventDialog.dismiss()
        EventBus.getDefault().unregister(this)
        mHandler.removeCallbacksAndMessages(null)
    }

    /**
     * fcm绑定主题
     */
    private fun subscribeTopic() {
        if (UserDataUtil.isLogin()) {
            if (UserDataUtil.isIB()) {
                Firebase.messaging.unsubscribeFromTopic("commonuser")
                Firebase.messaging.subscribeToTopic("ibuser").addOnCompleteListener {}
            } else {
                Firebase.messaging.unsubscribeFromTopic("ibuser")
                Firebase.messaging.subscribeToTopic("commonuser").addOnCompleteListener {}
            }
        } else {
            Firebase.messaging.unsubscribeFromTopic("ibuser")
            Firebase.messaging.unsubscribeFromTopic("commonuser")
        }
    }

    private fun deepLinkStartTrade(deepSymbolName: String) {
        /*
         如果未登录跳转到登录页，如果已登录，匹配XXXXXX产品跳转到下单页
         如果无该产品或者该产品不可交易或当前是ib返佣账户则只打开啊app
         比如http://Trade_USDCAD
         进行匹配，如果匹配到 USDCAD.sc  则跳转到该产品下单页
        */
        if (!UserDataUtil.isLogin()) {
            openActivity(LoginActivity::class.java)
            return
        }

        if (VAUSdkUtil.symbolList().size == 0) {
            mPresenter.deepSymbolName = deepSymbolName
            return
        }

        startDeepSymbolName(deepSymbolName)

    }

    /**
     * app flyer 的链接 跳转 如果MainActivity存在也会直接跳转到MainActivity ，所以MainActivity也需要做数据接收 和 跳转的判断
     */
    private fun appFlyerLinks(appLinkData: Uri?) {
        if (appLinkData.toString().startsWith("https://vantageapp.onelink.me/")) {
            // 注册邀请码 // https://vantageapp.onelink.me/qaPD?af_xp=referral&pid=SHARE-SP&deep_link_value=code-ABC123&deep_link_sub1=spid-123456
            appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
                if (value.contains("code-")) {
                    mPresenter.appLinkType = 7
                    SpManager.putInviteCodeRegister(value.drop("code-".length))
                } else if (value.contains("mt4id-")) {
                    mPresenter.appLinkType = 7
                    SpManager.putInviteCodeRegister(value.drop("mt4id-".length))
                }
            }
            appLinkData?.getQueryParameter("deep_link_sub1")?.let { value ->
                if (UserDataUtil.isLogin()) {
                    if (value.contains("spid-")) {
                        mPresenter.appLinkType = 8
                        SpManager.putIdSignalSource(value.drop("spid-".length))
                    }
                }
            }
            initAppLink()
        } else if (appLinkData.toString().startsWith("https://vantagefxapp.onelink.me/")) {
            // 跳转下单页面 https://vantagefxapp.onelink.me/Qgw0?deep_link_value=trade_USDJPY
            appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
                if (value.lowercase().contains("trade_")) {
                    deepLinkStartTrade(value.drop("trade_".length))
                }
            }
        }
        appLinkData?.getQueryParameter("deep_link_value")?.let { value ->
            if (value.contains("rs-")) {
                SpManager.putResourceCodeRegister(
                    value.drop("rs-".length)
                )
            }
        }
    }

    /**
     * 超链接 1:开户  2:入金  3:h5
     */
    private fun initAppLink() {

        when (mPresenter.appLinkType) {
            1 -> skipDepositOrOpenAccount(EnumLinkSkipState.OPEN_APP)
            2 -> skipDepositOrOpenAccount(EnumLinkSkipState.GOLDEN)
            3 -> {
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                } else {
                    openActivity(HtmlActivity::class.java, Bundle().apply {
                        putString("title", "")
                        putString("url", SpManager.getUrlH5(""))
                        putInt("tradeType", 3)
                    })
                }
            }

            4 -> {
                //跳转页面 - Coupon
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }

                val supervise = SpManager.getSuperviseNum("")
                if (supervise == "1") {
                    mPresenter.queryUserIsProclient()
                } else {
                    openActivity(CouponManagerActivity::class.java)
                }
            }
            //跳转页面 - TopTraders
            5 -> EventBus.getDefault()
                .postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY))
            //跳转直播室 - Livestream
            6 -> EventBus.getDefault()
                .postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE))

            //1. 已登录去首页
            //2. 未登录去注册页，注册页面选中Referral并填充邀请码，可手动更改
            7 -> {
                if (!UserDataUtil.isLogin()) {
                    openActivity(SignUpActivity::class.java)
                    return
                } else {
                    setSelectedFragment(0)
                }
            }
            // 跳信号源详情
            8 -> {
                VAUStartUtil.redirect(this, "113", PushParam().apply {
                    id = SpManager.getIdSignalSource("")
                })
            }
        }
    }

    private fun startDeepSymbolName(deepSymbolName: String) {

        if (UserDataUtil.isDemoAccount()) {
            ToastUtil.showToast(getString(R.string.this_product_is_only_available_for_live_accounts))
        } else {
            val symbolData = VAUSdkUtil.symbolList().firstOrNull {
                it.symbol == deepSymbolName
            } ?: return ToastUtil.showToast(getString(R.string.this_product_is_not_available_for_your_account_type))

            OrderViewModel.openOrder(this, symbolData.symbol, checkReadOnly = SpManager.isV1V2().not())
        }
        // 跳转的方法最后清空保存的字段
        mPresenter.deepSymbolName = ""
    }

    private fun updateApk() {
        try {
            if (!HttpUrl.isAppStore) {
                jumpWeb()
                return
            }
            val hasGooglePlayApp = GoogleApiAvailability.getInstance()
                .isGooglePlayServicesAvailable(this) == ConnectionResult.SUCCESS

            if (hasGooglePlayApp) {
                // 跳转谷歌应用市场
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("market://details?id=${context.packageName}")
                intent.setPackage("com.android.vending")
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
            } else {
                jumpWeb()
            }
        } catch (e: Exception) {
            jumpWeb()
        }

    }

    private fun jumpWeb() {
        // 没有谷歌应用市场，直接下载更新
        val intent1 = Intent()
        intent1.action = "android.intent.action.VIEW"
        // val uri = Uri.parse("https://vau-usa.oss-accelerate.aliyuncs.com//apk/au/Vantage.apk")
        val uri = Uri.parse(UrlConstants.APK_UPDATE_URL)
        intent1.data = uri
        try {
            // 可以使用intent1.resolveActivity(packageManager) != null 检查是否有应用能够处理这个 Intent
            // 但是暂未有无法跳转的toast，所以先catch一下，有toast的话 catch也能用。
            startActivity(intent1)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_Tab点击 -> 点击app内五个tab时触发
     *
     * 只在点击时埋点，刚进入页面不需要处理
     */
    private fun sensorsTrack(tabName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, tabName) // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_CLICK, properties)
    }
}
