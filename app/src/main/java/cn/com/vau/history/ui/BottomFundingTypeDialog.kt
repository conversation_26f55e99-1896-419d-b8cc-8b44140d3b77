package cn.com.vau.history.ui

import android.app.Activity
import android.content.Context
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.PopupHistoryFundingTypeFilterBinding
import cn.com.vau.history.viewmodel.FundingHistoryViewModel
import cn.com.vau.history.ui.adapter.FundingTypeAdapter
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.buidler.ActionBuilder

/**
 * Create data：2025/1/21 14:15
 * @author: Brin
 * Describe:
 */
class BottomFundingTypeDialog(
    context: Context, title: String,
    val viewModel: FundingHistoryViewModel
) : BottomDialog<PopupHistoryFundingTypeFilterBinding>(
    context = context, title = title,
    viewBinding = PopupHistoryFundingTypeFilterBinding::inflate,
    viewInterval = 4
) {

    val adapter: FundingTypeAdapter by lazy { FundingTypeAdapter(viewModel) }

    override fun setContentView() {
        mContentBinding.rvTypes.layoutManager = WrapContentLinearLayoutManager(context)
        mContentBinding.rvTypes.adapter = adapter
        viewModel.initTypesList()
        viewModel.typeList.observe(this) {
            adapter.submitList(it.toMutableList())
        }
    }

    @Suppress("unused")
    class Builder(activity: Activity, val viewModel: FundingHistoryViewModel) : ActionBuilder<PopupHistoryFundingTypeFilterBinding>(activity) {

        override fun build(): BottomFundingTypeDialog {
            config = config.copy(viewMode = true, isMoveUpToKeyboard = false, animationDuration = 400)
            return super.build() as BottomFundingTypeDialog
        }

        override fun createDialog(context: Context): IDialog<PopupHistoryFundingTypeFilterBinding> {
            return BottomFundingTypeDialog(
                context,
                title = activity.resources.getString(R.string.type),
                viewModel = viewModel,
            )
        }
    }
}
