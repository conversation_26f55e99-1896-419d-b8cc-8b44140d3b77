package cn.com.vau.history.ui

import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityHistotyBinding
import cn.com.vau.history.HistoryTrack
import cn.com.vau.util.*

class HistoryActivity : BaseMvvmBindingActivity<ActivityHistotyBinding>() {

    val titleList by lazy { arrayListOf<String>(getString(R.string.position_history), getString(R.string.funding_history)) }
    val fragmentList by lazy { arrayListOf<Fragment>(HistoryPositionFragment(), HistoryFundingFragment()) }

    override fun initView() {
        // 这里的账户类型中，有 Demo、Live、Copy Trading 三种类型，
        // 因为 demo、live 做多语言翻译时都不做处理，
        // 而 Copy Trading 做了处理，为了保持账户类型显示的一致性，这里的 Copy Trading 写死
        val loginAccount = if (UserDataUtil.isCopyTradingAccount()) {
            "Copy Trading"
        } else if (UserDataUtil.isDemoAccount()) {
            getString(R.string.demo)
        } else {
            getString(R.string.live)
        }
        val accountNumber = UserDataUtil.accountCd()
        mBinding.mHeaderBar.setTitleText("$loginAccount | $accountNumber")
        mBinding.mViewPager.init(fragmentList, titleList, supportFragmentManager, this, null)
        mBinding.mTabLayout.setVp(
            mBinding.mViewPager,
            titleList,
            TabType.LINE_INDICATOR,
            selectCallback = { position ->
                when (position) {
                    0 -> {
                        HistoryTrack.trackHistoryTabClick("Position History")
                    }

                    1 -> {
                        HistoryTrack.trackHistoryTabClick("Funding History")
                    }
                }
            })
    }
}