package cn.com.vau.history.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.databinding.ItemSelectSymbolBinding
import cn.com.vau.history.HistoryTrack
import cn.com.vau.history.data.ItemSymbolData
import cn.com.vau.history.viewmodel.HistoryPositionViewModel
import cn.com.vau.util.matcherSearchText

/**
 * Create data：2025/1/15 15:02
 * @author: Brin
 * Describe:
 */
class SymbolsAdapter(val viewModel: HistoryPositionViewModel) : ListAdapter<ItemSymbolData, SymbolsAdapter.SymbolsViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SymbolsViewHolder {
        return SymbolsViewHolder(ItemSelectSymbolBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: SymbolsViewHolder, position: Int) {
        holder.binding.apply {
            val item = getItem(holder.bindingAdapterPosition)

            val symbleStr = if (item.inputKey.isNullOrEmpty()) item.symbol else item.symbol.matcherSearchText(item.inputKey,  ContextCompat.getColor(root.context, R.color.ce35728))
            if (item.symbol.equals("ALL") && item.allTag) {
                tvName.text = viewModel.getString(R.string.all)
            }else{
                tvName.text = symbleStr
            }
            mCheckBox.isChecked = item.selected
            root.setOnClickListener {
                HistoryTrack.trackPositionHistorySymbolClick()
                viewModel.selectSymbol(holder.bindingAdapterPosition)
            }
        }
    }

    inner class SymbolsViewHolder(val binding: ItemSelectSymbolBinding) : RecyclerView.ViewHolder(binding.root) {}

    class DiffCallback : DiffUtil.ItemCallback<ItemSymbolData>() {
        override fun areItemsTheSame(oldItem: ItemSymbolData, newItem: ItemSymbolData): Boolean {
            val areItemsTheSame = oldItem.symbol == newItem.symbol
            return areItemsTheSame
        }

        override fun areContentsTheSame(oldItem: ItemSymbolData, newItem: ItemSymbolData): Boolean {
            val areContentsTheSame = oldItem.symbol == newItem.symbol && oldItem.selected == newItem.selected && oldItem.inputKey == newItem.inputKey
            return areContentsTheSame
        }
    }
}