package cn.com.vau.history.ui

import android.content.*
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.recyclerview.widget.ConcatAdapter
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.*
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.ui.adapter.*
import cn.com.vau.history.viewmodel.*
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.base.BottomListDialog

/**
 * Create data：2025/1/15 19:27
 * @author: Brin
 * Describe:
 */
class HistoryPositionDetailActivity : BaseMvvmBindingActivity<ActivityHistoryPositionDetailBinding>() {


    private val orderId: String by lazy { intent.getStringExtra(ORDERID) ?: "" }
    private val closeOrderId: String by lazy { intent.getStringExtra(CLOSEORDER) ?: "" }
    private val openTime: String by lazy { intent.getStringExtra(OPENTIME) ?: "" }
    private val closeTime: String by lazy { intent.getStringExtra(CLOSETIME) ?: "" }

    private val historyViewModel: HistoryPositionDetailViewModel by viewModels {
        HistoryPositionDetailViewModel.Factory(orderId, closeOrderId, openTime, closeTime)
    }
    private val historyPositionDetailAdapter: HistoryPositionDetailAdapter by lazy { HistoryPositionDetailAdapter(historyViewModel) }
    private val historyPositionDetailHeaderAdapter: HistoryPositionDetailHeaderAdapter by lazy { HistoryPositionDetailHeaderAdapter(historyViewModel) }

    private val bottomChargesDialog: BottomListDialog by lazy {
        val hintList = mutableListOf<HintLocalData>().apply {
            add(HintLocalData(getString(R.string.charges), getString(R.string.the_commissions_and_all_the_account)))
            add(HintLocalData(getString(R.string.swap), getString(R.string.the_rollover_interest_either_trading_hours)))
        }
        val statusAdapter = BottomTipsListAdapter(this, hintList)
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.charges_swap_dialog_title))
            .setAdapter(statusAdapter)
            .build()
    }

    private val bottomReasonDialog: BottomListDialog by lazy {
        val hintList = mutableListOf<HintLocalData>().apply {
            add(HintLocalData(getString(R.string.manual_closing), getString(R.string.position_manually_closed_by_you)))
            add(HintLocalData(getString(R.string.tp_sl), getString(R.string.position_automatically_closed_being_triggered)))
            add(HintLocalData(getString(R.string.stop_out), getString(R.string.position_automatically_closed_in_your_account)))
            add(HintLocalData(getString(R.string.others), getString(R.string.other_reasons_that_close_position)))
        }
        val statusAdapter = BottomTipsListAdapter(this, hintList)
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.reason))
            .setAdapter(statusAdapter)
            .build()
    }

    override fun initView() {
        mBinding.mRecyclerView.adapter = ConcatAdapter(historyPositionDetailHeaderAdapter, historyPositionDetailAdapter)
        mBinding.mEmptyView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mEmptyView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(this, R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(this, R.attr.icNoDataBase))
            vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mErrorView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mErrorView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(this, R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(this, R.attr.icNoConnection))
            vs.mNoDataView.setHintMessage("No internet connection or server error, please try again later.")
            vs.mNoDataView.setBottomBtnText("try again")
            vs.mNoDataView.setBottomBtnViewClickListener {
                historyViewModel.getHistoryPositionDetail()
            }
        }
    }

    override fun createObserver() {
        historyViewModel.historyDetailStatus.observe(this) {
            when (it) {
                HistoryStatus.Refreshing -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                HistoryStatus.RefreshingEnd -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = false
                }

                HistoryStatus.RefreshingFailed -> {
                    mBinding.mErrorView.isVisible = true
                    mBinding.mEmptyView.isVisible = false
                }

                HistoryStatus.Empty -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                else -> {}
            }
        }
        historyViewModel.historyPositionDetailHeader.observe(this) {
            historyPositionDetailHeaderAdapter.submitList(it.toMutableList())
        }
        historyViewModel.historyPositionDetail.observe(this) {
            historyPositionDetailAdapter.submitList(it.toMutableList())
        }
        historyViewModel.positionDetailBottomDialogState.run {
            observeState(this@HistoryPositionDetailActivity, PositionDetailBottomDialogState::chargeDialog) {
                if (it) bottomChargesDialog.showDialog()
            }
            observeState(this@HistoryPositionDetailActivity, PositionDetailBottomDialogState::reasonDialog) {
                if (it) bottomReasonDialog.showDialog()
            }
        }
    }

    companion object {
        const val ORDERID = "orderId"
        const val CLOSEORDER = "closeOrder"
        const val OPENTIME = "openTime"
        const val CLOSETIME = "closeTime"

        @JvmStatic
        fun start(context: Context, orderId: String, closeOrder: String, openTime: String, closeTime: String) {
            val intent = Intent(context, HistoryPositionDetailActivity::class.java)
            intent.putExtra(ORDERID, orderId)
            intent.putExtra(CLOSEORDER, closeOrder)
            intent.putExtra(OPENTIME, openTime)
            intent.putExtra(CLOSETIME, closeTime)
            context.startActivity(intent)
        }
    }

}
