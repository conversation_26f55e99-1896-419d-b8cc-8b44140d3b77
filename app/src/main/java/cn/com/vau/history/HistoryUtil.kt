package cn.com.vau.history

import android.text.SpannedString
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.util.arabicText
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.mathCompTo
import java.util.Locale

/**
 * Create data：2025/2/26 10:55
 * @author: Brin
 * Describe:
 */
object HistoryUtil {

    /**
     * 根据 text 判断是否为正数，如果是正数则返回绿色文字，否则返回红色文字
     */

    fun getColoredEarningsText(text: String): SpannedString {
        return buildSpannedString {
            if (text.isPositive()) {
                color(VauApplication.context.getColor(R.color.c00c79c)) { append(text) }
            } else {
                color(VauApplication.context.getColor(R.color.cf44040)) { append(text.arabicText()) }
            }
        }
    }

    fun String.isPositive(): Boolean = if (this.mathCompTo("0") >= 0) true else false

    /**
     * 获取后缀带有 (货币符号) 的字符串：
     * 如 "123456.78" -> "123456.78 (USB)"
     * 如 "123456.78" -> "(USB) 123456.78" (阿拉伯语)
     */
    fun String.withCurrencySuffix(): String {
        // 获取当前货币类型
        val currencyType = UserDataUtil.currencyType()
        // 获取当前语言
        val locale: Locale = LanguageHelper.getAppLocale()
        // 判断是否为阿拉伯语
//        val isArabic = locale.language == "ar" || locale.language == "ara"
        return "$this (${currencyType})"

    }
}