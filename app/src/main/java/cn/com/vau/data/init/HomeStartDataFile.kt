package cn.com.vau.data.init

import androidx.annotation.Keep
import cn.com.vau.common.view.popup.NewVersionGuideBean
import cn.com.vau.data.BaseBean
import cn.com.vau.data.msg.PushBean

// 首页 & 主页面启动（APP 启动默认调用接口 初始化除外）

/**
 * Splash
 */
@Keep
data class ImgQueryData(
    var obj: DataObj?
) {
    @Keep
    data class DataObj(
        val eventId: String?,
        val appJumpDefModel: PushBean?,
        val imgUrl: String?,
    )
}

/**
 * Created by roy on 2018/12/22.
 * 时令 时区
 */
@Keep
data class SeasonBean(
    val obj: Obj?
) : BaseBean() {
    @Keep
    data class Obj(
        val season: Int?
    )
}

/**
 * 获取服务器时间戳
 */
@Keep
data class ServerTimeBean(
    val `data`: Data?
) : BaseBean() {
    @Keep
    data class Data(val obj: Obj?)

    @Keep
    data class Obj(val st: String?)
}

/**
 * 是否显示 promoTab
 */
@Keep
data class PromoTabBean(
    val `data`: Data?
) : BaseBean() {
    @Keep
    data class Data(
        val promoTab: Boolean?
    )
}

/**
 * Created by roy on 2018/12/20.
 * 检测用户是否需要更新版本
 */
@Keep
class AppVersionBean : BaseBean() {
    var data: AppVersionData? = null
}

@Keep
class AppVersionData {
    var obj: AppVersionObj? = null
}

@Keep
class AppVersionObj {
    var id = 1      // 验证版本号是不是最新的   1:是最新的  0:不是最新的
    var versionName: String? = null
    var dlPath: String? = null
    var introduction: String? = null
    var forceFlag = 0
}

/**
 * 首页 --> 弹窗接口
 */
@Keep
data class PopWindowBean(
    val `data`: PopWindowData? = null
) : BaseBean()

@Keep
data class PopWindowData(
    val obj: PopWindowObj? = null
)

@Keep
data class PopWindowObj(
    val appEvent: PopWindowAppEvent? = null,
    val appVersion: PopWindowAppVersion? = null,
    val demoAccount: PopWindowDemoAccount? = null,
    val tokenExpire: String? = null,
    //  真实账号归档 true
    var realAccount: Boolean? = false,
    var accountApply: PushBean? = null,
    var stopLossCoupon: StopLossCoupon? = null,
    var supervise: String?,
    var isID: String?,  // 后台验证IP是否来源于印尼    1:是  0:不是
    val newerGiftActivityV2: NewerGiftActivityBean?,
    val bottomGuidePopUp: InAppBean.InAppData?,
    val display: Boolean?,   // 针对EEA地区用户是否展示Firebase数据收集的弹窗
    val eeaCountry: Boolean?,  // 用户是否属于EEA国家区域
    val currentSwitch: Boolean?  // 用户是否同意开启Firebase数据收集
)

@Keep
data class NewerGiftActivityBean(
    // 是否展示底部引导:1
    var showBottom: String?,
    // 底部引导图片链接
    var bottomPicUrl: String?,
    // 是否展示侧边引导:1
    var showSide: String?,
    // 侧边引导图片链接
    var sidePicUrl: String?,
    // 是否直接跳转
    var isDirectJump: String?,
    // 活动跳转地址
    var jumpUrl: String?,
    // 链接类型 1（内部链接） 2（外部链接）
    var jumpType: String?,
    // 链接地址
    var tcLink: String?
)

@Keep
data class StopLossCoupon(
    val imgs: List<String>?,
    val title: String?,
    val target: String?
)

@Keep
data class PopWindowDemoAccount(
    val demoCode: String = "",
    val demoMsg: String = ""
)

@Keep
data class PopWindowAppEvent(
    val imgUrl: String = "",
    val eventId: String = "",
    val appJumpDefModel: PushBean,
    val eventsName: String?,
    val eventsDesc: String?,
    val imgType: Int?,
    val promoLibraryName: String?, // 埋点专用
    val originalEventDesc: String?, // 埋点专用
)

@Keep
data class PopWindowAppVersion(
    val dlPath: String = "",
    val forceFlag: Int = -1,
    val introduction: String = "",
    val versionName: String = ""
)

/**
 * 广告位接口 请求跟单交易页面 (首页)
 */
@Keep
data class ImgAdvertInfoBean(
    val data: ImgAdvertInfoData? = null
) : BaseBean()

@Keep
data class ImgAdvertInfoData(
    val obj: ImgAdvertInfoObj? = null
)

@Keep
data class ImgAdvertInfoObj(
    val showClose: String? = null,
    val eventsList: List<ImgQueryData.DataObj>? = null
)

/**
 * 用户同意开启Firebase数据收集状态变更
 */
@Keep
data class CollectDataBean(
    val `data`: CollectDataData?
) : BaseBean()

@Keep
data class CollectDataData(
    val obj: CollectDataObj?
)

@Keep
data class CollectDataObj(
    val switch: Boolean?,
    val display: Boolean?,
    val currentSwitch: Boolean?
)

@Keep
data class BasicMyProfileData(
    val obj: BasicMyProfileObj? = null
)

@Keep
data class BasicMyProfileObj(
    val accountType: Int? = null,
    val country: String? = null,
    val countryCode: String? = null,
    val emailAddress: String? = null,
    val emailPlainText: String? = null,
    val emailStatus: Int? = null,
    val firstUse: Int? = null,
    val ipChangeWarn: Boolean? = null,
    val name: String? = null,
    val phone: String? = null,
    val phoneCode: String? = null,
    val phoneCountryCode: String? = null,
    val phonePlainText: String? = null,
    val phoneStatus: Int? = null
)

/**
 * 公告查询 -- InApp
 */
@Keep
data class InAppBean(
    var data: MutableList<InAppData>? = null,
) : BaseBean() {
    @Keep
    data class InAppData(
        var appJumpDefModel: PushBean? = null,
        var autoCloseTime: Long? = null, // 7200
        var displayLocation: String? = null, // 2
        var endTime: String? = null, // 31/12/2023
        var eventId: String? = null, // 500
        var eventsDesc: String? = null, // Indices Dividends for Period of 5 October to 13 October 2023 Vantage
        var eventsHot: Int? = null, // 0
        var eventsName: String? = null, // Dividend Announcement
        var eventsStatus: Int? = null, // 0
        var id: Int? = null, // 500
        var imgType: Int? = null, // 20
        var imgUrl: String? = null, // https://app-vau-test.s3.amazonaws.com/news/2023/11/20231102100132.png
        var longTerm: Int? = null, // 0
        var startTime: String? = null, // 02/11/2023
        var imgList: MutableList<NewVersionGuideBean>? = null, // 02/11/2023
        val promoLibraryName: String?, // 埋点专用
        val originalEventDesc: String?, // 埋点专用
    )
}

/**
 * 获取双域名配置
 *
 */
@Keep
data class ServerBaseUrlBean(
    var data: ServerBaseUrlData? = null
) : BaseBean()

@Keep
data class ServerBaseUrlData(
    var obj: ServerBaseUrlObj? = null
)

@Keep
data class ServerBaseUrlObj(
    var domestic: BaseUrlBean? = null,  //国内（大陆）
    var abroad: BaseUrlBean? = null,    //国外（非大陆）
    var config: BaseUrlConfig? = null,
    var currentCountry: String? = null, // 当前所在国
    var recommend: String? = null,      // 推荐使用
    var xtoken: String? = null, // 用户token，SSO需求后全部换成这个token
)

@Keep
data class BaseUrlBean(
    var h5: String? = null,

    var tradingMt4: String? = null,
    var tradingMt5: String? = null,
    var tradingSt: String? = null,

    var nontradingMt4: String? = null,
    var nontradingMt5: String? = null,
    var nontradingSt: String? = null,

    var websocketNost: String? = null,
    var websocketStmts: String? = null,
    var websocketStmsg: String? = null,

    var websocketapikeyStmts: String? = null,
    var websocketapikeyStmsg: String? = null
)

@Keep
data class BaseUrlConfig(
    var countrysName: String? = null,   // 返回："[中国,香港,新加坡,马来西亚,台湾,泰国,越南]"
)

/**
 * 是否显示跟单入口接口定义
 */
@Keep
data class StIsShowBean(
    var `data`: Data?
) : BaseBean() {
    @Keep
    data class Data(
        var obj: Obj?
    )

    @Keep
    data class Obj(
        // true: 显示，false: 不显示
        var isShow: Boolean?
    )
}

/**
 * 活动更新v2
 */
@Keep
class MaintenanceBean(
    var data: MaintenanceData?
) : BaseBean()

@Keep
data class MaintenanceData(
    var obj: MaintenanceObj?
)

@Keep
data class MaintenanceObj(
    // 维护内容文案
    var maintenanceMessage: String?,
    // 1:显示，0：不显示
    var showMaintenance: String?,

    var inAPPPinInformation: MutableList<InAppInformationData>? = null

)

@Keep
data class InAppInformationData(
    //自动关闭时间，类型为IN APP 置顶信息时设置时间(秒)，其他设为NULL
    var autoCloseTime: Long? = 0L,
    //展现位置-1:全部; 2首页; 3:订单页 作为信息展示位置
    var displayLocation: String? = "",
    //活动结束时间，格式dd/MM/yyyy
    var endTime: String? = "",
    //活动描述, 图片管理内容
    var imgDesc: String? = "",
    //活动标题
    var imgName: String? = "",
    //Event id
    var imgRelevancy: String? = "",
    //图片类型
    var imgType: Int? = 0,
    //  活动开始时间，格式dd/MM/yyyy
    var startTime: String? = "",
    //链接类型, 0:内部链接, 1:外部链接, 2:无跳转
    var urlType: Int? = 0,
    // 内部链接类型的字类型, 10:活动详情页,  11:公告详情, 12:新手学堂-小视频页面, 13:新手学堂-文章页面, 14:V学院直播页面
    var urlTypeLayer: Int? = 0,

    var showTime: Long = 0
)

/**
 * 是否入过金
 */
@Keep
data class CheckDepositStatusBean(
    var data: Data? = null
) : BaseBean() {
    @Keep
    data class Data(
        val obj: Obj
    )

    @Keep
    data class Obj(
        val isDepositMoney: String? = null,
    )
}


