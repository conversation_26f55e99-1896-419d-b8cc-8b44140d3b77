package cn.com.vau.data.discover

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import java.io.Serializable

/**
 * 直播列表
 */
@Keep
data class LiveListData(
    val obj: List<Row>,
)

/**
 * 历史直播列表
 */
@Keep
data class LiveHistoryData(
    val obj: Obj?
)

@Keep
data class Obj(
    val records: Int?,
    val rows: List<Row>?,
    val total: Int?
)

@Keep
data class Row(
    var channel: String? = "",
    val coverPicture: String? = "",
    var endTime: String? = "",
    var extractionServerUrl: String? = "",
    var id: Long? = 0,
    var introduction: String? = "",
    var isDisplay: String? = "",
    var ivestreamTitle: String? = "",
    var liveStatus: Int? = -1,
    var messageNode: String? = "",
    var planEndTime: String? = "",
    var planStartTime: String? = "",
    var playbackUrl: String? = "",
    var recRoadcastUrl: String? = "",
    var roomArn: String? = "",
    var roomId: Long? = 0,
    var roomName: String? = "",
    var startTime: String? = "",
    var strTimeLength: String? = "",
    var streamKey: String? = "",
    var timeLength: String? = "",
    var virtualCount: Long? = 0,
    var virtualLikeCount: Long? = 0,
    var titleType : String? = "",
    var itemType : Int? = -1,
    var shareContent : String = "",
    var height : Double = 0.0,
    var width : Double = 0.0
)

/**
 * 获取观看人数等信息
 */
@Keep
data class LiveInfoData(
    val data: LiveInfoBean? = null
): BaseBean()

@Keep
data class LiveInfoBean(
    val obj: LiveInfoDetailBean? = null
)

@Keep
data class LiveInfoDetailBean(
    val channel: String? = null,
    val coverPicture: String? = null,
    val extractionServerUrl: String? = null,
    val id: Int,
    val introduction: String? = null,
    val isDisplay: String? = null,
    val ivestreamTitle: String? = null,
    val liveStatus: Int? = null,
    val messageNode: String? = null,
    val planEndTime: String? = null,
    val planStartTime: String? = null,
    val playbackUrl: String? = null,
    val startTime: String? = null,
    val streamKey: String? = null,
    val virtualCount: Int? = null,
    val virtualLikeCount: Int? = null
)

/**
 * 直播里面的活动
 */
@Keep
class LivePromoData(
    val `data`: PromoData
): BaseBean()

@Keep
data class PromoData(
    val obj: List<PromoEventData>
)

/**
 * 进入直播
 */
@Keep
class AddAWSData(
    val `data`: UserChart
): BaseBean()

@Keep
data class UserChart(
    val obj: ChartTokenBean
)

@Keep
data class ChartTokenBean(
    val chatToken: String,
    val userId: String
)

/**
 * 直播点赞
 */
@Keep
class LiveLikes(
    val `data`: LikeCount
): BaseBean()

@Keep
data class LikeCount(
    val obj: Long
)

/**
 * 获取聊天内容 -- 直播
 */
@Keep
data class HistoryMessageData(
    val `data`: Data
) : BaseBean()

@Keep
data class Data(
    val obj: List<ChartMessage>
)

@Keep
data class ChartMessage(
    val brand: String,
    val chatConfigureId: Int,
    val chatContent: String,
    val chatTime: Long,
    val id: String,
    val userName: String,
    var messageType: Int = 0,
    var replyUserName: String = "",
)

/**
 * 过滤聊天内容 -- 直播
 */
@Keep
class FilterChartData(
    val `data`: Content
): BaseBean()

@Keep
data class Content(
    val obj: ChartData
)

@Keep
data class ChartData(
    val chatCode: String,
    val chatContent: String,
    val userId: String,
    val chatId: String //消息id，发送消息时需要传给接口
)
