package cn.com.vau.data.discover

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import cn.com.vau.data.msg.PushBean

@Keep
data class EventListData(
    var obj: List<PromoEventData>?
) : BaseBean()

@Keep
class PromoEventData(
    val eventId: String?,
    val appJumpDefModel: PushBean?,
    val imgUrl: String?,
    val startTime: String?,
    val endTime: String?,
    val eventsStatus: Int?,
    val eventsHot: Int?,
    val longTerm: String?,
    var eventsName: String?,
    var eventsDesc: String?
)

/**
 * 获取新用户引导礼包的信息
 * 新手活动 -- 后期转 H5
 */
@Keep
data class WbpStatusData(
    val data: Data?,
) : BaseBean() {
    @Keep
    data class Data(
        val obj: Obj?
    )

    @Keep
    data class Obj(
        var creditActivity: CreditActivity?,
        var depositBonus: DepositBonus?,
        var tradeLoss: TradeLoss?,
        val refer: Refer?,

        // --------------------------------------
        val newerGiftActivity: NewerGiftActivity?,
        // 闪电活动
        val timeLimitActivity: TimeLimitActivity?

    )

    @Keep
    data class CreditActivity(
        val getCredit: String?,
        val getCreditTime: String?,
        var status: String = "-1",
        var activityUrl: String?,
        var activityName: String?
    )

    @Keep
    data class DepositBonus(
        val status: String?,
        var activityUrl: String?,
        var activityName: String?

    )

    @Keep
    data class TradeLoss(
        val status: String?,
        var activityUrl: String?,
        var activityName: String?
    )

    @Keep
    data class Refer(
        val activityUrl: String?,
        val status: String?,
        var activityName: String?
    )

    // ----------------------------------------------------------
    @Keep
    data class NewerGiftActivity(
        // 是否展示该活动（1:展示、2:不展示）
        val isShow: String?,
        // 背景图地址
        val backgroundImage: String,
        // T&C链接
        val tcLink: String,
        val depositActivities: DepositActivities,
        val openAccountActivities: OpenAccountActivities,
        val referActivities: ReferActivities,
        val tradeActivities: TradeActivities
    )

    @Keep
    data class TimeLimitActivity(
        // 是否展示该活动（0:不展示、1:展示）
        val isShow: String?,
        // 文案
        val textContent: String?,
        // 活动地址
        val activityUrl: String?,
        // 倒计时，时间戳
        val countDownTime: String?,
        // 活动状态（1:未参加、2:已完成）
        val status: String?,
        // 链接类型 1:内部链接 2:外部链接
        val linkType: String?
    )

    @Keep
    data class DepositActivities(
        val activities: List<Activity>?,
        // 活动数量
        val count: String?,
        // 是否同时参加 0：否、1：是
        val isJoinTogether: String?,
        val isShow: String?,
        // 是否完成开户、入金、交易 0：否、1：是
        val isDone: String?
    )

    @Keep
    data class OpenAccountActivities(
        val activities: List<Activity>?,
        val count: String?,
        //  是否同时参加 0：否 1：是
        val isJoinTogether: String?,
        val isShow: String?,
        val isDone: String?
    )

    @Keep
    data class ReferActivities(
        val activities: List<Activity>?,
        val count: String?,
        val isJoinTogether: String?,
        val isShow: String?,
        val isDone: String?
    )

    @Keep
    data class TradeActivities(
        val activities: List<Activity>?,
        val count: String?,
        val isJoinTogether: String?,
        val isShow: String?,
        val isDone: String?
    )

    @Keep
    data class Activity(
        // 活动地址 || code
        /**
         * code
         * 1 - 不完全开户：判断是否开户，如果未开户跳转到开户（不用考虑id是否提交/审核）  如果已完成则跳转tc
         * 2 - 完全开户：判断是否开户，如果未开户跳转到开户（需要考虑id是否提交/审核）  如果已完成则跳转tc
         * 3 - 入金：先判断是否完全开户，如果已经完全开户则跳转入金
         * 4 - 交易：先判断是否完全开户，如果已经完全开户判断是否入金，如果没入金跳转到入金  已入金跳转到tc页
         */
        val activityUrl: String?,
        // 活动状态（0:未参加、1:已完成）
        val status: String?,
        // 文案
        val textContent: String?,
        // 链接类型 1:内部链接 2:外部链接
        val linkType: String?
    )

}





