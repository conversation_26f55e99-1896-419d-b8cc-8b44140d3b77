package cn.com.vau.data.discover

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import java.io.Serializable

/**
 * 财经日历 列表
 */
@Keep
class CalendarData(
    val obj: CalendarObj
) : Serializable

@Keep
class CalendarObj(
    val finIndexs: MutableList<CalendarObjFinindex>? = null
) : Serializable

/**
 * Created by roy on 2018/12/11.
 * K线底部 财经日历
 */
@Keep
data class ProductItemInfoData(
    val data: ProductItemDataBean
) : BaseBean()

@Keep
data class ProductItemDataBean(
    val obj: ProductItemObjData
)

@Keep
data class ProductItemObjData(
    val finIndex: MutableList<CalendarObjFinindex>? = null,
    val productNews: List<NewsLetterObjData>? = null,
    val reuterNews: List<ReutersObj>? = null,
)

@Keep
data class ReutersObj(
    val id: String? = null,
    val newsThumbnail: String? = null,  // Thumbnail 图案 url
    val newsHeadline: String? = null,  // 名称
    val newsDateTime: String? = null,  // Newsletter 发布时间 如：23/03/2024 11:36
    val newsContent: String? = null,  // 内容
    val newsSourceName: String? = null,  // Reuters
    val newsEpochTime: Long? = null,  // Newsletter 发布时间时间戳
    val view: Int = 0,  // 阅读量
)

@Keep
class CalendarObjFinindex(
    var dataId: String? = "",
    var title: String? = "",
    var country: String? = "",
    var countryEn: String? = "",
    var countryImg: String? = "",
    var actualVal: String? = "",
    var previous: String? = "",
    var consensus: String? = "",
    var unit: String? = "",
    var star: Int = 0,
    var timeShow: String? = "",
    var isRemind: Int = 0,
    var importance: String? = ""
) : Serializable

/**
 * 财经日历详情
 */
@Keep
class EconomicCalendarDataBean(
    val obj: EconomicCalendarObjBean
)

@Keep
class EconomicCalendarObjBean(
    var actualVal: String? = "",
    var consensus: String? = "",
    var country: String? = "",
    var dataId: Int = 0,
    var previous: String? = "",
    var publishTime: String? = "",
    var star: Int = 0,
    var title: String? = "",
    var unit: String? = "",
    var importance: String? = "",
    var isRemind: Int = -1,
)

/**
 * 财经日历图表数据
 */
@Keep
data class ChartCalendarDataBean(
    val obj: List<ChartCalendarObjData>
)

@Keep
data class ChartCalendarObjData(
    val actualVal: String,
    val pubTime: String
)

/**
 * 财经日历国家地区
 * Created by zhy on 2018/11/1.
 */
@Keep
data class FiltersCountryData(var obj: List<FiltersCountryObj>? = null)

@Keep
data class FiltersCountryObj(
    var parentCode: Any? = null,
    var areaCode: String? = null,
    var areaName: String? = null,
)
