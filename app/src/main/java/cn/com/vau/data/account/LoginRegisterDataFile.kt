package cn.com.vau.data.account

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import kotlinx.parcelize.Parcelize
import java.io.Serializable

// Type: 登录 && 注册 && 绑定 && 修改密码 && 忘记密码 && 修改账户信息 等

/**
 * 登录
 */
@Keep
data class LoginBean(
    var data: LoginDataBean?
) : BaseBean()

@Keep
data class LoginDataBean(
    var obj: LoginObjBean?
)

@Keep
data class LoginObjBean(
    var userId: String?, // 用户id
    var token: String?, // 用户token
    var xtoken: String, // 用户token，SSO需求后全部换成这个token
    var countryCode: String?, // 国家代码
    var code: String?, // 手机区号
    var userTel: String?, // 手机号
    var email: String?, // 邮箱
    var userNick: String?, // 昵称
    var pic: String?, // 头像
    var fastCloseState: String?, // 是否快速平仓，（注：返回值对应用户设置的快速平仓值，没有为“”）
    var status: String?, // 0:接口错误, 4:绑定手机, 5:账号绑定, 6:注册
    var accountMN: LoginUserInfoBean?, // 模拟账户对象
    var regulator: String?, // 监管（数字）
    var twoFactorUser: Boolean?,     // 是否启用了 google 2fa
    val emailEventID: String?, //神策埋点绑定用户使用
    val crmUserId: String?, //crmUserId，神策埋点使用
    var fastCloseCopyOrder: String?, // 快速停止跟单
    var orderConfirmation: String?, // 交易订单确认
    var needAuthOptList: List<String>?, // 2FA可以切换OTP方式的数组。黄金开户新增，只有登录V10036会返此参数
    var validateType: Int?, // 0：手机和邮箱都未认证，1: 邮箱已认证，2：手机号已认证。都已认证的情况下接口会返回1。黄金开户新增
    var screenShare: String? // 是否可以截屏分享
)

@Keep
class LoginUserInfoBean {

    var acountCd: String? = null // 账户号
    var id: String? = null
    var lastLoginDate: String? = null
    var mt4Password: String? = null
    var reqDate: String? = null
    var restCnt: Double = 0.toDouble()
    var state: String? = null
    var userId: String? = null // app用户id
    var userNick: String? = null

    var accountServer: String? = null // 服务器id
    var currencyType: String? = null // 币种
    var accountType: String? = null // 账户类型
    var platform: String? = null // 平台
    var accountDealType: String? = null // 账户类别（1：交易账户，2：返佣账户，3：app模拟账户）
    var virtualId: String? = null // 虚拟账户id
}

/**
 * 获取验证码
 */
@Keep
class ForgetPwdVerificationCodeBean : BaseBean() {
    var data: ForgetPwdVerificationCodeData? = null
}

@Keep
class ForgetPwdVerificationCodeData {
    var obj: ForgetPwdVerificationCodeObj? = null
}

@Keep
class ForgetPwdVerificationCodeObj {
    //用户id
    var userId: String? = null

    //用户类型(0：新用户，1：crm用户，2：用户不存在)
    var userType = 0

    var smsCodeId: String? = null

    var txId: String? = null
}

/**
 * 邮箱绑定
 */
@Keep
data class EmailBindBean(
    var data: EmailBindData? = null
) : BaseBean()

@Keep
data class EmailBindData(
    var obj: EmailBindObj? = null
)

@Keep
data class EmailBindObj(
    var userId: String? = null,
    var token: String? = null,
    var xtoken: String? = null,
    var countryCode: String? = null,
    var code: String? = null,
    var userTel: String? = null,
    var userNick: String? = null,
    var pic: String? = null,
    var email: String? = null,
    var regulator: String?,
    val emailEventID: String?, // 神策埋点绑定用户使用
    val crmUserId: String?, // 神策埋点使用
)

/**
 * 选择电话区号
 * Created by wj.
 */
@Keep
data class SelectCountryNumberBean(
    var data: SelectCountryNumberData? = null
) : BaseBean()

@Keep
data class SelectCountryNumberData(
    var obj: List<SelectCountryNumberObj>? = null
)

@Keep
data class SelectCountryNumberObj(
    var lettername: String? = null,
    var list: List<SelectCountryNumberObjDetail>? = null
) : Serializable

@Keep
@Parcelize
data class SelectCountryNumberObjDetail(
    var countryCode: String? = null,
    var countryName: String? = null,
    var country: String? = null,
    var countryNum: String? = null,
    var mobile: String? = null,
    var pwd: String? = null,
    var handleType: Int = 0,
    var email: String? = null,
) : Serializable, Parcelable

/**
 * 绑定邮箱
 * Created by THINKPAD on 2019/9/11.
 */
@Keep
data class BindEmailBean(
    var data: BindEmailData?
) : BaseBean()

@Keep
data class BindEmailData(
    var obj: BindEmailObj?
)

@Keep
data class BindEmailObj(
    var type: String?,
    var reserveUserId: String?,
    var deletedUserId: String?,
    var reserveUserToken: String?,
    var xtoken: String? = null, // 用户token，SSO需求后全部换成这个token
    var userType: String?,
    val crmUserId: String?, //crmUserId，神策埋点使用
    val emailEventID: String?, //神策埋点绑定用户使用
)

/**
 * 获取验证码的Data类
 */
@Keep
data class VerificationCodeData(
    var data: ObjBean?,
) : BaseBean() {
    @Keep
    class ObjBean(
        val obj: SmsCodeBean?
    )

    @Keep
    class SmsCodeBean(
        val smsCodeId: String?,
    )
}

/**
 * 获取手机号
 * Created by THINKPAD on 2020/6/10.
 */
@Keep
data class GetMobileBean(
    var data: GetMobileData?
) : BaseBean()

@Keep
class GetMobileData(
    var obj: GetMobileObj?
)

@Keep
data class GetMobileObj(
    val code: String?,
    val phoneCountryCode: String?,
    val phoneNum: String?
)

/**
 * 修改手机号
 */
@Keep
data class ChangeUserInfoSuccessBean(
    var data: ChangeUserInfoData? = null,
) : BaseBean()

@Keep
data class ChangeUserInfoData(
    var msg: String? = null,
    var type: String? = null,
)