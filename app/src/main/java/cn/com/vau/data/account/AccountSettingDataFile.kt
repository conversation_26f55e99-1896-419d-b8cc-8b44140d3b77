package cn.com.vau.data.account

import androidx.annotation.Keep
import cn.com.vau.common.view.popup.bean.SelectTitle
import cn.com.vau.data.BaseBean

/**
 * 解除 2fa 绑定
 * Author: GG
 * Date: 2024/2/27
 * Description:
 */
@Keep
data class TFADisableBeanData(
    val data: TFADisableBeanObj? = null
) : BaseBean()

@Keep
data class TFADisableBeanObj(
    val obj: TFADisableBean? = null
)

@Keep
data class TFADisableBean(
    val disableSuccessMsg: String? = ""
)

/**
 * 获取2fa配置
 */
@Keep
data class TFASettingData(val obj: String?) {
    @Keep
    data class Obj(
        val qrCode: String? = "",
        val secretKey: String? = "",
        val twoFactorMsg: String? = "",
        val twoFactorChangeMsg: String? = "",
    )
}

@Keep
data class TFADeviceData(val obj: Obj?) {
    @Keep
    data class Obj(
        val deviceChange: Boolean? = null,
        val popUpMsg: String? = ""
    )
}

@Keep
data class TFAVerificationData(val obj: Obj?) {
    @Keep
    data class Obj(
        val authType: Int? = null,
    )
}

@Keep
data class ChangeTFAOtpData(val obj: Obj?) {
    @Keep
    data class Obj(
        val txId: String? = null,
        val smsCodeId: String? = null
    )
}

@Keep
data class TFAResultData(val obj: Obj?) {
    @Keep
    data class Obj(
        val msg: String? = null,
    )
}

/**
 * 获取用户账户列表用于发送邮件
 */
@Keep
data class AccountObjBean(
    var obj: AccountListBean? = null
) {
    @Keep
    data class AccountListBean(
        var accountList: MutableList<Account>? = null,
        var isShow: Boolean? = false
    ) {
        @Keep
        data class Account(
            var mt4Account: String? = ""
        ) : SelectTitle {
            override fun getTitle(): String = mt4Account ?: ""
        }
    }
}

/**
 * 用户设备历史
 */
@Keep
data class DeviceHistoryObj(
    val obj: MutableList<Obj>?
) {

    @Keep
    data class Obj(
        var currentDevice: Boolean = false,
        var deviceId: String? = "",
        var id: Int? = 0,
        var ipAddress: String? = null,
        var ipLocation: String? = null,
        var lastLoginDateTime: String? = "",
        var model: String? = "",
        var systemType: String? = "",
        var timeZone: String? = ""
    )
}

/**
 * 通过区号查询国家名称 -- 国际化
 * Created by THINKPAD on 2021/1/29.
 */
@Keep
data class SelectCountryCodeObjBean(
    val obj: SelectCountryCodeBean?
)

@Keep
data class SelectCountryCodeBean(
    val phoneNum: String?,
    val countryCode: String?,
    val countryName: String?
)

/**
 * 用户添加日志
 */
@Keep
data class UserLogsData(
    val `data`: Data,
) : BaseBean() {
    @Keep
    data class Data(
        val obj: Obj?
    )

    @Keep
    data class Obj(
        val logUrl: String?,
        val uploadTime: String?
    )
}