package cn.com.vau.trade.st.fragment.child

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.strategy.StrategyOtherData
import cn.com.vau.databinding.FragmentStCopyTradingPositionsRejectedBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.trade.st.adapter.StStrategyOtherRecyclerAdapter
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.*

/**
 * 被拒绝策略列表
 */
class StCopyTradingPositionsRejectedFragment : BaseFragment() {

    private val mBinding by lazy {
        FragmentStCopyTradingPositionsRejectedBinding.inflate(layoutInflater)
    }

    private var mAdapter: StStrategyOtherRecyclerAdapter? = null

    var dataList = arrayListOf<StrategyOtherData.StrategyData>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        super.initView()

        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setBottomBtnText(getString(R.string.discover_strategies))
                vs.mNoDataScrollView.setBottomBtnViewClickListener {
                    EventBus.getDefault().postSticky(
                        StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY)
                    )
                }
            }
        })

        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mAdapter = StStrategyOtherRecyclerAdapter(
            requireContext(),
            getString(R.string.rejected),
            dataList
        )
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(12.dp2px()))
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)
    }

    override fun initData() {
        super.initData()
        stUserMainStrategyFollowers()
    }

    override fun initListener() {
        super.initListener()

        mBinding.mRefreshLayout.setOnRefreshListener {
            stUserMainStrategyFollowers()
        }

        mAdapter?.setOnItemClickListener(object :
            StStrategyOtherRecyclerAdapter.OnItemClickListener {

            override fun onItemClick(position: Int) {
                val data = dataList.getOrNull(position)

                openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                    putSerializable("data_strategy", StrategyOrderBaseData().apply {
                        this.type = EnumStrategyFollowState.REJECTED
                        this.signalStrategyId = data?.strategyId
                        this.followRequestId = data?.followRequestId

                        this.date = data?.applyDate

                        this.invested = data?.investmentAmount
                        this.investmentCredit = data?.investmentCredit
                        this.balance = data?.investmentAmount
                    })
                })
            }

            override fun onNextStartClick(position: Int) {

                val data = dataList.getOrNull(position)

                openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                    putSerializable("data_strategy", StrategyOrderBaseData().apply {
                        this.type = EnumStrategyFollowState.REJECTED
                        this.signalStrategyId = data?.strategyId
                        this.followRequestId = data?.followRequestId

                        this.date = data?.applyDate

                        this.invested = data?.investmentAmount
                        this.investmentCredit = data?.investmentCredit
                        this.balance = data?.investmentAmount
                    })
                })
            }

            override fun onNextEndClick(position: Int) {

                val data = dataList.getOrNull(position)

                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.rejected_order))
                    .setContent(
                        getString(
                            R.string.this_order_was_x_your_fully_returned,
                            TimeUtil.formatDate(
                                data?.reviewDate?.toLongOrNull() ?: 0L,
                                "dd/MM/yyyy"
                            )
                        )
                    )
                    .setSingleButton(true)
                    .setSingleButtonText(getString(R.string.ok))
                    .build()
                    .showDialog()

            }
        })
    }

    private fun stUserMainStrategyFollowers() {

        val jsonObject = JsonObject()
        jsonObject.addProperty("stUserId", UserDataUtil.stUserId())
        jsonObject.addProperty("status", "Rejected")
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().strategyCopyRequestList(requestBody),
            object : BaseObserver<StrategyOtherData>() {
                @SuppressLint("NotifyDataSetChanged")
                override fun onNext(dataBean: StrategyOtherData?) {

                    mBinding.mRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)

                    if ("200" != dataBean?.code) {
                        ToastUtil.showToast(dataBean?.msg)
                        return
                    }

                    dataList.clear()
                    val list = dataBean.data ?: arrayListOf()
                    dataList.addAll(list)
                    refreshAdapter()

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    rxManager.add(d)
                }

            }
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    fun refreshAdapter() {
        lifecycleScope.launch(Dispatchers.Default) {
            dataList.forEach {
                it.pnlUI = it.totalHistoryProfit?.numCurrencyFormat2()
                it.equityUI = it.equity?.numCurrencyFormat2()
                it.investedUI = it.investmentAmount?.numCurrencyFormat2()
                it.totalSharedProfitUI = it.totalSharedProfit?.numCurrencyFormat2()
            }
            withContext(Dispatchers.Main) {
                mAdapter?.notifyDataSetChanged()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_REJECTED -> {
                stUserMainStrategyFollowers()
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 当待审核策略被拒绝时会收到 202 通知，直接调用 stTradeListOrder 接口，接口请求一定会发送下面两个通知
            NoticeConstants.Init.DATA_SUCCESS_ORDER,
            NoticeConstants.Init.DATA_ERROR_ORDER -> {
                stUserMainStrategyFollowers()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}