package cn.com.vau.trade.fragment.kchart

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.adapter.AnalysesRecyclerAdapter
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.trade.viewmodel.KLineAnalysisViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataView
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 21 15:08
 * @updateUser:
 * @updateDate: 2024 10月 21 15:08
 */
class KLineAnalysisFragment : BaseMvvmBindingFragment<FragmentRefreshBinding>() {

    private val mViewModel: KLineAnalysisViewModel by viewModels()

    private val activityViewModel: KLineViewModel by activityViewModels()

    private var prodName: String = ""

    private val adapter by lazy {
        AnalysesRecyclerAdapter().apply {
            setEmptyView(emptyView = NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_analyses))
            })
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        prodName = activityViewModel.symbol
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {
        mBinding.mRecyclerView.adapter = adapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(requireContext(), R.attr.color_c1f1e1e1e_c1fffffff)))
    }

    override fun initData() {
        super.initData()
        mViewModel.loadData(prodName)
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.loadData(prodName)
        }
        adapter.setNbOnItemClickListener { _, _, position ->
            val bean = adapter.data.elementAtOrNull(position) ?: return@setNbOnItemClickListener
            val htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/analysis/${bean.id ?: ""}"
            NewHtmlActivity.openActivity(requireActivity(), url = htmlUrl)
            // 埋点
            SensorsDataUtil.track(SensorsConstant.V3510.ANALYSIS_TAB_PAGE_ARTICLE_CLICK, JSONObject().apply {
                put(SensorsConstant.Key.ARTICLE_TITLE, bean.product.ifNull())
            })
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, adapter, mBinding.mRefreshLayout)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventBus(tag: String) {
        if (tag == NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT) {
            prodName = activityViewModel.symbol
            mViewModel.loadData(prodName)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        @JvmStatic
        fun newInstance(): KLineAnalysisFragment {
            return KLineAnalysisFragment()
        }
    }

}