package cn.com.vau.trade.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.init.ShareProductData


/**
 * Created by roy on 2018/10/30.
 * 自选页面 --> top
 */
class OptionalSelectedRecyclerAdapter(
        var mContext: Context,
        var dataList: MutableList<ShareProductData>
) : RecyclerView.Adapter<OptionalSelectedRecyclerAdapter.ViewHolder>() {

    private var monItemClickListener: OnItemClickListener? = null
    private var isShowEditItem: Boolean = true
    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if(isShowEditItem) {
            holder.cbSelect.visibility = View.VISIBLE
            holder.ivToTop.visibility = View.VISIBLE
            holder.ivEdit.visibility = View.VISIBLE
            if(position == 0) {
                holder.ivToTop.visibility = View.INVISIBLE
            }
        } else {
            holder.cbSelect.visibility = View.GONE
            holder.ivToTop.visibility = View.GONE
            holder.ivEdit.visibility = View.GONE
        }


        val shareSymbolData = dataList.elementAtOrNull(position)?:return

        holder.tvName.text = shareSymbolData.symbol

        holder.cbSelect.isChecked = shareSymbolData.isOptionSelected

        holder.itemView.setOnLongClickListener {
            monItemClickListener?.onItemEditLongClickListener(holder, position)
            true
        }

        holder.ivToTop.setOnClickListener {
            monItemClickListener?.onItemToTopClickListener(holder, position)
        }

        holder.cbSelect.setOnClickListener {
            monItemClickListener?.onItemCheckBoxClickListener(holder, position)
        }

    }

    fun showEditItemChange() {
        isShowEditItem = !isShowEditItem
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
            ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_optional_selected, parent, false))

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view){
        val cbSelect = view.findViewById<CheckBox>(R.id.cb_select)
        val ivToTop = view.findViewById<ImageView>(R.id.ivToTop)
        val tvName = view.findViewById<TextView>(R.id.tvName)
        val ivEdit = view.findViewById<ImageView>(R.id.ivEdit)
    }

    fun setOnItemClickListener(monItemClickListener: OnItemClickListener) {
        this.monItemClickListener = monItemClickListener
    }

    interface OnItemClickListener {

        fun onItemEditLongClickListener(holder: RecyclerView.ViewHolder, position: Int)

        fun onItemToTopClickListener(holder: RecyclerView.ViewHolder, position: Int)

        fun onItemCheckBoxClickListener(holder: OptionalSelectedRecyclerAdapter.ViewHolder, position: Int)
    }


}