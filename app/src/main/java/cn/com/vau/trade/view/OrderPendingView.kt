package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.OrderPendingViewBinding
import cn.com.vau.databinding.VsOrderInputBinding
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.addComma
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.numFormat

@SuppressLint("NotifyDataSetChanged")
class OrderPendingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val mBinding by lazy {
        OrderPendingViewBinding.inflate(LayoutInflater.from(context), this)
    }

    private var vsAtPriceBinding: VsOrderInputBinding? = null
    private var vsStopLimitBinding: VsOrderInputBinding? = null

    private val strStopPrice by lazy { context.getString(R.string.stop_price) }
    private val strLimitPrice by lazy { context.getString(R.string.limit_price) }
    private val strMaxValue by lazy { context.getString(R.string.max_value) }
    private val strMinValue by lazy { context.getString(R.string.min_value) }

    private val atPriceSelectOpenOrderEtBg by lazy { ContextCompat.getDrawable(context, R.drawable.select_open_order_et_bg) }
    private val stopLimitPriceSelectOpenOrderEtBg by lazy { ContextCompat.getDrawable(context, R.drawable.select_open_order_et_bg) }
    private val drawShapeStrokeCf44040SolidC0a1e1e1eC262930C6 by lazy { ContextCompat.getDrawable(context, R.drawable.draw_shape_stroke_cf44040_solid_c0a1e1e1e_c262930_r6) }

    private var oldSymbol = ""

    // at price 输入框 默认值
    private var defaultAtPrice: String = ""

    // at price 输入框 默认值 具有千分位的值
    private var defaultAtPriceStr: String = ""

    // stop limit price 输入框 默认值
    private var defaultStopLimitPrice: String = ""

    // stop limit price 输入框 默认值 具有千分位的值
    private var defaultStopLimitPriceStr: String = ""

    private var isNeedUpdatePrice = false

    // at price 是否获取焦点
    private var isAtPriceFocus = false

    // stop limit price 是否获取焦点
    private var isStopLimitPriceFocus = false

    var mViewModel: OrderViewModel? = null
        get() {
            if (field == null) {
                throw RuntimeException("OpenOrderView 未初始化，请调用initOpenOrderView初始化")
            }
            return field
        }

    init {
        initListener()
    }

    fun initViewModel(viewModel: OrderViewModel) {
        this.mViewModel = viewModel
        createObserver()
    }

    private fun initListener() {
        mBinding.vsAtPrice.setOnInflateListener { _, view ->
            vsAtPriceBinding = VsOrderInputBinding.bind(view)
            vsAtPriceBinding?.orderInputView?.onFocus = { hasFocus ->
                vsAtPriceBinding?.orderInputView?.run {
                    isSelected = hasFocus
                    // 如果输入框失去焦点，并且输入框不为空，则说明输入过值，那么就格式化输入框的值
                    if (!hasFocus && getAtPrice().isNotBlank()) {
                        setEtInputValue(getAtPrice().numFormat(mViewModel?.digits.ifNull(), false))
                    }
                }
                // 保存 at price 是否获取焦点
                isAtPriceFocus = hasFocus
                // 如果输入框没有焦点，则不显示上面的提示区间，如果有焦点，则判断下面的错误区间提醒是否显示，下面的错误区间显示了，上面的提示区间就不显示
                // 上面的提示 隐藏是不能设置gone，不然会有错位的bug，所以用invisible
                vsAtPriceBinding?.tvTransfer?.isInvisible = if (hasFocus) {
                    vsAtPriceBinding?.tvPendingTip?.isVisible == true
                } else {
                    true
                }
                setAtPriceTransferTxt()
            }
            vsAtPriceBinding?.orderInputView?.onTextChanged = { text ->
                mViewModel?.atPriceInput = checkAtPriceDigits(text).toString()
            }
            vsAtPriceBinding?.orderInputView?.onSubClick = { text ->
                subAtPrice(text)
            }
            vsAtPriceBinding?.orderInputView?.onAddClick = { text ->
                addAtPrice(text)
            }
        }
        mBinding.vsStopLimit.setOnInflateListener { _, view ->
            vsStopLimitBinding = VsOrderInputBinding.bind(view)
            vsStopLimitBinding?.orderInputView?.setInputTitleText(context.getString(R.string.limit_price))
            vsStopLimitBinding?.orderInputView?.onFocus = { hasFocus ->
                vsStopLimitBinding?.orderInputView?.run {
                    isSelected = hasFocus
                    // 如果输入框失去焦点，并且输入框不为空，则说明输入过值，那么就格式化输入框的值
                    if (!hasFocus && getStopLimitPrice().isNotBlank()) {
                        setEtInputValue(getStopLimitPrice().numFormat(mViewModel?.digits.ifNull(), false))
                    }
                }
                // 保存 stop limit price 是否获取焦点
                isStopLimitPriceFocus = hasFocus
                // 如果输入框没有焦点，则不显示上面的提示区间，如果有焦点，则判断下面的错误区间提醒是否显示，下面的错误区间显示了，上面的提示区间就不显示
                // 上面的提示 隐藏是不能设置gone，不然会有错位的bug，所以用invisible
                vsStopLimitBinding?.tvTransfer?.isInvisible = if (hasFocus) {
                    vsStopLimitBinding?.tvPendingTip?.isVisible == true
                } else {
                    true
                }
                setStopLimitTransferTxt()
            }
            vsStopLimitBinding?.orderInputView?.onTextChanged = { text ->
                mViewModel?.stopLimitPriceInput = checkAtPriceDigits(text).toString()
            }
            vsStopLimitBinding?.orderInputView?.onSubClick = { text ->
                subStopLimitPrice(text)
            }
            vsStopLimitBinding?.orderInputView?.onAddClick = { text ->
                addStopLimitPrice(text)
            }
        }
    }

    fun clearEtFocus() {
        vsStopLimitBinding?.orderInputView?.clearEtFocus()
        vsAtPriceBinding?.orderInputView?.clearEtFocus()
    }

    /**
     * stop limit price 减
     */
    private fun subStopLimitPrice(text: String) {
        vsStopLimitBinding?.orderInputView?.setEtInputValue(mViewModel?.subPendingPrice(text, defaultStopLimitPrice).ifNull())
    }

    /**
     * stop limit price 加
     */
    private fun addStopLimitPrice(text: String) {
        vsStopLimitBinding?.orderInputView?.setEtInputValue(mViewModel?.addPendingPrice(text, defaultStopLimitPrice).ifNull())
    }

    /**
     * at price 减
     */
    private fun subAtPrice(text: String) {
        vsAtPriceBinding?.orderInputView?.setEtInputValue(mViewModel?.subPendingPrice(text, defaultAtPrice).ifNull())
    }

    /**
     * at price 加
     */
    private fun addAtPrice(text: String) {
        vsAtPriceBinding?.orderInputView?.setEtInputValue(mViewModel?.addPendingPrice(text, defaultAtPrice).ifNull())
    }

    /**
     * 初始化产品并刷新价格，如果新旧产品相同则不用刷新
     */
    fun initPrice(data: ShareProductData?) {
        if (data == null || oldSymbol == data.symbol) return
        oldSymbol = data.symbol
        // 初始化价格时，清除停损限价输入框里的值
        vsStopLimitBinding?.orderInputView?.setEtInputValue("")
        isNeedUpdatePrice = true
        refreshPrice()
    }

    private fun createObserver() {
        if (mViewModel == null) return
        mViewModel?.refreshProductDataLiveData?.observe(context as MainActivity) {
            if (it == null) return@observe
            refreshPrice()
        }

        mViewModel?.buyOrSellChangeLiveData?.observe(context as MainActivity) {
            if (it == null) return@observe
            updatePrice {
                updatePriceUI()
            }
        }

        mViewModel?.tradeTypeChangeLiveData?.observe(context as MainActivity) {
            if (it == null) return@observe
            when (it) {
                OrderViewModel.INDEX_MARKET -> {
                    // 切换回市价选项时 清除保存的产品名
                    oldSymbol = ""
                    mBinding.tvPending.isVisible = true
                    mBinding.vsAtPrice.isGone = true
                    mBinding.vsStopLimit.isGone = true
                }

                OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT -> {
                    mBinding.tvPending.isGone = true
                    mBinding.vsAtPrice.isVisible = true
                    mBinding.vsStopLimit.isGone = true
                    vsAtPriceBinding?.orderInputView?.setInputTitleText(strLimitPrice)
                    vsAtPriceBinding?.orderInputView?.initInputView()
                }

                OrderViewModel.INDEX_SELL_STOP_BUY_STOP -> {
                    mBinding.tvPending.isGone = true
                    mBinding.vsAtPrice.isVisible = true
                    mBinding.vsStopLimit.isGone = true
                    vsAtPriceBinding?.orderInputView?.setInputTitleText(strStopPrice)
                    vsAtPriceBinding?.orderInputView?.initInputView()
                }

                OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT -> {
                    mBinding.tvPending.isGone = true
                    mBinding.vsAtPrice.isVisible = true
                    mBinding.vsStopLimit.isVisible = true
                    vsAtPriceBinding?.orderInputView?.setInputTitleText(strStopPrice)
                    vsAtPriceBinding?.orderInputView?.initInputView()
                    vsStopLimitBinding?.orderInputView?.initInputView()
                }
            }
            updatePrice {
                updatePriceUI()
                isNeedUpdatePrice = getAtPrice().isBlank()
            }
        }
    }

    private fun refreshPrice() {
        if (mViewModel?.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            updatePrice {
                if (isNeedUpdatePrice) {
                    updatePriceUI()
                    isNeedUpdatePrice = getAtPrice().isBlank()
                }
                if (mBinding.vsAtPrice.isVisible) {
                    checkAtPrice()
                }
                if (mBinding.vsStopLimit.isVisible) {
                    checkStopLimitPrice()
                }
                updateTransferString()
            }
        }
    }

    /**
     * 挂单价格小数位检查
     */
    private fun checkAtPriceDigits(edt: Editable): Editable {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            //删除前进行数值校验，看是否在length内
            if (temp.length - posDot - 1 > mViewModel?.digits.ifNull()) {
                val endIndex = posDot + 2 + mViewModel?.digits.ifNull()
                if (endIndex <= edt.length) {
                    edt.delete(posDot + mViewModel?.digits.ifNull() + 1, endIndex)
                }
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                edt.delete(posDot - 1, posDot)
            }
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        checkAtPrice()
        checkStopLimitPrice()
        return edt
    }

    /**
     * 设置提示atPrice价格区间
     */
    private fun setAtPriceTransferTxt() {
        if (vsAtPriceBinding?.tvTransfer?.isVisible == false)
            return
        vsAtPriceBinding?.tvTransfer?.text = if (mViewModel?.tradeType == OrderViewModel.TRADE_SELL) {
            if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT)
                "$strLimitPrice ≥ $defaultAtPriceStr"
            else
                "$strStopPrice ≤ $defaultAtPriceStr"
        } else {
            if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT)
                "$strLimitPrice ≤ $defaultAtPriceStr"
            else
                "$strStopPrice ≥ $defaultAtPriceStr"
        }
    }

    /**
     * 设置提示stopLimit价格区间
     */
    private fun setStopLimitTransferTxt() {
        if (vsStopLimitBinding?.tvTransfer?.isVisible == false)
            return
        vsStopLimitBinding?.tvTransfer?.text = if (mViewModel?.tradeType == OrderViewModel.TRADE_SELL) {
            "$strLimitPrice ≥ $defaultStopLimitPriceStr"
        } else {
            "$strLimitPrice ≤ $defaultStopLimitPriceStr"
        }
    }

    /**
     * 检查atPrice 价格是否在有效区间内 ，并设置提示信息
     */
    private fun checkAtPrice() {
        if (mViewModel?.tradeType == OrderViewModel.TRADE_SELL) {
            if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) {
                checkAtPriceMin()
            } else {
                checkAtPriceMax()
            }
        } else {
            if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_SELL_LIMIT_BUY_LIMIT) {
                checkAtPriceMax()
            } else {
                checkAtPriceMin()
            }
        }
    }

    /**
     * 检查atPrice 价格是否在有效区间内 ，并设置 max 类型的提示信息
     */
    private fun checkAtPriceMax() {
        if (getAtPrice().mathCompTo(defaultAtPrice) != 1) {
            vsAtPriceBinding?.tvTransfer?.isInvisible = !isAtPriceFocus
            vsAtPriceBinding?.tvPendingTip?.isVisible = false
            vsAtPriceBinding?.orderInputView?.background = atPriceSelectOpenOrderEtBg
            vsAtPriceBinding?.orderInputView?.isSelected = isAtPriceFocus
            return
        }
        vsAtPriceBinding?.tvPendingTip?.text = buildString {
            append(strMaxValue)
            append(" : ")
            append(defaultAtPriceStr)
        }
        vsAtPriceBinding?.tvTransfer?.isInvisible = true
        vsAtPriceBinding?.tvPendingTip?.isVisible = true
        vsAtPriceBinding?.orderInputView?.background = drawShapeStrokeCf44040SolidC0a1e1e1eC262930C6
    }

    /**
     * 检查atPrice 价格是否在有效区间内 ，并设置 min 类型的提示信息
     */
    private fun checkAtPriceMin() {
        if (getAtPrice().mathCompTo(defaultAtPrice) != -1) {
            vsAtPriceBinding?.tvTransfer?.isInvisible = !isAtPriceFocus
            vsAtPriceBinding?.tvPendingTip?.isVisible = false
            vsAtPriceBinding?.orderInputView?.background = atPriceSelectOpenOrderEtBg
            vsAtPriceBinding?.orderInputView?.isSelected = isAtPriceFocus
            return
        }
        vsAtPriceBinding?.tvPendingTip?.text = buildString {
            append(strMinValue)
            append(" : ")
            append(defaultAtPriceStr)
        }
        vsAtPriceBinding?.tvTransfer?.isInvisible = true
        vsAtPriceBinding?.tvPendingTip?.isVisible = true
        vsAtPriceBinding?.orderInputView?.background = drawShapeStrokeCf44040SolidC0a1e1e1eC262930C6
    }

    /**
     * 检查stopLimit 价格是否在有效区间内 ，并设置提示信息
     */
    private fun checkStopLimitPrice() {
        if (mViewModel?.tradeType == OrderViewModel.TRADE_SELL) {
            checkStopLimitPriceMin()
        } else {
            checkStopLimitPriceMax()
        }
    }

    /**
     * 检查stopLimit 价格是否在有效区间内 ，并设置 min 类型的提示信息
     */
    private fun checkStopLimitPriceMin() {
        if (getStopLimitPrice().mathCompTo(defaultStopLimitPrice) != -1) {
            vsStopLimitBinding?.tvTransfer?.isInvisible = !isStopLimitPriceFocus
            vsStopLimitBinding?.tvPendingTip?.isVisible = false
            vsStopLimitBinding?.orderInputView?.background = stopLimitPriceSelectOpenOrderEtBg
            vsStopLimitBinding?.orderInputView?.isSelected = isStopLimitPriceFocus
            return
        }
        vsStopLimitBinding?.tvPendingTip?.text = buildString {
            append(strMinValue)
            append(" : ")
            append(defaultStopLimitPriceStr)
        }
        vsStopLimitBinding?.tvTransfer?.isInvisible = true
        vsStopLimitBinding?.tvPendingTip?.isVisible = true
        vsStopLimitBinding?.orderInputView?.background = drawShapeStrokeCf44040SolidC0a1e1e1eC262930C6
    }

    /**
     * 检查stopLimit 价格是否在有效区间内 ，并设置 max 类型的提示信息
     */
    private fun checkStopLimitPriceMax() {
        if (getStopLimitPrice().mathCompTo(defaultStopLimitPrice) != 1) {
            vsStopLimitBinding?.tvTransfer?.isInvisible = !isStopLimitPriceFocus
            vsStopLimitBinding?.tvPendingTip?.isVisible = false
            vsStopLimitBinding?.orderInputView?.background = stopLimitPriceSelectOpenOrderEtBg
            vsStopLimitBinding?.orderInputView?.isSelected = isStopLimitPriceFocus
            return
        }
        vsStopLimitBinding?.tvPendingTip?.text = buildString {
            append(strMaxValue)
            append(" : ")
            append(defaultStopLimitPriceStr)
        }
        vsStopLimitBinding?.tvTransfer?.isInvisible = true
        vsStopLimitBinding?.tvPendingTip?.isVisible = true
        vsStopLimitBinding?.orderInputView?.background = drawShapeStrokeCf44040SolidC0a1e1e1eC262930C6
    }

    /**
     * 刷新默认挂单价 以及 atPrice 限制价格 以及  stopLimitPrice
     */
    private fun updatePriceUI() {
        if (mViewModel?.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            showProductData()
            updateTransferString()
        }
    }

    /**
     * 刷新atPrice 和 stopLimitPrice 的提示信息
     */
    private fun updateTransferString() {
        setAtPriceTransferTxt()
        setStopLimitTransferTxt()
    }

    /**
     * 设置atPrice 和 stopLimitPrice 的输入框默认价格
     */
    private fun showProductData() {
        if (isNeedUpdatePrice)
            vsAtPriceBinding?.orderInputView?.setEtInputValue(defaultAtPrice)
        if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            if (isNeedUpdatePrice || getStopLimitPrice().isBlank()) {
                vsStopLimitBinding?.orderInputView?.setEtInputValue(defaultStopLimitPrice)
            }
        }
    }

    /**
     * 获取最新价格，用于显示和逻辑判断
     * 并计算出用于显示千分位的价格字符串
     */
    private fun updatePrice(endFlow: (() -> Unit)? = null) {
        if (mViewModel?.tradeTypeIndex != OrderViewModel.INDEX_MARKET) {
            defaultAtPrice = mViewModel?.getDefaultAtPrice().ifNull()
            defaultAtPriceStr = defaultAtPrice.addComma(mViewModel?.digits ?: -1)
            defaultStopLimitPrice = mViewModel?.getStopLimitPrice(if (isNeedUpdatePrice) defaultAtPrice else getAtPrice().ifBlank { defaultAtPrice }).ifNull()
            defaultStopLimitPriceStr = defaultStopLimitPrice.addComma(mViewModel?.digits ?: -1)
        }
        endFlow?.invoke()
    }

    /**
     * 获取atPrice价格
     */
    private fun getAtPrice(): String {
        return vsAtPriceBinding?.orderInputView?.getInputText() ?: ""
    }

    /**
     * 获取stopLimit价格
     */
    private fun getStopLimitPrice(): String {
        return vsStopLimitBinding?.orderInputView?.getInputText() ?: ""
    }

    /**
     * 检查 atPrice 是否有效 返回 true 为有效
     */
    fun isAtPriceValid(): Boolean {
        return mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_MARKET || (vsAtPriceBinding?.tvPendingTip?.isVisible == false && vsAtPriceBinding?.orderInputView?.isVisible == true && getAtPrice().isNotBlank())
    }

    /**
     * 检查 stopLimitPrice 是否有效 返回 true 为有效
     */
    fun isStopLimitPriceValid(): Boolean {
        return mViewModel?.tradeTypeIndex != OrderViewModel.INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT || (vsStopLimitBinding?.tvPendingTip?.isVisible == false && vsStopLimitBinding?.orderInputView?.isVisible == true && getStopLimitPrice().isNotBlank())
    }
}