package cn.com.vau.trade.bean

import android.os.Parcelable
import androidx.annotation.Keep
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.trade.ext.copyData
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class SymbolItemBean(var product: ShareProductData, var isSelected: Boolean, var searchKey: String = "") : Parcelable {
    fun deepCopy(): SymbolItemBean = this.copy(
        product = this.product.copyData(),
        isSelected = this.isSelected,
        searchKey = this.searchKey
    )
}

@Keep
data class FollowProductData(var symbol: String, var isSelected: Boolean)

@Keep
data class SearchResult(
    val input: String,
    val searchMap: LinkedHashMap<String, List<SymbolItemBean>>
)