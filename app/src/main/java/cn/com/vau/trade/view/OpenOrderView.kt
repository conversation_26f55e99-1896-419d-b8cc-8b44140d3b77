package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.util.AttributeSet
import android.view.*
import android.widget.EditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.viewModelScope
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.OpenOrderViewBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.data.ProductState
import cn.com.vau.trade.data.TradeOrderType
import cn.com.vau.trade.data.toProductState
import cn.com.vau.trade.dialog.OrderConfirmDialog
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*
import java.util.*

@SuppressLint("NotifyDataSetChanged")
class OpenOrderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val inputViewIds by lazy {
        arrayListOf(R.id.etInput,R.id.tvInputTitle,R.id.tvVolumeTitle,R.id.etVolume)
    }

    private val mBinding by lazy {
        OpenOrderViewBinding.inflate(LayoutInflater.from(context), this)
    }

    private val performManager by lazy {
        PerformManager(context as MainActivity)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(context as MainActivity)
    }

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }
    private val draw_shape_cf44040_r100 by lazy { R.drawable.draw_shape_cf44040_r100 }
    private val draw_shape_c00c79c_r100 by lazy { R.drawable.draw_shape_c00c79c_r100 }
    private val draw_shape_c731e1e1e_c61ffffff_r100 by lazy { R.drawable.draw_shape_c731e1e1e_c61ffffff_r100 }

    var mViewModel: OrderViewModel? = null
        get() {
            if (field == null) {
                throw RuntimeException("OpenOrderView 未初始化，请调用initOpenOrderView初始化")
            }
            return field
        }

    /**
     * 订单类型说明弹框
     */
    private val orderTypePopup: BottomListDialog by lazy {
        BottomListDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.order_types))
            .setAdapter(PlatAdapter().apply {
                setList(arrayListOf<HintLocalData>().apply {
                    add(HintLocalData(context.getString(R.string.market_order), context.getString(R.string.an_order_to_market_price)))
                    add(HintLocalData(context.getString(R.string.limit_order), context.getString(R.string.an_order_placed_to_either_certain_price)))
                    add(HintLocalData(context.getString(R.string.stop_order), context.getString(R.string.an_order_placed_to_buy_certain_price)))
                    if (UserDataUtil.isMT5()) {
                        add(HintLocalData(context.getString(R.string.stop_limit_order), context.getString(R.string.an_order_that_and_for_execution)))
                    }
                })
            })
            .build()
    }

    /**
     * 订单类型选择
     */
    private val typeAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false).apply {
            if (mViewModel == null) return@apply
            setNewInstance(mViewModel?.tradeTypeList)
            selectTitle = mViewModel?.tradeTypeList?.getOrNull(0)?.getShowItemValue()

            setOnItemClickListener { _, _, position ->
                if (mViewModel == null || mViewModel?.tradeTypeIndex == position) {
                    tradeTypePopup.dismiss()
                    return@setOnItemClickListener
                }
                mViewModel?.tradeTypeIndex = position
                selectTitle = data.getOrNull(position)?.getShowItemValue()
                notifyDataSetChanged()
                tradeTypePopup.dismiss()
            }
        }
    }
    private val tradeTypePopup: BottomListDialog by lazy {
        BottomListDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.type))
            .setAdapter(typeAdapter)
            .build()
    }

    init {
        initListener()
        addPerformance()
    }

    /**
     * 初始化下单器，必现先调用
     */
    fun initOpenOrderView(viewModel: OrderViewModel) {
        this.mViewModel = viewModel
        mBinding.orderVolumeView.initOrderVolumeView(viewModel)
        mBinding.tpSlView.initOrderTpSlView(viewModel)
        mBinding.orderPendingView.initViewModel(viewModel)
        mBinding.orderPciView.initViewModel(viewModel)
        mViewModel?.initTradeTypeList(context)
        createObserver()
    }

    private fun initListener() {
        // 切换买卖方向
        mBinding.mTradeTypeView.setOnSelectListener {
            selectedOrderType(it)
        }
        //切换下单类型（市价还是挂单）
        mBinding.clMarketExecution.clickNoRepeat {
            tradeTypePopup.showDialog()
        }
        //下单类型说明弹框
        mBinding.ivMarketIntroduce.clickNoRepeat {
            orderTypePopup.showDialog()
            mViewModel?.tradePageOrderTypesAnnotationClick()
        }
        // margin 说明
        mBinding.tvMarginTile.clickNoRepeat {
            BottomInfoListDialog.Builder(context as Activity)
                .setTitle(context.getString(R.string.margin))
                .setDataList(arrayListOf(HintLocalData(context.getString(R.string.a_portion_of_open_position))))
                .setLinkText(context.getString(R.string.formulas_and_examples))
                .setLinkListener {
                    (context as MainActivity).openActivity(HtmlActivity::class.java, Bundle().apply {
                        putString("title", context.getString(R.string.margin_formulas))
                        putString("url", "${HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix}/socialTrading/marginCalculation")
                        putInt("tradeType", 3)
                    })
                }
                .build()
                .showDialog()
            mViewModel?.tradePageMarginAnnotationClick()
        }
        //下单点击
        mBinding.tvNext.clickNoRepeat {
            if ("1" == UserDataUtil.orderConfirmState()) {
                tradePermissionPerformance.run {
                    if (handleTradeBlockType(true) { submitOrder() }) return@clickNoRepeat
                }
                submitOrder()
            } else {
                mViewModel?.createApiParam()
                tradePermissionPerformance.run {
                    if (handleTradeBlockType(true) { showSubmitOrderConfirmDialog() }) return@clickNoRepeat
                }
                showSubmitOrderConfirmDialog()
            }
            mViewModel?.sensorsTrack()
        }

        KeyboardUtil.registerSoftInputChangedListener(context as Activity) {
            if (0 == it) {
                clearAllFocus()
            }
        }
    }

    private fun clearAllFocus() {
        mBinding.orderVolumeView.clearEtFocus()
        mBinding.tpSlView.clearEtFocus()
        mBinding.orderPendingView.clearEtFocus()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }
    /**
     * 订单确认弹框
     */
    private fun showSubmitOrderConfirmDialog() {
        OrderConfirmDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.order_confirmation_uppercase))
            .setOrderViewModel(mViewModel)
            .setOnConfirm { isShowNotAgain ->
                submitOrder()
                if (isShowNotAgain) {
                    mViewModel?.setOrderConfirmation(true)
                }
                mViewModel?.tradeOpenConfirm()
            }
            .build()
            .showDialog()
    }

    /**
     * 提交订单
     */
    private fun submitOrder() {
        //检查手数是否合法
        if (mViewModel?.checkInputVolumeValid(context) == false) {
            return
        }
        //检查止盈输入是否合法
        if (mBinding.tpSlView.tpTipsIsInvalid()) {
            ToastUtil.showToast(context.getString(R.string.the_set_take_is_invalid))
            return
        }
        //检查止损输入是否合法
        if (mBinding.tpSlView.slTipsIsInvalid()) {
            ToastUtil.showToast(context.getString(R.string.the_set_stop_is_invalid))
            return
        }
        //检查止挂单价格（atPrice）输入是否合法
        if (mBinding.orderPendingView.isAtPriceValid().not()) {
            ToastUtil.showToast(context.getString(R.string.the_set_open_is_invalid))
            return
        }
        //检查止limit价格（stopLimitPrice）输入是否合法
        if (mBinding.orderPendingView.isStopLimitPriceValid().not()) {
            ToastUtil.showToast(context.getString(R.string.the_set_stop_limit_is_invalid))
            return
        }
        //接口参数赋值
        mViewModel?.createApiParam()
        //开仓
        if (mViewModel?.tradeTypeIndex == OrderViewModel.INDEX_MARKET) {
            if (UserDataUtil.isStLogin()) {
                mViewModel?.stSubmitOrder()
            } else {
                mViewModel?.submitOrder()
            }
        } else {
            // 挂单
            val pendingPriceNum = mViewModel?.atPriceParam.toDoubleCatching()
            if (pendingPriceNum <= 0.0) {
                ToastUtil.showToast(context.getString(R.string.the_set_open_is_invalid))
                return
            }
            if (UserDataUtil.isStLogin()) {
                mViewModel?.stSubmitPendingOrder()
            } else {
                mViewModel?.submitPendingOrder()
            }
        }
    }

    private fun createObserver() {
        if (mViewModel == null) {
            return
        }
        if (context !is MainActivity) {
            return
        }
        val activity = context as MainActivity

        // 交易类型变化
        mViewModel?.tradeTypeChangeLiveData?.observe(activity) {
            setTradeTypeView()
        }
        mViewModel?.buyOrSellChangeLiveData?.observe(activity) {
            updateSellBuyType(it)
            setTvNext()
        }

        //交易产品变化
        mViewModel?.productDataChangeLieData?.observe(activity) {
            mBinding.orderPendingView.initPrice(it)
            mBinding.orderVolumeView.showProductData()
            updateSellBuyPriceAndState(it)
            setTvNext()
        }

        //保证金变化
        mViewModel?.requestMarginChangeLiveData?.observe(activity) {
            showMargin(it)
        }

        //提交订单成功
        mViewModel?.submitOrderSuccessLiveData?.observe(activity) {
            handleSuccess()
        }

        //资金不足
        mViewModel?.fundLackLiveData?.observe(activity) {
            showFundLackDialog()
        }

        //token 失效
        mViewModel?.tokenErrorLiveData?.observe(activity) {
            showTokenErrorDialog(it)
        }

        //提交订单失败
        mViewModel?.hintDataDialogLiveData?.observe(activity) {
            showHintDataDialog(it)
        }

        //价格波动大，下单失败
        mViewModel?.checkDelayLiveData?.observe(activity) {
            showCheckDelayDialog()
        }

        //产品交易状态变化
        mViewModel?.productStateLiveData?.observe(context as MainActivity) {
            mBinding.mTradeTypeView.setProductState(it)
            if (it == ProductState.NoProduct) {
                resetProduct()
            }
        }
    }

    /**
     * 行情刷新，根据最新价价格计算 margin，止盈止损范围
     */
    private fun refreshProductData() {
        mViewModel?.refreshProduce()
    }

    /**
     *  设置交易产品
     */
    fun setProduceData(shareProductData: ShareProductData?) {
        mViewModel?.setProduceData(shareProductData)
    }

    @SuppressLint("SetTextI18n")
    fun resetProduct() {
        mBinding.tvMargin.text = "--"
        mBinding.tvFreeMargin.text = "--"
        mBinding.tvMarginLevel.text = "--"
        mBinding.orderVolumeView.resetOrderVolumeView()
        mViewModel?.initTradeTypeList(context)
        if (mViewModel?.isNeedResetTradeType() == true) {
            mViewModel?.tradeTypeIndex = 0
            typeAdapter.selectTitle = mViewModel?.tradeTypeList?.getOrNull(0)?.getShowItemValue()
            typeAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 交易类型变化时，需要更新的UI
     */
    private fun setTradeTypeView() {
        if (mViewModel == null) return
        mBinding.tvMarketExecution.text =
            mViewModel?.tradeTypeList?.getOrNull(mViewModel?.tradeTypeIndex.ifNull(0))?.getShowItemValue()
    }

    /**
     * 设置订单类型buy 或 sell
     * tradeType: OrderViewModel.TRADE_SELL 是 sell； OrderViewModel.TRADE_BUY是buy
     */
    private fun selectedOrderType(tradeType: String) {
        mViewModel?.tradeType = tradeType
    }

    /**
     * 更新下单按钮状态
     */
    private fun setTvNext() {
        mBinding.tvNext.text = context.getString(if (mViewModel?.tradeType == OrderViewModel.TRADE_BUY) R.string.buy else R.string.sell)
        if (mViewModel?.productData?.marketClose == true) {
            mBinding.tvNext.text = context.getString(R.string.market_closed)
        }
        if (mViewModel?.checkProduceCanTrade().ifNull(false).not()) {
            mBinding.tvNext.setBackgroundResource(draw_shape_c731e1e1e_c61ffffff_r100)
            return
        }
        mBinding.tvNext.setBackgroundResource(if (mViewModel?.tradeType == OrderViewModel.TRADE_SELL) draw_shape_cf44040_r100 else draw_shape_c00c79c_r100)
    }

    /**
     * 更新Sell、Buy的价格、状态
     */
    private fun updateSellBuyPriceAndState(productData: ShareProductData?) {
        mBinding.mTradeTypeView.updatePrice(productData)
        mBinding.mTradeTypeView.setProductState(productData.toProductState())
    }

    /**
     * 更新Sell、Buy的类型
     */
    private fun updateSellBuyType(type: String) {
        mBinding.mTradeTypeView.selectedOrderType(if (type == OrderViewModel.TRADE_BUY) TradeOrderType.Buy else TradeOrderType.Sell)
    }

    /**
     * 400ms 刷新
     */
    fun onCallback() {
        if (mViewModel == null) return
        updateSellBuyPriceAndState(mViewModel?.productData)
        refreshProductData()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        EventBus.getDefault().register(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(eventTag: String) {
        //检查是否闭市
        if (eventTag == NoticeConstants.Init.MARKET_CLOSE_CHECK) {
            setTvNext()
        }
    }

    /**
     * 展示保证金和可用保证金
     */
    @SuppressLint("SetTextI18n")
    private fun showMargin(margin: String) {
        if (mViewModel == null || mViewModel?.productData == null) return
        val freeMargin = mViewModel?.getFreeMargin()
        mBinding.tvMargin.text = margin.addComma()
        mBinding.tvFreeMargin.text = "${freeMargin?.addComma()} ${UserDataUtil.currencyType()}".arabicReverseTextByFlag(" ")
        if (1 == margin.mathCompTo(freeMargin)) {
            mBinding.tvFreeMargin.setTextColor(context.getColor(R.color.cf44040))
        } else {
            mBinding.tvFreeMargin.setTextColor(color_c1e1e1e_cebffffff)
        }
        mBinding.tvMarginLevel.setTextDiff("${mViewModel?.getMarginLevelAfterTrading(mViewModel?.productData, mViewModel?.inputVolume.ifNull(), mViewModel?.inputPrice.ifNull())?.addComma(2)}%")
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        hideSoftKeyboard(this, ev)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 隐藏键盘
     */
    private fun hideSoftKeyboard(rootView: View?, event: MotionEvent?) {
        try {
            val tappedView = findViewAtPosition(
                rootView ?: findViewById(R.id.clRoot),
                event?.rawX?.toInt().ifNull(),
                event?.rawY?.toInt().ifNull()
            )
            if (inputViewIds.contains(tappedView?.id).not()) {
                KeyboardUtil.hideSoftInput(this)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 查询点击的位置x，y组件
     */
    private fun findViewAtPosition(rootView: View, x: Int, y: Int): View? {
        val rect = Rect()

        // 查看rootView是否为ViewGroup，没有则没有子组件
        if (rootView !is ViewGroup) {
            rootView.getGlobalVisibleRect(rect)
            return if (rect.contains(x, y)) rootView else null
        }

        val stack = Stack<View>()
        stack.push(rootView)

        var tappedView: View? = null

        while (stack.isNotEmpty()) {
            val view = stack.pop()

            view.getGlobalVisibleRect(rect)

            if (rect.contains(x, y)) {
                tappedView = view

                if (view is ViewGroup) {
                    for (i in 0 until view.childCount) {
                        stack.push(view.getChildAt(i))
                    }
                }
            }
        }
        return tappedView
    }

    /**
     * 处理下单成功
     */
    private fun handleSuccess() {
        ToastUtil.showToast(context.getString(R.string.order_submitted))
        mBinding.tpSlView.resetTpSlInputValue()
        mViewModel?.viewModelScope?.launch {
            delay(200)
            // 刷接口
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
        }
        mViewModel?.storageProduceLots(mViewModel?.inputVolume.ifNull())
        when (mViewModel?.tradeTypeIndex) {
            0 -> EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN))

            else -> EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING))
        }
    }

    private fun showFundLackDialog() {
        if (UserDataUtil.isDemoAccount()) {
            ToastUtil.showToast(context.getString(R.string.insufficient_funds))
        } else {
            CenterActionDialog.Builder(context as Activity)
                .setTitle(context.getString(R.string.order_rejected))
                .setEndText(context.getString(R.string.deposit))
                .setContent(context.getString(R.string.insufficient_funds_to_deposit))
                .setOnEndListener {
                    NewHtmlActivity.openActivity(context, url = UrlConstants.HTML_FUND_DEPOSIT)
                }
                .build()
                .showDialog()
        }
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(context as Activity)
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnDismissListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    private fun showHintDataDialog(hintMsg: String) {
        CenterActionDialog.Builder(context as Activity)
            .setContent(hintMsg)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    private fun showCheckDelayDialog() {
        CenterActionDialog.Builder(context as Activity)
            .setTitle(
                context.getString(
                    R.string.do_you_wish_order_at_x,
                    if (mViewModel?.tradeType == "0") "${mViewModel?.productData?.ask}" else "${mViewModel?.productData?.bid}"
                )
            )
            .setContent(context.getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mViewModel?.volumeParam = mViewModel?.inputVolume.ifNull()
                mViewModel?.tpParam = mViewModel?.tpInputValue.ifNull()
                mViewModel?.slParam = mViewModel?.slInputValue.ifNull()
                mViewModel?.submitOrder(0)
            }.build()
            .showDialog()
    }
}