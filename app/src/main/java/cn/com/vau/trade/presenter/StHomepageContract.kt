
import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.KycVerifyLevelDataBean
import cn.com.vau.data.account.KycVerifyLevelObj
import cn.com.vau.data.discover.*
import io.reactivex.disposables.Disposable

/**
 * 產品列表
 */
interface StHomepageContract {

    interface Model : BaseModel {
        fun imgCloseApi(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>): Disposable
        fun strategyDiscoverListAllApi(accountId: String, baseObserver: BaseObserver<StrategyRecommendAllBean>): Disposable
        fun userQueryUserLevel(map: HashMap<String, Any>, baseObserver: BaseObserver<KycVerifyLevelDataBean>): Disposable
    }

    interface View : BaseView {
        fun tableUpdate()
        fun assetsCardUpdate()
        fun hideOperationBanner()
        fun finishRefresh()
        fun showStrategyRecommend(data: StrategyRecommendAllData?)
        fun kycGuideInfo(kycInfo: KycVerifyLevelObj?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun imgCloseApi(idList: String)
        abstract fun strategyDiscoverListAllApi(accountId: String)
        abstract fun userQueryUserLevel()
    }

}
