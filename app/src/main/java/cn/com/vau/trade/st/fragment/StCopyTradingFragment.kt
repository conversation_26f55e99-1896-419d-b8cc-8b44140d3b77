package cn.com.vau.trade.st.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.*
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.HintMaintenanceView
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentStCopyTradingBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.profile.adapter.*
import cn.com.vau.trade.st.fragment.child.*
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import kotlinx.coroutines.*
import org.greenrobot.eventbus.*

/**
 * 订单-跟单页面
 */
class StCopyTradingFragment : BaseFragment(), SDKIntervalCallback {

    private val mBinding by lazy { FragmentStCopyTradingBinding.inflate(layoutInflater) }

    private var isViewCreated: Boolean = false
    private var isUIVisible: Boolean = false

    private val c00c79c by lazy { ContextCompat.getColor(requireContext(), R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(requireContext(), R.color.cf44040) }

    private val stCopyTradingOtherFragment by lazy { StCopyTradingOtherFragment() }

    private val fragmentList by lazy {
        ArrayList<Fragment>().apply {
            add(StCopyTradingPositionsOpenFragment())
            add(StCopyTradingHistoryFragment())
            add(StCopyTradingProfitSharingFragment())
            add(stCopyTradingOtherFragment)
        }
    }

    private var mHintMaintenanceView: HintMaintenanceView? = null

    private val titleList = ArrayList<String>()

    private val statusPopup: BottomListDialog? by lazy {
        BottomListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.status))
            .setAdapter(statusAdapter)
            .build()
    }
    private val statusAdapter by lazy {
        SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false).apply {
            val titleList = arrayListOf(
                SelectBean(getString(R.string.pending_review)),
                SelectBean(getString(R.string.rejected)),
            )
            selectTitle = titleList.getOrNull(0)?.getShowItemValue() // 默认选中项
            setList(titleList)

            setOnItemClickListener { _, _, position ->
                selectTitle = titleList.getOrNull(position)?.getShowItemValue()
                // 更新对应tab标题
                val tvTab = mBinding.mTabLayout.getChildAt(3)?.findViewById<TextView>(R.id.tvTab)
                tvTab?.text = selectTitle
                // 切换对应tab的页面
                stCopyTradingOtherFragment.switchFragment(position)
                notifyItemRangeChanged(0, itemCount)
                statusPopup?.dismiss()
            }
        }
    }

    override fun onCallback() {
        if (InitHelper.isNotSuccess()) return
        showAccountInfo()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Order_St_Create_First)
        isViewCreated = true
        lazyInitView()
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding.mViewStubMarketMaintenance.setOnInflateListener { stub, inflated ->
            if (inflated is HintMaintenanceView) {
                mHintMaintenanceView = inflated
            }
        }
        PerfTraceUtil.firstFrameTrace(mBinding.root, PerfTraceUtil.StartTrace.Perf_v6_Order_St_Create_First, PerfTraceUtil.StartTrace.Perf_v6_Order_St_First_Finish)
    }

    @SuppressLint("SetTextI18n")
    override fun initParam() {

        super.initParam()
        EventBus.getDefault().register(this)

        titleList.add(getString(R.string.positions))
        titleList.add(getString(R.string.history))
        titleList.add(getString(R.string.profit_sharing))
        titleList.add(getString(R.string.pending_review))
    }

    @SuppressLint("ObsoleteSdkInt", "UseCompatLoadingForDrawables")
    private fun lazyInitView() {

        if (!isViewCreated || !isUIVisible) return

        isViewCreated = false
        isUIVisible = false

        mBinding.mViewPager2.init(fragmentList, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(
            mBinding.mViewPager2, titleList, TabType.WRAP_INDICATOR, arrayListOf(3),
            reselectedCallback = {
                if (it == 3) statusPopup?.show()
            })

        showAccountInfo()

    }

    override fun initListener() {
        super.initListener()
        mBinding.ivAccountInfoGlossary.clickNoRepeat {
            BottomListDialog.Builder(requireActivity())
                .setAdapter(PlatAdapter().apply {
                    setList(arrayListOf<HintLocalData>().apply {
                        add(HintLocalData(getString(R.string.money_in_pending_review_status), getString(R.string.the_money_allocated_please_review_tab)))
                        add(HintLocalData(getString(R.string.automatic_negative_equity_reset), getString(R.string.system_will_only_copying_applications)))
                    })
                })
                .build()
                .show()
        }
    }

    //控制是否显示系统维护页面
    private fun controlIsShowMaintenanceLayout(isShowMaintenance: Boolean) {
        if (isShowMaintenance) {
            mBinding.tvEquity.setTextDiff("...")

            mBinding.tvFloatingPnL.setTextDiff("...")
            mBinding.tvFloatingPnL.setTextColorDiff(c00c79c)

            mBinding.tvCurrency.setTextDiff("...")
            mBinding.tvBalance.setTextDiff("...")

            mBinding.tvFreeMargin.setTextDiff("...")
            mBinding.tvCredit.setTextDiff("...")
            mBinding.llOrderTab.visibility = View.GONE
            mBinding.mViewStubMarketMaintenance.isVisible = true
            val content = if (!TextUtils.isEmpty(Constants.MAINTENANCE_MSG)) "\n" + Constants.MAINTENANCE_MSG + "\n" else "\n"
            val fullContent = getString(R.string.maintenance_dialog_content_1) + content
            mHintMaintenanceView?.setContentText(fullContent)
        } else {
            mBinding.mViewStubMarketMaintenance.isVisible = false
            mBinding.llOrderTab.isVisible = true
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showAccountInfo() {

        context ?: return

        if (Constants.MARKET_MAINTAINING) {
            controlIsShowMaintenanceLayout(true)
            return
        } else {
            controlIsShowMaintenanceLayout(false)
        }

        val currencyType = UserDataUtil.currencyType()

        mBinding.tvCurrency.setTextDiff(currencyType)

        if (InitHelper.isNotSuccess()) return

        val stShareAccountBean = VAUSdkUtil.stShareAccountBean()
        val totalPnl = stShareAccountBean.followTotalHistoryProfit + stShareAccountBean.followFloatingPl
        mBinding.tvFloatingPnL.setTextColorDiff(
            if (totalPnl < 0) cf44040 else c00c79c
        )

        lifecycleScope.launch(Dispatchers.Default) {
            val equityUi = stShareAccountBean.followEquity.numCurrencyFormat2()
            val floatingPnLUi = totalPnl.numCurrencyFormat2()
            val freeMarginUi = (stShareAccountBean.followEquity - stShareAccountBean.followMarginUsed).numCurrencyFormat2()
            val balanceUi = "${stShareAccountBean.followBalance.numCurrencyFormat2()} $currencyType".arabicReverseTextByFlag(" ").ifNull()
            val creditUi = "${stShareAccountBean.followCredit.numCurrencyFormat2()} $currencyType".arabicReverseTextByFlag(" ").ifNull()
            withContext(Dispatchers.Main) {
                mBinding.tvEquity.setTextDiff(equityUi)
                mBinding.tvFloatingPnL.setTextDiff("${if (totalPnl > 0) "+" else ""}${floatingPnLUi}")

                mBinding.tvFreeMargin.setTextDiff("$freeMarginUi $currencyType".arabicReverseTextByFlag(" ").ifNull())
                mBinding.tvBalance.setTextDiff(balanceUi)
                mBinding.tvCredit.setTextDiff(creditUi)
                PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Order_St_First_Finish)
            }
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_OPEN -> {
                mBinding.mViewPager2.post {
                    mBinding.mViewPager2.currentItem = titleList.indexOfFirst {
                        it == getString(R.string.positions)
                    }
                }
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_PENDING_REVIEW -> {
                mBinding.mViewPager2.post {
                    // 当前页面切换到对应tab
                    val index = titleList.indexOfFirst {
                        it == getString(R.string.pending_review) || it == getString(R.string.rejected)
                    }
                    mBinding.mViewPager2.setCurrentItem(index, false)
                    // 设置tabLayout标题
                    val tvTab = mBinding.mTabLayout.getChildAt(3)?.findViewById<TextView>(R.id.tvTab)
                    tvTab?.text = getString(R.string.pending_review)
                    // 设置底部切换状态(待审核/已拒绝)弹框的文案
                    statusAdapter.selectTitle = getString(R.string.pending_review)
                    // StCopyTradingOtherFragment的viewPager切换到对应fragment
                    stCopyTradingOtherFragment.switchFragment(0)
                }
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_REJECTED -> {
                mBinding.mViewPager2.post {
                    // 当前页面切换到对应tab
                    val index = titleList.indexOfFirst {
                        it == getString(R.string.pending_review) || it == getString(R.string.rejected)
                    }
                    mBinding.mViewPager2.setCurrentItem(index, false)
                    // 设置tabLayout标题
                    val tvTab = mBinding.mTabLayout.getChildAt(3)?.findViewById<TextView>(R.id.tvTab)
                    tvTab?.text = getString(R.string.rejected)
                    // 设置底部切换状态(待审核/已拒绝)弹框的文案
                    statusAdapter.selectTitle = getString(R.string.rejected)
                    // StCopyTradingOtherFragment的viewPager切换到对应fragment
                    stCopyTradingOtherFragment.switchFragment(1)
                }
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_HISTORY -> {
                mBinding.mViewPager2.post {
                    mBinding.mViewPager2.currentItem = titleList.indexOfFirst {
                        it == getString(R.string.history)
                    }
                }
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 切换账户 || 退出登陆
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                showAccountInfo()
            }

            // 应用在后台放置超过一分钟
            NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M -> {
                mBinding.tvEquity.text = "..."
                mBinding.tvFloatingPnL.text = "..."
                mBinding.tvBalance.text = "..."
                mBinding.tvCredit.text = "..."
                mBinding.tvFreeMargin.text = "..."
            }
        }

    }

    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            isUIVisible = true
            lazyInitView()

            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)

        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
    }

}