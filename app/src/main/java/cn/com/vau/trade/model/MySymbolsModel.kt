package cn.com.vau.trade.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.trade.presenter.MySymbolsContract
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * Created by roy on 2018/11/6.
 */
class MySymbolsModel : MySymbolsContract.Model {
    override fun prodUpdApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().prodUpdApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun stAccountProductMyUpdApi(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().stAccountProductMyUpdApi(requestBody), baseObserver)
        return baseObserver.disposable
    }

}