package cn.com.vau.trade.kchart.pop

import android.app.Activity
import android.content.Context
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.DialogBottomKLineSettingsBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.view.SelectableItem
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog

/**
 * KLine设置弹窗
 */
class BottomKLineSettingsDialog private constructor(context: Context) : BottomDialog<DialogBottomKLineSettingsBinding>(context, DialogBottomKLineSettingsBinding::inflate) {

    private var selectLineCallback: ((Int, Boolean) -> Unit)? = null
    private var newGuideCallback: (() -> Unit)? = null

    override fun setContentView() {
        super.setContentView()
        initView()
    }

    private fun initView() {
        mContentBinding.run {
            //新手引导
            tvKNewGuide.setOnClickListener {
                newGuideCallback?.invoke()
                dismiss()
            }

            //图表显示
            rvChartDisplay.submitList(getChartDisplayList())
            rvChartDisplay.setOnItemClickListener { item: SelectableItem ->
                handleLineCallback(handleChartDisplayChoice(item))
            }

            //订单显示
            rvOrderDisplay.submitList(getOrderDisplayList())
            rvOrderDisplay.setOnItemClickListener { item: SelectableItem ->
                handleLineCallback(handleOrderDisplayChoice(item))
            }

            //图表模式
            rvChartMode.submitList(getChartModeList())
            rvChartMode.setOnItemClickListener { item: SelectableItem ->
                //小余等于Android 9，不可点击
                if (!HttpUrl.isSwitchTradingView) {
                    ToastUtil.showToast(activity.getString(R.string.the_current_system_please_system_version))
                    return@setOnItemClickListener
                }
                handleLineCallback(handleChartModeChoice(item))
            }

        }
    }

    /**
     * 图表模式列表
     */
    private fun getChartModeList(): List<SelectableItem> {
        val isSelectedProMode = HttpUrl.isSwitchTradingView && SpManager.getSelectTradingViewMode(true)
        val list = mutableListOf<SelectableItem>()
        list.add(SelectableItem.ChartModePro(context.getString(R.string.pro_mode_landscape_only), isSelectedProMode))
        return list
    }

    /**
     * 图表显示列表
     */

    private fun getChartDisplayList(): List<SelectableItem> {

        val userData = KLineDataUtils.userData
        val askLineDisplay = userData?.askLineDisplay ?: true
        val bidLineDisplay = userData?.bidLineDisplay ?: true

        return listOf(
            SelectableItem.AskPriceLine(context.getString(R.string.ask_price_line), askLineDisplay),
            SelectableItem.BidPriceLine(context.getString(R.string.bid_price_line), bidLineDisplay),
        )
    }

    /**
     * 订单显示列表
     */

    private fun getOrderDisplayList(): List<SelectableItem> {

        val userData = KLineDataUtils.userData
        val positionLineDisplay = userData?.positionLineDisplay ?: true
        val tpLineDisplay = userData?.tpLineDisplay ?: true
        val slLineDisplay = userData?.slLineDisplay ?: true

        return listOf(
            SelectableItem.OpenPositionLine(context.getString(R.string.open_position_line), positionLineDisplay),
            SelectableItem.TakeProfitLine(context.getString(R.string.take_profit_line), tpLineDisplay),
            SelectableItem.StopLossLine(context.getString(R.string.stop_loss_line), slLineDisplay),
        )
    }

    /**
     * 处理图表显示item点击
     */
    private fun handleChartDisplayChoice(clickedItem: SelectableItem): List<SelectableItem> {
        val currentList = mContentBinding.rvChartDisplay.getCurrentList()
        val toggleItem = clickedItem.toggleSelected()
        val newList = currentList.map { item ->
            if (item.title == clickedItem.title) toggleItem else item
        }
        mContentBinding.rvChartDisplay.submitList(newList)
        return newList
    }

    /**
     * 处理订单显示item点击
     * 1、选中or取消选中当前点击项
     * 2、持仓线关闭，止盈线和止损线同步关闭
     * 3、止盈线或者止损线开启，持仓线同步开启
     */
    private fun handleOrderDisplayChoice(clickedItem: SelectableItem): List<SelectableItem> {
        val currentList = mContentBinding.rvOrderDisplay.getCurrentList()
        // 1、选中or取消选中当前点击项
        val toggledList = currentList.map { item ->
            if (item.title == clickedItem.title) item.toggleSelected() else item
        }

        val newList = when {
            // 2、持仓线关闭 → 关闭止盈止损
            clickedItem is SelectableItem.OpenPositionLine && clickedItem.isSelected ->
                toggledList.map { item ->
                    when (item) {
                        is SelectableItem.TakeProfitLine -> item.copy(isSelected = false)
                        is SelectableItem.StopLossLine -> item.copy(isSelected = false)
                        else -> item
                    }
                }

            // 3、止盈/止损开启 → 开启持仓线
            (clickedItem is SelectableItem.TakeProfitLine || clickedItem is SelectableItem.StopLossLine) && !clickedItem.isSelected ->
                toggledList.map { item ->
                    if (item is SelectableItem.OpenPositionLine) item.copy(isSelected = true) else item
                }

            else -> toggledList
        }
        mContentBinding.rvOrderDisplay.submitList(newList)
        return newList
    }

    /**
     * 处理图表模式item点击
     */
    private fun handleChartModeChoice(clickedItem: SelectableItem): List<SelectableItem> {
        val currentList = mContentBinding.rvChartMode.getCurrentList()
        val newList = currentList.map { item ->
            if (item.title == clickedItem.title) item.toggleSelected() else item
        }
        mContentBinding.rvChartMode.submitList(newList)
        return newList
    }

    /**
     * 保存KLine设置状态
     */
    private fun handleLineCallback(list: List<SelectableItem>) {
        list.forEach { item ->
            when (item) {
                is SelectableItem.AskPriceLine -> {
                    selectLineCallback?.invoke(0, item.isSelected)
                }

                is SelectableItem.BidPriceLine -> {
                    selectLineCallback?.invoke(1, item.isSelected)
                }

                is SelectableItem.OpenPositionLine -> {
                    selectLineCallback?.invoke(2, item.isSelected)
                }

                is SelectableItem.TakeProfitLine -> {
                    selectLineCallback?.invoke(3, item.isSelected)
                }

                is SelectableItem.StopLossLine -> {
                    selectLineCallback?.invoke(4, item.isSelected)
                }

                is SelectableItem.ChartModePro -> {
                    SpManager.putSelectTradingViewMode(item.isSelected)
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V345.TRADE_KLINE_DISPLAY_MODE_BUTTON_CLICK, bundleOf(
                            "Account_type" to KLineActivity.getPointAccountType(),
                            "Mode" to "Pro"
                        )
                    )
                }
            }
        }

    }

    fun onSelectLineListener(listener: ((Int, Boolean) -> Unit)?) {
        this.selectLineCallback = listener
    }

    fun newGuideClick(listener: (() -> Unit)?) {
        this.newGuideCallback = listener
    }

    class Builder(activity: Activity) : IBuilder<DialogBottomKLineSettingsBinding, Builder>(activity) {
        override fun createDialog(context: Context): IDialog<DialogBottomKLineSettingsBinding> {
            return BottomKLineSettingsDialog(activity)
        }

        override fun build(): BottomKLineSettingsDialog {
            return super.build() as BottomKLineSettingsDialog
        }
    }

}

