package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.ViewTradeTitleBinding
import cn.com.vau.trade.data.ConnectState
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.setFontG400
import cn.com.vau.util.setFontG500
import cn.com.vau.util.setFontG600

/**
 * Created by array on 2025/5/9 15:20
 * Desc: 交易标题
 */
class TradeTitleView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy { ViewTradeTitleBinding.inflate(LayoutInflater.from(context), this) }

    private var onTitleViewClick: (() -> Unit)? = null
    private var onSettingClick: (() -> Unit)? = null
    private var onMessageClick: (() -> Unit)? = null

    init {
        initFont()
        initListener()
    }

    private fun initFont() {
        mBinding.tvAccountStatus.setFontG600()
        mBinding.tvConnecting.setFontG500()
    }

    fun initListener() {

        mBinding.root.clickNoRepeat {
            onTitleViewClick?.invoke()
        }

        mBinding.ivSettingTrade.clickNoRepeat {
            onSettingClick?.invoke()
        }

        mBinding.ivMessage.clickNoRepeat {
            onMessageClick?.invoke()
        }

    }

    /**
     * 点击事件：标题
     */
    fun setOnTitleViewClick(onTitleViewClick: (() -> Unit)?) {
        this.onTitleViewClick = onTitleViewClick
    }

    /**
     * 点击事件：设置
     */
    fun setOnSettingClick(onSettingClick: (() -> Unit)?) {
        this.onSettingClick = onSettingClick
    }

    /**
     * 点击事件：消息
     */
    fun setOnMessageClick(onMessageClick: (() -> Unit)?) {
        this.onMessageClick = onMessageClick
    }

    /**
     * 设置账号ID
     */
    fun setAccountId(accountId: String) {
        mBinding.tvAccountId.text = accountId
    }

    /**
     * 设置账号状态文案
     */
    fun setAccountStatus(status: String) {
        mBinding.tvAccountStatus.text = status
    }

    fun getConnectVisibility(): Int {
        return mBinding.tvConnecting.visibility
    }

    /**
     * 设置连接文案
     */
    @SuppressLint("SetTextI18n")
    fun setConnectText(state: ConnectState) {
        mBinding.tvConnecting.text = when (state) {
            is ConnectState.Connecting -> {//连接中...
                "${context.getString(R.string.connecting)}..."
            }

            is ConnectState.Connected -> {//已连接
                context.getString(R.string.connected)
            }

            is ConnectState.SlowConnection -> {//网络缓慢
                context.getString(R.string.slow_connection)
            }

            is ConnectState.Reconnecting -> {//重连中
                context.getString(R.string.reconnecting)
            }
        }
    }

    /**
     * 获取连接文案
     */
    fun getConnectText(): String {
        return mBinding.tvConnecting.text.toString()
    }

    /**
     * 获取连接显示控件
     */
    fun getTvConnect(): TextView {
        return mBinding.tvConnecting
    }

    /**
     * 设置连接显示状态
     * @param visibility View.VISIBLE View.INVISIBLE
     */
    fun setConnectVisibility(visibility: Int) {
        mBinding.tvConnecting.visibility = visibility
    }

    /**
     * 设置小红点显示状态
     * @param isVisible true 显示 false 隐藏
     */
    fun setRedDotVisibility(isVisible: Boolean) {
        mBinding.ivRedDot.isVisible = isVisible
    }

    /**
     * 设置AccountNum显示状态
     * @param isVisible true 显示 false 隐藏
     */
    fun setAccountNumVisibility(isVisible: Boolean) {
        mBinding.tvAccountId.isVisible = isVisible
    }

}
