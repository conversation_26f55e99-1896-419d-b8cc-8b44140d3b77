package cn.com.vau.trade.st.fragment.strategy

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.*
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.view.share.*
import cn.com.vau.data.trade.StTradeHistoryOrdersBean
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.trade.activity.*
import cn.com.vau.trade.st.adapter.StHistoryOrderAdapter
import cn.com.vau.trade.st.model.*
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView
import org.greenrobot.eventbus.*

/**
 * 策略订单页 -- 历史订单
 */
class StStrategyOrdersHistoryFragment : BaseMvvmFragment<FragmentRefreshBinding, StStrategyOrderHistoryViewModel>() {

    private val activityViewModel by activityViewModels<StStrategyOrdersViewModel>()
    private val mVsNoData by lazy {
        NoDataView(requireContext()).apply {
            setHintMessage(getString(R.string.no_history))
        }
    }
    private val mAdapter by lazy {
        StHistoryOrderAdapter(Constants.ST_STRATEGY_ORDERS_HISTORY).apply {
            setEmptyView(mVsNoData)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.mRecyclerView.adapter = mAdapter
    }

    override fun initData() {
        super.initData()
        mViewModel.refreshHistoryListData(activityViewModel.baseData?.portfolioId, false)
    }

    override fun createObserver() {
        super.createObserver()

        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, mAdapter, mBinding.mRefreshLayout)
    }

    override fun initListener() {
        super.initListener()

        mAdapter.setNbOnItemClickListener { _, _, position ->
            val bean = mAdapter.getItem(position)
            if (bean.itemType == StTradeHistoryOrdersBean.Data.PortfolioDealsData.TYPE_ORDER_HISTORY) {
                activity?.let {
                    StStrategyHistoryDetailsActivity.open(it, bean)
                }
            }
        }

        mAdapter.setNbOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.ivKLine -> {
                    openActivity(KLineActivity::class.java, Bundle().apply {
                        putString(
                            Constants.PARAM_PRODUCT_NAME, mAdapter.data.getOrNull(position)?.symbol.ifNull()
                        )
                    })
                }

                R.id.ivShare -> {
                    mAdapter.data.getOrNull(position)?.let {
                        ShareHelper.orderHistoryShare(requireActivity() as AppCompatActivity, it.symbol, it.openPrice, it.closePrice, it.profit,  if ("DealSell" == it.dealAction) "1" else "0", it.closeTime.ifNull().toString())
                    }
                }

                R.id.tvVolume -> {
                    mAdapter.data.getOrNull(position)?.let {
                        if (it.positionVolume.mathCompTo(it.closedVolume) == 0) {
                            StStrategyHistoryDetailsActivity.open(requireActivity(), it)
                            return@let
                        }
                        val bundle = Bundle()
                        bundle.putString(Constants.PARAM_ORDER_NUMBER, it.positionId)
                        bundle.putString(Constants.PARAM_ORDER_OPEN_TIME, it.openTimeMT4)
                        bundle.putString(Constants.PARAM_ORDER_PORTFOLIO_ID, it.portfolioId)
                        openActivity(CloseHistoryActivity::class.java, bundle)
                    }

                }
            }
        }

        // 下拉刷新
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refreshHistoryListData(activityViewModel.baseData?.portfolioId, false)
        }
        // 加载更多
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMoreHistoryListData(activityViewModel.baseData?.portfolioId)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.Init.DATA_SUCCESS_FOLLOWERS_ORDER_ST -> {
                mViewModel.refreshHistoryListData(activityViewModel.baseData?.portfolioId, false)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}
