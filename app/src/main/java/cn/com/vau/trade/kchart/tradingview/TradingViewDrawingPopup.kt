package cn.com.vau.trade.kchart.tradingview

import android.content.Context
import cn.com.vau.R
import cn.com.vau.common.view.SpanItemDecoration
import cn.com.vau.common.view.popup.adapter.TradingViewDrawingAdapter
import cn.com.vau.common.view.popup.bean.DrawingBean
import cn.com.vau.databinding.PopupTradingViewDrawingNewBinding
import cn.com.vau.util.dp2px
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.lxj.xpopup.core.*

/**
 * Filename: TradingViewDrawingPopup
 * Author: GG
 * Date: 2023/9/6 0006 9:45
 * Description:
 */
class TradingViewDrawingPopup(context: Context, private val bridge: TradingViewInterface?) : DrawerPopupView(context) {

    companion object {
        var selectId = -1
        const val DRAWING_BRUSH = 11
        const val DRAWING_HIGHLIGHTER = 12
        const val DRAWING_ERASER = 13
        const val DRAWING_TREND = 21
        const val DRAWING_HORIZONTAL = 22
        const val DRAWING_VERTICAL = 23
        const val DRAWING_PATH = 24
        const val DRAWING_RECTANGLE = 31
        const val DRAWING_POLYLINE = 32
    }

    private var binding: PopupTradingViewDrawingNewBinding? = null
    private val drawingAdapter = TradingViewDrawingAdapter(mutableListOf<DrawingBean>().apply {
        add(DrawingBean(DRAWING_BRUSH, R.attr.imgTradingViewDrawingBrush, context.getString(R.string.brush)))
        add(DrawingBean(DRAWING_HIGHLIGHTER, R.attr.imgTradingViewDrawingHighLighter, context.getString(R.string.highlighter)))
        add(DrawingBean(DRAWING_ERASER, R.attr.imgTradingViewDrawingEraser, context.getString(R.string.eraser)))
    })
    private val lineAdapter = TradingViewDrawingAdapter(mutableListOf<DrawingBean>().apply {
        add(DrawingBean(DRAWING_TREND, R.attr.imgTradingViewLineTrend, context.getString(R.string.trend_line)))
        add(DrawingBean(DRAWING_HORIZONTAL, R.attr.imgTradingViewLineHorizontal, context.getString(R.string.horizontal_line)))
        add(DrawingBean(DRAWING_VERTICAL, R.attr.imgTradingViewLineVertical, context.getString(R.string.vertical_line)))
        add(DrawingBean(DRAWING_PATH, R.attr.imgTradingViewLinePath, context.getString(R.string.path)))
    })
    private val shapeAdapter = TradingViewDrawingAdapter(mutableListOf<DrawingBean>().apply {
        add(DrawingBean(DRAWING_RECTANGLE, R.attr.imgTradingViewShapeRectangle, context.getString(R.string.rectangle)))
        add(DrawingBean(DRAWING_POLYLINE, R.attr.imgTradingViewShapePolyline, context.getString(R.string.polyline)))
    })

    override fun getImplLayoutId(): Int {
        return R.layout.popup_trading_view_drawing_new
    }

    override fun onCreate() {
        super.onCreate()
        binding = PopupTradingViewDrawingNewBinding.bind(popupImplView)
        binding?.apply {
            rvDrawing.adapter = drawingAdapter
            rvDrawing.addItemDecoration(SpanItemDecoration(14f.dp2px()))
            rvLine.adapter = lineAdapter
            rvLine.addItemDecoration(SpanItemDecoration(14f.dp2px()))
            rvShape.adapter = shapeAdapter
            rvShape.addItemDecoration(SpanItemDecoration(14f.dp2px()))
        }

        drawingAdapter.setOnItemClickListener(onItemClickListener)
        lineAdapter.setOnItemClickListener(onItemClickListener)
        shapeAdapter.setOnItemClickListener(onItemClickListener)
    }

    val onItemClickListener =
        OnItemClickListener { adapter, _, position ->
            selectId = (adapter as TradingViewDrawingAdapter).getItem(position).id
            notifyAllAdapterChanged()
            bridge?.callDrawing(selectId)
            dismiss()
        }

    private fun notifyAllAdapterChanged() {
        drawingAdapter.notifyDataSetChanged()
        lineAdapter.notifyDataSetChanged()
        shapeAdapter.notifyDataSetChanged()
    }

    override fun show(): BasePopupView {
        selectId = -1
        notifyAllAdapterChanged()
        return super.show()
    }
}