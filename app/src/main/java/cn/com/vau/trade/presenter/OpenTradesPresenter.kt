package cn.com.vau.trade.presenter

import OpenTradesContract
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.*
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.*
import cn.com.vau.data.trade.StTradePositionUpdateBean
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class OpenTradesPresenter : OpenTradesContract.Presenter() {

    var currentPosition = 0
    var currentOrderId = "0"

    override fun tradeOrdersClose(orderBean: ShareOrderData, checkDelay: Int) {

        mView?.showNetDialog()

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("order", orderBean.order)

        var volume = orderBean.volume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        )
        if (volume.contains("."))
            volume = volume.split(".")[0]
        jsonObject.addProperty("volume", volume)

        jsonObject.addProperty("price", orderBean.closePrice ?: "")
        jsonObject.addProperty("checkDelay", checkDelay)
        jsonObject.addProperty("lasttime", orderBean.lasttime)

        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("maxOffset", "*********")
        jsonObject.addProperty("symbol", orderBean.symbol)
        jsonObject.addProperty("cmd", orderBean.cmd)
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("st", VAUSdkUtil.serverTimeMillis ?: "")

        val startTimeMillisOrdersClose = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "close order:#${orderBean.order}  volume:${orderBean.volume}",
            startTimeMillisOrdersClose
        )

        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mModel?.tradeOrdersClose(requestBody, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: BaseBean?) {
                // ******** 订单状态已变化
                mView?.hideNetDialog()

                if (dataBean?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        dataBean.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    tradeAccountLogin()
                    ToastUtil.showToast(dataBean.info)
                }

                // 价格波动较大
                if (dataBean?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        dataBean.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )

                    mView?.showCheckDelayDialog(orderBean)
                    return
                }
                if (dataBean?.code == "********") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        dataBean.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )

                    mView?.showHintDataDialog(dataBean.info ?: "")
                    return
                }
                if (dataBean?.code != "200") {
                    DealLogUtil.saveFailedDealLog(
                        "close order:#${orderBean.order}",
                        dataBean?.code.toString(),
                        "close",
                        startTimeMillisOrdersClose
                    )
                    ToastUtil.showToast(dataBean?.info)
                    return
                }

                DealLogUtil.saveSuccessDealLog(
                    "close order:#${orderBean.order}",
                    "close",
                    startTimeMillisOrdersClose
                )
                handleBalance(orderBean)
                mView?.deletePastOrder()
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                DealLogUtil.saveFailedDealLog(
                    "close order:#${orderBean.order}",
                    "-1",
                    "close",
                    startTimeMillisOrdersClose
                )
                mView?.hideNetDialog()
            }

        })

        // 神策自定义埋点(v3500)
        sensorsTrack(orderBean)

    }

    /**
     *手动处理余额，暂时解决平仓瞬间净值显示问题
     */
    private fun handleBalance(data: ShareOrderData?) {
        if (UserDataUtil.isStLogin()) {
            VAUSdkUtil.stShareAccountBean().balance =  VAUSdkUtil.stShareAccountBean().balance.mathAdd(data?.profit?:0)
            return
        }
        VAUSdkUtil.shareAccountBean().balance = VAUSdkUtil.shareAccountBean().balance.mathAdd(data?.profit?:0)
    }

    override fun stTradePositionClose(orderBean: ShareOrderData) {

        mView?.showNetDialog()

        val jsonObject = JsonObject()
        jsonObject.addProperty(
            "portfolioId", UserDataUtil.stMasterPortfolioId()
        )
        jsonObject.addProperty("positionId", orderBean.stOrder.ifNull())
        jsonObject.addProperty("volume", orderBean.volume.ifNull())
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mModel?.stTradePositionClose(requestBody, object : BaseObserver<BaseBean>() {
            override fun onNext(baseData: BaseBean?) {
                mView?.hideNetDialog()
                if ("200" != baseData?.code ) {
                    ToastUtil.showToast(baseData?.msg)
                    return
                }
                handleBalance(orderBean)
                mView?.deletePastOrder()
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

        // 神策自定义埋点(v3500)
        sensorsTrack(orderBean)
    }

    /**
     * 批量平仓
     */
    override fun tradeOrdersBatchCloseV2(orderList:CopyOnWriteArrayList<ShareOrderData>) {
        mView?.showNetDialog()
        val map = mutableMapOf<String, Any>(
            "token" to UserDataUtil.tradeToken(),
            "login" to UserDataUtil.accountCd(),
            "serverId" to UserDataUtil.serverId(),
            "orders" to getParamOrders(orderList)
        )
        val dataObject = JsonObject().apply {
            addProperty("data", GsonUtil.toJson(map))
        }
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mModel?.tradeOrdersBatchCloseV2(requestBody, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: BaseBean?) {
                mView?.hideNetDialog()
                if ("200" != baseBean?.code) {
                    ToastUtil.showToast(baseBean?.msg)
                } else {
                    mView?.submitSuccessDialog()
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }

        })

    }

    override fun tradeAccountLogin() {

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("password", UserDataUtil.mt4PWD())
        jsonObject.addProperty("token", UserDataUtil.loginToken())
//        jsonObject.addProperty("accountType", Integer.valueOf(UserDataUtil.mt4State()) - 1)

        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())

        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        mModel?.tradeAccountLogin(requestBody, object : BaseObserver<TradeAccountLoginBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: TradeAccountLoginBean?) {
                if ("200" != baseBean?.code) {
                    return
                }
                UserDataUtil.setTradeToken(baseBean.data?.token ?: "")
            }

        })

    }

    override fun tradePositionBatchClose(orderList:CopyOnWriteArrayList<ShareOrderData>) {
        mView?.showNetDialog()
        val map = mapOf(
            "portfolioId" to UserDataUtil.stMasterPortfolioId(),
            "positionIds" to orderList.map {
                it.stOrder
            }
        )
        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        mModel.tradePositionBatchClose(requestBody,object : BaseObserver<BaseBean>(){
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(t: BaseBean?) {
                mView?.hideNetDialog()
                if ("200" != t?.code) {
                    ToastUtil.showToast(t?.msg)
                } else {
                    mView?.submitSuccessDialog()
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }

        })
    }

    override fun userSetItemset(value: Int) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        paramMap["code"] = Constants.KEY_FAST_CLOSE
        paramMap["value"] = value
        mModel?.userSetItemset(paramMap, object : BaseObserver<BaseBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: BaseBean?) {
                if ("00000000" == dataBean?.resultCode) {
                    UserDataUtil.setFastCloseState("1")
                }
            }

        })
    }

    override fun tradeOrdersUpdate(orderData: ShareOrderData?) {
        orderData?.order ?: return

        var handleCount = orderData?.volume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        )
        if (handleCount.contains("."))
            handleCount = handleCount.split(".")[0]

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("price", orderData?.openPrice)
        jsonObject.addProperty("sl",  "0")
        jsonObject.addProperty("tp",  "0" )
        jsonObject.addProperty("order", orderData?.order)
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("cmd", orderData?.cmd)
        jsonObject.addProperty("symbol", orderData?.symbol)
        jsonObject.addProperty("volume", handleCount)
        jsonObject.addProperty("serverId", UserDataUtil.serverId())

        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "modify order:#${orderData?.order}  " +
                    "take profit:0" +
                    "stop loss:0}",
            "modify", startTimeMillis
        )

        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())

        val requestBody =
            jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mView?.showNetDialog()
        mModel.tradeOrdersUpdate(requestBody,object : BaseObserver<BaseBean>() {
            override fun onNext(baseBean: BaseBean) {
                when (baseBean.code) {
                    // token 过期
                    "********" -> {
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderData?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        mView?.showTokenErrorDialog(baseBean.info)
                    }
                    "********" -> {
                        mView?.hideNetDialog()
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderData?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        mView?.showHintDataDialog(baseBean.info ?: "")
                    }
                    "200" -> {
                        DealLogUtil.saveSuccessDealLog(
                            "modify order:#${orderData?.order}",
                            "modify",
                            startTimeMillis
                        )
                        mView?.hideNetDialog()
                        ToastUtil.showToast(baseBean.info)
                        EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                    }
                    else -> {
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderData?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        mView?.hideNetDialog()
                        ToastUtil.showToast(baseBean.info)
                    }
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${orderData?.order}",
                    "-1", "modify",
                    startTimeMillis
                )
                mView?.hideNetDialog()
            }
        })
    }

    override fun stTradePositionUpdate(orderBean: ShareOrderData?) {
        mView?.showNetDialog()

        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("positionId", orderBean?.stOrder ?: "")
        jsonObject.addProperty("stopLoss", "")
        jsonObject.addProperty("takeProfit", "")

        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mModel?.stTradePositionUpdate(requestBody,object :BaseObserver<StTradePositionUpdateBean>(){
            override fun onNext(openData: StTradePositionUpdateBean?) {
                mView?.hideNetDialog()
                if ("200" != openData?.code){
                    ToastUtil.showToast(openData?.msg)
                    return
                }
                ToastUtil.showToast(openData.msg)
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }

        })
    }


    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack(orderBean: ShareOrderData) {
        val isBuy = OrderUtil.isBuyOfOrder(orderBean.cmd)
        // 交易详情页按钮点击 -> 交易详情页点击关闭平仓按钮时触发
        SensorsDataUtil.track(SensorsConstant.V3500.TRADE_CLOSE_SUBMIT, JSONObject().apply {
            // 交易类型
            put(SensorsConstant.Key.TRADE_TYPE, SensorsHelper.getTradeType())
            // 交易产品组
            put(SensorsConstant.Key.PRODUCT_GROUP, "")
            // 交易产品
            put(SensorsConstant.Key.PRODUCT_SYMBOL, orderBean.symbol.ifNull())
            // 交易方向
            put(SensorsConstant.Key.TRADE_DIRECTION, if (isBuy) "Buy" else "Sell")
            // 按钮名称
            put(SensorsConstant.Key.BUTTON_NAME, "Close")
            // 订单ID
            put(SensorsConstant.Key.ORDER_ID, orderBean.order.ifNull())
            // 是否选择止盈
            put(SensorsConstant.Key.IS_PROFIT, if (orderBean.takeProfit.mathCompTo("0") == 1) 1 else 0)
            // 是否选择止损
            put(SensorsConstant.Key.IS_LOSS, if (orderBean.stopLoss.mathCompTo("0") == 1) 1 else 0)
            // 交易方式
            put(SensorsConstant.Key.TRADE_MODE, "")
            // 账户币种
            put(SensorsConstant.Key.ACCOUNT_CURRENCY, UserDataUtil.currencyType())
        })
    }

    private fun getParamOrders(orderList:CopyOnWriteArrayList<ShareOrderData>): ArrayList<MutableMap<String, String?>> {
        val list = arrayListOf<MutableMap<String, String?>>()
        orderList.forEach {
            val map = mutableMapOf(
                "symbol" to it.symbol,
                "price" to it.closePrice,
                "order" to it.order
            )
            list.add(map)
        }
        return list
    }

    fun traceEditButClick(butName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, butName)
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONCARD_EDITBTN_CLICK, properties)
    }

    fun traceKlineClick(){
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONCARD_CANDLESTBTN_CLICK)
    }
    fun traceOrderIdCopyClick(){
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONCARD_ORDERIDCOPYTBTN_CLICK)
    }

    fun traceDepositClick() {
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONPAGE_DEPOSIT_CLICK)
    }

    fun traceNewOrderClick() {
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONPAGE_NEWORDER_CLICK)
    }

}