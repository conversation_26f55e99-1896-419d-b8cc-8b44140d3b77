package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.ActivityModifiedCloseConfigurationEndBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.adapter.CloseConfigEndAdapter
import cn.com.vau.trade.bean.CloseConfigSymbolBean
import cn.com.vau.trade.model.ModifiedCloseConfigurationEndViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * 批量平仓 -- 筛选订单 End 页
 */
class ModifiedCloseConfigurationEndActivity :
    BaseMvvmActivity<ActivityModifiedCloseConfigurationEndBinding, ModifiedCloseConfigurationEndViewModel>(),
    SDKIntervalCallback {

    private val selectAll by lazy {
        getString(R.string.select_all)
    }
    private val unSelectAll by lazy {
        getString(R.string.unselect_all)
    }
    private val draw_shape_c1e1e1e_cebffffff_r100 by lazy {
        ContextCompat.getDrawable(
            this,
            R.drawable.draw_shape_c1e1e1e_cebffffff_r100
        )
    }
    private val draw_shape_c0a1e1e1e_c0affffff_r100 by lazy {
        ContextCompat.getDrawable(
            this,
            R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
    }
    private val c00c79c by lazy { ContextCompat.getColor(this, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(this, R.color.cf44040) }
    private val color_c1e1e1e_cebffffff by lazy {
        AttrResourceUtil.getColor(
            this,
            R.attr.color_c1e1e1e_cebffffff
        )
    }

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cebffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    val adapter by lazy {
        CloseConfigEndAdapter()
    }

    override fun onCallback() {
        updateMainOrderViewData()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.batchCloseLiveData.observe(this) {
            showSuccessDialog()
        }
    }

    private fun showSuccessDialog() {
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.order_submitted_successfully))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .setOnSingleButtonListener {
                EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN))
                ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
                finish()
            }
            .build()
            .showDialog()
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {

        mBinding.mHeaderBar.setTitleText(getString(R.string.close_configuration))
            .setEndText(getString(R.string.unselect_all))
            .setEndTextClickListener {
                selectAllOrNot()
            }

        mBinding.tvEstimatedPnlTitle.text = "${getString(R.string.estimated_pnl)}:"
        mBinding.tvCurrencyType.text = UserDataUtil.currencyType()

        mBinding.stvCloseAll.text = getString(R.string.by_clicking_close_you_the_disclaimer)
        mBinding.stvCloseAll
            .set(getString(R.string.disclaimer), isShowUnderLine = true)

        initRecyclerView()
        checkSelected()

    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        mBinding.mRecyclerView.adapter = adapter
        adapter.setNbOnItemClickListener { adapter, view, position ->
            adapter.data[position]?.let {
                selectPosition(it as CloseConfigSymbolBean)
            }
            adapter.notifyItemChanged(position, "")
        }
        initFilterData()
    }

    private fun selectPosition(closeConfigSymbolBean: CloseConfigSymbolBean) {
        closeConfigSymbolBean.isSelected = closeConfigSymbolBean.isSelected.not()
        closeConfigSymbolBean.shareOrderData?.let {
            if (closeConfigSymbolBean.isSelected) {
                CloseConfigHelper.selectedOrderList.add(it)

            } else {
                CloseConfigHelper.selectedOrderList.remove(it)
            }
        }
        calculationEstimated()
        setTitleRightText()
        checkSelected()
        if (closeConfigSymbolBean.isSelected.not()) {
            sensorsTrack("", closeConfigSymbolBean.shareOrderData?.symbol ?: "")
        }
    }

    private fun sensorsTrack(buttonName: String, symbolName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        properties.put(SensorsConstant.Key.SYMBOL_NAME, symbolName)
        SensorsDataUtil.track(SensorsConstant.V3510.CLOSE_CONFIGUATION_PAGE_STEP2_BTN_CLICK, properties)
    }

    private fun sensorsTrackClick() {
        SensorsDataUtil.track(SensorsConstant.V3510.CLOSE_CONFIGUATION_PAGE_STEP2_CLOSE_ALL_BTN_CLICK)
    }

    private fun sensorsTrackDialog(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        SensorsDataUtil.track(SensorsConstant.V3510.CLOSE_CONFIGUATION_PAGE_STEP3_CLOSE_POSITION_MENU_BTN_CLICK, properties)
    }

    private fun calculationEstimated() {
        var sum = "0.0"
        CloseConfigHelper.selectedOrderList.forEach {
            sum = sum.mathAdd(it.totalProfit)
        }
        mBinding.tvEstimatedPnl.text = sum.numCurrencyFormat()
        if (sum.mathCompTo("0") == 1) {
            mBinding.tvEstimatedPnl.setTextColor(c00c79c)
        } else if (sum.mathCompTo("0") == -1) {
            mBinding.tvEstimatedPnl.setTextColor(cf44040)
        } else {
            mBinding.tvEstimatedPnl.setTextColor(color_c1e1e1e_cebffffff)
        }
    }

    private fun checkSelected() {
        mBinding.tvNext.setTextColor(if (CloseConfigHelper.hasSelectedOrders()) color_cebffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
        mBinding.tvNext.background =
            if (CloseConfigHelper.hasSelectedOrders()) draw_shape_c1e1e1e_cebffffff_r100 else draw_shape_c0a1e1e1e_c0affffff_r100
    }

    override fun initListener() {
        super.initListener()

        mBinding.tvNext.setOnClickListener(this)
        mBinding.stvCloseAll.clickNoRepeat {
            openActivity(BatchCloseDisclaimerActivity::class.java)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ivLeft -> finish()
            R.id.tvNext -> {
                if (CloseConfigHelper.hasSelectedOrders().not()) {
                    ToastUtil.showToast(getString(R.string.please_select_your_order))
                    return
                }
                showCloseDialog()
                sensorsTrackClick()
            }
        }
    }

    private fun showCloseDialog() {
        CenterActionDialog.Builder(this)
            .setTitle(getString(R.string.close_position))
            .setContent(getString(R.string.please_confirm_to_any_be_affected))
            .setOnStartListener {
                sensorsTrackDialog("Cancel")
            }
            .setOnEndListener {
                mViewModel.tradeOrdersBatchCloseV2()
                sensorsTrackDialog("Confirm")
            }
            .build()
            .showDialog()
    }

    private fun selectAllOrNot() {
        if (CloseConfigHelper.selectedOrderList.size == adapter.data.size) {
            unSellAll()
        } else {
            selectAll()
        }
        adapter.notifyDataSetChanged()
        calculationEstimated()
        checkSelected()
        setTitleRightText()

    }

    private fun selectAll() {
        mViewModel.filterSymbols.forEach {
            it.isSelected = true
            it.shareOrderData?.let { shareOrder ->
                if (CloseConfigHelper.selectedOrderList.contains(shareOrder).not()) {
                    CloseConfigHelper.selectedOrderList.add(shareOrder)
                }
            }
        }
        sensorsTrack("select All", "")
    }

    private fun setTitleRightText() {
        mBinding.mHeaderBar.setEndText(if (CloseConfigHelper.selectedOrderList.size == adapter.data.size) unSelectAll else selectAll)
    }

    private fun unSellAll() {
        mViewModel.filterSymbols.forEach {
            it.isSelected = false
            it.shareOrderData?.let { shareOrder ->
                CloseConfigHelper.selectedOrderList.remove(shareOrder)
            }
        }
        sensorsTrack("Unselect All", "")
    }

    /**
     * 更新主订单数据
     */
    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    fun updateMainOrderViewData() {
        for (i in mViewModel.filterSymbols.indices) {
            adapter.notifyItemChanged(i, "")
        }
        calculationEstimated()
    }

    private fun initFilterData() {
        adapter.setList(mViewModel.filterSymbols)
        calculationEstimated()
    }

    override fun onMsgEvent(eventTag: String) {
        super.onMsgEvent(eventTag)
        when (eventTag) {
            // 持仓订单有变化
            NoticeConstants.Init.DATA_SUCCESS_ORDER -> {

            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
        CloseConfigHelper.clearSelectedOrderList()
    }

}