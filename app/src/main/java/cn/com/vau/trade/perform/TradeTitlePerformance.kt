package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.application.LinkStateManager
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.ext.launchActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.DefaultUiLinkStateCallback
import cn.com.vau.databinding.FragmentOrderThemeBinding
import cn.com.vau.page.notice.activity.NoticeActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.trade.activity.TradeSettingActivity
import cn.com.vau.trade.data.ConnectState
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * Created by array on 2025/5/12 14:31
 * Desc: 标题
 */
class TradeTitlePerformance(
    val fragment: Fragment,
    private val mBinding: FragmentOrderThemeBinding
) : AbsPerformance() {
    /**
     * 连接状态监听
     */
    private var stateChangeCallback: DefaultUiLinkStateCallback? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        EventBus.getDefault().register(this)
        initView()
        initListener()
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        refreshData()
    }

    fun initView() {
        stateChangeCallback = DefaultUiLinkStateCallback(mBinding.mTitleView.getTvConnect(), fragment)
        stateChangeCallback?.run { LinkStateManager.registerCallback(this) }
    }

    fun initListener() {
        with(mBinding.mTitleView) {
            setOnTitleViewClick { fragment.launchActivity<AccountManagerActivity>(requireLogin = true) }
            setOnSettingClick { fragment.launchActivity<TradeSettingActivity>(requireLogin = true) }
            setOnMessageClick { handleMessageClick() }
        }
    }

    /**
     * 点击跳转：消息
     */
    private fun handleMessageClick() {
        fragment.launchActivity<NoticeActivity>(requireLogin = true)
        traceOrderPageMessagesIconClick()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        EventBus.getDefault().unregister(this)
        if (VauApplication.abOptNetParallel) {
            stateChangeCallback?.run {
                LinkStateManager.unregisterCallback(this)
            }
        }
    }

    // 显示用户信息（登陆根据账户类型显示，未登录显示登陆按钮）
    @SuppressLint("SetTextI18n")
    private fun setAccountInfo() {
        mBinding.mTitleView.setAccountId(UserDataUtil.accountCd())
        mBinding.mTitleView.setAccountStatus(getAccountStatus())
        mBinding.mTitleView.setAccountNumVisibility(UserDataUtil.isLiveVirtualAccount().not())
        mBinding.mTitleView.setRedDotVisibility(SpManager.getRedPointState(false))
    }

    /**
     * 账户状态
     */
    private fun getAccountStatus(): String {
        return fragment.getString(
            if (UserDataUtil.isLiveAccount()) {
                R.string.live
            } else if (UserDataUtil.isLiveVirtualAccount()) {
                R.string.live
            } else {
                R.string.demo
            }
        )
    }

    private fun refreshConnection() {
        if (VauApplication.abOptNetParallel) {
            if (!LinkStateManager.isStateLinkSuccess()) {
                updateConnectionState(ConnectState.Connecting, View.VISIBLE)
            }
        } else {
            //原逻辑，产品列表正在请求，才视为正在连接状态。
            if (InitHelper.stepNum() == 1 || InitHelper.stepNum() == 2) {
                updateConnectionState(ConnectState.Connecting, View.VISIBLE)
            }
        }
    }

    fun refreshData() {
        setAccountInfo()
        refreshConnection()
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            //加载中
            NoticeConstants.Init.APPLICATION_START -> {
                if (VauApplication.abOptNetParallel) return
                updateConnectionState(ConnectState.Connecting, View.VISIBLE)
            }

            //长链接连接成功
            NoticeConstants.Init.WS_SUCCESS_CONNECT -> {
                if (VauApplication.abOptNetParallel) return
                //原逻辑：如果WebSocket成功，则显示已连接
                with(mBinding.mTitleView) {
                    if (getConnectVisibility() != View.VISIBLE) {
                        return
                    }
                    setConnectText(ConnectState.Connected)
                    fragment.lifecycleScope.launch {
                        delay(1000)
                        mBinding.mTitleView.setConnectVisibility(View.INVISIBLE)
                    }
                }

            }

            //心跳 / 网络缓慢
            NoticeConstants.WS.SOCKET_HEARTBEAT_ERROR -> {
                if (VauApplication.abOptNetParallel) return
                updateConnectionState(ConnectState.SlowConnection, View.VISIBLE)
            }

            //心跳 / 正常
            NoticeConstants.WS.SOCKET_HEARTBEAT_NORMAL -> {
                if (VauApplication.abOptNetParallel) return
                with(mBinding.mTitleView) {
                    if (getConnectText() != fragment.getString(R.string.slow_connection)) {
                        return
                    }
                    setConnectVisibility(View.INVISIBLE)
                }
            }

            //Socket断开连接
            NoticeConstants.WS.SOCKET_DISCONNECTED -> {
                if (VauApplication.abOptNetParallel) return
                updateConnectionState(ConnectState.Reconnecting, View.VISIBLE)
            }

            //切换账户 || 退出登录
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                refreshData()
            }

            //显示小红点
            NoticeConstants.WS.POINT_REMIND_MSG_SHOW -> mBinding.mTitleView.setRedDotVisibility(true)

            //隐藏小红点
            NoticeConstants.WS.POINT_REMIND_MSG_HIDE -> mBinding.mTitleView.setRedDotVisibility(false)
        }
    }

    /**
     * 更新连接状态
     * @param state 连接状态
     * @param visibility 连接状态显示控件
     */
    private fun updateConnectionState(state: ConnectState, visibility: Int) {
        with(mBinding.mTitleView) {
            setConnectText(state)
            setConnectVisibility(visibility)
        }
    }

    /**
     * 回传有unread消息还是都read(有红点表示有unread)，值回传如：unread、read
     */
    private fun traceOrderPageMessagesIconClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.ORDER_PAGE_MESSAGES_ICON_CLICK, JSONObject().apply {
            put(SensorsConstant.Key.MESSAGES_STATUS, if (SpManager.getRedPointState(false)) "unread" else "read")
        })
    }

}