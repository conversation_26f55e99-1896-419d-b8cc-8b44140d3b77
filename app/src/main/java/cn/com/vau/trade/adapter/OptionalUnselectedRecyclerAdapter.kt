package cn.com.vau.trade.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.init.ShareProductData


/**
 * Created by roy on 2018/10/30.
 * 自选页面
 */
class OptionalUnselectedRecyclerAdapter(
        var mContext: Context,
        var dataList: MutableList<ShareProductData>
) : RecyclerView.Adapter<OptionalUnselectedRecyclerAdapter.ViewHolder>() {

    private var monItemClickListener: OnItemClickListener? = null

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val shareSymbolData = dataList.elementAtOrNull(position)?:return

        holder.tvName.text = shareSymbolData.symbol

        holder.itemView.setOnClickListener {
            monItemClickListener?.onItemClickListener(position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
            ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_optional_unselected, parent, false))

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view){
        val tvName = view.findViewById<TextView>(R.id.tvName)
    }

    fun setOnItemClickListener(monItemClickListener: OnItemClickListener) {
        this.monItemClickListener = monItemClickListener
    }

    interface OnItemClickListener {
        fun onItemClickListener(position: Int)
    }

}