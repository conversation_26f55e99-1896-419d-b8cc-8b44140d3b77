package cn.com.vau.trade.kchart.pop

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.DialogBottomKLinePriceChangeBinding
import cn.com.vau.trade.bean.kchart.ChartPriceChange
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.numFormat
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog

@SuppressLint("ViewConstructor")
class BottomKLinePriceChangeDialog private constructor(
    context: Context,
    private val data: ShareProductData?,
) : BottomDialog<DialogBottomKLinePriceChangeBinding>(context, DialogBottomKLinePriceChangeBinding::inflate) {

    private var priceBean: ChartPriceChange? = null

    override fun setContentView() {
        super.setContentView()
        mContentBinding.priceBar1H.setUnit("1h")
        mContentBinding.priceBar1D.setUnit("1D")
        mContentBinding.priceBar30D.setUnit("1M")
    }

    fun fillData(priceBean: ChartPriceChange?) {
        priceBean?.let {
            this.priceBean = it
            mContentBinding.run {
                priceBar1H.setDigit(data?.digits.ifNull(3))
                priceBar1H.setMin(it.hourLow.toFloatCatching())
                priceBar1H.setMax(it.hourHigh.toFloatCatching())

                priceBar1D.setDigit(data?.digits.ifNull(3))
                priceBar1D.setMin(it.dayLow.toFloatCatching())
                priceBar1D.setMax(it.dayHigh.toFloatCatching())

                priceBar30D.setDigit(data?.digits.ifNull(3))
                priceBar30D.setMin(it.monthLow.toFloatCatching())
                priceBar30D.setMax(it.monthHigh.toFloatCatching())
            }
        }
    }

    @SuppressLint("SetTextI18n")
    fun refresh() {
        if (isShow) {
            mContentBinding.run {
                if (data != null) {
                    tvSellPrice.text = if (data.bidUI == "-") Constants.DOUBLE_LINE else data.bidUI
                    val rose = data.rose
                    val add = if (rose > 0) "+" else ""
                    tvRate.text = "($add${data.roseUI}%)"
                    val diff = data.diff
                    val add2 = if (diff > 0) "+" else ""
                    tvDiff.text = add2 + data.diffUI

                    if (diff < 0) {
                        tvDiff.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                        tvRate.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                    } else {
                        tvDiff.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                        tvRate.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                    }
                }

                if (priceBean != null) {
                    val bid = data?.bid.ifNull()
                    // 1D
                    val close1D = priceBean?.yesterdayClose.toDoubleCatching(-1.0)
                    if (close1D == -1.0) {
                        tv1DValue.text = "--"
                        tv1DValue.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
                    } else {
                        val diff = bid - close1D
                        val rate = diff.div(close1D).times(100)
                        tv1DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                        if (diff < 0) {
                            tv1DValue.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                        } else {
                            tv1DValue.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                        }
                    }
                    // 7D
                    val close7D = priceBean?.weekClose.toDoubleCatching(-1.0)
                    if (close7D == -1.0) {
                        tv7DValue.text = "--"
                        tv7DValue.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
                    } else {
                        val diff = bid - close7D
                        val rate = diff.div(close7D).times(100)
                        tv7DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                        if (diff < 0) {
                            tv7DValue.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                        } else {
                            tv7DValue.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                        }
                    }
                    // 30D
                    val close30D = priceBean?.monthClose.toDoubleCatching(-1.0)
                    if (close30D == -1.0) {
                        tv30DValue.text = "--"
                        tv30DValue.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
                    } else {
                        val diff = bid - close30D
                        val rate = diff.div(close30D).times(100)
                        tv30DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                        if (diff < 0) {
                            tv30DValue.setTextColor(ContextCompat.getColor(context, R.color.cf44040))
                        } else {
                            tv30DValue.setTextColor(ContextCompat.getColor(context, R.color.c00c79c))
                        }
                    }

                    priceBar1H.updateCurrent(bid)
                    priceBar1D.updateCurrent(bid)
                    priceBar30D.updateCurrent(bid)
                }
            }
        }
    }

    class Builder(activity: Activity) :
        IBuilder<DialogBottomKLinePriceChangeBinding, Builder>(activity) {
        private var data: ShareProductData? = null
        fun setData(data: ShareProductData?): Builder {
            this.data = data
            return this
        }

        override fun build(): BottomKLinePriceChangeDialog {
            return super.build() as BottomKLinePriceChangeDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomKLinePriceChangeBinding> {
            return BottomKLinePriceChangeDialog(
                context,
                data
            )
        }
    }
}