package cn.com.vau.trade.view

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewStub
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.transition.ChangeBounds
import androidx.transition.Fade
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.view.kchart.viewbeans.BrokenLineNewOrder
import cn.com.vau.common.view.kchart.viewbeans.ChartType
import cn.com.vau.common.view.kchart.viewbeans.Coordinates
import cn.com.vau.common.view.kchart.viewbeans.IndicatorLine
import cn.com.vau.common.view.kchart.views.ChartViewImp
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.KChartBean
import cn.com.vau.data.trade.KChartBean.DataBean.ChartsBean
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.MyExtremeCalculator
import cn.com.vau.trade.kchart.MyFocusedCoordinateAdapter
import cn.com.vau.trade.viewmodel.TimeSharingViewModel
import cn.com.vau.util.AppUtil
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.formatProductPrice
import cn.com.vau.util.ifNull
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.toLongCatching
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Calendar

class TimeSharingChartView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver {

    private var mVsChart: ViewStub? = null
    private var mVsNoData: ViewStub? = null
    private var tvSymbol: TextView? = null
    private var ivArrow: ImageView? = null
    private var splitView2: View? = null
    private var timeIndicatorLine: IndicatorLine? = null
    private var timeBuyLine: IndicatorLine? = null
    private var timeBrokenLine: BrokenLineNewOrder? = null
    private var timeChartAdapter: MyFocusedCoordinateAdapter? = null
    private var chartTimeView: ChartViewImp? = null
    private var scaleTimeLeft: TextView? = null
    private var scaleTimeMiddle: TextView? = null
    private var scaleTimeRight: TextView? = null

    private var isExpand = false    // 是否是展开状态
    private var isDataError = false // 显示显示缺省布局
    private var isUpdatingSymbol = false    // 是否正在更新产品数据
    private var mViewModel: TimeSharingViewModel? = null
    private var listener: ((state: Boolean) -> Unit)? = null

    init {
        inflate(context, R.layout.layout_time_sharing_chart_view, this)
        mVsChart = findViewById(R.id.mVsChart)
        mVsNoData = findViewById(R.id.mVsNoData)
        tvSymbol = findViewById(R.id.tvSymbol)
        ivArrow = findViewById(R.id.ivArrow)
        splitView2 = findViewById(R.id.splitView2)
        val activity = context as? FragmentActivity
        activity?.let {
            it.lifecycle.addObserver(this)
            mViewModel = ViewModelProvider(it)[TimeSharingViewModel::class.java]
            initView()
            observ(it)
        }
    }

    /**
     * 暴露方法 设置数据
     */
    fun setData(data: ShareProductData?, isUpdating: Boolean = false) {
        if (isUpdating) {
            isUpdatingSymbol = true
        }
        mViewModel?.data = data
        mViewModel?.data?.let { dataBean ->
            // 保证原始价格不为0
            if (dataBean.originalAsk == 0f) dataBean.originalAsk = dataBean.ask
            if (dataBean.originalBid == 0f) dataBean.originalBid = dataBean.bid
        }
        tvSymbol?.text = "${data?.symbol} ${ContextCompat.getString(context, R.string.chart_timesharing)}".arabicReverseTextByFlag(" ")
        mViewModel?.requestKChartData()
    }

    fun setExpandStateListener(callback: ((state: Boolean) -> Unit)? = null) {
        this.listener = callback
    }

    /**
     * 暴露方法 更新行情信息
     */
    fun updateQuotation() {
        if (!isUpdatingSymbol && isExpand) {
            updateProdInfo()
        }
    }

    private fun initView() {
        tvSymbol?.clickNoRepeat(800) {
            val transitionSet = TransitionSet().apply {
                addTransition(Fade().apply {
                    duration = 300
                })
                addTransition(ChangeBounds().apply {
                    duration = 300
                })
                interpolator = DecelerateInterpolator()
            }
            TransitionManager.beginDelayedTransition(this, transitionSet)
            if (isExpand) {
                mVsChart?.isVisible = false
                mVsNoData?.isVisible = false
                splitView2?.isInvisible = true
                ivArrow?.rotation = 0f
            } else {
                mVsChart?.isVisible = true
                mVsNoData?.isVisible = false || isDataError
                splitView2?.isInvisible = false
                ivArrow?.rotation = 180f
                // 获取K线数据
                mViewModel?.requestKChartData()
                // 埋点
                SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_BOTTOM_CHART_CLICK)
            }
            isExpand = !isExpand
            listener?.invoke(isExpand)
        }

        mVsChart?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                chartTimeView = inflated.findViewById(R.id.chartTimeView)
                scaleTimeLeft = inflated.findViewById(R.id.scaleTimeLeft)
                scaleTimeMiddle = inflated.findViewById(R.id.scaleTimeMiddle)
                scaleTimeRight = inflated.findViewById(R.id.scaleTimeRight)
                // 加载数据
                loadDataToChart()
            }
        })

        mVsNoData?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(context, R.attr.mainLayoutBg))
                vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(context, R.attr.icNoConnection))
                vs.mNoDataView.setPadding(0, 0, 0, 25.dp2px())
                vs.mNoDataView.setHintMessage(ContextCompat.getString(context, R.string.something_went_wrong_try_again))
                vs.mNoDataView.setBottomBtnText(ContextCompat.getString(context, R.string.try_again))
                vs.mNoDataView.setBottomBtnViewClickListener {
                    // 点击重试
                    mViewModel?.requestKChartData()
                }
            }
        })
    }

    private fun observ(activity: FragmentActivity) {
        // 空布局显示
        // 这里为true有可能是：接口失败、接口返回空集、网络异常 导致，所以不能清除图表数据
        // 现象：缺省布局显示后，收缩再打开Mini分时图时短暂显示图表后再次显示缺省布局，看似造成的图表闪动
        // 解决：全局记录缺省布局显示状态，当展开时，如果缺省布局之前已显示，则同步显示缺省布局
        mViewModel?.noDataLiveData?.observe(activity) {
            // 图表展开时才去处理缺省布局的显隐
            if (isExpand) {
                mVsNoData?.isVisible = it
                isDataError = it
            }
        }

        // 事件处理
        activity.lifecycleScope.launch {
            mViewModel?.eventFlow?.collectLatest {
                when (it) {
                    // 重新计算延时
                    NoticeConstants.SEND_EVENT_TAG_KLINE_DELAY_TIMER -> {
                        delayTimer()
                    }

                    NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_CHART -> {
                        // 接口获取到了数据 准备加载到组件
                        isUpdatingSymbol = false
                        loadDataToChart()
                    }
                }
            }
        }
    }

    private fun initTimeChartView() {
        timeIndicatorLine = mViewModel?.getIndicatorLine(context)
        timeBuyLine = mViewModel?.getBuyLine(context)
        chartTimeView?.setOnTouchListener(object : View.OnTouchListener {
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                return true
            }
        })
        chartTimeView?.setCoordinateBackground(ChartUIParamUtil.coordinateBgColor)
        // 分时图
        chartTimeView?.setMarginRight(ChartUIParamUtil.coordinateMarginRightDp.dp2px())
        // 设置坐标系文字居中模式
        chartTimeView?.setCoordinateTextGravity(Coordinates.TextGravity.VERTICAL_CENTER_LINE)
        chartTimeView?.type = ChartType.TIME_CHART
        chartTimeView?.setCoordinateLineColor(ChartUIParamUtil.coordinateLineColor)
        chartTimeView?.setCoordinateTextColor(ChartUIParamUtil.coordinateScaleColor)
        chartTimeView?.setCoordinateLatitudeNum(ChartUIParamUtil.newOrderTimeCoordinateLatitudeNum)
        chartTimeView?.setCoordinateLongitudeNum(ChartUIParamUtil.timeCoordinateLongitudeNum)
        chartTimeView?.setYPaddingPercent(ChartUIParamUtil.paddingPercent)
        timeChartAdapter = MyFocusedCoordinateAdapter(mViewModel?.data)
        timeChartAdapter?.setKeepNums(ChartUIParamUtil.digits)
        chartTimeView?.setCoordinateScaleAdapter(timeChartAdapter)
        chartTimeView?.setXScaleAdapter { scaleIndex, listIndex, indexToEnd, _ ->
            timeChartXScaleHandle(scaleIndex, listIndex, indexToEnd)
        }
        chartTimeView?.invalidate()

        // 分时图现价线
        timeIndicatorLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val timeShareBean = dataList?.getOrNull(dataList.size - 1) as? KChartBean.DataBean.TimeChartBean
                return timeShareBean?.close?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始卖价（未加点）
            }
        })
        // 分时图买价线
        timeBuyLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val timeShareBean = dataList?.getOrNull(dataList.size - 1) as? KChartBean.DataBean.TimeChartBean
                return timeShareBean?.originalAsk?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始买价（未加点）
            }
        })
    }

    private fun timeChartXScaleHandle(scaleIndex: Int, listIndex: Int, indexToEnd: Int) {
        val bean = mViewModel?.timeShareList?.getOrNull(listIndex)
        var time = TimeUtil.formatDateTime(bean?.mt4TimeMills.ifNull(), "dd/MM HH:mm")
        if (scaleIndex == 0) {
            scaleTimeLeft?.text = time
        } else if (scaleIndex == 1) {
            scaleTimeMiddle?.text = time
        } else {
            scaleTimeRight?.text = time
        }
    }

    // 获取到了数据 准备加载到组件
    private fun loadDataToChart() {
        if (mViewModel?.mainOrderList?.isNotEmpty() == true && chartTimeView != null) {
            refreshKchart()
        }
    }

    // 开启定时，每隔一时间段请求一次数据
    private fun startTimer() {
        mHandler.removeMessages(1000)
        mHandler.sendEmptyMessageDelayed(1000, (60 * 1000).toLong())
    }

    private fun delayTimer() {
        mHandler.removeMessages(1000)
        mHandler.sendEmptyMessageDelayed(1000, caclDelayTime())
    }

    private fun caclDelayTime(): Long {
        val second = 60L // 每个时间段的总计秒数
        val calendar = Calendar.getInstance()
        val currentMinute = calendar.get(Calendar.MINUTE)
        val currentSecond = calendar.get(Calendar.SECOND)
        return (second - (currentMinute * 60 + currentSecond) % second) * 1000
    }

    private fun refreshKchart() {
        ChartUIParamUtil.chartShowEndPosition = mViewModel?.mainOrderList?.size.ifNull()
        // 分时图
        initTimeChartView()
        initTimeShareData()
        initTimeShareChart()
        updateProdInfo()
    }

    // 初始化分时图数据
    private fun initTimeShareData() {
        mViewModel?.timeShareList?.clear()
        mViewModel?.timeShareList?.addAll(mViewModel?.getTimeShareDataList() ?: emptyList())
        timeIndicatorLine?.setKeepNums(ChartUIParamUtil.digits)
        timeIndicatorLine?.dataList = mViewModel?.timeShareList
        timeIndicatorLine?.setShow(ChartUIParamUtil.newOrderSellLineDisplay)
        timeBuyLine?.setKeepNums(ChartUIParamUtil.digits)
        timeBuyLine?.dataList = mViewModel?.timeShareList
        timeBuyLine?.setShow(ChartUIParamUtil.newOrderBuyLineDisplay)
    }

    // 初始化分时图
    private fun initTimeShareChart() {
        chartTimeView?.removeAllChildren()
        timeBrokenLine = mViewModel?.getTimeBrokenLine(context)
        timeBrokenLine?.requestFocused()
        timeBrokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(chartTimeView, mViewModel?.data)) // 实际分时图的最大最小值显示不是通过这个计算类计算的 而是在BrokenLine中自定义方法计算的
        var rate = 0f
        val dataBean = mViewModel?.data
        if (dataBean != null) {
            rate = dataBean.rose
        }
        if (rate < 0) {
            timeBrokenLine?.setLineColor(ContextCompat.getColor(context, R.color.ce35728))
            timeBrokenLine?.setLineFillColor(ContextCompat.getColor(context, R.color.ce35728), ContextCompat.getColor(context, R.color.transparent), 100)
        } else if (rate >= 0) {     // 这里先与iOS保持一致，后面由产品决定是否会展示灰色
            timeBrokenLine?.setLineColor(ContextCompat.getColor(context, R.color.c00c79c))
            timeBrokenLine?.setLineFillColor(ContextCompat.getColor(context, R.color.c00c79c), ContextCompat.getColor(context, R.color.transparent), 100)
        } else {
            val isLight = AppUtil.isLightTheme()
            timeBrokenLine?.setLineColor(
                if (isLight) ContextCompat.getColor(context, R.color.c731e1e1e)
                else ContextCompat.getColor(context, R.color.c61ffffff)
            )
            timeBrokenLine?.setLineFillColor(
                if (isLight) ContextCompat.getColor(context, R.color.c731e1e1e)
                else ContextCompat.getColor(context, R.color.c61ffffff), ContextCompat.getColor(context, R.color.transparent), 100
            )
        }
        chartTimeView?.addChild(timeBrokenLine)
        chartTimeView?.addChild(timeIndicatorLine)
        chartTimeView?.addChild(timeBuyLine)
    }

    // 利用行情数据画出下一根蜡烛图
    private fun drawNextCandle() {
        val dataBean = mViewModel?.data
        if (dataBean?.marketClose == false && mViewModel?.mainOrderList?.isNotEmpty() == true) {
            val lastBean = mViewModel?.mainOrderList?.getOrNull(mViewModel?.mainOrderList?.size.ifNull() - 1)
            val newBean = ChartsBean()
            newBean.open = dataBean.bid.toDouble()
            newBean.close = dataBean.bid.toDouble()
            newBean.high = dataBean.bid.toDouble()
            newBean.low = dataBean.bid.toDouble()
            newBean.volume = 0.0
            newBean.timestamp = (lastBean?.timestamp?.toLongCatching().ifNull() + 60).toString()
            mViewModel?.mainOrderList?.add(newBean)
            refreshKchart()
        }
    }

    private fun updateProdInfo() {
        if (InitHelper.isNotSuccess()) return
        val dataBean = mViewModel?.data ?: return
        if (chartTimeView == null) return
        val mainList = mViewModel?.mainOrderList
        // 更改蜡烛图数据
        if (mainList == null || mainList.isEmpty()) return
        val mainBean = mainList.getOrNull(mainList.size - 1)
        val timeShareList = mViewModel?.timeShareList
        if (dataBean.bid != 0f && dataBean.originalBid != 0f) {
            mainBean?.close = dataBean.bid.toDouble()
            if (timeShareList?.isNotEmpty() == true) {
                if (mViewModel?.timeOrderList?.size.ifNull() >= timeShareList.size) {
                    mViewModel?.timeOrderList?.set(timeShareList.size - 1, dataBean.originalBid.toString())
                }
                val timeShareBean = mViewModel?.timeShareList?.getOrNull(mViewModel?.timeShareList?.size.ifNull() - 1)
                timeShareBean?.close = dataBean.originalBid.toDouble()
                timeShareBean?.originalAsk = dataBean.originalAsk.toDouble()
                chartTimeView?.requestSyncDataWithFocused()
                chartTimeView?.invalidate()
            }
            if (dataBean.bid > mainBean?.high.ifNull()) mainBean?.high = dataBean.bid.toDouble()
            if (dataBean.bid < mainBean?.low.ifNull()) mainBean?.low = dataBean.bid.toDouble()
        }
    }

    private val mHandler = Handler(Looper.getMainLooper(), object : Handler.Callback {
        override fun handleMessage(msg: Message): Boolean {
            when (msg.what) {
                1000 -> {
                    val dataBean = mViewModel?.data
                    if (dataBean?.ask == 0f && dataBean.bid == 0f) return true
                    mViewModel?.isAutoRefresh = true
                    if (KLineDataUtils.isFrontPortrait) {
                        startTimer()
                        mViewModel?.requestKChartData()
                        drawNextCandle()
                    }
                }
            }
            return true
        }
    })

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        mHandler.removeCallbacksAndMessages(null)
    }
}