package cn.com.vau.trade.kchart.tradingview

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.RectF
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.appcompat.widget.*
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.*
import cn.com.vau.databinding.DialogDrawerTradingViewSettingNewBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.base.DrawerDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.github.tifezh.kchartlib.helper.bean.*
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.enums.PopupPosition
import java.util.*

/**
 * Filename: TradingViewSettingPopup
 * Author: GG
 * Date: 2023/9/4 0004 14:49
 * Description:
 */
@SuppressLint("ViewConstructor")
class DrawerTradingViewSettingDialogNew private constructor(context: Context, onDismissListener: (() -> Unit)? = null) : DrawerDialog<DialogDrawerTradingViewSettingNewBinding>(context, DialogDrawerTradingViewSettingNewBinding::inflate, onDismissListener = onDismissListener) {

    private val data: KLineViewSettingData by lazy { KLineViewSettingData.getHistoryData() }
    private var selectedTabIndex: Int = -1

    var stillOpenTabIndex: Int = -1

    private val mainTabAdapter: TabAdapter by lazy {
        TabAdapter(data.mainSelectType)
    }

    private val subTabAdapter: TabAdapter by lazy {
        TabAdapter(data.subSelectType)
    }

    override fun setContentView() {
        super.setContentView()

        mContentBinding.run {
            // 设置main 的 tab
            setMainTab()
            // 设置sub 的 tab
            setSubTab()
            // 设置line
            setLine()
            setClickListener()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun DialogDrawerTradingViewSettingNewBinding.setMainTab() {
        viewMain.tabRV.layoutManager = LinearLayoutManager(context).apply {
            orientation = LinearLayoutManager.HORIZONTAL
        }
        viewMain.tabRV.adapter = mainTabAdapter
        mainTabAdapter.setList(data.mainMap.keys.toList())

        mainTabAdapter.setNbOnItemClickListener { _, _, position ->
            mainTabAdapter.data.getOrNull(position)?.let {
                randerMainTab(it as KlineMainEnum)
                indicatorBuryPoint("Main", it.getShowName())
            }
        }

        randerMainTab(data.mainSelectType, true)
        // 设置 TabLayout 监听器
        tlMain.setOnTabSelectedListener {
            viewMain.nsView.isVisible = it != 0
        }
    }

    @SuppressLint("SetTextI18n")
    private fun DialogDrawerTradingViewSettingNewBinding.setSubTab() {
        viewSub.tabRV.layoutManager = LinearLayoutManager(context).apply {
            orientation = LinearLayoutManager.HORIZONTAL
        }
        viewSub.tabRV.adapter = subTabAdapter
        subTabAdapter.setList(data.subMap.keys.toList())

        subTabAdapter.setNbOnItemClickListener { _, _, position ->
            subTabAdapter.data.getOrNull(position)?.let {
                randerSubTab(it as KlineOtherEnum)
                indicatorBuryPoint("Sub", it.getShowName())
            }
        }

        randerSubTab(data.subSelectType, true)
        // 设置 TabLayout 监听器
        tlSub.setOnTabSelectedListener {
            viewSub.nsView.isVisible = it != 0
        }
    }

    private fun DialogDrawerTradingViewSettingNewBinding.setLine() {
        cbAsk.isChecked = data.line.ask.status == 1
        cbBid.isChecked = data.line.bid.status == 1
        cbTake.isChecked = data.line.tp.status == 1
        cbStop.isChecked = data.line.sl.status == 1
        cbOpen.isChecked = data.line.position.status == 1
    }

    private fun DialogDrawerTradingViewSettingNewBinding.setClickListener() {
        clMainView.post {
            if (stillOpenTabIndex != -1) {
                val tabOpenIndex = stillOpenTabIndex
                stillOpenTabIndex = -1
//                LogUtil.d("wj", "still: $tabOpenIndex")
                selectedTab(tabOpenIndex, tabOpenIndex)
            }
        }

        ivMainMore.setOnClickListener { selectedTab(0) }
        tvMainTitle.setOnClickListener { selectedTab(0) }
        ivSubMore.setOnClickListener { selectedTab(1) }
        tvSubTitle.setOnClickListener { selectedTab(1) }
        ivLineMore.setOnClickListener { selectedTab(2) }
        tvLineTitle.setOnClickListener { selectedTab(2) }
        cbAsk.setOnCheckedChangeListener { _, isChecked ->
            data.line.ask.status = if (isChecked) 1 else 0
            lineBuryPoint("Ask", if (isChecked) "On" else "Off")
        }
        cbBid.setOnCheckedChangeListener { _, isChecked ->
            data.line.bid.status = if (isChecked) 1 else 0
            lineBuryPoint("Bid", if (isChecked) "On" else "Off")
        }
        cbTake.setOnCheckedChangeListener { _, isChecked ->
            data.line.tp.status = if (isChecked) 1 else 0
            /** 止盈线或者止损线开启，持仓线同步开启 */
            if (isChecked) {
                cbOpen.isChecked = true
                data.line.position.status = 1
            }
            lineBuryPoint("Open", if (isChecked) "On" else "Off")
        }
        cbStop.setOnCheckedChangeListener { _, isChecked ->
            data.line.sl.status = if (isChecked) 1 else 0
            /** 止盈线或者止损线开启，持仓线同步开启 */
            if (isChecked) {
                cbOpen.isChecked = true
                data.line.position.status = 1
            }
            lineBuryPoint("TP", if (isChecked) "On" else "Off")
        }
        cbOpen.setOnCheckedChangeListener { _, isChecked ->
            data.line.position.status = if (isChecked) 1 else 0
            /** 持仓线关闭，止盈线和止损线同步关闭 */
            if (isChecked.not()) {
                cbTake.isChecked = false
                data.line.tp.status = 0
                cbStop.isChecked = false
                data.line.sl.status = 0
            }
            lineBuryPoint("SL", if (isChecked) "On" else "Off")
        }

        viewMain.apply {
            tvReset.setOnClickListener {
                mainDataReset()
            }
            tvDetail.setOnClickListener {}
        }

        viewSub.apply {
            tvReset.setOnClickListener {
                subDataReset()
            }
            tvDetail.setOnClickListener {}
        }
    }

    /**
     * 重置主指标对应tab 的数据
     */
    private fun mainDataReset() {
        data.mainDataReset(mainTabAdapter.selectType)
        randerMainTab(mainTabAdapter.selectType as? KlineMainEnum, true)
    }

    /**
     * 重置副指标对应tab 的数据
     */
    private fun subDataReset() {
        data.subDataReset(subTabAdapter.selectType)
        randerSubTab(subTabAdapter.selectType as? KlineOtherEnum, true)
    }

    private fun lineBuryPoint(selectLine: String, toggle: String) {
        val bundle = Bundle()
        bundle.putString("Account_type", KLineActivity.getPointAccountType())
        bundle.putString("Mode", "Pro-horizontal")
        bundle.putString("Line", selectLine)
        bundle.putString("Toggle", toggle)
        LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK, bundle)
    }

    private fun indicatorBuryPoint(category: String, indicator: String) {
        val bundle = Bundle()
        bundle.putString("Account_type", KLineActivity.getPointAccountType())
        bundle.putString("Mode", "Pro-horizontal")
        bundle.putString("Category", category)
        bundle.putString("Indicators", indicator)
        LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_INDICATORS_BUTTON_CLICK, bundle)
    }

    private fun selectedTab(type: Int, stillOpenIndex: Int = -1) {
        if (selectedTabIndex != 0) {
//            LogUtil.d("wj", "main ${if (type == 0) "打开" else "关闭"}")
            mainDrawerVisible(type == 0)
        } else {
//            LogUtil.d("wj", "main 判断关闭")
            if (0 != stillOpenIndex) {
//                LogUtil.d("wj", "main 关闭")
                selectedTabIndex = -1
                mainDrawerVisible(false)
            }
        }

        if (selectedTabIndex != 1) {
//            LogUtil.d("wj", "sub ${if (type == 1) "打开" else "关闭"}")
            subDrawerVisible(type == 1)
        } else {
//            LogUtil.d("wj", "sub 判断关闭")
            if (1 != stillOpenIndex) {
//                LogUtil.d("wj", "sub 关闭")
                selectedTabIndex = -1
                subDrawerVisible(false)
            }
        }

        if (selectedTabIndex != 2) {
//            LogUtil.d("wj", "line ${if (type == 2) "打开" else "关闭"}")
            lineDrawerVisible(type == 2)
        } else {
//            LogUtil.d("wj", "line 判断关闭")
            if (2 != stillOpenIndex) {
//                LogUtil.d("wj", "line 关闭")
                selectedTabIndex = -1
                lineDrawerVisible(false)
            }
        }
    }

    private fun mainDrawerVisible(visible: Boolean) {
        if (visible) {
            mContentBinding.ivMainMore.rotation = 0f
            selectedTabIndex = 0
            mContentBinding.groupMain.isVisible = true
        } else {
            mContentBinding.ivMainMore.rotation = 180f
            mContentBinding.groupMain.isVisible = false
        }
    }

    private fun subDrawerVisible(visible: Boolean) {
        TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())
        if (visible) {
            mContentBinding.ivSubMore.rotation = 0f
            selectedTabIndex = 1
            mContentBinding.groupSub.isVisible = true
        } else {
            mContentBinding.ivSubMore.rotation = 180f
            mContentBinding.groupSub.isVisible = false
        }
    }

    private fun lineDrawerVisible(visible: Boolean) {
        TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())
        if (visible) {
            mContentBinding.ivLineMore.rotation = 0f
            selectedTabIndex = 2
            mContentBinding.groupLine.isVisible = true
        } else {
            mContentBinding.ivLineMore.rotation = 180f
            mContentBinding.groupLine.isVisible = false
        }
    }

    private fun randerMainTab(type: KlineMainEnum?, isFirst: Boolean = false) {
        if (mainTabAdapter.selectType == type && !isFirst)
            return
        mContentBinding.viewMain.run {
            val item = data.mainMap[type]
            kseView.setKlineSettingItem(item)
            tvReset.isVisible = !item?.itemList.isNullOrEmpty()
            tvDetail.text = item?.desc
        }
        data.mainSelectType = type
        mainTabAdapter.selectType = type
        mainTabAdapter.notifyDataSetChanged()
    }

    private fun randerSubTab(type: KlineOtherEnum?, isFirst: Boolean = false) {
        if (subTabAdapter.selectType == type && !isFirst)
            return
        mContentBinding.viewSub.apply {
            val item = data.subMap[type]
            kseView.setKlineSettingItem(item)
            tvReset.isVisible = !item?.itemList.isNullOrEmpty()
            tvDetail.text = item?.desc
        }
        data.subSelectType = type
        subTabAdapter.selectType = type
        subTabAdapter.notifyDataSetChanged()
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        hideSoftKeyboard(event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        hideSoftKeyboard(event)
        return super.onTouchEvent(event)
    }

    /**
     * 隐藏键盘
     */
    private fun hideSoftKeyboard(event: MotionEvent) {
        try {
            if (event.action == MotionEvent.ACTION_DOWN) {
                val focusView: View? = activity.currentFocus
                //除了点击EditText自身区域的其他任何区域，都将键盘收起
                if (focusView?.windowToken != null && !isTouchView(event, focusView)) {
                    KeyboardUtil.hideSoftInput(this)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 是否触摸了当前焦点控件
     * @param event
     * @param focusView
     * @return
     */
    fun isTouchView(event: MotionEvent?, focusView: View?): Boolean {
        if (null == event || null == focusView) {
            return false
        }
        val x = event.x
        val y = event.y
        val outLocation = IntArray(2)
        focusView.getLocationOnScreen(outLocation)
        val rectF = RectF(
            outLocation[0].toFloat(),
            outLocation[1].toFloat(),
            (outLocation[0] + focusView.width).toFloat(),
            (outLocation[1] + focusView.height).toFloat()
        )
        return x >= rectF.left && (x <= rectF.right) && y >= rectF.top && y <= rectF.bottom
    }

    override fun show(): BasePopupView {
        if (stillOpenTabIndex != -1) {
            val tabOpenIndex = stillOpenTabIndex
            stillOpenTabIndex = -1
            selectedTab(tabOpenIndex, tabOpenIndex)
        }
        return super.show()
    }

    override fun beforeDismiss() {
        super.beforeDismiss()
        // 保存数据到本地
        data.save()
        LogEventUtil.setLogEvent(
            BuryPointConstant.V345.TRADE_KLINE_PRO_HORIZONTAL_TOOLS_EDIT_BUTTON_CLICK, bundleOf(
                "Account_type" to KLineActivity.getPointAccountType(),
            )
        )
    }

    fun getKlineSettingData(): KLineViewSettingData {
        return data
    }

    override fun dismiss() {
        if (KeyboardUtil.isSoftInputVisible(activity)) {
            KeyboardUtil.hideSoftInput(activity)
        } else {
            super.dismiss()
        }
    }

    class Builder(activity: Activity) : IBuilder<DialogDrawerTradingViewSettingNewBinding, Builder>(activity) {

        override fun getPopupPosition(): PopupPosition {
            val locale: Locale = LanguageHelper.getAppLocale()
            val isArabic = locale.language == "ar" || locale.language == "ara"
            return if (isArabic) {
                PopupPosition.Left
            } else {
                PopupPosition.Right
            }
        }

        override fun createDialog(context: Context): DrawerTradingViewSettingDialogNew {
            return DrawerTradingViewSettingDialogNew(activity, config.onDismissListener)
        }

        override fun build(): DrawerTradingViewSettingDialogNew {
            setViewMode(true)
            return super.build() as DrawerTradingViewSettingDialogNew
        }
    }

    inner class TabAdapter(var selectType: KlineEnum?) : BaseQuickAdapter<KlineEnum, BaseViewHolder>(R.layout.item_recycler_trading_view_parameters_tab) {
        override fun convert(holder: BaseViewHolder, item: KlineEnum) {
            holder.getViewOrNull<AppCompatTextView>(R.id.tabTitle)?.let { cb ->
                cb.text = item.getShowName()
                if (selectType?.getValue() == item.getValue()) {
                    cb.setFontG600()
                    cb.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e))
                    cb.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100))
                } else {
                    cb.setFontG500()
                    cb.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
                    cb.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100))
                }
            }
        }
    }

}