package cn.com.vau.trade.st.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.init.StShareStrategyData
import cn.com.vau.util.*

/**
 * 正在跟随策略
 */
class StStrategyFollowingRecyclerAdapter(
    var mContext: Context,
    var dataList: List<StShareStrategyData>
) : RecyclerView.Adapter<StStrategyFollowingRecyclerAdapter.ViewHolder>() {

    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(mContext, R.color.cf44040) }
    private val initiating by lazy {ContextCompat.getString(mContext,R.string.initiating)}
    private val color_c0a1e1e1e_c0affffff by lazy {
        AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff)
    }
    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(mContext, R.attr.color_c731e1e1e_c61ffffff)
    }
    private val color_c1e1e1e_cebffffff by lazy {
        AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff)
    }
    private val pending_close by lazy { mContext.getString(R.string.pending_close) }
    private val manage by lazy { mContext.getString(R.string.manage) }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val holder = ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_recycler_st_strategy_following, parent, false)
        )

        holder.itemView.setOnClickListener {
            mOnItemClickListener?.onItemClick(holder.bindingAdapterPosition)
        }

        holder.ivShare.setOnClickListener {
            mOnItemClickListener?.onShareClick(holder.bindingAdapterPosition)
        }

        holder.tvNextStart.setOnClickListener {
            mOnItemClickListener?.onNextStartClick(holder.bindingAdapterPosition)
        }

        holder.tvNextEnd.setOnClickListener {
            mOnItemClickListener?.onNextEndClick(
                holder.bindingAdapterPosition,
                dataList.getOrNull(holder.bindingAdapterPosition)?.followingStatus.ifNull()
            )
        }

        return holder

    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val dataBean = dataList.getOrNull(position) ?: return

        ImageLoaderUtil.loadImage(
            mContext,
            dataBean.profilePictureUrl,
            holder.ivHead,
            R.mipmap.ic_launcher
        )

        holder.tvName.setTextDiff(dataBean.strategyName.ifNull())
        holder.tvIdKey.text = "${mContext.getString(R.string.strategy_id)}："
        holder.tvId.setTextDiff(dataBean.strategyNo.ifNull())

        /**
         * pending order  停止跟随有订单没有平仓成功  --> 显示 pending_orders
         * "2" == dataBean.followingStatus  暂停跟单  --> 显示 paused_copying
         * "6" == dataBean.followingStatus 临时账户创建中间态 --> 显示 initiating
         */
        holder.tvLabel.isVisible =
            (true == dataBean.hasPendingOrder || "2" == dataBean.followingStatus || "6" == dataBean.followingStatus)

        holder.tvLabel.setTextDiff(
            mContext.getString(
                if ("6" == dataBean.followingStatus){
                    R.string.initiating
                }else if (true == dataBean.hasPendingOrder)
                    R.string.pending_orders
                else
                    R.string.paused_copying
            )
        )

        holder.ivShare.isVisible = holder.tvLabel.isVisible.not()

        // 临时账户中间态
        if ("6" == dataBean.followingStatus) {
            holder.tvPnl.setTextDiff("0".numCurrencyFormat())
            holder.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
            holder.tvTotalSharedProfit.setTextDiff("0".numCurrencyFormat())

            holder.tvBalance.setTextDiff(initiating)
            holder.tvEquity.setTextDiff(initiating)
            holder.tvCredit.setTextDiff(initiating)
        }else {
            holder.tvBalance.setTextDiff((dataBean.balance ?: "0").numCurrencyFormat())
            holder.tvEquity.setTextDiff(dataBean.equityUI.ifNull())
            holder.tvCredit.setTextDiff((dataBean.investmentCredit ?: "0").numCurrencyFormat())

            holder.tvPnl.setTextDiff(dataBean.pnlUI.ifNull())
            holder.tvPnl.setTextColorDiff(
                if (dataBean.followTotalProfit < 0) cf44040 else c00c79c
            )
            holder.tvTotalSharedProfit.setTextDiff(dataBean.totalShareProfitUI.ifNull())
        }

        // pending close 已停止跟单，部分订单平仓异常
        if ("5" == dataBean.followingStatus) {
            holder.tvNextEnd.setTextDiff(pending_close)
            holder.tvNextEnd.setTextColorDiff(color_c731e1e1e_c61ffffff)
        } else {
            holder.tvNextEnd.setTextDiff(manage)
            holder.tvNextEnd.setTextColorDiff(color_c1e1e1e_cebffffff)
        }

        holder.offView.setBackgroundColor(color_c0a1e1e1e_c0affffff)

    }

    override fun getItemCount(): Int = dataList.size

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        val ivHead: ImageView = view.findViewById(R.id.ivHead)
        val tvName: TextView = view.findViewById(R.id.tvName)
        val tvIdKey: TextView = view.findViewById(R.id.tvIdKey)
        val tvId: TextView = view.findViewById(R.id.tvId)
        val tvLabel: TextView = view.findViewById(R.id.tvLabel)
        val ivShare: ImageView = view.findViewById(R.id.ivShare)

        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)
        val tvBalanceTitle: TextView = view.findViewById(R.id.tvBalanceTitle)
        val tvBalance: TextView = view.findViewById(R.id.tvBalance)
        val tvEquityTitle: TextView = view.findViewById(R.id.tvEquityTitle)
        val tvEquity: TextView = view.findViewById(R.id.tvEquity)
        val tvCreditTitle: TextView = view.findViewById(R.id.tvCreditTitle)
        val tvCredit: TextView = view.findViewById(R.id.tvCredit)
        val tvTotalSharedProfitTitle: TextView = view.findViewById(R.id.tvTotalSharedProfitTitle)
        val tvTotalSharedProfit: TextView = view.findViewById(R.id.tvTotalSharedProfit)
        val tvPlaceholderTitle: TextView = view.findViewById(R.id.tvPlaceholderTitle)
        val tvPlaceholder: TextView = view.findViewById(R.id.tvPlaceholder)

        val tvNextStart: TextView = view.findViewById(R.id.tvNextStart)
        val tvNextEnd: TextView = view.findViewById(R.id.tvNextEnd)
        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
        fun onShareClick(position: Int)
        fun onNextStartClick(position: Int)
        fun onNextEndClick(position: Int, followingStatus: String)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}