package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.trade.ClosedHistoryBean
import cn.com.vau.data.trade.PartyCloseItemBean
import cn.com.vau.databinding.ActivityCloseHistoryBinding
import cn.com.vau.databinding.HeaderRecyclerCloseHistoryBinding
import cn.com.vau.trade.adapter.ClosedHistoryAdapter
import cn.com.vau.trade.model.CloseHistoryViewModel
import cn.com.vau.util.*

/**
 * 平仓历史页,开启时需要传入positionId，非跟单的还需要传入开仓时间
 */
class CloseHistoryActivity : BaseMvvmActivity<ActivityCloseHistoryBinding, CloseHistoryViewModel>() {

    private val shape_c1f00c79c_r100: Drawable? by lazy { ContextCompat.getDrawable(this, R.drawable.shape_c1f00c79c_r100) }
    private val shape_c1fe35728_r100: Drawable? by lazy { ContextCompat.getDrawable(this, R.drawable.shape_c1fe35728_r100) }

    private val mAdapter: ClosedHistoryAdapter by lazy {
        ClosedHistoryAdapter()
    }

    private val headerView: HeaderRecyclerCloseHistoryBinding by lazy {
        HeaderRecyclerCloseHistoryBinding.inflate(layoutInflater)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        intent.extras?.let {
            mViewModel.positionId = it.getString(Constants.PARAM_ORDER_NUMBER, "")
            mViewModel.openTime = it.getString(Constants.PARAM_ORDER_OPEN_TIME, "")
            mViewModel.portfolioId = it.getString(Constants.PARAM_ORDER_PORTFOLIO_ID, "")
        }

    }

    override fun initView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.close_history))
        mBinding.mVsNoData.isVisible = true
        initRecyclerView()
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.closedHistoryLiveData.observe(this) {
            showPartList(it)
        }
    }

    override fun initData() {
        super.initData()
        mViewModel.tradeListCloseHistory {
            mBinding.mVsNoData.isVisible = false
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showHeaderData(closedHistoryBean: ClosedHistoryBean) {
        headerView.tvProduceName.text = closedHistoryBean.symbol
        headerView.tvVolumeClosed.text = "${closedHistoryBean.closedVolume ?: "--"} ${getString(R.string.lots)}"
        headerView.tvVolumeRemained.text = "${closedHistoryBean.remainedVolume ?: "--"} ${getString(R.string.lots)}"
        headerView.tvClosedPnl.text = "${closedHistoryBean.closePnl?.numCurrencyFormat(mViewModel.currencyType).ifNull("--")} ${mViewModel.currencyType}".arabicReverseTextByFlag(" ")
        headerView.tvClosedNetPnl.text = "${closedHistoryBean.closeNetPnl?.numCurrencyFormat(mViewModel.currencyType).ifNull("--")} ${mViewModel.currencyType}".arabicReverseTextByFlag(" ")
    }

    private fun showPartList(closedHistoryBean: ClosedHistoryBean?) {
        closedHistoryBean?.let {
            showHeaderData(it)
            it.partCloseList?.let { closeList ->
                if (closeList.isNotEmpty()) {
                    mBinding.mVsNoData.isVisible = false
                    mAdapter.setNewInstance(closeList)
                }

            }
        }

    }

    override fun initListener() {
        super.initListener()
    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        mBinding.mRecyclerView.adapter = mAdapter
        mAdapter.setHeaderView(headerView.root)
        mAdapter.setNbOnItemChildClickListener { adapter, view, position ->
            val bean = mAdapter.data.elementAtOrNull(position)?:return@setNbOnItemChildClickListener
            when (view.id) {
                R.id.titleClickView -> {
                    adapter.data.get(position)?.let {
                        (it as PartyCloseItemBean).isExpand = it.isExpand.not()
                        adapter.notifyItemChanged(position + adapter.headerLayoutCount)
                    }
                }

                R.id.tvDealNum -> { // 复制订单号
                    bean.tradeDealId.copyText(getString(R.string.number_copied))
                }
            }
        }
    }

}