package cn.com.vau.trade.model

import cn.com.vau.trade.presenter.DealItemContract

/**產品model*/
class DealItemModel : DealItemContract.Model {
//    override fun queryWeekTrend(requestBody: RequestBody, baseObserver: BaseObserver<TrendBean>): Disposable {
//        HttpUtils.loadData(RetrofitHelper.getHttpService2().trend<PERSON>hart(requestBody), baseObserver)
//        return baseObserver.disposable
//    }

}