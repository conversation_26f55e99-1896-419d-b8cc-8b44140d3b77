package cn.com.vau.trade.data

import android.view.View
import android.widget.TextView
import cn.com.vau.R

/**
 * 账户信息类型
 */
sealed class AccountInfoType {
    data object Equity : AccountInfoType()//净值
    data object FloatingPnL : AccountInfoType()//浮动盈亏
    data object MarginLevel : AccountInfoType()//保证金水平
    data object Credit : AccountInfoType()//信用额度
    data object MarginAndFreeMargin : AccountInfoType()//保证金和可用保证金
    data object Balance : AccountInfoType()//余额

    companion object {
        val allTypes: MutableSet<AccountInfoType>
            get() = mutableSetOf(
                Equity,
                FloatingPnL,
                MarginLevel,
                Credit,
                MarginAndFreeMargin,
                Balance
            )
    }
}

data class AccountInfoItem(
    val clItemBg: View,
    val tvItemTitle: TextView,
    val type: AccountInfoType,
    val selectedBg: Int = R.drawable.draw_shape_c0a1e1e1e_c1fffffff_r4,
    val defaultBg: Int = R.drawable.draw_shape_transparent_r4
)

/**
 * 保证金水平风险等级
 */
sealed class MarginRiskLevel {
    data object Low : MarginRiskLevel()
    data object Medium : MarginRiskLevel()
    data object High : MarginRiskLevel()
}