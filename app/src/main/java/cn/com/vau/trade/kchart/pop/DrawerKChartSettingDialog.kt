package cn.com.vau.trade.kchart.pop

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import cn.com.vau.databinding.PopupKlineLiteSettingBinding
import cn.com.vau.trade.adapter.KLineLiteSettingIndicatorAdapter
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.widget.dialog.base.DrawerDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.enums.PopupPosition
import java.util.Locale

@SuppressLint("ViewConstructor")
class DrawerKChartSettingDialog private constructor(
    context: Context,
    private val mainTypeList: MutableList<String>,
    private val subTypeList: MutableList<String>
) : DrawerDialog<PopupKlineLiteSettingBinding>(context, PopupKlineLiteSettingBinding::inflate) {

    private val mainAdapter by lazy { KLineLiteSettingIndicatorAdapter(mainTypeList) }
    private val subAdapter by lazy { KLineLiteSettingIndicatorAdapter(subTypeList) }
    private var indicatorListener: ((Int, String) -> Unit)? = null
    private var lineListener: ((Int, Boolean) -> Unit)? = null

    override fun setContentView() {
        super.setContentView()
        mContentBinding.run {
            mainRecyclerView.adapter = mainAdapter
            subRecyclerView.adapter = subAdapter

            if (null != KLineDataUtils.userData) {
                cbAsk.isChecked = KLineDataUtils.userData.askLineDisplay
                cbBid.isChecked = KLineDataUtils.userData.bidLineDisplay
                cbOpen.isChecked = KLineDataUtils.userData.positionLineDisplay
                cbTake.isChecked = KLineDataUtils.userData.tpLineDisplay
                cbStop.isChecked = KLineDataUtils.userData.slLineDisplay
            }
            cbAsk.setOnCheckedChangeListener { _, isChecked ->
                lineListener?.invoke(0, isChecked)
            }
            cbBid.setOnCheckedChangeListener { _, isChecked ->
                lineListener?.invoke(1, isChecked)
            }
            cbOpen.setOnCheckedChangeListener { _, isChecked ->
                lineListener?.invoke(2, isChecked)
                /** 持仓线关闭，止盈线和止损线同步关闭 */
                if (isChecked.not()) {
                    cbTake.isChecked = false
                    lineListener?.invoke(3, false)
                    cbStop.isChecked = false
                    lineListener?.invoke(4, false)
                }
            }
            cbTake.setOnCheckedChangeListener { _, isChecked ->
                lineListener?.invoke(3, isChecked)
                /** 止盈线或者止损线开启，持仓线同步开启 */
                if (isChecked) {
                    cbOpen.isChecked = true
                    lineListener?.invoke(2, true)
                }
            }
            cbStop.setOnCheckedChangeListener { _, isChecked ->
                lineListener?.invoke(4, isChecked)
                /** 止盈线或者止损线开启，持仓线同步开启 */
                if (isChecked) {
                    cbOpen.isChecked = true
                    lineListener?.invoke(2, true)
                }
            }
        }

        mainAdapter.setOnItemClickListener { _, _, position ->
            val selectedText = mainAdapter.getItem(position)
            if (selectedText != mainAdapter.getSelected()) {
                mainAdapter.selected(selectedText)
                indicatorListener?.invoke(0, selectedText)
            }
        }
        subAdapter.setOnItemClickListener { _, _, position ->
            val selectedText = subAdapter.getItem(position)
            if (selectedText != subAdapter.getSelected()) {
                subAdapter.selected(selectedText)
                indicatorListener?.invoke(1, selectedText)
            }
        }
    }

    fun update() {
        if (KLineDataUtils.userData != null) {
            mainAdapter.selected(KLineDataUtils.userData.mainChartName)
            subAdapter.selected(KLineDataUtils.userData.subChartName)

            mContentBinding.run {
                cbAsk.isChecked = KLineDataUtils.userData.askLineDisplay
                cbBid.isChecked = KLineDataUtils.userData.bidLineDisplay
                cbOpen.isChecked = KLineDataUtils.userData.positionLineDisplay
                cbTake.isChecked = KLineDataUtils.userData.tpLineDisplay
                cbStop.isChecked = KLineDataUtils.userData.slLineDisplay
            }
        }
    }

    override fun show(): BasePopupView {
        update()
        return super.show()
    }

    // Int: 0主图类型  1副图类型     String: 选中类型名称
    fun setOnIndicatorSelect(listener: ((Int, String) -> Unit)? = null) {
        this.indicatorListener = listener
    }

    // Int: 0买价线  1卖价线  2持仓线  3止盈线  4止损线    Boolean: 选中状态
    fun setOnLineSelect(listener: ((Int, Boolean) -> Unit)? = null) {
        this.lineListener = listener
    }

    class Builder(activity: Activity) : IBuilder<PopupKlineLiteSettingBinding, Builder>(activity) {

        private var mainTypeList: MutableList<String> = mutableListOf()
        private var subTypeList: MutableList<String> = mutableListOf()

        fun setMainTypeList(mainTypeList: MutableList<String>): Builder {
            this.mainTypeList.clear()
            this.mainTypeList.addAll(mainTypeList)
            return this
        }

        fun setSubTypeList(subTypeList: MutableList<String>): Builder {
            this.subTypeList.clear()
            this.subTypeList.addAll(subTypeList)
            return this
        }

        override fun getPopupPosition(): PopupPosition {
            val locale: Locale = LanguageHelper.getAppLocale()
            val isArabic = locale.language == "ar" || locale.language == "ara"
            return if (isArabic) {
                PopupPosition.Left
            } else {
                PopupPosition.Right
            }
        }

        override fun createDialog(context: Context): DrawerKChartSettingDialog {
            return DrawerKChartSettingDialog(activity, mainTypeList, subTypeList)
        }

        override fun build(): DrawerKChartSettingDialog {
            setViewMode(true)
            return super.build() as DrawerKChartSettingDialog
        }
    }
}