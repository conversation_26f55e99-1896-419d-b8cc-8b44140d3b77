package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.trade.bean.CloseConfigSymbolBean
import cn.com.vau.util.CloseConfigHelper
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.json
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * 批量平仓 -- 筛选订单 End 页
 */
class ModifiedCloseConfigurationEndViewModel : BaseViewModel() {

    val batchCloseLiveData by lazy {
        MutableLiveData<ApiResponse<*>>()
    }
    val filterSymbols by lazy {
        val list = VAUSdkUtil.shareOrderList().filter {
            (isInConfig(it) && checkOrderType(it) && checkConfigProfit(it))
        }.sortedBy { it.symbol }
        val closeConfigSymbolBeanList = mutableListOf<CloseConfigSymbolBean>()
        list.forEach {
            closeConfigSymbolBeanList.add(CloseConfigSymbolBean().apply {
                this.isSelected = true
                this.shareOrderData = it
            })
            CloseConfigHelper.selectedOrderList.add(it)
        }
        closeConfigSymbolBeanList
    }


    private fun isInConfig(shareOrderData: ShareOrderData): Boolean {
        CloseConfigHelper.selectSymbols.forEach {
            if (it.symbol == shareOrderData.symbol) {
                return true
            }
        }
        return false
    }

    private fun checkConfigProfit(shareOrderData: ShareOrderData):Boolean {
        if (CloseConfigHelper.configProfitLoss == CloseConfigHelper.CONFIG_SELECT_ALL){
            return true
        }
        if (CloseConfigHelper.CONFIG_PROFIT == CloseConfigHelper.configProfitLoss and CloseConfigHelper.CONFIG_PROFIT){
            return shareOrderData.profit > 0
        }else {
            return shareOrderData.profit < 0
        }
    }

    private fun checkOrderType(shareOrderData: ShareOrderData): Boolean {
        if (CloseConfigHelper.configDirection == CloseConfigHelper.CONFIG_SELECT_ALL) {
            return true
        }
        return if (CloseConfigHelper.configDirection and CloseConfigHelper.CONFIG_BUY == CloseConfigHelper.CONFIG_BUY) {
            OrderUtil.isBuyOfOrder(shareOrderData.cmd)
        } else {
            OrderUtil.isBuyOfOrder(shareOrderData.cmd).not()
        }
    }

    fun tradeOrdersBatchCloseV2() {
        requestNet({
            if (UserDataUtil.isStLogin()) {
                val map = mapOf(
                    "portfolioId" to UserDataUtil.stMasterPortfolioId(),
                    "positionIds" to getSelectedOrderIdList()
                )
                val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
                stTradingService.tradePositionBatchCloseApi(requestBody)
            } else {
                val map = mutableMapOf<String, Any>(
                    "token" to UserDataUtil.tradeToken(),
                    "login" to UserDataUtil.accountCd(),
                    "serverId" to UserDataUtil.serverId(),
                    "orders" to getSelectedOrders()
                )
                val dataObject = JsonObject()
                dataObject.addProperty("data", GsonUtil.toJson(map))
                val requestBody =
                    dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
                tradingService.tradeOrdersBatchCloseV2Api(requestBody)
            }
        },
            {
                if (it.isSuccess()) {
                    batchCloseLiveData.value = it
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            }, isShowDialog = true)
    }

    private fun getSelectedOrders():ArrayList<MutableMap<String, String?>> {
        val list = arrayListOf<MutableMap<String, String?>>()
        CloseConfigHelper.selectedOrderList.forEach {
            val map = mutableMapOf(
                "symbol" to it.symbol,
                "price" to it.closePrice,
                "order" to it.order
            )
            list.add(map)
        }
       return list
    }

    private fun getSelectedOrderIdList():ArrayList<String> {
        val list = arrayListOf<String>()
        CloseConfigHelper.selectedOrderList.forEach {
            list.add(it.stOrder?:"")
        }
        return list
    }

}