package cn.com.vau.trade.presenter

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.BaseBean
import cn.com.vau.trade.bean.ManageOptionalNetBean
import cn.com.vau.util.ToastUtil
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Created by roy on 2018/11/6.
 * 自选页面
 */
class MySymbolsPresenter : MySymbolsContract.Presenter() {

    var isLoading = false
    var netBean: ManageOptionalNetBean = ManageOptionalNetBean()

    /**
     * 自选产品列表数据有变动，上传数据
     */
    override fun updOptionalProdApi() {
//        mView?.showNetDialog()
        isLoading = true

        // 移动后，上一个产品英文代码，如果是最顶层，值：0
        var symbols = ""
        VAUSdkUtil.collectSymbolList.forEachIndexed { index, optionalObj ->
            symbols += "${optionalObj.symbol}${
                if (index != VAUSdkUtil.collectSymbolList.lastIndex) {
                    ","
                } else {
                    ""
                }
            }"
        }

        if (!UserDataUtil.isStLogin()) {
            val paramMap = hashMapOf<String, Any>()
            paramMap["login"] = UserDataUtil.accountCd()
            paramMap["token"] = UserDataUtil.loginToken()
            paramMap["serverId"] = UserDataUtil.serverId()
            paramMap["symbols"] = symbols

            mModel?.prodUpdApi(
                paramMap,
                object : BaseObserver<BaseBean>() {
                    override fun onNext(baseData: BaseBean?) {
                        isLoading = false
                        mView?.hideNetDialog()
                        if ("********" != baseData?.resultCode) {
                            // 刷新自选
                            ToastUtil.showToast(baseData?.msgInfo)
                            return
                        }
                    }

                    override fun onHandleSubscribe(d: Disposable?) {
                        mRxManager?.add(d)
                    }

                    override fun onError(e: Throwable?) {
                        super.onError(e)
                        isLoading = false
                        mView?.hideNetDialog()
                    }
                }
            )
        } else {
            /**[跟單]*/
            val jsonObject = JsonObject()
            jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
            jsonObject.addProperty("symbols", symbols)

            val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
            mModel?.stAccountProductMyUpdApi(
                requestBody,
                object : BaseObserver<BaseBean>() {
                    override fun onNext(baseData: BaseBean?) {
                        isLoading = false
                        mView?.hideNetDialog()
                        if ("200" != baseData?.code) {
                            // 刷新自选
                            ToastUtil.showToast(baseData?.msg)
                            return
                        }
                    }

                    override fun onHandleSubscribe(d: Disposable?) {
                        mRxManager?.add(d)
                    }

                    override fun onError(e: Throwable?) {
                        super.onError(e)
                        isLoading = false
                        mView?.hideNetDialog()
                    }
                }
            )
        }
    }
}