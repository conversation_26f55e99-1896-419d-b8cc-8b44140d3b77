package cn.com.vau.trade.kchart.tradingview

import android.content.Context
import androidx.core.os.bundleOf
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.popup.bean.DragAction
import cn.com.vau.common.view.popup.bean.IndicatorBean
import cn.com.vau.common.view.popup.bean.LineItem
import cn.com.vau.common.view.popup.bean.ProfitLossBean
import cn.com.vau.common.view.popup.bean.TradingViewQuoteData
import cn.com.vau.common.view.popup.bean.TradingViewSettingData
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.bean.HKLineChartNetBean
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_BRUSH
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_ERASER
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_HIGHLIGHTER
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_HORIZONTAL
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_PATH
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_POLYLINE
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_RECTANGLE
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_TREND
import cn.com.vau.trade.kchart.tradingview.TradingViewDrawingPopup.Companion.DRAWING_VERTICAL
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import com.github.lzyzsd.jsbridge.BridgeWebView
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.google.gson.JsonObject
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

class TradingViewInterface(
    private val context: Context,
    private val data: HKLineChartNetBean,
    private val webView: BridgeWebView?,
    private val symbolData: ShareProductData?
) {

    var activity: ChartCandleLandscapeActivity? = null

    val shareOrderList = CopyOnWriteArrayList<ShareOrderData>()

    var selectedOrder: ShareOrderData? = null

    val loading: BasePopupView by lazy {
        XPopup.Builder(context)
            .isViewMode(true)
            .dismissOnBackPressed(true)
            .asCustom(LoadingPopup(context))
    }

    init {
        activity = context as? ChartCandleLandscapeActivity?
        register()
    }

    // 向TradingView更新画板浮窗设置值
    fun callDrawing(type: Int) {
        var name =
            when (type) {
                DRAWING_BRUSH -> "brush"
                DRAWING_HIGHLIGHTER -> "highlighter"
                DRAWING_ERASER -> "eraser"
                DRAWING_TREND -> "trend_line"
                DRAWING_HORIZONTAL -> "horizontal_line"
                DRAWING_VERTICAL -> "vertical_line"
                DRAWING_PATH -> "path"
                DRAWING_RECTANGLE -> "rectangle"
                DRAWING_POLYLINE -> "polyline"
                else -> "undefined"
            }
//        webView?.loadUrl("javascript:drawingTools(\"${name}\")")
        val json = JsonObject()
        json.addProperty("title", name)
//        LogUtil.d("wj", "[callHandler] drawingTools: ${Gson().toJson(json)}")
        webView?.callHandler("drawingTools", GsonUtil.buildGson().toJson(json), null)

        val point = when (type) {
            DRAWING_BRUSH -> "Brush"
            DRAWING_HIGHLIGHTER -> "Highlighter"
            DRAWING_ERASER -> "Eraser"
            DRAWING_TREND -> "Trend"
            DRAWING_HORIZONTAL -> "Horizontal"
            DRAWING_VERTICAL -> "Vertical"
            DRAWING_PATH -> "Path"
            DRAWING_RECTANGLE -> "Rectangle"
            DRAWING_POLYLINE -> "Polyline"
            else -> "undefined"
        }
        LogEventUtil.setLogEvent(
            BuryPointConstant.V345.TRADE_KLINE_PRO_HORIZONTAL_TOOLS_BUTTON_CLICK, bundleOf(
                "Account_type" to KLineActivity.getPointAccountType(),
                "Tools" to point
            )
        )
    }

    // 向TradingView更新设置浮窗设置值
    fun callSettings() {
        val data = KLineDataUtils.userDataTV ?: TradingViewSettingData.getHistoryData()
        // storeIndicators
        val map: Map<String, List<Int>> = data.outputIndicators()
        val jsonIndicators = JsonObject()
        jsonIndicators.add("value", GsonUtil.buildGson().toJsonTree(map))
        jsonIndicators.addProperty("mainCurrent", data.mainChartName)
        jsonIndicators.addProperty("subCurrent", data.subChartName)
//        webView?.loadUrl("javascript:androidCallFunction('storeIndicators', $jsonIndicators)")
//        LogUtil.d("wj", "[callHandler] storeIndicators: ${Gson().toJson(jsonIndicators)}")
        webView?.callHandler("storeIndicators", GsonUtil.buildGson().toJson(jsonIndicators), null)

        // storeOrders
        sendSelectedOrder()
    }

    // 行情更新
    fun updateQuotes(data: TradingViewQuoteData) {
        webView?.callHandler("websocketUpdate", GsonUtil.buildGson().toJson(data), null)
//        LogUtil.d("wj", "[websocketUpdate] ${Gson().toJson(data)}")
    }

    // 点击右上角的绘图或者图线设置浮层的时候 调用次功能来关闭指标的列表
    fun closeIndicatorSelection() {
        webView?.callHandler("closeIndicatorSelection", "", null)
    }

    // 设置Extra Line
    fun sendSelectedOrder() {
        val data = KLineDataUtils.userDataTV ?: TradingViewSettingData.getHistoryData()
        val list = setLineData(data)
        val jsonLines = JsonObject()
        jsonLines.add("value", GsonUtil.buildGson().toJsonTree(list))
//        webView?.loadUrl("javascript:androidCallFunction('storePriceLines', $jsonLines)")
//        LogUtil.d("wj", "[callHandler] storeOrders: ${Gson().toJson(jsonLines)}")
        webView?.callHandler("storeOrders", GsonUtil.buildGson().toJson(jsonLines), null)
    }

    private fun setLineData(data: TradingViewSettingData): MutableList<LineItem> {
        val buyAndsell = data.outputLines()
        val result = mutableListOf<LineItem>()
        if (shareOrderList.isNotEmpty() && selectedOrder != null) {
//            for (order in shareOrderList) {
            selectedOrder?.let { order ->

                // Position Line
                val cmd = if ("0" == order.cmd || "2" == order.cmd || "4" == order.cmd) "Buy" else "Sell"
                val price = order.openPrice.toDoubleCatching()
                val volume = order.volume?.toDoubleCatching().ifNull()
                val positionStatus = data.line?.position?.status.ifNull()
                result.add(
                    LineItem(
                        ordernumber = order.order.ifNull(), type = "open", tradetype = cmd, typeName = cmd, price = price, volume = volume, status = positionStatus
                    )
                )
                // TP Line
                val tpPrice = order.takeProfit?.toDoubleCatching().ifNull()
                val tpStatus = data.line?.tp?.status.ifNull()
                if (0.toDouble() != tpPrice) {
                    result.add(
                        LineItem(
                            ordernumber = order.order.ifNull(), type = "profit", tradetype = cmd, typeName = "TP", price = tpPrice, volume = volume, status = tpStatus
                        )
                    )
                }
                // SL Line
                val slPrice = order.stopLoss?.toDoubleCatching().ifNull()
                val slStatus = data.line?.sl?.status.ifNull()
                if (0.toDouble() != slPrice) {
                    result.add(
                        LineItem(
                            ordernumber = order.order.ifNull(), type = "loss", tradetype = cmd, typeName = "SL", price = slPrice, volume = volume, status = slStatus
                        )
                    )
                }
            }

//            }
        }
        result.addAll(buyAndsell)
        return result
    }

    private fun saveChart(json: String?, function: CallBackFunction? = null) {
//        LogUtil.d("wj", "[saveChart]:  json: $json")
        val userid =
            if (UserDataUtil.isLogin()) UserDataUtil.userId() else "default"
        SpManager.putTradingViewDrawingData("${userid}_${data.nameEn}_",json.ifNull())
        function?.onCallBack("saveChart")
    }

    private fun getWebLocalStorage(key: String, callback: ((String) -> Unit)? = null) {
        webView?.evaluateJavascript("window.localStorage.getItem('$key')") {
//            LogUtil.d("wj", "getWebLocalStorage:  $it") // it value is "null" when it return empty
            var value = it.replace("\\", "")
            if (value != "null" && value.length > 3) {
                if (value.startsWith("\"")) {
                    value = value.substring(1)
                }
                if (value.endsWith("\"")) {
                    value = value.substring(0, value.length - 1)
                }
            }
            callback?.invoke(value)
        }
    }

    // 保存画板数据
    fun saveDrawingData(symbol: String) {
        getWebLocalStorage(symbol) {
            saveChart(it)
        }
    }

    // 注册与H5交互的方法
    fun register() {
        webView?.registerHandler("sendEvent") { json, function ->
//            LogUtil.d("wj", "[sendEvent]:  json: $json")
            try {
                val jsonObject = JSONObject(json)

                when (jsonObject.optString("code")) {
                    "209" -> {
                        activity?.updateIndicatorTradingView()
                    }

                    "223" -> {
                        loading.show()
                        activity?.hideLoadDialog()
                    }

                    "224" -> {
                        MainScope().launch {
                            loading.dismiss()
                            activity?.hideLoadDialog()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            function?.onCallBack("sendEvent")
        }

        webView?.registerHandler("saveChart") { json, function ->
            saveChart(json, function)
        }

        webView?.registerHandler("loadChart") { _, function ->
//            LogUtil.d("wj", "[loadChart]: ")
            if (UserDataUtil.isLogin()) {
                val userid = UserDataUtil.userId()
                val userData = SpManager.getTradingViewDrawingData("${userid}_${data.nameEn}_","")
                if (userData.isNotEmpty()) {
//                    LogUtil.d("wj", "loadChart(user:$userid): $userData")
                    function?.onCallBack(userData)
                } else {
                    // 查找历史数据
                    val defaultData = SpManager.getTradingViewDrawingData("default_${data.nameEn}_")
                    function?.onCallBack(
                        if (defaultData.isNotEmpty()) {
                            SpManager.putTradingViewDrawingData("${userid}_${data.nameEn}_",defaultData)
//                            LogUtil.d("wj", "loadChart(default): $defaultData")
                            defaultData
                        } else ""
                    )
                }
            } else {
                // 查找历史数据
                val defaultData = SpManager.getTradingViewDrawingData("default_${data.nameEn}_")
//                LogUtil.d("wj", "loadChart(default): $defaultData")
                function?.onCallBack(defaultData)
            }

        }

        // TradingView设置时间段交互
        webView?.registerHandler("changeInterval") { json, function ->
//            LogUtil.d("wj", "[changeInterval]: json: $json")
            activity?.tradingViewInterval = json
            activity?.intervalBuryPoint(json)
        }

        // TradingView设置拖动止盈止损交互
        webView?.registerHandler("updatePriceLineStatus") { json, function ->
//            LogUtil.d("wj", "[updatePriceLineStatus]: json: $json")
            val drag = GsonUtil.buildGson().fromJson(json, DragAction::class.java)
            when (drag.action) {
                "move" -> {
                    activity?.onPositionMoveOver(drag)
                }

                "delete" -> {
                    activity?.onDeleteTpOrSl(drag)
                }
            }
        }

        // TradingView打开设置浮窗
        webView?.registerHandler("openIndicatorSelection") { json, function ->
//            LogUtil.d("wj", "[openIndicatorSelection]: json: $json")
            val jsonBean = GsonUtil.buildGson().fromJson(json, IndicatorBean::class.java)
//            val data = KLineDataUtils.userDataTV ?: TradingViewSettingData.getHistoryData()
//            when (jsonBean.type) {
//                "main" -> {
//                    data.mainSelectedIndex = mainIndicatores.indexOf(jsonBean.value.ifNull())
//                }
//                "sub" -> {
//                    data.subSelectedIndex = subIndicatores.indexOf(jsonBean.value.ifNull())
//                }
//            }
            activity?.showSettingDialog(if (jsonBean.type == "main") 0 else 1)
        }

        webView?.registerHandler("getProfitLoss") { json, function ->
//            LogUtil.d("wj", "[getProfitLoss]: json: $json")
            val bean = runCatching { GsonUtil.buildGson().fromJson(json, ProfitLossBean::class.java) }.getOrDefault(null)
            val orderNum = bean?.ordernumber
            if (shareOrderList.isNotEmpty()) {
                val order = shareOrderList.firstOrNull { it.order == orderNum }
                order?.let {
                    synchronized(this) {
                        val profit = VAUSdkUtil.getProfitLoss(
                            symbolData ?: ShareProductData(),
                            order.openPrice,
                            order.volume ?: "",
                            order.cmd,
                            bean?.price ?: ""
                        ).toString().numCurrencyFormat()
//                        LogUtil.d("wj", "profit: $profit")
                        function?.onCallBack(
                            profit
                        )
                    }
                }
            }
        }
    }

}