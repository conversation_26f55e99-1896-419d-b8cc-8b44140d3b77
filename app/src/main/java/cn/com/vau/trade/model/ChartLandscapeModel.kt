package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.trade.StTradePositionUpdateBean
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathMul
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody

class ChartLandscapeModel: BaseViewModel() {

    var notifyLiveData = MutableLiveData<Boolean>()

    fun setTakeProfitOrStopLoss(tpPrice: Double, slPrice: Double, bean: ShareOrderData?) {
        if (UserDataUtil.isStLogin()) {
            stTradePositionUpdate(tpPrice, slPrice, bean)
            return
        }
        if (bean != null) {
            showLoading()
            val jsonObject = JsonObject()
            jsonObject.addProperty("login", UserDataUtil.accountCd())
            jsonObject.addProperty("price", bean.openPrice)
            jsonObject.addProperty("tp", tpPrice)
            jsonObject.addProperty("sl", slPrice)
            jsonObject.addProperty("order", bean.order)
            jsonObject.addProperty("token", UserDataUtil.tradeToken())
            jsonObject.addProperty("cmd", bean.cmd)
            jsonObject.addProperty("symbol", bean.symbol)
            var mulNum = "100"
            if (UserDataUtil.isMT5()) {
                mulNum = "10000"
            }
            var handleCount = bean.volume.mathMul(mulNum)
            if (handleCount.contains(".")) {
                handleCount = handleCount.split("\\.".toRegex())[0]
            }
            jsonObject.addProperty("volume", handleCount)
            jsonObject.addProperty("serverId", UserDataUtil.serverId())
            val jsonObject2 = JsonObject()
            jsonObject2.addProperty("data", jsonObject.toString())
            val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject2.toString())
            HttpUtils.loadData<BaseBean>(RetrofitHelper.getHttpService2().tradeOrdersUpdate(requestBody), object : BaseObserver<BaseBean>() {
                override fun onNext(baseBean: BaseBean) {
                    hideLoading()
                    if ("200" != baseBean.code) {
                        ToastUtil.showToast(baseBean.info)
                        notifyLiveData.value = false
                        return
                    }
                    ToastUtil.showToast(baseBean.info)
                    notifyLiveData.value = true
                }

                override fun onHandleSubscribe(d: Disposable) {
                    addDisposable(d)
                }
                override fun onError(e: Throwable) {
                    super.onError(e)
                    hideLoading()
                    notifyLiveData.value = false
                }
            })
        }
    }

    private fun stTradePositionUpdate(tpPrice: Double, slPrice: Double, bean: ShareOrderData?) {
        showLoading()
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("positionId", bean?.stOrder.ifNull())
        jsonObject.addProperty("takeProfit", tpPrice)
        jsonObject.addProperty("stopLoss", slPrice)
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        HttpUtils.loadData<StTradePositionUpdateBean>(RetrofitHelper.getStHttpService().tradePositionUpdate(requestBody), object : BaseObserver<StTradePositionUpdateBean>() {
            override fun onNext(baseBean: StTradePositionUpdateBean) {
                hideLoading()
                if (baseBean.code != "200") {
                    ToastUtil.showToast(baseBean.msg)
                    notifyLiveData.value = false
                    return
                }
                ToastUtil.showToast(baseBean.msg)
                notifyLiveData.value = true
            }

            override fun onHandleSubscribe(d: Disposable) {
                addDisposable(d)
            }
            override fun onError(e: Throwable) {
                super.onError(e)
                hideLoading()
                notifyLiveData.value = false
            }
        })
    }

}