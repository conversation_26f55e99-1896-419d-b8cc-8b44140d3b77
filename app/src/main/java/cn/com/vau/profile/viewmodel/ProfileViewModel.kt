package cn.com.vau.profile.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.AccountHomeData
import cn.com.vau.data.account.SecurityStatusData
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 07 11:01
 * @updateUser:
 * @updateDate: 2024 12月 07 11:01
 */
class ProfileViewModel : BaseViewModel() {
    val userDataLiveData by lazy { MutableLiveData<AccountHomeData.MyHome>() }
    val userDataOtherLiveData by lazy { MutableLiveData<AccountHomeData.MyHome>() }
    val twoFactorStatusLiveData by lazy { MutableLiveData<SecurityStatusData.Obj>(null) }

    /**
     * eventbus 发送通知过来需要请求接口，但是没有感知生命周期，所以记录一下这个标记，在onResume的时候再请求
     */
    var isNeedResumeRefresh = false

    /**
     * 如果eventBus刷新了telegram图标状态，请求接口后不需要再次刷新，避免接口返回慢导致状态错误问题
     */
    var isEventBusRefreshTelegramStatus = false

    var verifiedStatus: CharSequence = ""

    /**
     * 请求所有接口
     */
    fun queryAllData() {
        // 未登录不请求以下接口
        if (!isLogin()) {
            sendEvent(false)
            return
        }
        isEventBusRefreshTelegramStatus = false
        if (isCopyTrading()) {
            accountHomeApi()
            strategySignalProviderCenterApi()
            sendEvent(DataEvent(tag = TAG_FAVOURITES, data = true))
        } else {
            accountHomeBaseApi()
            accountHomeOtherApi()
            // 虽然跟单切换非跟单账号会重建页面，但是为了防止极其特殊情况没有重建，所以这里也发送一个事件 手动去掉收藏item
            sendEvent(DataEvent(tag = TAG_FAVOURITES, data = false))
        }

        // asic 普通用户不显示优惠券管理入口，pro用户显示 (先隐藏掉入口，确认 pro 再显示)
        if (isAsic()) {
            queryUserIsProclientApi()
        } else {
            // 非asic监管才进行认证中心的请求
            // kyc验证的账号 请求 kyc 的验证等级接口 非kyc的 请求 非kyc的接口
            if (isKycAccount()) {
                userAuthenticationstatusApi()
            } else {
                getAuditStatusApi()
            }
            sendEvent(DataEvent(tag = TAG_COUPONS, data = true))
        }

        // 跟单 非跟单 Demo 都要请求
        twoFactorStatusApi()
    }

    /**
     * 是否已经登录
     */
    fun isLogin() = UserDataUtil.isLogin()

    /**
     * 是否登录过
     */
    fun isLoggedIn() = SpManager.getExitStatus(false)

    /**
     * 是否是asic监管
     */
    fun isAsic() = SpManager.getSuperviseNum("") == "1"

    /**
     * asic 和 fca 这两个监管不显示 Rewards 这个tab
     */
    fun isHideRewards() = isAsic() || SpManager.getSuperviseNum("") == "13"

    /**
     * 是否是kyc验证的账户
     */
    fun isKycAccount() = SpManager.isV1V2()

    /**
     * 是否是虚拟MT5的假Live账户
     */
    fun isLiveVirtualAccount() = UserDataUtil.isLiveVirtualAccount()

    /**
     * 是否是v1 v2监管
     */
    fun isV1V2Supervise() = SpManager.getSuperviseNum("") == "8" || SpManager.getSuperviseNum("") == "14"

    /**
     * 获取用户显示的编号
     */
    fun getUserAccount() = UserDataUtil.accountCd()

    /**
     * crm id 用于显示 uid
     */
    fun getUid() = SpManager.getCrmUserId()

    /**
     * 用户头像
     */
    fun userPic() = UserDataUtil.userPic()

    /**
     * 用户昵称
     */
    fun userNickName() = UserDataUtil.nickname()

    /**
     * 是跟单账号
     */
    fun isCopyTrading() = UserDataUtil.accountDealType() == "5"

    /**
     * 是demo账号
     */
    fun isDemo() = UserDataUtil.accountDealType() == "3"

    /**
     * 是否公开交易
     */
    fun isStPublicTrade() = UserDataUtil.isStPublicTrade()

    /**
     * 设置了本地指纹锁或者手势锁
     */
    fun isSetLocalLock() = SpManager.getSecuritySetState(0) != 0

    /**
     * 通过网络请求的数据当前的账号是ib账号， 所以判断本地数据当前不是ib账号的话 修改本地数据 将当前账号设置为ib类型
     */
    fun setUserIsIB() {
        if (!UserDataUtil.isIB()) {
            UserDataUtil.setUserType(0)
        }
    }

    /**
     * 获取 跟单 用户基本数据
     */
    private fun accountHomeApi() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "userId" to UserDataUtil.userId(),
                "mt4AccountId" to UserDataUtil.accountCd(),
                "token" to UserDataUtil.loginToken(),
                "apkType" to "android",
                "type" to "3",
                // 0：模拟，1：真实
                "isDemoAccount" to "1"
            )
            baseService.accountHomeApi(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            it.data?.obj?.myHome?.let { data ->
                saveUserData(data)
            }
            userDataLiveData.value = it.data?.obj?.myHome
            userDataOtherLiveData.value = it.data?.obj?.myHome
        }, onError = {
            sendEvent(false)
        })
    }

    /**
     * 获取 非跟单 用户基本数据
     */
    private fun accountHomeBaseApi() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "userId" to UserDataUtil.userId(),
                "mt4AccountId" to UserDataUtil.accountCd(),
                "token" to UserDataUtil.loginToken(),
                "apkType" to "android"
            )
            // 0：模拟，1：真实
            when (UserDataUtil.accountDealType()) {
                "3" -> map["isDemoAccount"] = "0"
                "1" -> map["isDemoAccount"] = "1"
                "6" -> map["isDemoAccount"] = "4"
            }
            baseService.accountHomeBaseApi(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            it.data?.obj?.myHome?.let { data ->
                saveUserData(data)
            }
            userDataLiveData.value = it.data?.obj?.myHome

            // 用户的基础数据 昵称 uid 头像 本地就有 所以网络请求失败了也不需要在顶部刷新的地方 显示请求失败
        })
    }

    private fun saveUserData(data: AccountHomeData.MyHome) {
        SpManager.putCrmUserId(data.crmUserId.ifNull())
        if (!data.userRealName.isNullOrBlank()) {
            UserDataUtil.setUserRealName(data.userRealName.ifNull())
        }
        if (!data.userNickName.isNullOrBlank()) {
            UserDataUtil.setUserNickName(data.userNickName.ifNull())
        }
        if (!data.pic.isNullOrBlank()) {
            UserDataUtil.setUserPic(data.pic.ifNull())
        }
        if (!data.currency.isNullOrBlank()) {
            UserDataUtil.setCurrencyType(data.currency.ifNull())
        }
    }

    /**
     * 获取用户其他数据
     */
    private fun accountHomeOtherApi() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "userId" to UserDataUtil.userId(),
                "mt4AccountId" to UserDataUtil.accountCd(),
                "token" to UserDataUtil.loginToken(),
                "apkType" to "android",
                "type" to "3"
            )
            // 3：模拟，1：真实  6 虚拟mt5账号
            when (UserDataUtil.accountDealType()) {
                "3" -> map["isDemoAccount"] = "0"
                "1" -> map["isDemoAccount"] = "1"
                "6" -> map["isDemoAccount"] = "4"
            }
            baseService.accountHomeOtherApi(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            userDataOtherLiveData.value = it.data?.obj?.myHome
        }, onError = {
            // 用户的其他数据 如 是否显示任务中心 会员中心 邀请等item 有接口控制 所以网络请求失败了就在顶部的刷新位置显示请求失败 ，如果不是下拉刷新的请求 就不用显示请求失败也可以
            sendEvent(false)
        })
    }

    /**
     * 获取审核状态
     */
    private fun getAuditStatusApi() {
        requestNet({
            baseService.getAuditStatusApi(token = UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(tag = TAG_AUDIT_STATUS, data = it.data?.obj))
        }, onError = {
            sendEvent(DataEvent(tag = TAG_AUDIT_STATUS))
        })
    }

    /**
     * 获取kyc等级认证状态
     */
    private fun userAuthenticationstatusApi() {
        requestNet({
            baseService.userAuthenticationstatusApi()
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(tag = TAG_KYC_AUDIT_STATUS, data = it.data?.obj?.authenticationstatus))
        }, onError = {
            sendEvent(DataEvent(tag = TAG_KYC_AUDIT_STATUS))
        })
    }

    /**
     * 获取 两步 验证状态
     */
    fun twoFactorStatusApi() {
        requestNet({
            baseService.twoFactorStatusApi(token = UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            twoFactorStatusLiveData.value = it.data?.obj
        })
    }

    /**
     * 查询用户是否可以跳转优惠券页面
     */
    private fun queryUserIsProclientApi() {
        requestNet({
            val paramMap = hashMapOf<String, Any>()
            paramMap["userId"] = UserDataUtil.userId()
            baseService.queryUserIsProclientApi(paramMap)
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(tag = TAG_COUPONS, data = it.data?.obj?.proclient == true))
        })
    }

    /**
     * 获取策略信号中心数据
     */
    fun strategySignalProviderCenterApi() {
        requestNet({
            val jsonObject = JsonObject()
            jsonObject.addProperty("stUserId", UserDataUtil.stUserId())
            val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
            stTradingService.strategySignalProviderCenterApi(requestBody)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(tag = TAG_ST_CENTER, data = it.data))
        })
    }

    /**
     * 出金跳转 先进行请求
     */
    fun jumpWithdraw() {
        showLoading()
        // v1 v2监管的话 就直接请求 获取出金地址的接口 然后跳转出金页面
        if (isKycAccount()) {
            fundIsH5WithdrawApi()
            return
        }

        if (isAsic()) {
            addressproofWithrawNeedUploadAddressProofApi()
        } else {
            addressproofWithrawNeedUploadIdPoaProofApi()
        }
    }

    /**
     * 出金是否需要上传身份地址证明(新) 非 asic 调用
     */
    private fun addressproofWithrawNeedUploadIdPoaProofApi() {
        requestNet({
            baseService.addressproofWithrawNeedUploadIdPoaProofApi(token = UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val data = it.data?.obj?.needUploadIdPoaProof

            if ("0" == data) {
                fundIsH5WithdrawApi()
                return@requestNet
            }

            // 1/3 lv2
            if ("1" == data || "3" == data) {
                hideLoading()
                sendEvent(DataEvent(tag = TAG_JUMP_SUMSUB))
                return@requestNet
            }

            // 4/6 lv3
            if ("4" == data || "6" == data) {
                hideLoading()
                sendEvent(DataEvent(tag = TAG_OPEN_ACCOUNT))
                return@requestNet
            }
            hideLoading()
            ToastUtil.showToast(it.data?.obj?.msg)
        }, onError = {
            hideLoading()
        })
    }

    /**
     * 出金查询是否需要上传地址证明 asic 调用
     */
    private fun addressproofWithrawNeedUploadAddressProofApi() {
        requestNet({
            baseService.addressproofWithrawNeedUploadAddressProofApi(token = UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }

            val needUploadAddressProof = it.data?.obj?.needUploadAddressProof

            if (needUploadAddressProof == "0") {
                fundIsH5WithdrawApi()
                return@requestNet
            }
            if (needUploadAddressProof == "3" || needUploadAddressProof == "1") {
                hideLoading()
                sendEvent(DataEvent(tag = TAG_OPEN_ASIC_ADDRESS))
                return@requestNet
            }
            hideLoading()
            ToastUtil.showToast(it.data?.obj?.msg)
        }, onError = {
            hideLoading()
        })
    }

    private fun fundIsH5WithdrawApi() {
        requestNet({
            val map = hashMapOf<String, Any?>("userToken" to UserDataUtil.loginToken())
            // h5新增切换测试环境的地址的功能，这里需要传h5的url，不然的话后台不知道h5用什么环境
            if (!HttpUrl.official) {
                map["h5Url"] = HttpUrl.getTestH5Url()
            }
            baseService.fundIsH5WithdrawApi(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val obj = it.data?.obj

            var htmlUrl = obj?.h5Url.ifNull()
            htmlUrl += if (htmlUrl.contains("?")) "" else "?"

            val socialTradingType = if (UserDataUtil.isStLogin()) {
                "1"
            } else {
                "0"
            }
            if (!htmlUrl.contains("userToken=")) htmlUrl = htmlUrl + "&userToken=" + UserDataUtil.loginToken()
            if (!htmlUrl.contains("mt4AccountId=")) htmlUrl = "$htmlUrl&mt4AccountId=${UserDataUtil.accountCd()}"
            if (!htmlUrl.contains("currency=")) htmlUrl = "$htmlUrl&currency=${UserDataUtil.currencyType()}"
            if (!htmlUrl.contains("type=")) htmlUrl = htmlUrl + "&type=" + SpManager.getSuperviseNum("")
            htmlUrl = "$htmlUrl&socialtradingtype=$socialTradingType"
            sendEvent(DataEvent(tag = TAG_JUMP_WITHDRAW, data = htmlUrl))
        }, onError = {
            hideLoading()
        })
    }

    /**
     * 点击后的调用
     */
    fun eventsAddClicksCountApi(eventsId: String) {
        requestNet({
            baseService.eventsAddClicksCountApi(eventsId = eventsId, token = UserDataUtil.loginToken())
        }, onSuccess = {
        })
    }

    companion object {
        const val TAG_COUPONS = "COUPONS"
        const val TAG_FAVOURITES = "FAVOURITES"
        const val TAG_AUDIT_STATUS = "AUDIT_STATUS"
        const val TAG_KYC_AUDIT_STATUS = "TAG_KYC_AUDIT_STATUS"
        const val TAG_ST_CENTER = "ST_CENTER"
        const val TAG_JUMP_SUMSUB = "JUMP_SUMSUB"
        const val TAG_OPEN_ACCOUNT = "OPEN_ACCOUNT"
        const val TAG_OPEN_ASIC_ADDRESS = "OPEN_ASIC_ADDRESS"
        const val TAG_JUMP_WITHDRAW = "JUMP_WITHDRAW"
    }
}