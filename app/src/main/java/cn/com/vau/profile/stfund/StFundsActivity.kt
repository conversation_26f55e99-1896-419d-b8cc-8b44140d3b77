package cn.com.vau.profile.stfund

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.depositcoupon.ManageFundsObj
import cn.com.vau.databinding.ActivityFundsStBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.activity.manageFunds.FundsContract
import cn.com.vau.profile.activity.manageFunds.FundsModel
import cn.com.vau.profile.activity.manageFunds.FundsPresenter
import cn.com.vau.profile.stfund.fragment.StAccountFundFragment
import cn.com.vau.profile.stfund.fragment.StFundDetailFragment
import cn.com.vau.util.init
import cn.com.vau.util.noRepeat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil

/**
 * 资金管理
 * Created by zhy on 2018/11/16.
 */
class StFundsActivity : BaseFrameActivity<FundsPresenter, FundsModel>(), FundsContract.View {

    private val mBinding by lazy { ActivityFundsStBinding.inflate(layoutInflater) }

    private var profit = "0.0"

    var accountId: String? = ""

    private var accountFundFragmentList = ArrayList<Fragment>()
    private var fundDetailFragmentList = ArrayList<Fragment>()
    private val titleList = arrayListOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initView() {
        super.initView()
        initfundDetailViewPager2()
        mBinding.tvWithdraw.setOnClickListener(this)
        mBinding.tvDeposit.setOnClickListener(this)
    }

    override fun initData() {
        super.initData()
        mBinding.tvCurrencyType.text = UserDataUtil.currencyType()
    }

    override fun onResume() {
        super.onResume()
        accountId = UserDataUtil.accountCd()
        // 查询资金管理
        mPresenter?.queryManageFunds(UserDataUtil.loginToken(), accountId, "1", false)
    }

    private fun initAccountFundViewPager() {
        accountFundFragmentList.clear()
        accountFundFragmentList.add(StAccountFundFragment.newInstance("Manual"))
        accountFundFragmentList.add(StAccountFundFragment.newInstance("Copy"))

        mBinding.accountFundViewPager2.init(
            accountFundFragmentList, titleList, supportFragmentManager, this
        ) { position ->
            mBinding.mTopIndicator.changeIndicator(position)
            mBinding.fundDetailViewPager2.setCurrentItem(position, true)
            mBinding.ctlBottom.isVisible = (position != 1)
        }
    }

    private fun initfundDetailViewPager2() {
        fundDetailFragmentList.clear()
        fundDetailFragmentList.add(StFundDetailFragment.newInstance("Manual"))
        fundDetailFragmentList.add(StFundDetailFragment.newInstance("Copy"))

        mBinding.fundDetailViewPager2.init(
            fundDetailFragmentList, titleList, supportFragmentManager, this
        ) { position ->
            mBinding.mTopIndicator.changeIndicator(position)
            mBinding.accountFundViewPager2.setCurrentItem(position, true)
            mBinding.ctlBottom.isVisible = (position != 1)
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {

            R.id.tvWithdraw -> noRepeat {
                mPresenter?.needUploadAddressProof()
            }

            R.id.tvDeposit -> {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V331.DEPOSIT_TRAFFIC_BUTTON_CLICK, bundleOf(
                        "Position" to "Funds"
                    )
                )
            }
        }
    }

    @SuppressLint("WrongConstant")
    override fun refreshManageFunds(bean: ManageFundsObj?) {
        profit = bean?.profit ?: ""
        initAccountFundViewPager()
        mBinding.mTopIndicator.initIndicatorCount(2)
    }
}