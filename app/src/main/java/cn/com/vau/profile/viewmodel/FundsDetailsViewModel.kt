package cn.com.vau.profile.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.depositcoupon.ManageFundsDetailsObj
import cn.com.vau.util.AppUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull

class FundsDetailsViewModel : BaseViewModel() {

    var orderType = ""
    var accountId: String = ""

    var data: ManageFundsDetailsObj? = null
    val depositDetailsLiveData by lazy {
        MutableLiveData<ManageFundsDetailsObj?>()
    }
    val fundCancelLiveData by lazy {
        MutableLiveData<Pair<Boolean, String?>>()
    }

    fun queryDepositDetails(userToken: String, orderNo: String) {
        val params = HashMap<String, Any>()
        params["userToken"] = userToken
        params["accountId"] = accountId
        params["orderNo"] = orderNo
        params["orderCode"] = orderNo
        params["timeZone"] = AppUtil.getTimeZoneRawOffsetToHour()
        requestNet({
            if (TextUtils.equals(orderType, "00")) {
                baseService.fundMoneyInDetailApi(params)
            } else {
                baseService.fundWithdrawDetailApi(params)
            }
        }, onSuccess = {
            if (it.isSuccess()) {
                it.data?.obj?.let {
                    data = it
                    depositDetailsLiveData.postValue(data)
                }
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    fun fundCancelWithdrawalOrder() {
        val params = hashMapOf<String, Any>(
            "userToken" to UserDataUtil.loginToken(),
            "withdrawId" to data?.orderNum.ifNull(),
            "withdrawMethod" to data?.withdrawMethod.ifNull()
        )
        requestNet({
            baseService.fundCancelWithdrawalOrderApi(params)
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val isSuccess = "true" == it.data?.obj
            fundCancelLiveData.postValue(Pair(isSuccess, it.getResponseMsg()))
        }, isShowDialog = true)
    }
}