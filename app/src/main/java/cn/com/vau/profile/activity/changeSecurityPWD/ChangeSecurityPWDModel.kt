package cn.com.vau.profile.activity.changeSecurityPWD

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean

/**
 * Created by zhy on 2018/11/29.
 */
class ChangeSecurityPWDModel : ChangeSecurityPWDContract.Model {

    override fun updateFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>) {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetUpfundpwdApi(map), baseObserver)
    }
}
