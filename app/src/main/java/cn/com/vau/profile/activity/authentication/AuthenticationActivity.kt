package cn.com.vau.profile.activity.authentication

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.databinding.ActivityAuthenticationBinding
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.init
import cn.com.vau.util.setVp

/**
 * @description: 认证中心界面，显示新版开户各阶段认证结果 ，提供跳转到新版开户界面的功能
 * @author: Guo
 * @createDate: 2023 7.15 0015 14:58
 * @updateUser:
 * @updateDate: 2023 7.15 0015 14:58
 */
class AuthenticationActivity :
    BaseMvvmActivity<ActivityAuthenticationBinding, AuthViewModel>(),
    OnClickListener {

    private val name: String? by lazy { intent.getStringExtra(KEY_NAME) ?: UserDataUtil.nickname() }
    private val iconUrl: String? by lazy { intent.getStringExtra(KEY_ICON) ?: UserDataUtil.userPic() }
    private val index: Int by lazy { intent.getIntExtra(KEY_INDEX, 0) }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        ImageLoaderUtil.loadImage(this, iconUrl ?: "", mBinding.ivIcon)
        mBinding.tvUserName.text = name ?: ""

        val tabs = mutableListOf(getString(R.string.standard), getString(R.string.plus))
        val fragmentList: MutableList<Fragment> = mutableListOf(AuthStandardFragment.newInstance(), AuthPlusFragment.newInstance())
        mBinding.run {
            mViewPager.init(fragmentList, tabs, supportFragmentManager, this@AuthenticationActivity)
            mTabLayout.setVp(mViewPager, tabs, TabType.LINE_INDICATOR)
            mViewPager.offscreenPageLimit = fragmentList.size
            mViewPager.descendantFocusability = ViewGroup.FOCUS_BLOCK_DESCENDANTS
            mViewPager.isUserInputEnabled = false // 禁止左右滑动
            mViewPager.currentItem = index

            // 先设置默认颜色
            changeTextViewLeftDrawAndColor(mBinding.tvTitleAcount, false)
            changeTextViewLeftDrawAndColor(mBinding.tvTitleDeposit, false)
            // lv2
            changeTextViewLeftDrawAndColor(mBinding.tvTitleTrade, false)
            // lv3
            changeTextViewLeftDrawAndColor(mBinding.tvTitleWithdrawal, false)
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getAuditStatusApi()
    }

    override fun initListener() {
        super.initListener()
        mViewModel.accountStatusLiveData.observe(this) {
            updateView(it)
        }
    }

    /**
     * 根据接口数据 设置 顶部 lv1和lv2的状态
     */
    private fun updateView(data: AuditStatusData.Obj?) {
        data?.let {
            updateLv1(it)
            updateLv2(it)

            if (data.accountAuditStatus == TYPE_LV1_COMPLETED && data.poiAuditStatus == TYPE_COMPLETED) {
                updateTextView(mBinding.tvVerifiedState, TEXTVIEW_TYPE_VERIFIED)
            } else if (data.accountAuditStatus == TYPE_LV1_COMPLETED || data.poiAuditStatus == TYPE_COMPLETED) {
                updateTextView(mBinding.tvVerifiedState, TEXTVIEW_TYPE_SEMI_VERIFIED)
            } else {
                updateTextView(mBinding.tvVerifiedState, TEXTVIEW_TYPE_GET_VERIFIED)
            }
            // lv1
            changeTextViewLeftDrawAndColor(mBinding.tvTitleAcount, data.accountAuditStatus == TYPE_LV1_COMPLETED)
            changeTextViewLeftDrawAndColor(mBinding.tvTitleDeposit, data.accountAuditStatus == TYPE_LV1_COMPLETED)
            // lv2
            changeTextViewLeftDrawAndColor(mBinding.tvTitleTrade, data.poiAuditStatus == TYPE_COMPLETED)
            // lv3
            changeTextViewLeftDrawAndColor(mBinding.tvTitleWithdrawal, data.poaAuditStatus == TYPE_COMPLETED)
            // bank
            if (data.ibtPoaAuditStatus.isNullOrBlank() && data.ibtPoiAuditStatus.isNullOrBlank()) {
                changeTextViewLeftDrawAndColor(mBinding.tvTitleBank, false)
                mBinding.tvTitleBank.isVisible = false
            } else if (data.ibtPoaAuditStatus == TYPE_COMPLETED && data.ibtPoiAuditStatus == TYPE_COMPLETED) {
                changeTextViewLeftDrawAndColor(mBinding.tvTitleBank, true)
                mBinding.tvTitleBank.isVisible = true
            } else {
                changeTextViewLeftDrawAndColor(mBinding.tvTitleBank, false)
                mBinding.tvTitleBank.isVisible = true
            }
        }
    }

    /**
     * 根据lv1的状态 修改按钮样式
     */
    private fun updateLv1(data: AuditStatusData.Obj) {
        when (data.accountAuditStatus) {
            TYPE_LV1_SUBMITTED, TYPE_LV1_REAUDIT -> {
                updateTextView(mBinding.tvVerifiedLv1State, TEXTVIEW_TYPE_UNDER_REVIEW)
            }

            TYPE_LV1_NOTSUBMITTED, TYPE_LV1_PENDING -> {
                updateTextView(mBinding.tvVerifiedLv1State, TEXTVIEW_TYPE_UN_VERIFIED)
            }

            TYPE_LV1_REJECTED2, TYPE_LV1_REJECTED9 -> {
                updateTextView(mBinding.tvVerifiedLv1State, TEXTVIEW_TYPE_REJECTED)
            }

            TYPE_LV1_COMPLETED -> {
                updateTextView(mBinding.tvVerifiedLv1State, TEXTVIEW_TYPE_VERIFIED)
            }

            else -> {
                updateTextView(mBinding.tvVerifiedLv1State, TEXTVIEW_TYPE_UN_VERIFIED)
            }
        }
    }

    private fun updateLv2(data: AuditStatusData.Obj) {
        when (data.poiAuditStatus) {
            TYPE_SUBMITTED, TYPE_REAUDIT -> {
                updateTextView(mBinding.tvVerifiedLv2State, TEXTVIEW_TYPE_UNDER_REVIEW)
            }

            TYPE_PENDING -> {
                updateTextView(mBinding.tvVerifiedLv2State, TEXTVIEW_TYPE_UN_VERIFIED)
            }

            TYPE_REJECTED -> {
                updateTextView(mBinding.tvVerifiedLv2State, TEXTVIEW_TYPE_REJECTED)
            }

            TYPE_COMPLETED -> {
                updateTextView(mBinding.tvVerifiedLv2State, TEXTVIEW_TYPE_VERIFIED)
            }

            else -> {
                updateTextView(mBinding.tvVerifiedLv2State, TEXTVIEW_TYPE_UN_VERIFIED)
            }
        }
    }

    private fun changeTextViewLeftDrawAndColor(tv: TextView, isPass: Boolean) {
        val drawable = tv.compoundDrawables[0] // 获取左侧的图标
        if (isPass) {
            drawable?.mutate()?.let {
                if (tv.id != R.id.tvTitleWithdrawal) {
                    tv.compoundDrawableTintList = ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
                } else {
                    val drawableRes = AttrResourceUtil.getDrawable(this, R.attr.imgPermissionWithdrawal)
                    val d = ContextCompat.getDrawable(this, drawableRes)
                    tv.setCompoundDrawablesWithIntrinsicBounds(d, null, null, null)
                }
            }
            tv.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        } else {
            drawable?.mutate()?.let {
                if (tv.id != R.id.tvTitleWithdrawal) {
                    tv.compoundDrawableTintList = ColorStateList.valueOf(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff))
                } else {
                    val drawableRes = AttrResourceUtil.getDrawable(this, R.attr.imgPermissionWithdrawalFailed)
                    val d = ContextCompat.getDrawable(this, drawableRes)
                    tv.setCompoundDrawablesWithIntrinsicBounds(d, null, null, null)
                }
            }
            tv.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff))
        }
    }

    companion object {
        // "POA审核状态 0：Submitted；1：Pending；2：Completed；3：Rejected；4：Re-Audit；"
        const val TYPE_SUBMITTED = "0"
        const val TYPE_PENDING = "1"
        const val TYPE_COMPLETED = "2"
        const val TYPE_REJECTED = "3"
        const val TYPE_REAUDIT = "4"

        // -1:NotSubmitted;0:Submitted;1:Completed;2:Rejected;3:Pending;4:Re-Audit;9:Rejected
        const val TYPE_LV1_NOTSUBMITTED = "-1"
        const val TYPE_LV1_SUBMITTED = "0"
        const val TYPE_LV1_COMPLETED = "1"
        const val TYPE_LV1_REJECTED2 = "2"
        const val TYPE_LV1_PENDING = "3"
        const val TYPE_LV1_REAUDIT = "4"
        const val TYPE_LV1_REJECTED9 = "9"

        private const val KEY_NAME = "name"
        private const val KEY_ICON = "icon"
        private const val KEY_INDEX = "index"

        /**
         * 打开认证中心界面
         * @param name 用户名
         * @param iconUrl 用户头像
         * @param defaultSelectedIndex 默认展示的是哪个tab
         */
        fun openActivity(context: Context, name: String? = null, iconUrl: String? = null, defaultSelectedIndex: Int = 0) {
            val intent = Intent(context, AuthenticationActivity::class.java)
            intent.putExtra(KEY_NAME, name)
            intent.putExtra(KEY_ICON, iconUrl)
            intent.putExtra(KEY_INDEX, defaultSelectedIndex)
            context.startActivity(intent)
        }

        const val TEXTVIEW_TYPE_GET_VERIFIED = "Get Verified"
        const val TEXTVIEW_TYPE_UN_VERIFIED = "Unverified"
        const val TEXTVIEW_TYPE_SEMI_VERIFIED = "Semi-Verified"
        const val TEXTVIEW_TYPE_UNDER_REVIEW = "Under Review"
        const val TEXTVIEW_TYPE_REJECTED = "Rejected"
        const val TEXTVIEW_TYPE_VERIFIED = "Verified"

        /**
         * 根据type修改按钮样式，个人主页也有使用，所以放到了这里
         */
        fun updateTextView(tv: TextView, type: String): CharSequence {
            when (type) {
                TEXTVIEW_TYPE_GET_VERIFIED, TEXTVIEW_TYPE_UN_VERIFIED -> {
                    tv.text = tv.context.getString(if (type == TEXTVIEW_TYPE_GET_VERIFIED) R.string.get_verified else R.string.unverified)
                    tv.setTextColor(AttrResourceUtil.getColor(tv.context, R.attr.color_ca61e1e1e_c99ffffff))
                    tv.background = ContextCompat.getDrawable(tv.context, R.drawable.draw_shape_ce8e8e8_c414348_r100)
                    tv.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                }

                TEXTVIEW_TYPE_SEMI_VERIFIED -> {
                    tv.text = tv.context.getString(R.string.semi_verified)
                    tv.setTextColor(ContextCompat.getColor(tv.context, R.color.cff8e5c))
                    tv.background = ContextCompat.getDrawable(tv.context, R.drawable.draw_shape_cfcebe5_c3e3535_r100)
                    val drawable = ContextCompat.getDrawable(tv.context, R.drawable.bitmap_img_source_tick11x8_ce35728)
                    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                    tv.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
                }

                TEXTVIEW_TYPE_UNDER_REVIEW -> {
                    tv.text = tv.context.getString(R.string.under_review)
                    tv.setTextColor(ContextCompat.getColor(tv.context, R.color.ce35728))
                    tv.background = ContextCompat.getDrawable(tv.context, R.drawable.draw_shape_cf9ebe6_c3b2f2f_r100)
                    tv.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                }

                TEXTVIEW_TYPE_REJECTED -> {
                    tv.text = tv.context.getString(R.string.rejected)
                    tv.setTextColor(ContextCompat.getColor(tv.context, R.color.cf44040))
                    tv.background = ContextCompat.getDrawable(tv.context, R.drawable.draw_shape_cfae9e8_c3c2d32_r100)
                    tv.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                }

                TEXTVIEW_TYPE_VERIFIED -> {
                    tv.text = tv.context.getString(R.string.verified)
                    tv.setTextColor(ContextCompat.getColor(tv.context, R.color.c00c79c))
                    tv.background = ContextCompat.getDrawable(tv.context, R.drawable.draw_shape_ce5f7f3_c283b3d_r100)
                    val drawable = ContextCompat.getDrawable(tv.context, R.drawable.bitmap_img_source_tick11x8_c00c79c)
                    drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                    tv.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
                }
            }
            return tv.text
        }

        fun getVerifyString(context: Context, type: String) = when (type) {
            TEXTVIEW_TYPE_GET_VERIFIED -> {
                context.getString(R.string.get_verified)
            }

            TEXTVIEW_TYPE_SEMI_VERIFIED -> {
                context.getString(R.string.semi_verified)
            }

            TEXTVIEW_TYPE_UNDER_REVIEW -> {
                context.getString(R.string.under_review)
            }

            TEXTVIEW_TYPE_REJECTED -> {
                context.getString(R.string.rejected)
            }

            TEXTVIEW_TYPE_VERIFIED -> {
                context.getString(R.string.verified)
            }

            else -> {
                context.getString(R.string.unverified)
            }
        }

        const val PASS_STATUS_GREY = "grey"
        const val PASS_STATUS_GREEN = "green"
        const val PASS_STATUS_ORANGE = "orange"
        const val PASS_STATUS_REJECTED = "Rejected"

        /**
         * 根据type修改按钮样式，另外两个fragment也有使用，所以放到了这里
         */
        fun checkChildPassStatus(tv: TextView, type: String?) {
            val drawable = when (type) {
                PASS_STATUS_GREY -> ContextCompat.getDrawable(tv.context, R.drawable.draw_bitmap_circle_right_c731e1e1e_c61ffffff)
                PASS_STATUS_GREEN -> ContextCompat.getDrawable(tv.context, R.drawable.icon2_cb_tick_circle_c15b374)
                PASS_STATUS_ORANGE -> ContextCompat.getDrawable(tv.context, R.drawable.icon2_cb_tick_circle_ce35728)
                PASS_STATUS_REJECTED -> ContextCompat.getDrawable(tv.context, R.drawable.icon2_close_circle_cb)
                else -> ContextCompat.getDrawable(tv.context, R.drawable.draw_bitmap_circle_right_c731e1e1e_c61ffffff)
            }
            drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
            tv.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
        }
    }
}