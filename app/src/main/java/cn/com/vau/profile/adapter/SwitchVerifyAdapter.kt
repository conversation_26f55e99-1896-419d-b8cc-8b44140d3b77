package cn.com.vau.profile.adapter

import cn.com.vau.R
import cn.com.vau.page.common.BaseListBean
import cn.com.vau.util.AttrResourceUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: SwitchVerifyAdapter.kt
 * Author: GG
 * Date: 2024/2/21
 * Description:
 */
class SwitchVerifyAdapter(data: MutableList<SwitchVerifyData>) : BaseQuickAdapter<SwitchVerifyData, BaseViewHolder>(R.layout.item_switch_verify, data) {
    private val color_c1e1e1e_cebffffff by lazy {
        AttrResourceUtil.getColor(
            context,
            R.attr.color_c1e1e1e_cebffffff
        )
    }
    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(
            context,
            R.attr.color_c731e1e1e_c61ffffff
        )
    }
    override fun convert(holder: BaseViewHolder, item: SwitchVerifyData) {
        holder.setText(R.id.tvTitle, item.name)
            .setGone(R.id.tvPrompt, !item.isSplit)
            .setTextColor(R.id.tvTitle,if (item.enable) color_c1e1e1e_cebffffff else color_c731e1e1e_c61ffffff)
    }
}

data class SwitchVerifyData(val name: String, val isSplit: Boolean,var enable:Boolean = true) : BaseListBean {
    override fun getShowItemValue(): String = name
}