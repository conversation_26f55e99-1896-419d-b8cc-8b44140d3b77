package cn.com.vau.profile.adapter

import androidx.annotation.Keep
import com.chad.library.adapter.base.BaseNodeAdapter
import com.chad.library.adapter.base.entity.node.BaseNode

/**
 * @description:
 * @author: GG
 * @createDate: 2024 12月 07 14:10
 * @updateUser:
 * @updateDate: 2024 12月 07 14:10
 */
class ProfileItemAdapter : BaseNodeAdapter() {

    init {
        addFullSpanNodeProvider(ProfileTitleNodeAdapter())
        addNodeProvider(ProfileItemNodeAdapter())
    }

    override fun getItemType(data: List<BaseNode>, position: Int): Int {
        val node = data.getOrNull(position) ?: return -1
        if (node !is ProfileItemBean) return -1
        return node.itemType
    }
}

@Keep
data class ProfileItemBean(
    val title: String?,
    val icon: Int? = null,
    var rightTopIcon: Int? = null,
    var rightTopString: String? = null,
    val itemType: Int,
    override val childNode: MutableList<BaseNode> = mutableListOf()
) : BaseNode() {

    companion object {
        const val TYPE_TITLE = 1
        const val TYPE_ITEM = 2
    }
}