package cn.com.vau.profile.activity.twoFactorAuth.fragment

import android.os.Bundle
import androidx.core.view.*
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentTfaOptBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.CountDownTextHelper
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoWithIconListDialog
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collectLatest

/**
 * Filename: TFAUnBindOTPFragment.kt
 * Author: GG
 * Date: 2024/2/19
 * Description:
 */
class TFAOTPFragment : BaseMvvmBindingFragment<FragmentTfaOptBinding>() {

    private val mViewModel: TFAViewModel by activityViewModels()

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { requireContext().getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.currentPageLiveData.value = TFAViewModel.PAGE_OPT
    }

    override fun initView() {
        // 如果是邮箱验证 就隐藏 手机号发送的相关button
        if (mViewModel.verificationType == 1) {
            mBinding.tvSendEms.isGone = true
            mBinding.tvOr.isGone = true
            mBinding.groupWhatsApp.isGone = true
        }
        // 添加一个倒计时 适配iOS 不是同一个页面 无法维护上一个倒计时，所以设置进入页面 就进行一次倒计时
        reSendEmsCountDown()
        mBinding.tvReSendEms.setOnClickListener {
            if (mViewModel.verificationType == 1) {
                mViewModel.emailSendEmailCodeApi()
            } else {
                mViewModel.changeGetTelSmsApi()
            }
        }
        // 收不到验证码提示文案
        mBinding.tvNotReceiveCodeTips.setOnClickListener {
            showNotReceiveCodeDialog()
        }
        mBinding.viewWhatsApp.setOnClickListener {
            mViewModel.changeGetTelSmsApi("2")
        }
        mBinding.tvSendEms.setOnClickListener {
            mViewModel.changeGetTelSmsApi("1")
        }

        mBinding.tvAuthCodePrompt.text = buildString {
            append(getString(R.string.the_verification_code_has_been_sent_to))
            append(":")
        }
        mBinding.tvContactNum.text = if (mViewModel.verificationType == 1) {
            mViewModel.paramLiveData.value?.email.ifNull(UserDataUtil.email())
        } else {
            buildString {
                append("+")
                append(mViewModel.paramLiveData.value?.areaCode.ifNull(UserDataUtil.areaCode()))
                append(" ")
                append(mViewModel.paramLiveData.value?.mobile.ifNull(UserDataUtil.userTel()))
            }
        }

        mBinding.passwordView.setPasswordListener(object : PasswordView.PasswordListener {
            override fun passwordChange(changeText: String?) {

            }

            override fun passwordComplete() {
                mViewModel.otpValidateCode = mBinding.passwordView.getPassword()
                if (mViewModel.verificationType == 1) {
                    mViewModel.emailPreValidateEmailCodeApi()
                } else {
                    mViewModel.smsValidateSmsCodeApi()
                }
                mBinding.passwordView.hiddenSoftInputFromWindow()
            }

            override fun keyEnterPress(password: String?, isComplete: Boolean) {
            }

        })
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                delay(300)
                mBinding.passwordView.showSoftInput()
            }
        }
    }

    private fun reSendEmsCountDown() {
        mViewModel.countDown(60)
    }

    override fun createObserver() {
        super.createObserver()

        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it == "countDown") {
                    if (mViewModel.countDownLiveData.value == null) {
                        reSendEmsCountDown()
                    }
                    mBinding.tvReSendEms.isEnabled = false
                    if (mViewModel.verificationType == 3) {
                        mBinding.tvSendEms.isEnabled = false
                        mBinding.viewWhatsApp.isEnabled = false
                        when (mViewModel.sendTypeLiveData.value) {
                            "1" -> {
                                mBinding.tvSendEms.isVisible = false
                                mBinding.groupWhatsApp.isVisible = true
                            }

                            "2" -> {
                                mBinding.tvSendEms.isVisible = true
                                mBinding.groupWhatsApp.isVisible = false
                            }
                        }
                    }
                }
            }
        }

        mViewModel.sendTypeLiveData.observe(this) { value ->
            if (mViewModel.verificationType == 3) {
                mBinding.tvSendEms.isVisible = value == "1"
                mBinding.groupWhatsApp.isVisible = value == "2"
            }
        }

        // 倒计时
        mViewModel.countDownLiveData.observe(this) {
            it?.let { millisUntilFinished ->
                if (millisUntilFinished == 0) {
                    mBinding.tvReSendEms.setTextColor(ce35728)
                    mBinding.tvReSendEms.text = getString(R.string.resend)
                    mBinding.tvReSendEms.isEnabled = true

                    // 如果是手机号验证 就设置相关按钮
                    if (mViewModel.verificationType == 3) {
                        mBinding.tvSendEms.isEnabled = true
                        mBinding.viewWhatsApp.isEnabled = true
                        if (mBinding.tvSendEms.isVisible) {
                            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                            mBinding.tvSendEms.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_cebffffff_c1e1e1e))
                        }
                        if (mBinding.viewWhatsApp.isVisible)
                            mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
                    }
                } else {
                    val second = millisUntilFinished.toString() // 倒计时秒数
                    val fullText = getString(R.string.resend_code_in_x_seconds, second)
                    mBinding.tvReSendEms.setTextColor(color_c1e1e1e_cebffffff)
                    mBinding.tvReSendEms.text = countDownTextHelper.updateCountDownText(fullText, second)
                    mBinding.tvReSendEms.isEnabled = false
                    // 如果是手机号验证 就设置相关按钮
                    if (mViewModel.verificationType == 3) {
                        mBinding.tvSendEms.isEnabled = false
                        mBinding.viewWhatsApp.isEnabled = false

                        when (mViewModel.sendTypeLiveData.value) {
                            "1" -> {
                                mBinding.tvSendEms.isVisible = false
                                mBinding.groupWhatsApp.isVisible = true
                            }

                            "2" -> {
                                mBinding.tvSendEms.isVisible = true
                                mBinding.groupWhatsApp.isVisible = false
                            }
                        }

                        if (mBinding.tvSendEms.isVisible) {
                            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
                            mBinding.tvSendEms.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c731e1e1e_c61ffffff))
                        }
                        if (mBinding.viewWhatsApp.isVisible)
                            mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
                    }
                }
            }
        }
    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        mBinding.passwordView.hiddenSoftInputFromWindow()
        val data = if (mViewModel.verificationType == 1) {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_email_address),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips3)
                ),
                HintLocalData(
                    getString(R.string.check_your_spam_junk_folder),
                    getString(R.string.sometimes_the_email_by_mistake),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips4)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips2)
                )
            )
        } else {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_phone_number),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips1)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips2)
                ),
                HintLocalData(
                    getString(R.string.try_another_verification_method),
                    getString(R.string.switch_verification_methods_your_otp),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips3)
                )
            )
        }
        BottomInfoWithIconListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }
}