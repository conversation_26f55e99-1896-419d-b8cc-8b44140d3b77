package cn.com.vau.profile.activity.pricealert.activity

import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewStub
import android.view.ViewStub.OnInflateListener
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.databinding.ActivityPriceAlertsManageBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.profile.activity.pricealert.fragment.PriceAlertsFragment
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SELECT
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description: 价格提醒管理页面
 * @author: GG
 * @createDate: 2024 9月 27 14:28
 * @updateUser:
 * @updateDate: 2024 9月 27 14:28
 */
class PriceAlertsManageActivity : BaseMvvmActivity<ActivityPriceAlertsManageBinding, PriceAlertsManageViewModel>() {

    private val unSelectIcon: Drawable? by lazy {
        ContextCompat.getDrawable(this, R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    private val selectIcon: Drawable? by lazy {
        ContextCompat.getDrawable(this, R.drawable.icon2_cb_tick_circle_c15b374)?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    override fun initView() {
        mBinding.mVsNoData.setOnInflateListener(object : OnInflateListener {
            override fun onInflate(stub: ViewStub, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setHintMessage(getString(R.string.no_alert))
            }
        })
        mBinding.mVsNoData.isVisible = true
        SensorsDataUtil.track(SensorsConstant.V3510.PRICE_ALERT_PAGE_VIEW)
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setEndTextClickListener {
            if (mViewModel.priceAlertListLiveData.value.isNullOrEmpty())
                return@setEndTextClickListener
            val isEdit = mViewModel.isEditLiveData.value.ifNull(false)
            mViewModel.isEditLiveData.value = !isEdit
        }
        mBinding.tvSelectAll.setOnClickListener {
            changeSelectState(mBinding.tvSelectAll.compoundDrawablesRelative.getOrNull(0) == unSelectIcon)
            updateData()
            updateDeleteButton(mBinding.viewPager.currentItem)
        }
        mBinding.tvDelete.clickNoRepeat {
            if (mBinding.tvDelete.text == getString(R.string.delete) + "(0)")
                return@clickNoRepeat
            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.delete_alert)) //设置内容
                .setStartText(getString(R.string.no))
                .setEndText(getString(R.string.yes))
                .setOnEndListener { mViewModel.deletePriceWarn(mBinding.viewPager.currentItem) }
                .build()
                .showDialog()
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getPriceWarn()
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.isEditLiveData.observe(this) {
            if (it == true) {
                mBinding.mHeaderBar.setEndText(getString(R.string.done))
                mBinding.groupBottom.isVisible = true
                updateAllSelectButton(mBinding.viewPager.currentItem)
                updateDeleteButton(mBinding.viewPager.currentItem)
                SensorsDataUtil.track(SensorsConstant.V3510.PRICE_ALERT_PAGE_MANAGE_BTN_CLICK)
            } else {
                mBinding.mHeaderBar.setEndText(getString(R.string.manage))
                mBinding.mHeaderBar.setEndTextVisible(!mViewModel.priceAlertListLiveData.value.isNullOrEmpty())
                mBinding.groupBottom.isVisible = false
            }
        }
        mViewModel.priceAlertListLiveData.observe(this) { dataList ->
            if (dataList.isNullOrEmpty()) {
                mBinding.mVsNoData.isVisible = true
                mBinding.groupTab.isVisible = false
                mViewModel.isEditLiveData.value = false
                mBinding.mHeaderBar.setEndTextVisible(false)
                return@observe
            }
            mBinding.mHeaderBar.setEndText(getString(R.string.manage))
            mBinding.mHeaderBar.setEndTextVisible(true)
            mBinding.mVsNoData.isVisible = false
            mBinding.groupTab.isVisible = true
            // 使用 map 函数将每个列表转换为 ID 的集合
            val ids1 = mViewModel.oldData
            val ids2 = dataList.flatMap { it.list.orEmpty() }.flatMap { it.list.orEmpty() }.map { it.id }
            if (ids1?.containsAll(ids2) == true && ids2.containsAll(ids1))
                return@observe
            val titleList: MutableList<String> = dataList.map { VAUSdkUtil.getGroupNameLanguage(this, it.groupName.ifNull()) + " (${it.list?.flatMap { it.list.orEmpty() }?.size})" }.toMutableList()
            titleList.add(0, getString(R.string.all) + " (${dataList.asSequence().flatMap { it.list.orEmpty() }.flatMap { it.list.orEmpty() }.count()})")
            val fragmentList: MutableList<Fragment> = titleList.mapIndexed { index, _ -> PriceAlertsFragment.newInstance(index.toString()) }.toMutableList()
            mBinding.viewPager.init(fragmentList, titleList, supportFragmentManager, this)
            mBinding.tabLayout.setVp(mBinding.viewPager, titleList, TabType.LINE_INDICATOR) {
                updateAllSelectButton(it)
                updateDeleteButton(it)
                mViewModel.sendEvent(DataEvent(mBinding.viewPager.currentItem.toString()))
            }
            mBinding.viewPager.offscreenPageLimit = fragmentList.size
            updateAllSelectButton(mBinding.viewPager.currentItem)
            updateDeleteButton(mBinding.viewPager.currentItem)
            mViewModel.sendEvent(DataEvent(mBinding.viewPager.currentItem.toString()))
            mViewModel.updatePrice()
            mViewModel.oldData = dataList.flatMap { it.list.orEmpty() }.flatMap { it.list.orEmpty() }.map { it.id }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow
                .collectLatest { event ->
                    if (event == PriceAlertsManageViewModel.ADAPTER_SELECT) {
                        updateAllSelectButton(mBinding.viewPager.currentItem)
                        updateDeleteButton(mBinding.viewPager.currentItem)
                    }
                }
        }
    }

    /**
     * 点击全选按钮以后的数据更新
     */
    private fun updateData() {
        if (mBinding.viewPager.currentItem == 0) {
            mViewModel.priceAlertListLiveData.value?.forEach {
                it.list?.forEach {
                    it.list?.forEach {
                        it.isSelect = mBinding.tvSelectAll.compoundDrawablesRelative.getOrNull(0) != unSelectIcon
                    }
                }
            }
        } else {
            mViewModel.priceAlertListLiveData.value?.getOrNull(mBinding.viewPager.currentItem - 1)?.list?.forEach {
                it.list?.forEach {
                    it.isSelect = mBinding.tvSelectAll.compoundDrawablesRelative.getOrNull(0) != unSelectIcon
                }
            }
        }
        mViewModel.sendEvent(ADAPTER_SELECT)
    }

    /**
     * 修改全选按钮 的 选中图标样式
     */
    private fun changeSelectState(isSelect: Boolean) {
        if (isSelect) {
            mBinding.tvSelectAll.setCompoundDrawablesRelative(selectIcon, null, null, null)
        } else {
            mBinding.tvSelectAll.setCompoundDrawablesRelative(unSelectIcon, null, null, null)
        }
    }

    /**
     * 编辑状态下  通过数据刷新全选按钮 的 图标样式
     */
    private fun updateAllSelectButton(index: Int) {
        if (mViewModel.isEditLiveData.value == true) {
            if (index == 0) {
                // all 页面 过滤全部item的 select状态 设置给 全选按钮
                changeSelectState(mViewModel.priceAlertListLiveData.value?.all { it.list?.all { it.list?.all { it.isSelect } == true } == true } == true)
            } else {
                // 其他页面 过滤对应item的 select状态 设置给 全选按钮
                changeSelectState(mViewModel.priceAlertListLiveData.value?.getOrNull(index - 1)?.list?.all { it.list?.all { it.isSelect } == true } == true)
            }
        }
    }

    /**
     * 修改删除按钮的文本
     */
    private fun updateDeleteButton(index: Int) {
        if (mViewModel.isEditLiveData.value == true) {
            if (index == 0) {
                mBinding.tvDelete.text = buildString {
                    append(getString(R.string.delete))
                    append("(${mViewModel.priceAlertListLiveData.value?.sumOf { it.list?.sumOf { it.list?.count { it.isSelect }.ifNull() }.ifNull() }.ifNull()})")
                }
            } else {
                mBinding.tvDelete.text = buildString {
                    append(getString(R.string.delete))
                    append("(${mViewModel.priceAlertListLiveData.value?.getOrNull(index - 1)?.list?.sumOf { it.list?.count { it.isSelect }.ifNull() }.ifNull()})")
                }
            }
        }
    }
}