package cn.com.vau.profile.activity.twoFactorAuth.fragment

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentTfaResultBinding
import cn.com.vau.page.login.activity.*
import cn.com.vau.profile.activity.twoFactorAuth.activity.*
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import org.greenrobot.eventbus.EventBus

/**
 * Filename: TFAUnBindPwdFragment.kt
 * Author: GG
 * Date: 2024/2/19
 * Description:
 */
class TFAResultFragment : BaseMvvmBindingFragment<FragmentTfaResultBinding>() {

    private val mViewModel: TFAViewModel by activityViewModels()

    private val email = AppUtil.getSuperviseEmail()

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.currentPageLiveData.value = TFAViewModel.PAGE_RESULT
    }

    override fun initView() {
        mBinding.tvNext.setOnClickListener {
            ActivityManagerUtil.getInstance().finishActivity(TFAChangePromptActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(TFAResetActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(TFASettingActivity::class.java)
            ActivityManagerUtil.getInstance().finishAllSameActivity(TFABindActivity::class.java)
            ActivityManagerUtil.getInstance().finishAllSameActivity(TFAChangeActivity::class.java)
            ActivityManagerUtil.getInstance().finishAllSameActivity(TFAVerifyActivity::class.java)
            ActivityManagerUtil.getInstance().finishAllSameActivity(VerifySmsCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishAllSameActivity(VerifyEmailCodeActivity::class.java)
            EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
        }
    }

    override fun initData() {
        super.initData()

        when (mViewModel.pageType) {
            TFAViewModel.TYPE_BIND -> {
                when (mViewModel.tfaBindResult?.getResponseCode()) {
                    "V00000" -> {
                        mBinding.groupSuccess.isVisible = true
                        mBinding.tvPromptSuccess.text = getString(R.string.two_factor_authentication_2fa_successfully_enabled)

                        LogEventUtil.setLogEvent(BuryPointConstant.V347.PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_STATUS_PAGE_VIEW, Bundle().apply {
                            putString(
                                "Status", "Successful"
                            )
                        })
                    }

                    "V50006" -> {
                        mBinding.groupFail.isVisible = true

                        mBinding.tvFailPrompt1.text = buildString {
                            append(getString(R.string._2fa_bind_error_msg))
                            append("\n\n")
                            append(getString(R.string._2fa_x_email_error_msg, email))
                        }
                        mBinding.tvFailPrompt1.set(email, ContextCompat.getColor(requireContext(), R.color.ce35728)) {
                            val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                                data = Uri.parse("mailto:$email")
                            }

                            if (emailIntent.resolveActivity(requireContext().packageManager) != null) {
                                startActivity(emailIntent)
                            }

                            email.copyText(getString(R.string.success))
                        }
                    }
                }
            }

            TFAViewModel.TYPE_RESET -> {
                mBinding.groupFail.isVisible = true

                mBinding.tvFailPrompt1.text = buildString {
                    append(getString(R.string._2fa_reset_error_msg))
                    append("\n\n")
                    append(getString(R.string._2fa_x_email_error_msg, email))
                }
                mBinding.tvFailPrompt1.set(email, ContextCompat.getColor(requireContext(), R.color.ce35728)) {
                    val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                        data = Uri.parse("mailto:$email")
                    }

                    if (emailIntent.resolveActivity(requireContext().packageManager) != null) {
                        startActivity(emailIntent)
                    }
                    email.copyText(getString(R.string.success))
                }
                LogEventUtil.setLogEvent(BuryPointConstant.V347.PROFILE_ACCOUNT_SECURITY_2FA_RESET_STATUS_PAGE_VIEW, Bundle().apply {
                    putString(
                        "Status", "Contact_OP"
                    )
                })
            }

            TFAViewModel.TYPE_CHANGE -> {
                if (mViewModel.tfaChangeResult?.getResponseCode() == "V00000") {
                    mBinding.groupSuccess.isVisible = true
                    mBinding.tvResetPrompt.isVisible = true
                    mBinding.tvResetPrompt.text = mViewModel.tfaChangeResult?.data?.obj?.msg
                    mBinding.tvPromptSuccess.text = getString(R.string.two_factor_authentication_2fa_successfully_changed)
                    mBinding.tvPromptSuccess.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff))

                    LogEventUtil.setLogEvent(BuryPointConstant.V347.PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_STATUS_PAGE_VIEW, Bundle().apply {
                        putString(
                            "Status", "Successful"
                        )
                    })
                }
            }
        }
    }

}