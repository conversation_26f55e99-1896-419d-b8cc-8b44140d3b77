package cn.com.vau.profile.stfund.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.view.*
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.depositcoupon.FundHistoryData
import cn.com.vau.util.arabicReverseTextByFlag

/**
 * 资金明细Adapter
 * Created by zhy on 2018/11/19.
 */
class ManageStFundsAdapter(
    private val mContext: Context,
    private var mList: List<FundHistoryData>?,
    private val accType: String
) : RecyclerView.Adapter<ManageStFundsAdapter.ManageStFundsViewHolder>() {
    var currency: String = UserDataUtil.currencyType()
    private var onItemClickListener: OnItemClickListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ManageStFundsViewHolder {
        val view = LayoutInflater.from(mContext).inflate(R.layout.item_manage_funds, parent, false)
        return ManageStFundsViewHolder(view)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ManageStFundsAdapter.ManageStFundsViewHolder, position: Int) {
        val bean = mList?.elementAtOrNull(position)
        val lastBean = mList?.elementAtOrNull(position - 1)
        holder.tvDateYM.text = bean?.createDate
        holder.tvDateYM.visibility =
            if (bean?.createDate == lastBean?.createDate ?: "")
                View.GONE
            else
                View.VISIBLE

        holder.tvDate.text = bean?.createTimeStr

        holder.tvTitle.text = bean?.action?.arabicReverseTextByFlag("@")
        holder.tvState.text = bean?.status
        holder.tvMoney.text = (bean?.amountflag + bean?.amount + " " + currency).arabicReverseTextByFlag(" ")

//        if (bean?.actionCode == "00" || bean?.actionCode == "01") {
//            holder.ivFoundDetail.visibility = View.VISIBLE
//        } else {
//            holder.ivFoundDetail.visibility = View.INVISIBLE
//        }

        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(View.OnClickListener { v ->
                if ((TextUtils.equals(bean?.actionCode, "00")
                            || TextUtils.equals(bean?.actionCode, "01"))
                ) onItemClickListener?.onItemClick(v, position)
            })
        }
    }

    override fun getItemCount(): Int {
        return mList?.size ?: 0
    }

    inner class ManageStFundsViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvMoney = view.findViewById<TextView>(R.id.tvMoney)
        val tvState = view.findViewById<TextView>(R.id.tvState)
        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvDate = view.findViewById<TextView>(R.id.tvDate)
        val tvDateYM = view.findViewById<TextView>(R.id.tvDateYM)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener?) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onItemClick(view: View?, position: Int)
    }

    fun updateData(dataList: List<FundHistoryData>) {
        mList = dataList
        notifyDataSetChanged()
    }

}