package cn.com.vau.profile.activity.kycAuth

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.view.View.OnClickListener
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityKycAuthenticationBinding
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.addFragment
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.setVp
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Filename: KycAuthenticationActivity
 * Author: GG
 * Date: 2025/3/14
 * Description:
 */
class KycAuthActivity :
    BaseMvvmActivity<ActivityKycAuthenticationBinding, KycAuthViewModel>(),
    OnClickListener {
    private val iconUrl: String? by lazy { intent.getStringExtra(KEY_ICON) ?: UserDataUtil.userPic() }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        // 默认展示0
        mBinding.tvState.text = getString(R.string.x_3_standard_verification_completed, "0")
        ImageLoaderUtil.loadImage(this, iconUrl ?: "", mBinding.ivIcon)
        val tabs = mutableListOf(getString(R.string.standard))
        val fragmentList: MutableList<Fragment> = mutableListOf(KycAuthFragment.newInstance(KycAuthViewModel.KEY_STANDARD))
        mBinding.run {
            mViewPager.init(fragmentList, tabs, supportFragmentManager, this@KycAuthActivity)
            mTabLayout.setVp(mViewPager, tabs, TabType.LINE_INDICATOR)
            mViewPager.offscreenPageLimit = fragmentList.size
            mViewPager.descendantFocusability = ViewGroup.FOCUS_BLOCK_DESCENDANTS
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.statusPlus.observe(this) {
            if (!it.isNullOrEmpty()) {
                mBinding.mViewPager.addFragment(KycAuthFragment.newInstance(KycAuthViewModel.KEY_PLUS), getString(R.string.plus))
            }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    KycAuthViewModel.KEY_LEVEL -> {
                        mBinding.tvVerifiedState.text = getVerifyStatus(it.data as? Int)
                        mBinding.tvState.text = getString(R.string.x_3_standard_verification_completed, it.data.ifNull("0"))
                    }
                }
            }
        }
    }

    private fun getVerifyStatus(level: Int?) = getString(
        when (level) {
            0 -> R.string.unverified
            3 -> R.string.verified
            else -> R.string.partially_verified
        }
    )

    override fun onResume() {
        super.onResume()
        requestNet()
    }

    private fun requestNet() {
        mViewModel.userQueryUserLevelApi()
        mViewModel.userMyVerificationCenterApi()
    }

    override fun useEventBus(): Boolean = true

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.WS.SOCKET_KYC_LEVEL_CHANGED -> requestNet()
        }
    }

    companion object {
        private const val KEY_ICON = "icon"

        /**
         * 打开认证中心界面
         * @param iconUrl 用户头像
         */
        fun openActivity(context: Context, iconUrl: String? = null) {
            val intent = Intent(context, KycAuthActivity::class.java)
            intent.putExtra(KEY_ICON, iconUrl)
            context.startActivity(intent)
        }
    }
}