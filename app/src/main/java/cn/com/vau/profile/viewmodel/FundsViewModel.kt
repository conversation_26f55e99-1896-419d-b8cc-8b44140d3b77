package cn.com.vau.profile.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.depositcoupon.ManageFundsObj
import cn.com.vau.util.ToastUtil

/**
 * 资金管理
 */
class FundsViewModel : BaseViewModel() {

    val fundsObjLiveData = MutableLiveData<ManageFundsObj?>()
    val jumpAddressProofLiveData = MutableLiveData<Boolean>() //跳转地址证明选择页面
    val jumpSumSubLiveData = MutableLiveData<HashMap<String, String>>() //是否跳转到sumsub
    val jumpAccoGuideLv3LiveData = MutableLiveData<HashMap<String, String>>() //
    val htmlUrlLiveData = MutableLiveData<String>()//是否跳转到H5出金

    /**
     * 资金管理查询
     */
    fun queryManageFunds(accountId: String?, isDemo: String) {

        requestNet({

            val map = hashMapOf<String, Any>()
            map["userToken"] = UserDataUtil.loginToken()
            map["accountId"] = accountId ?: ""
            map["isDemo"] = isDemo

            baseService.fundIndex(map)
        }, {
            if ("********" != it?.resultCode) {
                ToastUtil.showToast(it?.msgInfo)
                return@requestNet
            }
            val data: ManageFundsObj? = it.data?.obj
            if (data != null) {
                fundsObjLiveData.value = data
            }

        })

    }

    /**
     * 出金查询是否需要上传地址证明
     */
    fun needUploadAddressProof() {

        val supervisionType = SpManager.getSuperviseNum("")
        if (supervisionType != "1") {
            addressproofWithrawNeedUploadAddressProof()
            return
        }

        requestNet({
            val map = hashMapOf<String, Any>()
            map["token"] = UserDataUtil.loginToken()
            baseService.addressproofWithrawNeedUploadAddressProofApi(map)
        }, {
            val data = it
            if ("V00000" == data?.resultCode) {
                var needUploadAddressProof: String? = ""
                if (data.data != null && data.data?.obj != null && data.data?.obj?.needUploadAddressProof != null)
                    needUploadAddressProof = data.data?.obj?.needUploadAddressProof

                if (TextUtils.equals("0", needUploadAddressProof)) {
                    isH5Withdraw()
                } else if (TextUtils.equals("3", needUploadAddressProof) || TextUtils.equals(
                        "1",
                        needUploadAddressProof
                    )
                ) {
                    jumpAddressProofLiveData.value = true
                    hideLoading()
                } else if (TextUtils.equals("2", needUploadAddressProof)) {
                    hideLoading()
                    ToastUtil.showToast(data.data?.obj?.msg)
                } else {
                    hideLoading()
                    ToastUtil.showToast(data.data?.obj?.msg)
                }
            } else {
                hideLoading()
                ToastUtil.showToast(data?.msgInfo)
            }
        }, isShowDialog = true, isAutoDismissDialog = false)
    }

    /**
     * 出金是否需要上传身份地址证明(新)
     */
    private fun addressproofWithrawNeedUploadAddressProof() {

        requestNet({
            val map = hashMapOf<String, Any>()
            map["token"] = UserDataUtil.loginToken()
            baseService.addressproofWithrawNeedUploadIdPoaProofApi(map)
        }, {
            val dataBean = it
            if ("V00000" != dataBean?.resultCode) {
                ToastUtil.showToast(dataBean?.msg)
                return@requestNet
            }

            val data = dataBean.data?.obj?.needUploadIdPoaProof

            if ("0" == data) {
                isH5Withdraw()
                return@requestNet
            }

            val buryHashMap = HashMap<String, String>()
            buryHashMap["Position"] = "Withdraw_button"

            // 1/3 lv2
            if ("1" == data || "3" == data) {
                jumpSumSubLiveData.value = buryHashMap
                return@requestNet
            }

            // 4/6 lv3
            if ("4" == data || "6" == data) {
                jumpAccoGuideLv3LiveData.value = buryHashMap
                return@requestNet
            }

            ToastUtil.showToast(dataBean.data?.obj?.msg)
        }, isShowDialog = true)

    }

    /**
     * 检查是否跳转到H5出金
     */
    private fun isH5Withdraw() {
        requestNet({
            val map = hashMapOf<String, Any?>()
            map["userToken"] = UserDataUtil.loginToken()
            // h5新增切换测试环境的地址的功能，这里需要传h5的url，不然的话后台不知道h5用什么环境
            if (!HttpUrl.official) {
                map["h5Url"] = HttpUrl.getTestH5Url()
            }
            baseService.fundIsH5WithdrawApi(map)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            val obj = it.data?.obj

            var htmlUrl = obj?.h5Url ?: ""
            htmlUrl += if (htmlUrl.contains("?")) "" else "?"

            val mt4AccountId = UserDataUtil.accountCd()
            val currency = UserDataUtil.currencyType()
            val socialTradingType = if (UserDataUtil.isStLogin()) "1" else "0"
            if (!htmlUrl.contains("userToken=")) htmlUrl =
                htmlUrl + "&userToken=" + UserDataUtil.loginToken()
            if (!htmlUrl.contains("mt4AccountId=")) htmlUrl =
                "$htmlUrl&mt4AccountId=$mt4AccountId"
            if (!htmlUrl.contains("currency=")) htmlUrl = "$htmlUrl&currency=$currency"
            if (!htmlUrl.contains("type=")) htmlUrl =
                htmlUrl + "&type=" + SpManager.getSuperviseNum("")
            htmlUrl = "$htmlUrl&socialtradingtype=$socialTradingType"

            htmlUrlLiveData.value = htmlUrl
        }, isShowDialog = true)
    }

}