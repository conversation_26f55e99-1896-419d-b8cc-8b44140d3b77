package cn.com.vau.profile.activity.manageFunds

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.depositcoupon.*

/**
 * Created by zhy on 2018/11/16.
 */
interface FundsContract {
    interface Model : BaseModel {
        fun queryManageFunds(map: HashMap<String, Any?>, baseObserver: BaseObserver<ManageFundsBean?>)

        fun addressproofWithrawNeedUploadAddressProofApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<NeedUploadAddressProofBean?>)

        fun addressproofWithrawNeedUploadIdPoaProofApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<NeedUploadIdProofData?>)

        fun fundIsH5WithdrawApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<NeedH5WithdrawBean?>)
    }

    interface View : BaseView {
        fun refreshManageFunds(bean: ManageFundsObj?)
    }

    abstract class Presenter : BasePresenter<Model?, View?>() {
        abstract fun queryManageFunds(userToken: String?, accountId: String?, isDemo: String?, isShowDialog: Boolean)

        abstract fun fundIsH5WithdrawApi()

        abstract fun needUploadAddressProof()

        abstract fun addressproofWithrawNeedUploadIdPoaProofApi()
    }
}
