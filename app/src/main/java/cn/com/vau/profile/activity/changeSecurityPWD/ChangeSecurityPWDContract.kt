package cn.com.vau.profile.activity.changeSecurityPWD

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean

/**
 * Created by zhy on 2018/11/29.
 */
interface ChangeSecurityPWDContract {

    interface Model : BaseModel {
        fun updateFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>)
    }

    interface View : BaseView {
        fun refreshFundPWD()
    }

    abstract class Presenter : BasePresenter<Model?, View?>() {
        abstract fun updateFundPWDApi(
            userToken: String?, optType: String?, oldFundPwd: String?,
            fundPwd: String?, confirmPwd: String?
        )
    }
}
