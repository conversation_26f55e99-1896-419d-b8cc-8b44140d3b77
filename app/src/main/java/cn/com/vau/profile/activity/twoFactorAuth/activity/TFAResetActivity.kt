package cn.com.vau.profile.activity.twoFactorAuth.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityTfaResetBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.profile.activity.twoFactorAuth.viewmodel.TFAViewModel
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.KeyboardUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * Filename: TwoFactorAuthActivity.kt
 * Author: GG
 * Date: 2024/2/18
 * Description:
 */
class TFAResetActivity : BaseMvvmActivity<ActivityTfaResetBinding, TFAViewModel>() {

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.pageType = TFAViewModel.TYPE_RESET
        mViewModel.paramLiveData.value = intent.getSerializableExtra(KEY_DATA) as? LoginVeriParam
    }

    override fun initView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.two_factor_authentication))

        mBinding.mHeaderBar.setStartBackIconClickListener {
            back()
        }
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
    }

    override fun initData() {
        super.initData()
        mViewModel.getUserAccountDataApi()
    }

    /**
     * 根据当前页面设置不同的返回页面
     */
    private fun back() = MainScope().launch {
        when (mViewModel.currentPageLiveData.value) {
            TFAViewModel.PAGE_RESET_PROMPT, TFAViewModel.PAGE_RESULT -> {
                EventBus.getDefault().post(NoticeConstants.NOTICE_SECURITY_REFRESH)
                finish()
            }
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        back()
    }

    override fun finish() {
        if (mViewModel.currentPageLiveData.value == TFAViewModel.PAGE_RESULT) {
            ActivityManagerUtil.getInstance().finishActivity(TFAVerifyActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(TFASettingActivity::class.java)
            // h5 出金会调到这个重置的页面，这里重置已经去除了成功的逻辑，就直接返回fasle
            EventBus.getDefault().post(DataEvent(NoticeConstants.TFA_RESET_SUCCESS, false))
        }
        super.finish()
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.currentPageLiveData.observe(this) {
            mBinding.mHeaderBar.setEndIconVisible(it == TFAViewModel.PAGE_PWD || it == TFAViewModel.PAGE_OPT)
            when (it) {
                TFAViewModel.PAGE_RESET_PROMPT -> {
                    mBinding.mHeaderBar.setTitleText(getString(R.string.two_factor_authentication))
                    mBinding.mHeaderBar.setEndIconVisible(false)
                }

                TFAViewModel.PAGE_RESULT -> {
                    mBinding.mHeaderBar.setTitleText(getString(R.string.reset_authenticator))
                    mBinding.mHeaderBar.setEndIconVisible(false)
                }
            }
        }
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
    }

    companion object {

        private const val KEY_DATA = "data"

        fun open(context: Context, bean: LoginVeriParam? = null) {
            context.startActivity(Intent(context, TFAResetActivity::class.java).apply {
                putExtra(KEY_DATA, bean)
            })
        }

    }
}