package cn.com.vau.profile.activity.kycLink

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityKycUpdateEmailBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.profile.viewmodel.UpdateEmailKycViewModel
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha

/**
 * author：lvy
 * date：2025/03/27
 * desc：kyc-修改邮箱
 */
class UpdateEmailKycActivity : BaseMvvmActivity<ActivityKycUpdateEmailBinding, UpdateEmailKycViewModel>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        sendCodeViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
    }

    override fun initData() {
        mViewModel.getWithdrawRestrictionMsgApi(5)
    }

    override fun createObserver() {
        // 出金限制横幅文案获取成功
        mViewModel.getWithdrawRestrictionMsgSuccessLiveData.observe(this) {
            mBinding.tvTips.isVisible = !it.isNullOrBlank()
            mBinding.tvTips.text = it
        }
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            VerifyEmailCodeActivity.open(this, sendCodeViewModel.signUpRequestBean)
        }
        // 密码校验成功
        sendCodeViewModel.checkPwdSuccessLiveData.observe(this) {
            sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.UPDATE_EMAIL_NEW_KYC
            sendCodeViewModel.sendEmailCodeApi(type = "28", validate = it)
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 邮箱
        mBinding.emailView.afterTextChangedListener {
            checkNextBtn()
        }
        // 密码
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            val email = mBinding.emailView.getInputText()
            if (!RegexUtil.isEmail(email)) {
                ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
                return@clickNoRepeat
            }
            sendCodeViewModel.signUpRequestBean?.email = email
            sendCodeViewModel.signUpRequestBean?.pwd = mBinding.pwdView.getInputText()
            sendCodeViewModel.checkUserPasswordApi()
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        mBinding.tvNext.isEnabled =
            mBinding.emailView.getInputText().isNotBlank() && mBinding.pwdView.getInputText().isNotBlank()
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCodeViewModel.checkUserPasswordApi(it)
        }
        mCaptcha?.validate()
    }

    override fun onDestroy() {
        super.onDestroy()
        mCaptcha?.destroy()
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, UpdateEmailKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}