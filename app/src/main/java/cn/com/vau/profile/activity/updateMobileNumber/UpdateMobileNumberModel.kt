package cn.com.vau.profile.activity.updateMobileNumber

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean

/**
 * Created by zhy on 2018/11/29.
 */
class UpdateMobileNumberModel : UpdateMobileNumberContract.Model {

    override fun getBindingTelSMSApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>) {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelSMSApi(map), baseObserver)
    }

    override fun getWithdrawRestrictionMsgApi(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>) {
        HttpUtils.loadData(RetrofitHelper.getHttpService().fundWithdrawRestrictionMessageApi(map), baseObserver)
    }
}
