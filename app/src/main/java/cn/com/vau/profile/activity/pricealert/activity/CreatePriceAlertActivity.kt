package cn.com.vau.profile.activity.pricealert.activity

import android.annotation.SuppressLint
import android.content.*
import android.os.Bundle
import android.text.*
import android.view.MotionEvent
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.pricealtert.ProduceAlterData
import cn.com.vau.databinding.ActivityCreatePriceAlertBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.profile.activity.pricealert.viewmodel.CreatePriceAlertViewModel
import cn.com.vau.profile.adapter.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * 价格提醒 创建页面
 */
@SuppressLint("NotifyDataSetChanged")
class CreatePriceAlertActivity : BaseMvvmActivity<ActivityCreatePriceAlertBinding, CreatePriceAlertViewModel>(), SDKIntervalCallback {

    private val c00c79c by lazy { ContextCompat.getColor(this, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(this, R.color.cf44040) }
    private val selectedBg by lazy { R.drawable.draw_shape_stroke_c00c79c_solid_c0a1e1e1e_c262930_r10 }
    private val unSelectedBg by lazy { R.drawable.draw_shape_c0a1e1e1e_c262930_r10 }
    private val priceUpDrawable by lazy { R.drawable.img_price_up }
    private val pricedDownDrawable by lazy { R.drawable.img_price_down }
    private val selectedDrawable by lazy { ContextCompat.getDrawable(this, R.drawable.icon2_cb_tick_circle_c15b374) }
    private val unSelectedDrawable by lazy { ContextCompat.getDrawable(this, R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14) }

    private var inputDigits = 0

    private var valueWatcher: TextWatcher? = null

    private val alterTypeAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(
            isChangeSelectTextColor = false,
        ).apply {
            setList(
                listOf<SelectBean>(
                    SelectBean(getString(R.string.price_rises_above)),
                    SelectBean(getString(R.string.price_falls_to)),
                    SelectBean(getString(R.string._d_rise_exceeds)),
                    SelectBean(getString(R.string._d_fall_exceeds))
                )
            )
            selectTitle = mBinding.tvSelectType.text.toString()
            setOnItemClickListener { _, _, position ->
                selectSell()
                if (isNeedChangeValueType(position)) {
                    mViewModel.alertType = position
                    initValueShow()
                    setCanChoiceSellOrBuy(isCanChoiceSellOrBuy())
                    selectSell()
                } else {
                    mViewModel.alertType = position
                }
                val selectAlterType = getItemOrNull(position)
                mBinding.tvSelectType.text = selectAlterType?.title
                selectTitle = selectAlterType?.title

                notifyDataSetChanged()
                bottomDialog?.dismiss()
                updatePriceChange()
            }
        }
    }
    private val frequencyAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(
            isChangeSelectTextColor = false,
        ).apply {
            setList(
                listOf<SelectBean>(
                    SelectBean(getString(R.string.once_only)),
                    SelectBean(getString(R.string.once_every_24h)),
                    SelectBean(getString(R.string.every_time))
                )
            )
            selectTitle = mBinding.tvFrequency.text.toString()
            setOnItemClickListener { _, _, position ->
                mViewModel.frequency = position
                val frequency = getItemOrNull(position)
                mBinding.tvFrequency.text = frequency?.title
                selectTitle = frequency?.title
                notifyDataSetChanged()
                bottomDialog?.dismiss()
            }
        }
    }

    private val bottomDialog: BottomSelectPopup? by lazy { BottomSelectPopup.build(this) }

    private val alterTypeIntroduceAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf<HintLocalData>(
                    HintLocalData(
                        getString(R.string.price_rises_above),
                        getString(R.string.triggered_when_the_last_price_rises_set_price)
                    ), HintLocalData(
                        getString(R.string.price_falls_to),
                        getString(R.string.triggered_when_the_last_price_falls_set_price)
                    ), HintLocalData(
                        getString(R.string._d_rise_exceeds),
                        getString(R.string.triggered_when_the_price_rise_trading_day)
                    ), HintLocalData(
                        getString(R.string._d_fall_exceeds),
                        getString(R.string.triggered_when_the_price_fall_trading_day)
                    )
                )
            )
        }
    }

    private val frequencyIntroduceAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf<HintLocalData>(
                    HintLocalData(
                        getString(R.string.once_only),
                        getString(R.string.receive_an_alert_is_reached)
                    ), HintLocalData(
                        getString(R.string.once_every_24h),
                        getString(R.string.receive_an_alert_every_24h)
                    ), HintLocalData(
                        getString(R.string.every_time),
                        getString(R.string.constant_alerts_until_to_you_10_minutes)
                    )
                )
            )
        }
    }

    /**
     * 400ms刷新
     */
    override fun onCallback() {
        refreshProductInfo()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.productName = intent?.extras?.getString(KEY_PRODUCT_NAME).ifNull()
        mViewModel.isEdit = intent?.extras?.getBoolean(KEY_IS_EDIT, false).ifNull()
        mViewModel.alterData = intent?.extras?.getParcelable(KEY_ALTER_DATA)

        showProduceInfo()
        initValueShow()
    }

    override fun initView() {
        initTitleView()
        initCreateOrEdite()
        selectedSellOrBuy()
    }

    override fun initData() {
        super.initData()
        mViewModel.alterData?.run {
            mViewModel.direction = direction.ifNull("1")
            selectedSellOrBuy()
            mViewModel.alertType = alertType.toIntCatching(0)
            mBinding.tvSelectType.text = alterTypeAdapter.data.getOrNull(mViewModel.alertType)?.title
            alterTypeAdapter.selectTitle = mBinding.tvSelectType.text.toString()

            mViewModel.frequency = frequency.toIntCatching(0)
            mBinding.tvFrequency.text = frequencyAdapter.data.getOrNull(mViewModel.frequency)?.title
            frequencyAdapter.selectTitle = mBinding.tvFrequency.text.toString()
            setCanChoiceSellOrBuy(isCanChoiceSellOrBuy())
            mBinding.etValue.setText(value)
        }
    }

    /**
     * 修改底部按钮显示
     */
    private fun initCreateOrEdite() {
        if (mViewModel.isEdit) {
            mBinding.tvNext.text = getString(R.string.save)
            mBinding.tvDelete.isVisible = true
        } else {
            mBinding.tvNext.text = getString(R.string.add_alert)
            mBinding.tvDelete.isVisible = false
        }
    }

    /**
     * 切换提醒类型以后 设置，前两个是价格提醒，默认填入买卖价格 ，后面是百分比提醒，默认填入0
     */
    private fun initValueShow() {
        if (isCanChoiceSellOrBuy()) {
            mViewModel.data?.let {
                mBinding.etValue.setText(
                    if (mViewModel.direction == "1") {
                        it.bid
                    } else {
                        it.ask
                    }.formatProductPrice(it.digits, false)
                )
            }
        } else {
            mBinding.etValue.setText("0")
        }
    }

    override fun onResume() {
        super.onResume()
        showNotificationTipsOrNot()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    private fun showProduceInfo() {
        VAUSdkUtil.shareGoodList().forEach loop@{
            it.symbolList?.forEach { data ->
                if (data.symbol == mViewModel.productName) {
                    mViewModel.groupName = it.groupname
                    mViewModel.data = data
                    return@loop
                }
            }
        }
        mViewModel.data?.let { data ->
            mBinding.tvSellPrice.text = data.bidUI
            mBinding.tvBuyPrice.text = data.askUI
            mBinding.tvSpread.text = data.spreadUI
        }
    }

    private fun initTitleView() {
        mBinding.mHeaderBar.setTitleText(mViewModel.productName)
        mBinding.linkTvNotificationEnableTip.text = buildString {
            append(getString(R.string.unable_to_receive_app_notifications))
            append(getString(R.string.enable_now))
        }
        mBinding.linkTvNotificationEnableTip.set(getString(R.string.enable_now), getColor(R.color.ce35728), isShowUnderLine = false) {
            IntentUtil.launchAppDetailsSettings()
            SensorsDataUtil.track(SensorsConstant.V3510.PUSH_ACCESS_POPUP_BTN_CLICK)
        }
    }

    /**
     * 判断是否需要切换输入框类型
     */
    private fun isNeedChangeValueType(positionNew: Int): Boolean {
        return (mViewModel.alertType < 2 && positionNew >= 2) || (mViewModel.alertType >= 2 && positionNew != mViewModel.alertType)
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivCloseTip.clickNoRepeat {
            mBinding.groupTips.isVisible = false
            SpManager.putPriceAlertNotificationDialogShow(true)
        }
        mBinding.llSell.clickNoRepeat {
            selectSell()
        }
        mBinding.ivSellSelected.clickNoRepeat {
            selectSell()
        }

        mBinding.llBuy.clickNoRepeat {
            selectBuy()
        }
        mBinding.ivBuySelected.clickNoRepeat {
            selectBuy()
        }
        mBinding.tvAlterTypeLabel.clickNoRepeat {
            bottomDialog?.setTitle(getString(R.string.alert_type))
            bottomDialog?.setAdapter(alterTypeIntroduceAdapter)
            bottomDialog?.show()
        }

        mBinding.tvSelectType.clickNoRepeat {
            showSelectAlterTypeDialog()
        }
        mBinding.tvFrequency.clickNoRepeat {
            showAlterFrequencyDialog()
        }

        mBinding.tvFrequencyLabel.clickNoRepeat {
            bottomDialog?.setTitle(getString(R.string.frequency))
            bottomDialog?.setAdapter(frequencyIntroduceAdapter)
            bottomDialog?.show()
        }

        mBinding.ivValueUp.setOnClickListener {
            if (isCanChoiceSellOrBuy()) {
                upPriceValue()
            } else {
                upPercentValue()
            }
            updatePriceChange()
        }
        mBinding.ivValueDown.setOnClickListener {
            if (isCanChoiceSellOrBuy()) {
                downPriceValue()
            } else {
                downPercentValue()
            }
            updatePriceChange()
        }
        mBinding.etValue.setOnFocusChangeListener { _, hasFouce ->
            if (hasFouce) {
                addFocus()
            } else {
                clearFocus()
            }
        }

        valueWatcher = object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                if (isCanChoiceSellOrBuy()) {
                    priceDigitsLimit(edt)
                } else {
                    percentLimit(edt)
                }
            }
        }
        mBinding.etValue.addTextChangedListener(valueWatcher)

        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mBinding.root.requestFocus()
                inputDigits = mBinding.etValue.text?.split(".")?.getOrNull(1)?.length.ifNull()
                mBinding.etValue.setText(mBinding.etValue.text.toString().numFormat(mViewModel.getDigits()))
                clearFocus()
            }
        }

        mBinding.tvDelete.clickNoRepeat {
            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.delete_alert))
                .setStartText(getString(R.string.no))
                .setEndText(getString(R.string.yes))
                .setOnEndListener {
                    mViewModel.deletePriceWarn()
                }
                .build()
                .showDialog()
            SensorsDataUtil.track(SensorsConstant.V3510.PRICE_ALERT_EDIT_PAGE_BTN_CLICK, JSONObject().apply {
                put(SensorsConstant.Key.BUTTON_NAME, "Delete")
            })
        }

        mBinding.tvNext.clickNoRepeat(1000) {
            if (checkNotificationEnable().not() && SpManager.getPriceAlertNotificationDialogShow(false).not()) {
                showMissAlterDialog()
                SpManager.putPriceAlertNotificationDialogShow(true)
            } else {
                mViewModel.addOrUpdatePriceAlert(mBinding.etValue.text.toString())
                if (mViewModel.isEdit) {
                    SensorsDataUtil.track(SensorsConstant.V3510.PRICE_ALERT_EDIT_PAGE_BTN_CLICK, JSONObject().apply {
                        put(SensorsConstant.Key.BUTTON_NAME, "Save")
                    })
                }
            }
        }
    }

    private fun clearFocus() {
        mBinding.clValue.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c262930_r10)
    }

    private fun addFocus() {
        mBinding.clValue.setBackgroundResource(R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10)
    }

    private fun selectBuy() {
        if (!isCanChoiceSellOrBuy()) return
        mViewModel.direction = "0"
        selectedSellOrBuy()
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    if (it is DataEvent) {
                        when (it.tag) {
                            "success" -> {
                                setResult(RESULT_OK)
                                finish()
                            }

                            else -> {
                                CenterActionDialog.Builder(this@CreatePriceAlertActivity)
                                    .setSingleButton(true)
                                    .setContent(it.data.toString())
                                    .setSingleButtonText(getString(R.string.ok))
                                    .build()
                                    .showDialog()
                            }
                        }
                    }
                }
        }
    }

    /**
     * 输入框价格增加
     */
    private fun upPriceValue() {
        val value = mBinding.etValue.text.toString()
        val editValue = value.mathAdd(mViewModel.getMinProfit())
        mBinding.etValue.setText(editValue.numFormat(mViewModel.getDigits(), false))
    }

    /**
     * 输入框价格减少
     */
    private fun downPriceValue() {
        val value = mBinding.etValue.text.toString()
        val editValue = value.mathSub(mViewModel.getMinProfit())
        if (editValue.mathCompTo("0") == -1) return
        mBinding.etValue.setText(editValue.numFormat(mViewModel.getDigits(), false))
    }

    /**
     * 输入框百分比增加
     */
    private fun upPercentValue() {
        val value = mBinding.etValue.text.toString()
        val editValue = value.mathAdd(mViewModel.getMinPercent())
        mBinding.etValue.setText(editValue.numFormat(mViewModel.percentLimitCount, false))
    }

    /**
     * 输入框百分比减少
     */
    private fun downPercentValue() {
        val value = mBinding.etValue.text.toString()
        val editValue = value.mathSub(mViewModel.getMinPercent())
        if (editValue.mathCompTo("0") == -1) return
        mBinding.etValue.setText(editValue.numFormat(mViewModel.percentLimitCount, false))
    }

    private fun percentLimit(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (temp.length - posDot - 1 > mViewModel.percentLimitCount) {
                val endIndex = posDot + 2 + mViewModel.percentLimitCount
                if (endIndex <= edt.length) {
                    edt.delete(posDot + mViewModel.percentLimitCount + 1, endIndex)
                }
            }
        }
    }

    private fun priceDigitsLimit(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            //删除前进行数值校验，看是否在length内
            if (temp.length - posDot - 1 > (mViewModel.data?.digits ?: 0)) {
                val endIndex = posDot + 2 + (mViewModel.data?.digits ?: 0)
                if (endIndex <= edt.length) {
                    edt.delete(posDot + (mViewModel.data?.digits ?: 0) + 1, endIndex)
                }
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                edt.delete(posDot - 1, posDot)
            }
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
    }

    /**
     *  是否可以选择sell buy 的类型，默认前两个可以选择
     */
    private fun isCanChoiceSellOrBuy(): Boolean {
        return mViewModel.alertType < 2
    }

    /**
     * 设置顶部买卖card 背景
     */
    private fun selectedSellOrBuy() {
        mBinding.llSell.setBackgroundResource(if (mViewModel.direction == "1") selectedBg else unSelectedBg)
        mBinding.ivSellSelected.setImageDrawable(if (mViewModel.direction == "1") selectedDrawable else unSelectedDrawable)
        mBinding.llBuy.setBackgroundResource(if (mViewModel.direction != "1") selectedBg else unSelectedBg)
        mBinding.ivBuySelected.setImageDrawable(if (mViewModel.direction != "1") selectedDrawable else unSelectedDrawable)
        if (isCanChoiceSellOrBuy())
            mBinding.etValue.setText(if (mViewModel.direction == "1") mViewModel.data?.bidUI else mViewModel.data?.askUI)
    }

    /**
     * 设置买卖类型是否可选 ，不可选的话 默认选中 sell 类型
     */
    private fun setCanChoiceSellOrBuy(isCan: Boolean) {
        mBinding.llSell.isClickable = isCan
        mBinding.llBuy.isClickable = isCan
        mBinding.ivBuySelected.isVisible = isCan
        mBinding.ivSellSelected.isVisible = isCan
        mBinding.tvPercent.isVisible = isCan.not()
    }

    private fun selectSell() {
        mViewModel.direction = "1"
        selectedSellOrBuy()
    }

    private fun showMissAlterDialog() {
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.never_miss_an_alert))
            .setLottieIcon(R.raw.lottie_dialog_alert)
            .setContent(getString(R.string.you_are_unable_turned_off))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.enable))
            .setLinkText(Html.fromHtml("<u>${getString(R.string.maybe_later)}</u>"))
            .setOnSingleButtonListener {
                IntentUtil.launchAppDetailsSettings()
                SensorsDataUtil.track(SensorsConstant.V3510.PUSH_ACCESS_POPUP_BTN_CLICK)
            }
            .setLinkListener {
                mViewModel.addOrUpdatePriceAlert(mBinding.etValue.text.toString())
            }
            .build()
            .showDialog()
    }

    private fun showAlterFrequencyDialog() {
        bottomDialog?.setTitle(getString(R.string.frequency))
        bottomDialog?.setAdapter(frequencyAdapter)
        bottomDialog?.show()
    }

    private fun showSelectAlterTypeDialog() {
        bottomDialog?.setTitle(getString(R.string.alert_type))
        bottomDialog?.setAdapter(alterTypeAdapter)
        bottomDialog?.show()
    }

    private fun showPriceChange() {
        val valuePrice = mBinding.etValue.text.toString()
        val currentPrice = if (mViewModel.direction == "1") mBinding.tvSellPrice.text.toString() else mBinding.tvBuyPrice.text.toString()
        val priceChange = valuePrice.mathSub(currentPrice)
        mBinding.tvPriceChange.text = when (priceChange.mathCompTo("0")) {
            0 -> getString(R.string.price_change) + " : 0 (0%)"
            else -> getString(R.string.price_change) + " : ${
                if (priceChange.mathCompTo("0") == 1) {
                    "${("+$priceChange")} (+"
                } else {
                    "$priceChange ("
                } + priceChange.mathDiv(currentPrice, 8).mathMul("100").numFormat(2, true)
            }%)"
        }.arabicReverseTextByFlag(" ")
    }

    private fun showPriceTo() {
        val percent = mBinding.etValue.text.toString().mathDiv("100", 8)
        val currentPrice = if (mViewModel.direction == "1") mBinding.tvSellPrice.text.toString() else mBinding.tvBuyPrice.text.toString()
        val percentTo = if (mViewModel.alertType == 2) percent.mathAdd("1") else "1".mathSub(percent)
        val priceTo = currentPrice.mathMul(percentTo).numFormat(mViewModel.getDigits(), false)
        mBinding.tvPriceChange.text = buildString {
            append(getString(R.string.price_to))
            append(" ≈ ")
            append(priceTo)
        }.arabicReverseTextByFlag(" ≈ ")
    }

    private fun refreshProductInfo() {
        updatePriceChange()
        mViewModel.data?.let {
            if (it.bidType == 1) { // 卖出 绿
                mBinding.tvSellPrice.setTextColorDiff(c00c79c)
                mBinding.tvSellPrice.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, priceUpDrawable, 0)
            } else {
                mBinding.tvSellPrice.setTextColorDiff(cf44040)
                mBinding.tvSellPrice.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, pricedDownDrawable, 0)
            }
            if (it.askType == 1) {
                mBinding.tvBuyPrice.setTextColorDiff(c00c79c)
                mBinding.tvBuyPrice.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, priceUpDrawable, 0)
            } else {
                mBinding.tvBuyPrice.setTextColorDiff(cf44040)
                mBinding.tvBuyPrice.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, pricedDownDrawable, 0)
            }
            mBinding.tvSellPrice.setTextDiff(it.bidUI)
            mBinding.tvBuyPrice.setTextDiff(it.askUI)
            mBinding.tvSpread.setTextDiff(it.spreadUI)
        }
    }

    /**
     * 刷新价格变化的文案
     */
    private fun updatePriceChange() {
        if (isCanChoiceSellOrBuy()) {
            showPriceChange()
        } else {
            showPriceTo()
        }
    }

    private fun checkNotificationEnable(): Boolean {
        val manager = NotificationManagerCompat.from(this)
        val isOpened = manager.areNotificationsEnabled()
        return isOpened
    }

    private fun showNotificationTipsOrNot() {
        val isOpen = checkNotificationEnable()
        mBinding.groupTips.isVisible = isOpen.not() && SpManager.getPriceAlertNotificationDialogShow(false).not()
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.onTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        mBinding.etValue.removeTextChangedListener(valueWatcher)
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {

        private const val KEY_PRODUCT_NAME = "product_name"
        private const val KEY_IS_EDIT = "is_edit"
        private const val KEY_ALTER_DATA = "alter_data"

        fun open(context: Context, productName: String?, isEdit: Boolean = false, alterData: ProduceAlterData? = null) {
            context.startActivity(Intent(context, CreatePriceAlertActivity::class.java).apply {
                putExtra(KEY_PRODUCT_NAME, productName)
                putExtra(KEY_IS_EDIT, isEdit)
                putExtra(KEY_ALTER_DATA, alterData)
            })
        }
    }
}