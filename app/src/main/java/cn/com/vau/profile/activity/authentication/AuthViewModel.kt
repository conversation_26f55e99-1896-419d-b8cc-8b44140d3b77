package cn.com.vau.profile.activity.authentication

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.util.ToastUtil

/**
 * Filename: AuthViewModel.kt
 * Author: GG
 * Date: 2024/1/12
 * Description:
 */
class AuthViewModel : BaseViewModel() {

    val accountStatusLiveData by lazy { MutableLiveData<AuditStatusData.Obj>() }

    var isLv1VerifyNowShow = false

    /**
     * 获取验证状态
     */
    fun getAuditStatusApi() {
        requestNet({
            baseService.getAuditStatusApi(UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            accountStatusLiveData.value = it.data?.obj
        })
    }
}