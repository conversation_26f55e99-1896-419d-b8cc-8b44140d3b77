package cn.com.vau.util

import android.os.Looper
import android.text.TextUtils
import cn.com.vau.BuildConfig
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.util.widget.FirebaseManager
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.*
import java.util.*

/**
 * BigDecimal
 * 对比 加 减 乘 除
 */
/**
 * 两数对比 1:大于  0:等于  -1:小于
 */
fun String?.mathCompTo(s2: String?): Int {
    var d1 = this
    var d2 = s2

    if (TextUtils.isEmpty(d1) || (d1 ?: "").contains("null")) d1 = "0"
    if (TextUtils.isEmpty(d2) || (d2 ?: "").contains("null")) d2 = "0"

    return try {
        val b1 = BigDecimal(d1)
        val b2 = BigDecimal(d2)
        b1.compareTo(b2)
    } catch (e: Exception) {
        e.printStackTrace()
        0
    }
}

/**
 * 加
 */
fun String?.mathAdd(s2: String?): String {
    var d1 = this
    var d2 = s2

    if (TextUtils.isEmpty(d1) || (d1 ?: "").contains("null")) d1 = "0"
    if (TextUtils.isEmpty(d2) || (d2 ?: "").contains("null")) d2 = "0"

    return try {
        BigDecimal(d1).add(BigDecimal(d2)).toString()
    } catch (e: Exception) {
        e.printStackTrace()
        "0"
    }
}

/**
 * 对可空的 String 类型扩展的函数，用于执行多个 String 值的加法操作。
 *
 * 这个函数接受一个可变数量的可空 String 参数 (`vararg args`)，将这些参数转换为列表，
 * 然后通过折叠操作将它们加到一个累加器中。最后，将接收者 String（即调用此函数的可空 String）
 * 添加到结果中。
 *
 * @param args 需要相加的可空 String 参数。
 * @return 将所有提供的 String 参数和接收者 String 相加后的结果，以一个单一的 String 返回。
 */
fun String?.mathAdd(vararg args: String?): String {
    // 将可变参数转换为列表，使用 "0" 作为初始累加器值
    // 对列表中的每个参数，调用 mathAdd 函数来更新累加器的值
    return args.asList().fold("0") { acc, s -> acc.mathAdd(s) }
        // 在折叠所有参数后，将接收者 String 添加到结果中
        .mathAdd(this)
}

fun Number.mathAdd(d2: Number): Double {
    return this.toString().mathAdd(d2.toString()).toDoubleCatching()
}

/**
 * 减
 */
fun String?.mathSub(s2: String?): String {
    var d1 = this
    var d2 = s2

    if (TextUtils.isEmpty(d1) || (d1 ?: "").contains("null")) d1 = "0"
    if (TextUtils.isEmpty(d2) || (d2 ?: "").contains("null")) d2 = "0"

    return try {
        val b1 = BigDecimal(d1)
        val b2 = BigDecimal(d2)
        b1.subtract(b2).toString()
    } catch (e: Exception) {
        e.printStackTrace()
        "0"
    }
}

fun Number.mathSub(d2: Number): Double {
    return this.toString().mathSub(d2.toString()).toDoubleCatching()
}

/**
 * 乘
 */
fun String?.mathMul(s2: String?): String {
    this ?: return "0"
    s2 ?: return "0"

    if (this.contains("null") || s2.contains("null")) return "0"

    return try {
        val b1 = BigDecimal(this)
        val b2 = BigDecimal(s2)
        b1.multiply(b2).toString()
    } catch (e: Exception) {
        e.printStackTrace()
        "0"
    }
}

fun String?.mathMul(vararg args: String?): String {
    var mulValue = "1"
    if (TextUtils.isEmpty(this) || (this ?: "").contains("null")) {
        return "0"
    }
    args.forEach {
        if (TextUtils.isEmpty(it) || (it ?: "").contains("null")) {
            return "0"
        }
        mulValue = it.mathMul(mulValue)
    }

    return mulValue.mathMul(this)
}

fun Number.mathMul(f2: Number): Float {
    return this.toString().mathMul(f2.toString()).toFloatCatching()
}

/**
 * 除以
 * 默认四舍五入
 */
fun String?.mathDiv(s2: String?, len: Int, roundingMode: Int = BigDecimal.ROUND_HALF_UP): String {
    this ?: return "0"
    s2 ?: return "0"

    if (this.contains("null") || s2.contains("null")) return "0"

    return try {
        val b1 = BigDecimal(this)
        val b2 = BigDecimal(s2)
        if (BigDecimal.ZERO.compareTo(b2) == 0) "0" else b1.divide(b2, len, roundingMode).toString()
    } catch (e: Exception) {
        e.printStackTrace()
        "0"
    }
}

fun Number.mathDiv(f2: Number, len: Int): Float {
    return this.toString().mathDiv(f2.toString(), len).toFloatCatching()
}

//参考https://blog.csdn.net/Jason_Lewis/article/details/79176984
private var dfs = DecimalFormatSymbols.getInstance(Locale.ENGLISH).apply {
    // 设置小数点符号为 '.'，即使用点号作为小数分隔符
    decimalSeparator = '.'
}

//   private static NumberFormat format = NumberFormat.getNumberInstance(Locale.ENGLISH);
private var format = (NumberFormat.getInstance(Locale.ENGLISH) as DecimalFormat).apply {
    // 禁用千位分隔符
    isGroupingUsed = false
    decimalFormatSymbols = dfs
}

/**
 * 仅这个类调用的 ， 统一的数字格式化函数
 */
private fun numberFormat(number: Number, keepNum: Int, isRound: Boolean): String { // 设置最大和最小的小数位数
    if (BuildConfig.DEBUG) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            throw RuntimeException("numberFormat must be call in main thread， 如果子线程中使用请使用Expend2")
        }
    }
    // 如果 keepNum 为负数，则小数位数为 0，否则为 keepNum 指定的位数
    format.maximumFractionDigits = keepNum.coerceAtLeast(0)
    format.minimumFractionDigits = keepNum.coerceAtLeast(0)

    try { // 根据 isRound 参数决定是否进行四舍五入
        // 转换为 BigDecimal 以更好地控制精度
        var bigDecimal = BigDecimal(number.toString())

        bigDecimal = if (!isRound) { // 使用 setScale 保留指定小数位，直接截取多余的小数位
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.FLOOR)
        } else { // 进行四舍五入
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.HALF_UP)
        }
        return format.format(bigDecimal)
    } catch (e: Exception) {
        e.printStackTrace() //上报为不严重类型
        FirebaseManager.recordException(Exception("numberFormat is Exception number=$number,keepNum=$keepNum,isRound=$isRound"))
        return "0.0"
    }
}

/**
 * 结果使用去尾法的数字格式化函数
 */
private fun numberFormatUp(number: Number, keepNum: Int): String { // 设置最大和最小的小数位数
//    if (BuildConfig.DEBUG) {
//        if (Looper.myLooper() != Looper.getMainLooper()) {
//            throw RuntimeException("numberFormat must be call in main thread， 如果子线程中使用请使用Expend2")
//        }
//    }
    // 如果 keepNum 为负数，则小数位数为 0，否则为 keepNum 指定的位数
    format.maximumFractionDigits = keepNum.coerceAtLeast(0)
    format.minimumFractionDigits = keepNum.coerceAtLeast(0)

    try { // 根据 isRound 参数决定是否进行四舍五入
        // 转换为 BigDecimal 以更好地控制精度
        var bigDecimal = BigDecimal(number.toString())

        bigDecimal = bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.UP)
        return format.format(bigDecimal)
    } catch (e: Exception) {
        e.printStackTrace() //上报为不严重类型
        FirebaseManager.recordException(Exception("numberFormat is Exception number=$number,keepNum=$keepNum,isRound="))
        return "0.0"
    }
}

/**
 * 向上进位的数字格式化扩展函数，结果使用去尾法
 */
fun String?.numFormatUp(keepNum: Int): String {
    if (this == null)
        return "0.0"

    return numberFormatUp(this.toDoubleCatching(), keepNum)
}

/**
 * 根据货币格式化，结果使用去尾法
 */
fun String?.numCurrencyFormatUp(
    currencyType: String = UserDataUtil.currencyType()
): String {

    if (TextUtils.isEmpty(this))
        return when (currencyType) {
            "JPY", "USC" -> "0"
            "BTC", "ETH" -> "0.00000000"
            else -> "0.00"
        }

    return formatByCurrencyUp(this ?: "", currencyType)
}

/**
 * 根据货币的类型进行不同小数位的格式化，结果使用去尾法
 */
private fun formatByCurrencyUp(amount: String, currencyType: String): String =
    amount.numFormatUp(
        when (currencyType) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        }
    )

/**
 * 仅这个类调用的 ， 统一的数字格式化函数
 * 临时方法，为了方便更好的定位问题， 其他业务不要调用，将来会删除
 */
private fun numberFormat_tmp(number: Number, keepNum: Int, isRound: Boolean, tmpBean: TmpRecordNaNExBean? = null): String { // 设置最大和最小的小数位数
    //    if (BuildConfig.DEBUG) {
    //        if (Looper.myLooper() != Looper.getMainLooper()) {
    //            throw RuntimeException("numberFormat must be call in main thread， 如果子线程中使用请使用Expend2")
    //        }
    //    }
    // 如果 keepNum 为负数，则小数位数为 0，否则为 keepNum 指定的位数
    format.maximumFractionDigits = keepNum.coerceAtLeast(0)
    format.minimumFractionDigits = keepNum.coerceAtLeast(0)

    try { // 根据 isRound 参数决定是否进行四舍五入
        // 转换为 BigDecimal 以更好地控制精度
        var bigDecimal = BigDecimal(number.toString())

        bigDecimal = if (!isRound) { // 使用 setScale 保留指定小数位，直接截取多余的小数位
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.FLOOR)
        } else { // 进行四舍五入
            bigDecimal.setScale(keepNum.coerceAtLeast(0), RoundingMode.HALF_UP)
        }
        return format.format(bigDecimal)
    } catch (e: Exception) {
        e.printStackTrace() //上报为不严重类型
        FirebaseManager.recordException(Exception("numberFormat is Exception number=$number,keepNum=$keepNum,isRound=$isRound,tmpBean=$tmpBean,symbol=${tmpBean?.data?.symbol}"))
        return "0.0"
    }
}

/**
 * 扩展函数，用于格式化数字为产品价格的形式
 */
fun Number.formatProductPrice(keepNum: Int, isRound: Boolean, zeroUI: String = "-"): String {
    // 如果当前数字为 0.0，则返回 "-"，表示没有价格
    if ("0".mathCompTo(this.toString()) == 0) return zeroUI

    return numberFormat(this, keepNum, isRound)
}

/**
 * 通用的数字格式化扩展函数
 */
fun Number?.numFormat(keepNum: Int, isRound: Boolean): String {
    if (this == null)
        return "0.0"

    return if (Looper.myLooper() == Looper.getMainLooper()) {
        numberFormat(this, keepNum, isRound)
    } else {
        this.numFormat2(keepNum, isRound)
    }

}

/**
 * 通用的数字格式化扩展函数
 * 临时方法，为了方便更好的定位问题， 其他业务不要调用，将来会删除
 */
fun Number?.numFormat_tmp(keepNum: Int, isRound: Boolean, tmpBean: TmpRecordNaNExBean? = null): String {
    if (tmpBean != null) {
        tmpBean.str3Number = this?.toString() ?: "----"
    }

    if (this == null)
        return "0.0"

    return numberFormat_tmp(this, keepNum, isRound, tmpBean)
}

/**
 * 需要四舍五入的第二个参数传 true
 */
fun String?.numFormat(digitParam: Int, isRound: Boolean = false): String {
    return if (Looper.myLooper() == Looper.getMainLooper()) {
        this.toDoubleCatching().numFormat(digitParam, isRound)
    } else {
        this.numFormat2(digitParam, isRound)
    }

}

/**
 * 需要四舍五入的第二个参数传 true
 *
 *  临时方法，临时新增了一个参数tmpBean，目的：方便线上查找问题
 *  这个tmpBean是为了方便问题定位而新增的参数，当问题修复后，可以删除此方法，使用numFormat()方法即可
 *  tmpBean参数是为了方便线上定位问题，而记录的，业务使用无需关心此参数
 *  其他业务也不要调用该方法
 *  firebase:  https://console.firebase.google.com/project/vantage-fx-app/crashlytics/app/android:cn.com.vau/issues/3ef2323abb9e53a5b7628d1c7b7a63fd?time=last-twenty-four-hours&sessionEventKey=67845AAE02EE00014FE8041CC345D1CB_2037634236499634047
 */
fun String?.numFormat_tmp(digitParam: Int, isRound: Boolean = false, tmpBean: TmpRecordNaNExBean? = null): String {
    if (tmpBean != null) {
        tmpBean.str2 = this
    }
    return this.toDoubleCatching().numFormat_tmp(digitParam, isRound, tmpBean)
}

/**
 * 根据货币格式化
 */
fun Number?.numCurrencyFormat(
    currencyType: String = UserDataUtil.currencyType(), isRound: Boolean = true
    //考虑到调用该方法的代码处，有可能在子线程也有可能在主线程，因此这里进行判断来调用不同的方法
): String = if (Looper.myLooper() == Looper.getMainLooper()) {
    this.toString().numCurrencyFormat(currencyType, isRound)
} else {
    this.toString().numCurrencyFormat2(currencyType, isRound)
}

/**
 * 根据货币格式化
 */
fun String?.numCurrencyFormat(
    currencyType: String = UserDataUtil.currencyType(), isRound: Boolean = true
): String {

    if (Looper.myLooper() != Looper.getMainLooper()) {
        return numCurrencyFormat2(currencyType, isRound)
    }

    if (TextUtils.isEmpty(this))
        return when (currencyType) {
            "JPY", "USC" -> "0"
            "BTC", "ETH" -> "0.00000000"
            else -> "0.00"
        }

    return formatByCurrency(this ?: "", currencyType, isRound)
}

/**
 * 根据货币的类型进行不同小数位的格式化
 */
private fun formatByCurrency(amount: String, currencyType: String, isRound: Boolean = true): String =
    amount.numFormat(
        when (currencyType) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        },
        isRound
    )

/**
 * 转换百分比
 */
fun String.percent(digits: Int = 2, isRoundUp: Boolean = true): String {
    return runCatching {
        val s1 = BigDecimal(this)
        val s2 = BigDecimal("100")
        if (isRoundUp) {
            s1.multiply(s2).setScale(digits, RoundingMode.HALF_UP).toPlainString()
        } else {
            s1.multiply(s2).setScale(digits).toPlainString()
        }
    }.getOrDefault("0")
}

/**
 * 格式化产品价格
 */
fun String?.formatProductPrice(digits: Int, isRound: Boolean = false, zeroUI: String = "-"): String {
    return this.toDoubleCatching().formatProductPrice(digits, isRound, zeroUI)
}

/**
 * 这个方法会给数字添加分隔符，如：123，456，789.00 ，所以这个方法格式话以后 不可再使用 数字格式化方法 如 numberFormat等方法， addComma只能最后调用
 */
fun String.addComma(): String {
    return addComma(-1)
}

/**
 * 这个方法会给数字添加分隔符，如：123，456，789.00 ，所以这个方法格式话以后 不可再使用 数字格式化方法 如 numberFormat等方法， addComma只能最后调用
 */
fun String.addComma(currencyType: String): String {
    return addComma(
        when (currencyType) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        }
    )
}

private val df = DecimalFormat.getInstance(Locale.ENGLISH) as DecimalFormat

/**
 * 这个方法会给数字添加分隔符，如：123，456，789.00 ，所以这个方法格式话以后 不可再使用 数字格式化方法 如 numberFormat等方法， addComma只能最后调用
 */
fun String.addComma(digitParam: Int): String {

    var digit = ""
    if (digitParam == -1) {
        val currencyType = UserDataUtil.currencyType()
        digit = when (currencyType) {
            "JPY", "USC" -> ""
            "BTC", "ETH" -> "00000000"
            else -> "00"
        }
    } else {
        for (i in 1..digitParam) {
            digit += "0"
        }
    }
    df.isGroupingUsed = false
    df.applyPattern(
        if (this.indexOf(".") > 0) {
            "###,##0.$digit"
        } else {
            "###,##0"
        }
    )

    return df.format(this.toDoubleCatching())
}

fun String?.toFloatCatching(default: Float = 0f): Float {
    return this?.let {
        runCatching { it.toFloat() }.getOrDefault(default)
    } ?: default
}

fun String?.toDoubleCatching(default: Double = 0.00): Double {
    return this?.let {
        runCatching { it.toDouble() }.getOrDefault(default)
    } ?: default
}

fun String?.toLongCatching(default: Long = 0L): Long {
    return this?.let {
        runCatching { it.toLong() }.getOrDefault(default)
    } ?: default
}

fun String?.toIntCatching(default: Int = 0): Int {
    return this?.let {
        runCatching { it.toInt() }.getOrDefault(default)
    } ?: default
}

fun Any?.asBoolean(): Boolean = when (this) {
    is Boolean -> this
    else -> false
}

/**
 * 截取字符串
 */
fun String?.substringCatching(startIndex: Int, endIndex: Int, default: String = ""): String {
    if (startIndex > endIndex) {
        return default
    }
    return this?.let {
        runCatching { it.substring(startIndex, endIndex) }.getOrDefault(default)
    } ?: default
}

/**
 * 科学计数法转正常数字
 */
fun String?.parseScientificNotation(): String {
    if (this.isNullOrEmpty()) return this.ifNull()
    return try {
        // 支持 E/e/[0-9]/+/- 的完整科学计数法格式
        val regex = Regex("""^([+-]?[\d.]+)[eE]([+-]?\d+)$""")
        val (coefficient, exponent) = regex.find(this)?.destructured ?: return this // 格式不匹配

        BigDecimal(coefficient).multiply(
            BigDecimal.TEN.pow(exponent.toInt())
        ).toString()
    } catch (e: Exception) {
        this // 格式异常/数值溢出等错误处理
    }
}