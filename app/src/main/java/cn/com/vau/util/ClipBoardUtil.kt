package cn.com.vau.util

import android.content.*
import android.text.TextUtils

/**
 * 剪切板读写工具
 */
object ClipBoardUtil {

    /**
     * 获取剪切板内容
     * @return
     */
    fun paste(): String {
        val manager = UtilApp.getApp().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
        if (manager != null) {
            if (manager.hasPrimaryClip() && (manager.primaryClip?.itemCount ?: 0) > 0) {
                val addedText = manager.primaryClip?.getItemAt(0)?.text
                val addedTextString = addedText.toString()
                if (!TextUtils.isEmpty(addedTextString)) {
                    return addedTextString
                }
            }
        }
        return ""
    }

    /**
     * 清空剪切板
     */
    fun clear() {
        val manager = UtilApp.getApp().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
        if (manager != null) {
            try {
                manager.primaryClip?.let {
                    manager.setPrimaryClip(it)
                    manager.setPrimaryClip(ClipData.newPlainText("", ""))
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

}
