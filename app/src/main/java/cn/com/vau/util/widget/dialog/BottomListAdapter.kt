package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.annotation.Keep
import androidx.appcompat.widget.AppCompatImageView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.util.LogUtil

class BottomListAdapter(var mContext: Context, var dataList: MutableList<BottomItemBean>, var selectIndex: Int = -1, var itemType: BottomItemType = BottomItemType.WITH_CHECKBOX) :
    RecyclerView.Adapter<BottomListAdapter.ViewHolder>() {

    private var onItemClickListener: ((position: Int, itemBean: BottomItemBean) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(mContext)
                .inflate(R.layout.item_dialog_title_content_select_arrow, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        super.onBindViewHolder(holder, position, payloads)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val currentItem = dataList.getOrNull(position) ?: return
        LogUtil.e("lwcccc", "position: $position,selectIndex: $selectIndex,itemType: $itemType, orderBean: $currentItem")
        when (itemType) {
            BottomItemType.WITH_CHECKBOX -> {
                holder.mCheckBox.visibility = View.VISIBLE
                holder.ivArrowRight.visibility = View.GONE
            }

            BottomItemType.WITH_ARROW -> {
                holder.mCheckBox.visibility = View.GONE
                holder.ivArrowRight.visibility = View.VISIBLE
            }

            BottomItemType.WITHOUT_CHECKBOX_ARROW -> {
                holder.mCheckBox.visibility = View.GONE
                holder.ivArrowRight.visibility = View.GONE
            }
        }


        holder.tvTitle.text = currentItem.title
        holder.tvContent.text = currentItem.content
        holder.mCheckBox.isChecked = selectIndex == position

        holder.itemView.setOnClickListener {
            updateSelectIndex(holder.bindingAdapterPosition)
            onItemClickListener?.invoke(holder.bindingAdapterPosition, dataList[holder.bindingAdapterPosition])
        }

        holder.mCheckBox.setOnClickListener {
            updateSelectIndex(holder.bindingAdapterPosition)
            onItemClickListener?.invoke(holder.bindingAdapterPosition, dataList[holder.bindingAdapterPosition])
        }
    }

    override fun getItemCount(): Int = dataList.size

    fun setOnItemClickListener(onItemClickListener: ((position: Int, itemBean: BottomItemBean) -> Unit)? = null) {
        this.onItemClickListener = onItemClickListener
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setList(newDataList: List<BottomItemBean>, newSelectIndex: Int = -1) {
        dataList.clear()
        dataList.addAll(newDataList)
        selectIndex = newSelectIndex
        notifyDataSetChanged()
    }

    fun updateSelectIndex(newIndex: Int) {
        if (selectIndex != newIndex) {
            val oldIndex = selectIndex
            selectIndex = newIndex
            notifyItemChanged(oldIndex)
            notifyItemChanged(newIndex)
        }
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val mCheckBox: CheckBox = view.findViewById(R.id.mCheckBox)
        val ivArrowRight: AppCompatImageView = view.findViewById(R.id.ivArrowRight)
        val tvTitle: TextView = view.findViewById(R.id.tvTitle)
        val tvContent: TextView = view.findViewById(R.id.tvContent)
    }

}

enum class BottomItemType {
    WITH_CHECKBOX,
    WITH_ARROW,
    WITHOUT_CHECKBOX_ARROW,
}

@Keep
data class BottomItemBean(val title: String? = "", val content: String? = "")
