package cn.com.vau.util.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.*
import android.text.TextUtils
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px

/**
 * 增强版虚线下划线TextView
 * 
 * 功能特性：
 * 1. 支持整个TextView设置下划线，也支持局部文字设置下划虚线
 * 2. 充分考虑TextView对齐标线，比如基线（Baseline）、Top、Ascent、Descent、Bottom、Leading、字母间距（Letter Spacing）对虚线的影响
 * 3. 支持设置虚线到基准线之间的间距
 * 4. 支持自定义虚线样式（长度、间隔、颜色、粗细）
 * 5. 支持虚线位置点击事件
 */
class DashLineTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    enum class DashAlignment {
        BASELINE,   // 基于基线（推荐默认，适用于大多数场景）
        DESCENT,    // 基于descent线（适用于包含下降字符的文本）
        BOTTOM      // 基于bottom线（适用于需要更大间距的场景）
    }

    // 虚线属性
    private var dashColor: Int = AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)
    private var dashStrokeWidth: Float = 1f.dp2px()
    private var dashLength: Float = 4f.dp2px()
    private var dashGap: Float = 4f.dp2px()
    private var dashOffsetFromBaseline: Float = 2f.dp2px() // DESCENT对齐下的合理偏移量
    private var dashAlignment: DashAlignment = DashAlignment.DESCENT // 默认使用DESCENT对齐
    private var dashEnabled: Boolean = true
    private var dashCap: Paint.Cap = Paint.Cap.ROUND



    private val dashPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
    }

    init {
        attrs?.let {
            val typedArray: TypedArray = context.obtainStyledAttributes(it, R.styleable.DashLineTextView)

            dashColor = typedArray.getColor(R.styleable.DashLineTextView_dlDashColor, dashColor)
            dashStrokeWidth = typedArray.getDimension(R.styleable.DashLineTextView_dlDashStrokeWidth, dashStrokeWidth)
            dashLength = typedArray.getDimension(R.styleable.DashLineTextView_dlDashLength, dashLength)
            dashGap = typedArray.getDimension(R.styleable.DashLineTextView_dlDashGap, dashGap)
            dashOffsetFromBaseline = typedArray.getDimension(R.styleable.DashLineTextView_dlDashOffsetFromBaseline, dashOffsetFromBaseline)
            dashEnabled = typedArray.getBoolean(R.styleable.DashLineTextView_dlDashEnabled, dashEnabled)

            val alignmentValue = typedArray.getInt(R.styleable.DashLineTextView_dlDashAlignment, 0)
            dashAlignment = when (alignmentValue) {
                0 -> DashAlignment.BASELINE
                1 -> DashAlignment.DESCENT
                2 -> DashAlignment.BOTTOM
                else -> DashAlignment.BASELINE
            }

            val capValue = typedArray.getInt(R.styleable.DashLineTextView_dlDashCap, 0)
            dashCap = when (capValue) {
                0 -> Paint.Cap.ROUND
                1 -> Paint.Cap.SQUARE
                else -> Paint.Cap.ROUND
            }

            typedArray.recycle()
        }
        
        updateDashPaint()
    }

    private fun updateDashPaint() {
        dashPaint.apply {
            color = dashColor
            strokeWidth = dashStrokeWidth
            strokeCap = dashCap
            pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
        }
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        // 先绘制文字
        super.onDraw(canvas)

        if (!dashEnabled || layout == null || TextUtils.isEmpty(text)) {
            return
        }

        // 启用软件渲染以支持虚线效果
        setLayerType(LAYER_TYPE_SOFTWARE, null)

        // 在文字绘制完成后绘制虚线
        drawDashedUnderlines(canvas)
    }

    private fun drawDashedUnderlines(canvas: Canvas) {
        val layout = layout ?: return
        val lineCount = layout.lineCount

        // 获取TextView的内容区域（排除padding和scrolling）
        val compoundPaddingLeft = compoundPaddingLeft.toFloat()
        val extendedPaddingTop = extendedPaddingTop.toFloat()

        for (i in 0 until lineCount) {
            // 获取行的基本信息
            val lineLeft = layout.getLineLeft(i) + compoundPaddingLeft
            val lineRight = layout.getLineRight(i) + compoundPaddingLeft

            // 关键：使用正确的基线位置
            // layout.getLineBaseline(i) 返回的是相对于layout顶部的位置
            // 需要加上TextView的实际内容顶部偏移
            val lineBaseline = layout.getLineBaseline(i).toFloat() + extendedPaddingTop

            // 计算虚线的Y坐标
            val dashY = calculateDashY(lineBaseline, i)

            // 考虑字母间距的影响
            val adjustedLeft = lineLeft + calculateLetterSpacingOffset()
            val adjustedRight = lineRight - calculateLetterSpacingOffset()

            // 绘制虚线
            canvas.drawLine(adjustedLeft, dashY, adjustedRight, dashY, dashPaint)
        }
    }

    private fun calculateDashY(baseline: Float, lineIndex: Int): Float {
        val fontMetrics = paint.fontMetricsInt

        return when (dashAlignment) {
            DashAlignment.BASELINE -> {
                // 基线对齐：在基线下方绘制虚线
                // 确保虚线在文字下方，至少距离基线2dp
                baseline + maxOf(dashOffsetFromBaseline, 2f.dp2px())
            }
            DashAlignment.DESCENT -> {
                // Descent对齐：在descent线下方绘制虚线
                // fontMetrics.descent是正值，表示基线下方的距离
                baseline + fontMetrics.descent + dashOffsetFromBaseline
            }
            DashAlignment.BOTTOM -> {
                // Bottom对齐：在bottom线下方绘制虚线
                // fontMetrics.bottom是正值，表示基线下方的最大距离
                baseline + fontMetrics.bottom + dashOffsetFromBaseline
            }
        }
    }

    private fun calculateLetterSpacingOffset(): Float {
        return letterSpacing * textSize * 0.1f // 根据字母间距调整
    }



    // 公共API方法



    /**
     * 设置虚线颜色
     */
    fun setDashColor(@ColorInt color: Int) {
        if (dashColor != color) {
            dashColor = color
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线线条宽度
     */
    fun setDashStrokeWidth(width: Float) {
        if (dashStrokeWidth != width) {
            dashStrokeWidth = width
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线长度
     */
    fun setDashLength(length: Float) {
        if (dashLength != length) {
            dashLength = length
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线间隔
     */
    fun setDashGap(gap: Float) {
        if (dashGap != gap) {
            dashGap = gap
            updateDashPaint()
            invalidate()
        }
    }

    /**
     * 设置虚线距离基准线的偏移量
     */
    fun setDashOffsetFromBaseline(offset: Float) {
        if (dashOffsetFromBaseline != offset) {
            dashOffsetFromBaseline = offset
            invalidate()
        }
    }

    /**
     * 设置虚线对齐方式
     */
    fun setDashAlignment(alignment: DashAlignment) {
        if (dashAlignment != alignment) {
            dashAlignment = alignment
            invalidate()
        }
    }

    /**
     * 启用或禁用虚线
     */
    fun setDashEnabled(enabled: Boolean) {
        if (dashEnabled != enabled) {
            dashEnabled = enabled
            invalidate()
        }
    }

    /**
     * 设置虚线端点样式
     */
    fun setDashCap(cap: Paint.Cap) {
        if (dashCap != cap) {
            dashCap = cap
            updateDashPaint()
            invalidate()
        }
    }

    // Getter方法
    fun getDashColor(): Int = dashColor
    fun getDashStrokeWidth(): Float = dashStrokeWidth
    fun getDashLength(): Float = dashLength
    fun getDashGap(): Float = dashGap
    fun getDashOffsetFromBaseline(): Float = dashOffsetFromBaseline
    fun getDashAlignment(): DashAlignment = dashAlignment
    fun isDashEnabled(): Boolean = dashEnabled
    fun getDashCap(): Paint.Cap = dashCap
}
