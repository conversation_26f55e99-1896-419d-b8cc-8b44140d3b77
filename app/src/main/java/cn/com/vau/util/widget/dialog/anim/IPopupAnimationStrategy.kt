package cn.com.vau.util.widget.dialog.anim

import com.lxj.xpopup.enums.PopupAnimation

interface IPopupAnimationStrategy {
    fun getAnimation(): PopupAnimation?
}

// 从中心进行缩放+透明渐变
class ScaleAlphaFromCenterStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.ScaleAlphaFromCenter
    }
}

//从左上角进行缩放+透明渐变
class ScaleAlphaFromLeftTopStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.ScaleAlphaFromLeftTop
    }
}

//从右上角进行缩放+透明渐变
class ScaleAlphaFromRightTopStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.ScaleAlphaFromRightTop
    }
}

//从左下角进行缩放+透明渐变
class ScaleAlphaFromLeftBottomStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.ScaleAlphaFromLeftBottom
    }
}

//从右下角进行缩放+透明渐变
class ScaleAlphaFromRightBottomStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.ScaleAlphaFromRightBottom
    }
}

//从左平移进入平移+透明渐变
class TranslateAlphaFromLeftStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateAlphaFromLeft
    }
}

//从右平移进入平移+透明渐变
class TranslateAlphaFromRightStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateAlphaFromRight
    }
}

//从上方平移进入平移+透明渐变
class TranslateAlphaFromTopStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateAlphaFromTop
    }
}

//从下方平移进入平移+透明渐变
class TranslateAlphaFromBottomStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateAlphaFromBottom
    }
}

//从左平移进入
class TranslateFromLeftStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateFromLeft
    }
}

//从右平移进入
class TranslateFromRightStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateFromRight
    }
}

//从上方平移进入
class TranslateFromTopStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateFromTop
    }
}

//从下方平移进入
class TranslateFromBottomStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.TranslateFromBottom
    }
}

//禁用动画
class NoAnimationStrategy : IPopupAnimationStrategy {
    override fun getAnimation(): PopupAnimation {
        return PopupAnimation.NoAnimation
    }
}