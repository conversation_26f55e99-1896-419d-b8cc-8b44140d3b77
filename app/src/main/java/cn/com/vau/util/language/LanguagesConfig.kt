package cn.com.vau.util.language

import android.content.Context
import cn.com.vau.common.application.VauApplication

/**
 * author：lvy
 * date：2024/12/07
 * desc：
 */
object LanguagesConfig {

    /**
     * 为了兼容老用户的语言配置，所以先保留几个版本，后续会统一收缩入口到 SpManager
     */
    private const val FILE_NAME = "language_setting"
//    private var systemCurrentLocal = Locale.ENGLISH

    private val sharedPreferences by lazy {
        VauApplication.context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE)
    }

    private val editor by lazy { sharedPreferences.edit() }

    @JvmStatic
    fun saveLanguage(key: String, select: Int) {
        editor.putInt(key, select)
        editor.apply()
    }

    @JvmStatic
    fun getSelectLanguage(key: String): Int {
        return sharedPreferences.getInt(key, -1)
    }

//    @JvmStatic
//    fun getSystemCurrentLocal(): Locale {
//        return systemCurrentLocal
//    }
//
//    @JvmStatic
//    fun setSystemCurrentLocal(local: Locale) {
//        this.systemCurrentLocal = local
//    }
}