package cn.com.vau.util;

import android.os.Looper;
import android.os.MessageQueue;
import android.util.Log;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Nullable;

/**
 * 单例或者 静态 方法这种 ， 如果 notInTimeRefreshIndexList是静态的，那么就会有问题，A 切换到B的Fragment，如果他们使用同一个 notInTimeRefreshIndexList 就不行
 * 因此这个类目前不能使用单例这种设计模式
 * 要考虑，这个页面切走了，但是idleHandler一直得不到执行怎么办??
 * 要考虑，这个页面切走了，但是notInTimeRefreshIndexList里面还有数据怎么办？
 */
public class AdapterRefreshNotifyItemController {

    private final static String TAG = "AdapterRefreshOptUtil";

    //堆积的 ， 未来得及的 需要刷新的索引
    private final HashSet<Integer> notInTimeRefreshIndexSet = new HashSet<>();
    private final WeakReference<RecyclerView> wrRecyclerView;
    private final WeakReference<RecyclerView.Adapter<?>> wrAdapter;

    //有可能业务使用的是MyRecyclerView 或者 WrapAdapter，所以还是传进来adapter对象更靠谱
    public AdapterRefreshNotifyItemController(RecyclerView recyclerView, RecyclerView.Adapter<?> adapter) {
        this.wrRecyclerView = new WeakReference<>(recyclerView);
        this.wrAdapter = new WeakReference<>(adapter);
    }

    private @Nullable RecyclerView.Adapter<?> getAdapter() {
        if (wrAdapter.get() == null) {
            return null;
        }
        return wrAdapter.get();
    }

    private @Nullable LinearLayoutManager getLayoutManager() {
        if (wrRecyclerView.get() == null) {
            return null;
        }
        if (!(wrRecyclerView.get().getLayoutManager() instanceof LinearLayoutManager)) {
            throw new RuntimeException("Your layoutManager must be of type LinearLayoutManager");
        }
        return (LinearLayoutManager) wrRecyclerView.get().getLayoutManager();
    }

    /**
     * 依次刷新itemView  避免一次性调用多条notifyItemChange
     * 当滑动时，如果一次调用很多的notifyItemChange，那么会造成很多的measureChild，从而导致页面卡顿
     * 该方法可以将一次多条刷新分批刷新，从而避免页面卡顿
     * 因此，该方法适合的场景：用户滑动过程，需要刷新，且大批量刷新
     *
     * @param refreshPositionList 需要更新的position集合
     *
     */
    public void refresh(List<Integer> refreshPositionList) {
        if (refreshPositionList == null || refreshPositionList.isEmpty()) {
            return;
        }
        if (getLayoutManager() == null) {
            return;
        }
        //筛选出屏幕可见的刷新的item索引，因为在屏幕外我们不需要刷新
        List<Integer> visiblePositionRefreshList = filterVisibleRefreshPositionList(refreshPositionList, getLayoutManager());
        if (visiblePositionRefreshList.isEmpty()) {
            return;
        }

        checkNotInTimeRefresh(getLayoutManager(), notInTimeRefreshIndexSet);

        //列表中可见item显示最多的数量
        int maxVisibleItemCount = getLayoutManager().findLastVisibleItemPosition() - getLayoutManager().findFirstVisibleItemPosition();
        //限制一次刷新的最多item数量
        int onceRefreshMax = (int) (maxVisibleItemCount / 3.5);
        if (onceRefreshMax < 1) {//屏幕上的列表数据很少的情况
            onceRefreshMax = 1;
        }

        for (int i = 0; i < visiblePositionRefreshList.size(); i++) {
            Integer visibleIndex = visiblePositionRefreshList.get(i);//刷新的Position
            if (i < onceRefreshMax) {
                //没有超过最大刷新量，正常刷新
                if (getAdapter() != null) {
                    getAdapter().notifyItemChanged(visibleIndex);
                }
                //从堆积集合中移除
                notInTimeRefreshIndexSet.remove(visibleIndex);
            } else {
                //超过的部分记录起来
                notInTimeRefreshIndexSet.add(visibleIndex);
            }
        }

        if (!notInTimeRefreshIndexSet.isEmpty()) {
            Looper.myQueue().addIdleHandler(new RefreshIdleHandler());
        }
    }

    private class RefreshIdleHandler implements MessageQueue.IdleHandler{

        @Override
        public boolean queueIdle() {
            if (!notInTimeRefreshIndexSet.isEmpty()) {
                //递归调用
                refresh(new ArrayList<>(notInTimeRefreshIndexSet));
            }
            return false;
        }
    }

    /**
     * 堆积集合中，我们把其中页面不可见的清除掉
     * （一般滑动场景）
     */
    private void checkNotInTimeRefresh(LinearLayoutManager layoutManager, HashSet<Integer> notInTimeRefreshIndexSet) {
        if (layoutManager == null || notInTimeRefreshIndexSet.isEmpty()) {
            return;
        }
        Iterator<Integer> iterator = notInTimeRefreshIndexSet.iterator();
        int findFirstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
        int findLastVisibleItemPosition = layoutManager.findLastVisibleItemPosition();
        while (iterator.hasNext()) {
            Integer refreshIndex = iterator.next();
            //判断当前是否在屏幕可见
            boolean currentItemIsVisible = refreshIndex >= findFirstVisibleItemPosition - 1 && refreshIndex <= findLastVisibleItemPosition + 1;
            if (!currentItemIsVisible) {
                //将不在显示屏幕范围内的移除掉
                iterator.remove();
            }
        }
    }

    /**
     * 筛选出需要刷新中的 可见的Position集合
     */
    private List<Integer> filterVisibleRefreshPositionList(List<Integer> refreshPositionList, LinearLayoutManager layoutManager) {
        if (refreshPositionList == null || refreshPositionList.isEmpty() || layoutManager == null) {
            return new ArrayList<>();
        }
        List<Integer> visibleIndexRefreshList = new ArrayList<>();
        int findFirstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
        int findLastVisibleItemPosition = layoutManager.findLastVisibleItemPosition();
        for (Integer refreshIndex : refreshPositionList) {
            //判断当前是否在屏幕可见
            boolean currentItemIsVisible = refreshIndex >= findFirstVisibleItemPosition - 1 && refreshIndex <= findLastVisibleItemPosition + 1;
            if (currentItemIsVisible) {
                //如果当前要刷新的index是在屏幕内可见的，则记录
                visibleIndexRefreshList.add(refreshIndex);
            }
        }
        return visibleIndexRefreshList;
    }
}
