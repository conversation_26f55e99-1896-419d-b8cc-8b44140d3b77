package cn.com.vau.util.widget.dialog.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.lxj.xpopup.core.DrawerPopupView

@SuppressLint("ViewConstructor")
open class DrawerDialog<VB : ViewBinding>(
    context: Context,
    viewBinding: ((LayoutInflater, ViewGroup, Boolean) -> VB),
    private val onCreateListener: ((VB) -> Unit)? = null,
    private val onDismissListener: (() -> Unit)? = null,
    private val width: Int = 0,
    private val height: Int = 0,
    private val maxWidth: Int = 0,
    private val maxHeight: Int = 0,
) : DrawerPopupView(context), IDialog<VB> {

    private val inflater = LayoutInflater.from(context)

    protected var mContentBinding = viewBinding.invoke(inflater, drawerContentContainer, false)

    override fun addInnerContent() {
        drawerContentContainer.addView(mContentBinding.root)
    }

    override fun getPopupWidth(): Int {
        return if (width != 0) width else super.getPopupWidth()
    }

    override fun getPopupHeight(): Int {
        return if (height != 0) height else super.getPopupHeight()
    }

    // 如果需要覆写 getMaxWidth 和 getMaxHeight，可以提供自定义的最大宽高
    override fun getMaxWidth(): Int {
        return if (maxWidth != 0) maxWidth else super.getMaxWidth()
    }

    override fun getMaxHeight(): Int {
        return if (maxHeight != 0) maxHeight else super.getMaxHeight()
    }

    override fun onCreate() {
        super.onCreate()
        onCreateListener?.invoke(mContentBinding)
        processBackPress()
        setContentView()
    }

    /**
     * 设置内容区域
     */
    open fun setContentView() {

    }

    private fun processBackPress() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && dialog != null) {
            dialog.onBackInvokedDispatcher.registerOnBackInvokedCallback(0) {
                if (onBackPressed()) {
                    return@registerOnBackInvokedCallback
                }
                if (popupInfo.isDismissOnBackPressed &&
                    (popupInfo.xPopupCallback == null || !popupInfo.xPopupCallback.onBackPressed(this))) {
                    dismissOrHideSoftInput()
                }
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        onDismissListener?.invoke()
    }

    override fun getContentViewBinding(): VB {
        return mContentBinding
    }

    override fun showDialog() {
        if (popupInfo == null) {
            return
        }
        super.show()
    }

    override fun dismissDialog() {
        super.dismiss()
    }

    override fun isShowDialog(): Boolean {
        return super.isShow()
    }

    @Suppress("unused")
    open class Builder<VB : ViewBinding>(activity: Activity) :
        IBuilder<VB, Builder<VB>>(activity) {

        override fun createDialog(context: Context): IDialog<VB> {
            return DrawerDialog<VB>(
                context,
                config.viewBinding,
                config.onCreateListener, config.onDismissListener,
                config.width, config.height,
                config.maxWidth, config.maxHeight,
            )
        }
    }
}