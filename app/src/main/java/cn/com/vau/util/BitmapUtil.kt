package cn.com.vau.util

import android.content.ContentValues
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.net.Uri
import android.provider.MediaStore
import android.util.Base64
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import java.io.File
import java.io.FileOutputStream

/**
 * 获取View中整张图片
 * 保存在本地图库
 * Created by zhy on 2018/7/9.
 */
object BitmapUtil {

    fun base64ToBitmap(base64Data: String?): Bitmap {
        val bytes: ByteArray = Base64.decode(base64Data, Base64.DEFAULT)
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }

    @JvmStatic
    fun loadBitmapFromView(v: View?): Bitmap? {
        if (v == null) {
            return null
        }
        val screenshot: Bitmap = Bitmap.createBitmap(v.width, v.height, Bitmap.Config.ARGB_4444)
        val canvas = Canvas(screenshot)
        canvas.translate(-v.scrollX.toFloat(), -v.scrollY.toFloat()) //我们在用滑动View获得它的Bitmap时候，获得的是整个View的区域（包括隐藏的），如果想得到当前区域，需要重新定位到当前可显示的区域
        v.draw(canvas) // 将 view 画到画布上
        return screenshot
    }

    fun saveImageByBitmap(bm: Bitmap?): Uri? {
        val fileName = System.currentTimeMillis().toString() + ".jpg"
        val resolver = UtilApp.getApp().contentResolver
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
        }
        try {
            val uri = resolver?.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues) ?: return null
            resolver.openOutputStream(uri).use {
                it?.let { stream ->
                    bm?.compress(Bitmap.CompressFormat.JPEG, 100, stream)
                }
            }
            return uri
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 只下载图片，保存到app的私有目录的缓存文件夹内，下载成功返回文件，失败返回null
     */
    fun saveImageFromUrl(context: Context, imageUrl: String, onComplete: (File?) -> Unit) {
        Glide.with(context)
            .asBitmap()
            .load(imageUrl)
            .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    // 创建本地文件
                    val fileName = "downloaded_image_${System.currentTimeMillis()}.jpg"
                    val file = File(PathUtil.getInternalAppCachePath(), fileName)
//                    val file = File(context.getDir("images", Context.MODE_PRIVATE), fileName)

                    try {
                        // 将图片保存到文件
                        val fos = FileOutputStream(file)
                        resource.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                        fos.flush()
                        fos.close()

                        onComplete(file)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        onComplete(null)
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // 清除资源时的处理（可选）
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // 下载失败时回调
                    onComplete(null)
                }
            })
    }
}