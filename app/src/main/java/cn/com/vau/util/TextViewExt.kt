package cn.com.vau.util

import android.content.res.Resources
import android.graphics.Paint
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.text.buildSpannedString
import cn.com.vau.util.span.DashedUnderlineSpan

/**
 * 将dp转换为px
 */
private fun Float.dpToPx(): Float = this * Resources.getSystem().displayMetrics.density

/**
 * TextView扩展函数，用于设置虚线下划线
 */

/**
 * 为TextView的指定文字设置虚线下划线
 * 
 * @param targetText 要设置虚线的目标文字
 * @param dashColor 虚线颜色，默认使用当前文字颜色
 * @param dashStrokeWidth 虚线线条宽度
 * @param dashLength 虚线长度
 * @param dashGap 虚线间隔
 * @param offsetFromBaseline 虚线距离基线的偏移量
 * @param alignment 虚线对齐方式
 */
fun TextView.setDashedUnderline(
    targetText: String,
    @ColorInt dashColor: Int = currentTextColor,
    dashStrokeWidth: Float = 1f.dpToPx(),
    dashLength: Float = 4f.dpToPx(),
    dashGap: Float = 4f.dpToPx(),
    offsetFromBaseline: Float = 2f.dpToPx(),
    alignment: DashedUnderlineSpan.DashAlignment = DashedUnderlineSpan.DashAlignment.DESCENT, // 默认使用DESCENT对齐

) {
    val currentText = text.toString()
    if (currentText.isEmpty() || targetText.isEmpty()) return
    
    val startIndex = currentText.indexOf(targetText)
    if (startIndex == -1) return
    
    val endIndex = startIndex + targetText.length
    
    val spannableString = if (text is SpannableString) {
        SpannableString(text)
    } else {
        SpannableString(currentText)
    }
    
    // 创建虚线绘制Span
    val dashedSpan = DashedUnderlineSpan(
        dashColor = dashColor,
        dashStrokeWidth = dashStrokeWidth,
        dashLength = dashLength,
        dashGap = dashGap,
        offsetFromBaseline = offsetFromBaseline,
        alignment = alignment
    )

    // 设置虚线绘制Span
    spannableString.setSpan(
        dashedSpan,
        startIndex,
        endIndex,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )

    text = spannableString
}

/**
 * 为TextView的多个文字片段设置虚线下划线
 * 
 * @param targets 要设置虚线的目标文字列表
 * @param dashColor 虚线颜色，默认使用当前文字颜色
 * @param dashStrokeWidth 虚线线条宽度
 * @param dashLength 虚线长度
 * @param dashGap 虚线间隔
 * @param offsetFromBaseline 虚线距离基线的偏移量
 * @param alignment 虚线对齐方式
 */
fun TextView.setDashedUnderlineMultiple(
    targets: List<String>,
    @ColorInt dashColor: Int = currentTextColor,
    dashStrokeWidth: Float = 1f.dpToPx(),
    dashLength: Float = 4f.dpToPx(),
    dashGap: Float = 4f.dpToPx(),
    offsetFromBaseline: Float = 2f.dpToPx(),
    alignment: DashedUnderlineSpan.DashAlignment = DashedUnderlineSpan.DashAlignment.DESCENT // 默认使用DESCENT对齐
) {
    val currentText = text.toString()
    if (currentText.isEmpty() || targets.isEmpty()) return
    
    val spannableString = if (text is SpannableString) {
        SpannableString(text)
    } else {
        SpannableString(currentText)
    }
    
    targets.forEach { targetText ->
        if (targetText.isNotEmpty()) {
            var startIndex = 0
            while (true) {
                val index = currentText.indexOf(targetText, startIndex)
                if (index == -1) break
                
                val endIndex = index + targetText.length
                
                // 创建虚线绘制Span
                val dashedSpan = DashedUnderlineSpan(
                    dashColor = dashColor,
                    dashStrokeWidth = dashStrokeWidth,
                    dashLength = dashLength,
                    dashGap = dashGap,
                    offsetFromBaseline = offsetFromBaseline,
                    alignment = alignment
                )

                // 设置虚线绘制Span
                spannableString.setSpan(
                    dashedSpan,
                    index,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )


                
                startIndex = endIndex
            }
        }
    }
    
    text = spannableString
}

/**
 * 构建带虚线下划线的SpannableString
 * 
 * @param text 完整文本
 * @param dashedParts 需要设置虚线的文字片段配置
 */
fun buildDashedSpannableString(
    text: String,
    vararg dashedParts: DashedTextPart
): SpannableString {
    val spannableString = SpannableString(text)
    
    dashedParts.forEach { part ->
        val startIndex = text.indexOf(part.text)
        if (startIndex != -1) {
            val endIndex = startIndex + part.text.length
            
            // 创建虚线绘制Span
            val dashedSpan = DashedUnderlineSpan(
                dashColor = part.dashColor,
                dashStrokeWidth = part.dashStrokeWidth,
                dashLength = part.dashLength,
                dashGap = part.dashGap,
                offsetFromBaseline = part.offsetFromBaseline,
                alignment = part.alignment
            )

            // 设置虚线绘制Span
            spannableString.setSpan(
                dashedSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )


        }
    }
    
    return spannableString
}

/**
 * 使用DSL风格构建带虚线的文本
 */
fun buildDashedText(builder: DashedTextBuilder.() -> Unit): SpannableStringBuilder {
    return DashedTextBuilder().apply(builder).build()
}

/**
 * 虚线文字片段配置
 */
data class DashedTextPart(
    val text: String,
    @ColorInt val dashColor: Int,
    val dashStrokeWidth: Float = 1f.dpToPx(),
    val dashLength: Float = 4f.dpToPx(),
    val dashGap: Float = 4f.dpToPx(),
    val offsetFromBaseline: Float = 2f.dpToPx(),
    val alignment: DashedUnderlineSpan.DashAlignment = DashedUnderlineSpan.DashAlignment.DESCENT // 默认使用DESCENT对齐
)

/**
 * DSL构建器
 */
class DashedTextBuilder {
    private val builder = SpannableStringBuilder()
    
    fun text(text: String) {
        builder.append(text)
    }
    
    fun dashedText(
        text: String,
        @ColorInt dashColor: Int,
        dashStrokeWidth: Float = 1f.dpToPx(),
        dashLength: Float = 4f.dpToPx(),
        dashGap: Float = 4f.dpToPx(),
        offsetFromBaseline: Float = 2f.dpToPx(),
        alignment: DashedUnderlineSpan.DashAlignment = DashedUnderlineSpan.DashAlignment.DESCENT // 默认使用DESCENT对齐
    ) {
        val start = builder.length
        builder.append(text)
        val end = builder.length
        
        // 创建虚线绘制Span
        val dashedSpan = DashedUnderlineSpan(
            dashColor = dashColor,
            dashStrokeWidth = dashStrokeWidth,
            dashLength = dashLength,
            dashGap = dashGap,
            offsetFromBaseline = offsetFromBaseline,
            alignment = alignment
        )

        // 设置虚线绘制Span
        builder.setSpan(dashedSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)


    }
    
    fun build(): SpannableStringBuilder = builder
}

/**
 * 清除TextView中的所有虚线下划线
 */
fun TextView.clearDashedUnderlines() {
    val currentText = text
    if (currentText is SpannableString) {
        // 清除虚线绘制Span
        val dashedSpans = currentText.getSpans(0, currentText.length, DashedUnderlineSpan::class.java)
        dashedSpans.forEach { span ->
            currentText.removeSpan(span)
        }
    }
}
