package cn.com.vau.util.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.dp2px

/**
 * 带ScrollView的 空布局
 */
class NoDataScrollView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : androidx.core.widget.NestedScrollView(context, attrs, defStyleAttr) {

    private lateinit var mIvIcon: ImageView
    private lateinit var mTvHintTitle: TextView//使用title替换icon
    private lateinit var mTvHintMessage: TextView
    private var mTvBottomBtn: TextView? = null

    private var hintMessage: String? = ""
    private var bottomButtonText: String? = ""
    private var iconDrawable: Drawable? = null

    init { //获取自定义属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.NoDataScrollView)

        hintMessage = typedArray.getString(R.styleable.NoDataScrollView_ndsv_hintMessage)
        bottomButtonText = typedArray.getString(R.styleable.NoDataScrollView_ndsv_bottomButtonText)
        iconDrawable = typedArray.getDrawable(R.styleable.NoDataScrollView_ndsv_icon)

        initView()
        typedArray.recycle()
    }

    /*
        初始化视图
     */
    private fun initView() {
        View.inflate(context, R.layout.layout_no_data_scroll_view, this)

        mIvIcon = findViewById(R.id.ivIconNd)
        ImageLoaderUtil.loadImage(context, AttrResourceUtil.getDrawable(context, R.attr.imgNoDataBase), mIvIcon)

        mTvHintTitle = findViewById(R.id.tvTitleNd)
        mTvHintMessage = findViewById(R.id.tvMsgNd)

        mTvHintMessage.text = hintMessage ?: ""

        showBtnTextView(bottomButtonText)

        if (iconDrawable != null) {
            mIvIcon.setImageDrawable(iconDrawable)
        }
    }

    private fun showBtnTextView(text: String?) {
        if (!TextUtils.isEmpty(text)) {
            if (mTvBottomBtn == null) { //说明没有inflate过
                findViewById<ViewStub>(R.id.tvNextNd).isVisible = true
                mTvBottomBtn = findViewById(R.id.tvBottomBtn)
                mTvBottomBtn?.text = text
            } else {
                mTvBottomBtn?.text = text
            }
            mTvBottomBtn?.isVisible = true
        } else {
            if (mTvBottomBtn != null) {
                mTvBottomBtn?.text = text
            }
        }
    }

    fun hideBtnTextView() {
        if (mTvBottomBtn != null) {
            mTvBottomBtn?.isVisible = false
        }
    }

    /**
     * 显示标题（同时隐藏icon）
     */
    fun showHintTitle(hintTitle: String?) {
        val layoutParams = mTvHintMessage.layoutParams as LinearLayout.LayoutParams
        if (!TextUtils.isEmpty(hintTitle)) {
            mTvHintTitle.text = hintTitle
            mTvHintTitle.isVisible = true
            mIvIcon.isVisible = false
            layoutParams.topMargin = 4.dp2px()
        } else {
            mTvHintTitle.isVisible = false
            mIvIcon.isVisible = true
            layoutParams.topMargin = 12.dp2px()
        }
        mTvHintMessage.layoutParams = layoutParams

    }

    fun setHintMessage(hintMessage: String?) {
        mTvHintMessage.text = hintMessage ?: ""
    }

    /**
     * 设置并显示底部按钮文本
     */
    fun setBottomBtnText(text: String?) {
        showBtnTextView(text)
    }

    fun setIconResource(@DrawableRes resId: Int) {
        mIvIcon.setImageResource(resId)
    }

    fun setIconDrawable(drawable: Drawable?) {
        mIvIcon.setImageDrawable(drawable)
    }

    fun setBottomBtnViewClickListener(listener: (() -> Unit)? = null) {
        mTvBottomBtn?.setOnClickListener {
            listener?.invoke()
        }
    }
}
