package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.TextPaint
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.databinding.DialogBottomActionBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.buidler.ActionBuilder

@SuppressLint("ViewConstructor")
@Suppress("unused")
class BottomActionDialog private constructor(
    context: Context,
    title: CharSequence?,
    private var content: CharSequence?,//可以是String或者SpannableString
    private var subTitle: CharSequence? = null,
    private var subContent: CharSequence? = null,//可以是String或者SpannableString
    private var onStartListener: ((TextView) -> Unit)? = null,
    private var onEndListener: ((TextView) -> Unit)? = null,
    private var isSingleButton: Boolean = false,
    private var onSingleButtonListener: ((TextView) -> Unit)? = null,
    private var singleButtonText: CharSequence? = null,
    private var startText: CharSequence? = null,
    private var endText: CharSequence? = null,
    private var linkText: CharSequence? = null,
    private var onLinkListener: ((TextView) -> Unit)? = null,
    onCreateListener: ((DialogBottomActionBinding) -> Unit)? = null,
    onDismissListener: (() -> Unit)? = null
) : BottomDialog<DialogBottomActionBinding>(
    context,
    DialogBottomActionBinding::inflate,
    title,
    onCreateListener,
    onDismissListener
) {
    override fun setContentView() {
        super.setContentView()
        setContent()
        setSubTitleView()
        setSubContentView()
        setButtonView()
        setLinkView()
    }

    /**
     * 设置内容
     */
    fun setContent(content: CharSequence?): BottomActionDialog {
        this.content = content
        setContent()
        return this
    }

    private fun setContent() {
        mContentBinding.tvContent.isVisible = !content.isNullOrEmpty()
        mContentBinding.tvContent.text = content
    }

    /**
     * 设置二级标题
     */
    fun setSubTitle(subTitle: CharSequence?): BottomActionDialog {
        this.subTitle = subTitle
        setSubTitleView()
        return this
    }

    private fun setSubTitleView() {
        mContentBinding.tvSubTitle.isVisible = !subTitle.isNullOrEmpty()
        mContentBinding.tvSubTitle.text = subTitle
    }

    /**
     * 设置二级内容
     */
    fun setSubContent(subContent: CharSequence?): BottomActionDialog {
        this.subContent = subContent
        setSubContentView()
        return this
    }

    private fun setSubContentView() {
        mContentBinding.tvSubContent.isVisible = !subContent.isNullOrEmpty()
        mContentBinding.tvSubContent.text = subContent
    }

    /**
     * 设置StartText
     */
    fun setStartText(startText: CharSequence?): BottomActionDialog {
        if (!startText.isNullOrEmpty()) {
            this.startText = startText
            mContentBinding.tvStart.text = this.startText
        }
        return this
    }

    /**
     * 设置EndText
     */
    fun setEndText(endText: CharSequence?): BottomActionDialog {
        if (!endText.isNullOrEmpty()) {
            this.endText = endText
            mContentBinding.tvEnd.text = this.endText
        }
        return this
    }

    /**
     * 设置StartText点击事件
     */
    fun setStartListener(onStartListener: ((TextView) -> Unit)?): BottomActionDialog {
        this.onStartListener = onStartListener
        setStartListener()
        return this
    }

    private fun setStartListener() {
        mContentBinding.tvStart.setOnClickListener {
            this.onStartListener?.invoke(mContentBinding.tvStart)
            dismissDialog()
        }
    }

    /**
     * 设置EndText点击事件
     */
    fun setEndListener(onEndListener: ((TextView) -> Unit)?): BottomActionDialog {
        this.onEndListener = onEndListener
        setEndListener()
        return this
    }

    private fun setEndListener() {
        mContentBinding.tvEnd.setOnClickListener {
            this.onEndListener?.invoke(mContentBinding.tvEnd)
            dismissDialog()
        }
    }

    /**
     * 设置单按钮
     */
    fun setSingleButton(isSingleButton: Boolean = true): BottomActionDialog {
        this.isSingleButton = isSingleButton
        setButtonView()
        return this
    }

    /**
     * 设置单按钮点击事件
     * 如果调用了此方法，则展示单按钮
     */
    fun setSingleButtonListener(onSingleButtonListener: ((TextView) -> Unit)?): BottomActionDialog {
        this.onSingleButtonListener = onSingleButtonListener
        setButtonView()
        return this
    }

    private fun setButtonView() {
        val showSingleButton = isSingleButton || onSingleButtonListener != null
        setButtonVisible(showSingleButton)
        if (showSingleButton) {
            setEndButtonInterval(24)
            setButtonAction(mContentBinding.tvEnd, singleButtonText, onSingleButtonListener)
            return
        }
        setEndButtonInterval(16)
        setButtonAction(mContentBinding.tvEnd, endText, onEndListener)
        setStartText(startText)
        setStartListener()
    }

    /**
     * 设置链接文本
     */
    fun setLinkText(linkText: CharSequence?): BottomActionDialog {
        this.linkText = linkText
        setLinkText()
        return this
    }

    private fun setLinkText() {
        val isVisible = !linkText.isNullOrEmpty()
        mContentBinding.tvLink.isVisible = isVisible
        mContentBinding.tvLink.text = this.linkText
        if (isVisible) {
            mContentBinding.tvLink.paintFlags =
                mContentBinding.tvLink.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG
        }
    }

    /**
     * 设置链接点击事件
     */
    fun setLinkListener(onLinkListener: ((TextView) -> Unit)?): BottomActionDialog {
        this.onLinkListener = onLinkListener
        setLinkListener()
        return this
    }

    private fun setLinkListener() {
        mContentBinding.tvLink.setOnClickListener {
            onLinkListener?.invoke(mContentBinding.tvLink)
            dismissDialog()
        }
    }


    private fun setLinkView() {
        setLinkText()
        setLinkListener()
    }

    private fun setEndButtonInterval(interval: Int) {
        val params = mContentBinding.tvEnd.layoutParams
        if (params is ConstraintLayout.LayoutParams) {
            params.topMargin = interval.dp2px()
            mContentBinding.tvEnd.layoutParams = params
        }
    }


    private fun setButtonAction(
        tvButton: TextView,
        text: CharSequence?,
        listener: ((TextView) -> Unit)?
    ) {
        if (!text.isNullOrEmpty()) {
            tvButton.text = text
        }
        tvButton.setOnClickListener {
            listener?.invoke(tvButton)
            dismissDialog()
        }
    }

    private fun setButtonVisible(isSingleButton: Boolean) {
        mContentBinding.tvEnd.isVisible = true
        mContentBinding.tvStart.isVisible = !isSingleButton
    }

    @Suppress("unused")
    class Builder(activity: Activity) : ActionBuilder<DialogBottomActionBinding>(activity) {

        override fun build(): BottomActionDialog {
            return super.build() as BottomActionDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomActionBinding> {
            return BottomActionDialog(
                context,
                title,
                content,
                subTitle,
                subContent,
                onStartListener,
                onEndListener,
                isSingleButton,
                onSingleButtonListener,
                singleButtonText,
                startText,
                endText,
                linkText,
                onLinkListener,
                config.onCreateListener,
                config.onDismissListener,
            )
        }
    }
}
