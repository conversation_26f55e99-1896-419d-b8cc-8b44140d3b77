package cn.com.vau.util.span

import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.text.style.ClickableSpan
import android.text.style.ReplacementSpan
import android.view.View
import androidx.annotation.ColorInt

/**
 * 将dp转换为px
 */
private fun Float.dpToPx(): Float = this * Resources.getSystem().displayMetrics.density

/**
 * 虚线下划线Span，用于SpannableString中的局部文字设置虚线下划线
 *
 * @param dashColor 虚线颜色
 * @param dashStrokeWidth 虚线线条宽度
 * @param dashLength 虚线长度
 * @param dashGap 虚线间隔
 * @param offsetFromBaseline 虚线距离基线的偏移量
 * @param alignment 虚线对齐方式
 */
class DashedUnderlineSpan(
    @ColorInt private val dashColor: Int,
    private val dashStrokeWidth: Float = 1f.dpToPx(),
    private val dashLength: Float = 4f.dpToPx(),
    private val dashGap: Float = 4f.dpToPx(),
    private val offsetFromBaseline: Float = 2f.dpToPx(),
    private val alignment: DashAlignment = DashAlignment.DESCENT // 默认使用DESCENT对齐
) : ReplacementSpan() {

    enum class DashAlignment {
        BASELINE,   // 基于基线（推荐默认）
        DESCENT,    // 基于descent线（适用于包含下降字符的文本）
        BOTTOM      // 基于bottom线（适用于需要更大间距的场景）
    }

    private val dashPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = dashColor
        strokeWidth = dashStrokeWidth
        style = Paint.Style.STROKE
        strokeCap = Paint.Cap.ROUND
        pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
    }



    override fun getSize(
        paint: Paint,
        text: CharSequence?,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        // 返回原始文本的宽度
        return paint.measureText(text, start, end).toInt()
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        // 绘制原始文本
        canvas.drawText(text!!, start, end, x, y.toFloat(), paint)

        // 计算文本宽度
        val textWidth = paint.measureText(text, start, end)

        // 计算虚线的Y坐标，确保在文字下方
        val fontMetrics = paint.fontMetricsInt
        val dashY = when (alignment) {
            DashAlignment.BASELINE -> y.toFloat() + offsetFromBaseline
            DashAlignment.DESCENT -> y.toFloat() + fontMetrics.descent + offsetFromBaseline
            DashAlignment.BOTTOM -> y.toFloat() + fontMetrics.bottom + offsetFromBaseline
        }

        // 绘制虚线
        canvas.drawLine(x, dashY, x + textWidth, dashY, dashPaint)
    }
}
