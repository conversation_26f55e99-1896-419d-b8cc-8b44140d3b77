package cn.com.vau.util

import android.content.res.Resources
import androidx.annotation.StringRes
import java.util.*

/**
 * <pre>
 * author: Blankj
 * blog  : http://blankj.com
 * time  : 2016/08/16
 * desc  : utils about string
</pre> *
 */
object StringUtil {

    /**
     * Return whether the string is null or white space.
     *
     * @param s The string.
     * @return `true`: yes<br></br> `false`: no
     */
    fun isSpace(s: String?): <PERSON><PERSON>an {
        if (s == null) return true
        var i = 0
        val len = s.length
        while (i < len) {
            if (!Character.isWhitespace(s[i])) {
                return false
            }
            ++i
        }
        return true
    }

    /**
     * Return the string value associated with a particular resource ID.
     *
     * @param id The desired resource identifier.
     * @return the string value associated with a particular resource ID.
     */
    fun getString(@StringRes id: Int): String {
        return getString(id, null)
    }

    /**
     * Return the string value associated with a particular resource ID.
     *
     * @param id         The desired resource identifier.
     * @param formatArgs The format arguments that will be used for substitution.
     * @return the string value associated with a particular resource ID.
     */
    fun getString(@StringRes id: Int, vararg formatArgs: Any?): String {
        try {
            return format(UtilApp.getApp().getString(id), *formatArgs)
        } catch (e: Resources.NotFoundException) {
            e.printStackTrace()
            return id.toString()
        }
    }

    /**
     * Format the string.
     *
     * @param str  The string.
     * @param args The args.
     * @return a formatted string.
     */
    fun format(str: String?, vararg args: Any?): String {
        var text = str
        if (text != null) {
            if (args.isNotEmpty() && args.all { it != null }) {
                try {
                    text = String.format(str, *args)
                } catch (e: IllegalFormatException) {
                    e.printStackTrace()
                }
            }
        }
        return text.ifNull()
    }
}
