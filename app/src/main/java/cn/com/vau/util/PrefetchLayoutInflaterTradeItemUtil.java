package cn.com.vau.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.annotation.UiContext;

import java.lang.ref.WeakReference;
import java.util.Iterator;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 交易-（首页）列表item 预加载
 * 注意：目前该工具类只能给首页交易页面来预加载使用
 */
public class PrefetchLayoutInflaterTradeItemUtil {

    private String TAG = this.getClass().getSimpleName();
    //最大预加载数量
    private static final int MAX_PRE_INFLATER_COUNT = 7;
    private WeakReference<Context> wrContext;
    //预加载布局id
    private @LayoutRes int resid;
    //加载后的View保存集合
    private final ConcurrentLinkedQueue<View> mCacheViewQueue = new ConcurrentLinkedQueue<>();
    //异步加载的线程池
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    private PrefetchLayoutInflaterTradeItemUtil(){}

    public static PrefetchLayoutInflaterTradeItemUtil getInstance() {
        return Holder.instance;
    }

    //预加载时，调用初始化
    public void init(@UiContext Context context, @LayoutRes int resid){
        this.resid = resid;
        this.wrContext = new WeakReference<>(context);
        load();
    }

    private @Nullable Context getContext() {
        return wrContext.get();
    }

    //加载
    private void load() {
        if (getContext() == null) {
            return;
        }
        if (mCacheViewQueue.size() < MAX_PRE_INFLATER_COUNT) {
            executorService.submit(new AsyncInflaterRunnable());
        }
    }

    private class AsyncInflaterRunnable implements Runnable {

        @Override
        public void run() {
            if (mCacheViewQueue.size() >= MAX_PRE_INFLATER_COUNT) {
                return;
            }
            if (getContext() == null) {
                return;
            }
            View view = LayoutInflater.from(getContext()).inflate(resid, null);
            mCacheViewQueue.add(view);
            load();
        }
    }

    /**
     * 获取View，如果缓存中有则返回，如果没有则加载完成后返回
     * @return
     */
    public @Nullable View getView() {
        View view = null;
        if (mCacheViewQueue.isEmpty()) {
            if (getContext() == null) {
                return null;
            }
            view = LayoutInflater.from(getContext()).inflate(resid, null);
        } else {
            Iterator<View> iterator = mCacheViewQueue.iterator();
            view =  iterator.next();
            iterator.remove();
        }
        load();
        return view;
    }

    /**
     * 释放空间 也会避免内存泄漏
     */
    public void release() {
        mCacheViewQueue.clear();
    }

    private static class Holder{
        @SuppressLint("StaticFieldLeak")
        private static final PrefetchLayoutInflaterTradeItemUtil instance = new PrefetchLayoutInflaterTradeItemUtil();
    }
}
