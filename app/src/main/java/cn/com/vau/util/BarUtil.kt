package cn.com.vau.util

import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsControllerCompat
import cn.com.vau.R

/**
 * Filename: BarUtils.kt
 * Author: GG
 * Date: 2024/1/8
 * Description:
 * 工具类：用于处理状态栏和导航栏的样式设置。
 * 包括透明化、文字颜色调整、高度获取等功能。
 */
object BarUtil {

    /**
     * 设置状态栏为透明，并调整其显示样式。
     *
     * @param window 当前窗口对象
     */
    fun transparentStatusBar(window: Window) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // 禁用状态栏对比度强制设置（Android Q及以上版本支持）
            window.setStatusBarContrastEnforced(false)
        }
        // 清除半透明状态栏标志位
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        // 添加绘制系统栏背景的标志位
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        var systemUiVisibility = window.decorView.systemUiVisibility
        // 设置全屏布局并保持稳定布局
        systemUiVisibility = systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        window.decorView.systemUiVisibility = systemUiVisibility
        // 设置状态栏背景为透明
        window.statusBarColor = Color.TRANSPARENT

        // 设置状态栏文字颜色为浅色（白色）
        setStatusBarTextColor(window, true)
    }

    /**
     * 设置状态栏文字颜色。
     *
     * @param window 当前窗口对象
     * @param light 是否使用浅色文字（true：白色文字，false：黑色文字）
     */
    fun setStatusBarTextColor(window: Window, light: Boolean) {
        var systemUiVisibility = window.decorView.systemUiVisibility
        systemUiVisibility = if (light) { // 白色文字
            systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
        } else { // 黑色文字
            systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        window.decorView.systemUiVisibility = systemUiVisibility
    }

    /**
     * 设置状态栏和导航栏颜色，并调整图标颜色。
     *
     * @param window 当前窗口对象
     * @param isLight 是否使用浅色背景（true：浅色背景，false：深色背景）
     */
    fun setBarColorAndIconColor(window: Window, isLight: Boolean) {
        window.statusBarColor = ContextCompat.getColor(window.context, R.color.c1e1e1e)
        // 新增导航栏颜色设置
        window.navigationBarColor = ContextCompat.getColor(window.context, R.color.c1e1e1e)
        setBarIconColor(window, isLight)
    }

    /**
     * 设置状态栏和导航栏图标颜色。
     *
     * @param window 当前窗口对象
     * @param isLight 是否使用浅色图标（true：白色图标，false：黑色图标）
     */
    @JvmStatic
    fun setBarIconColor(window: Window, isLight: Boolean) {
        // 让状态栏和导航栏图标颜色适配背景色（浅色背景用黑色图标，深色背景用白色图标）
        WindowInsetsControllerCompat(window, window.decorView).isAppearanceLightNavigationBars = isLight
    }

    /**
     * 设置导航栏为透明，并调整其显示样式。
     *
     * @param window 当前窗口对象
     */
    fun transparentNavigationBar(window: Window) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // 禁用导航栏对比度强制设置（Android Q及以上版本支持）
            window.isNavigationBarContrastEnforced = false
        }
        // 清除半透明导航栏标志位
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
        // 添加绘制系统栏背景的标志位
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        var systemUiVisibility = window.decorView.systemUiVisibility
        // 隐藏导航栏并保持布局稳定
        systemUiVisibility = systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        window.decorView.systemUiVisibility = systemUiVisibility
        // 设置导航栏背景为透明
        window.navigationBarColor = Color.TRANSPARENT

        // 设置导航栏按钮颜色为浅色（白色）
        setNavigationBarBtnColor(window, true)
    }

    /**
     * 设置导航栏按钮颜色。
     *
     * @param window 当前窗口对象
     * @param light 是否使用浅色按钮（true：白色按钮，false：黑色按钮）
     */
    private fun setNavigationBarBtnColor(window: Window, light: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            var systemUiVisibility = window.decorView.systemUiVisibility
            systemUiVisibility = if (light) { // 白色按钮
                systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
            } else { // 黑色按钮
                systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
            }
            window.decorView.systemUiVisibility = systemUiVisibility
        }
    }

    /**
     * 获取状态栏的高度。
     *
     * @return 状态栏的高度（单位：像素）
     */
    @JvmStatic
    val statusBarHeight: Int
        get() {
            val resources: Resources = UtilApp.getApp().getResources()
            val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
            return resources.getDimensionPixelSize(resourceId)
        }

    /**
     * 获取导航栏的高度。
     *
     * @return 导航栏的高度（单位：像素），如果设备没有导航栏则返回0
     */
    val navBarHeight: Int
        get() {
            val res: Resources = UtilApp.getApp().getResources()
            val resourceId = res.getIdentifier("navigation_bar_height", "dimen", "android")
            return if (resourceId != 0) {
                res.getDimensionPixelSize(resourceId)
            } else {
                0
            }
        }
}
