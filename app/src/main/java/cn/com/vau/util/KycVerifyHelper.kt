package cn.com.vau.util

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.buildSpannedString
import androidx.core.text.underline
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.dialog.BottomVerifyDialog
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.data.account.KycGuidanceLevelObj
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.sumsub.SumsubPromptActivity
import cn.com.vau.profile.activity.kycAuth.KycAuthActivity
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_1
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_2
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_3
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.KycDialogContentView
import cn.com.vau.util.widget.dialog.BottomActionWithIconDialog
import kotlinx.coroutines.Job
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Filename: KycVerifyHelper
 * Author: GG
 * Date: 2025/3/21
 * Description:
 */
object KycVerifyHelper {

    private var loadingDialog: CommonProcessDialog? = null

    /**
     * 检查kyc验证状态
     * @param context 上下文
     * @param nextLevel 下一级别
     * @param title 弹窗标题
     * @param content 弹窗内容
     */
    fun checkKycVerify(context: Context, nextLevel: Int, title: String, content: String, source: String = "App", isJumpFace: Boolean = false) {
        noRepeat {
            val activity = context as? AppCompatActivity ?: ActivityManagerUtil.getInstance().activityStack.last() as AppCompatActivity
            val viewModel = ViewModelProvider(activity)[KycVerifyViewModel::class.java]
            showLoading(activity)
            setBackCancelRequest(activity, viewModel)
            viewModel.userGuidanceLevel(nextLevel) { kycGuidanceLevelObj ->
                if (kycGuidanceLevelObj == null) {
                    hideLoading()
                    loadingDialog = null
                    return@userGuidanceLevel
                }
                showDialog(activity, nextLevel, title, content, kycGuidanceLevelObj.dependOnLevelList?.map { Pair(it?.level.ifNull(), it?.showLevelStatus.ifNull(0)) }.orEmpty(), source = source, isJumpFace = isJumpFace)
                hideLoading()
                loadingDialog = null
            }
        }

    }

    /**
     * 展示带有验证权限的 kyc验证提示弹窗
     */
    fun showDialog(activity: Activity, nextLevel: Int, title: String, content: String, list: List<Pair<Int, Int>>, source: String = "App", isJumpFace: Boolean = false) {
        BottomVerifyDialog.Builder(activity)
            .setIcon(R.raw.lottie_dialog_warning)
            .setTitle(title)
            .setContent(content)
            .setCustomView(KycDialogContentView(activity).apply {
                setData(list.toMutableList())
            })
            .setOnDismissListener {
                EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_KYC_CLOSE, true))
            }
            .apply {
                // 全都处于 {Under Review} 状态，则显示“Check Status”按钮，点击跳转【验证中心】
                // 全都处于 {Rejected} 状态，则显示“Resubmit”按钮，引导用户先重新提交最低等级认证
                // 有部分认证处于 {Not Submitted} 状态，显示未提交的认证中最低等级的认证按钮，引导用户先提交最低等级认证
                if (list.all { it.second == KycStandardAdapter.STATUS_AUDITING || it.second == KycStandardAdapter.STATUS_AUTHENTICATED }) {
                    setButtonStr(activity.getString(R.string.check_status))
                    setButtonClick {
                        KycAuthActivity.openActivity(activity)
                        sensorsTrack("Check Status", title, SensorsConstant.V3700.KYCVERIFYPOPUP_CLICK)
                    }
                } else {
                    setButtonStr(activity.getString(R.string.verify_now))
                    setButtonClick {
                        // h5 传 跳转sumsub 页面的话 就直接跳转sumsub页面
                        if (isJumpFace) {
                            SumsubPromptActivity.openActivity(activity, Constants.SUMSUB_TYPE_FACE)
                        } else {
                            showKycDialog(activity, mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC, Constants.GoldParam.NEXT_LEVEL to nextLevel, "source" to source))
                        }
                        sensorsTrack("Verify Now", title, SensorsConstant.V3700.KYCVERIFYPOPUP_CLICK)
                    }
                }
            }
            .setLinkStr(buildSpannedString { underline { append(activity.getString(R.string.maybe_later)) } })
            .setLinkClick {
                sensorsTrack("Maybe later", title, SensorsConstant.V3700.KYCVERIFYPOPUP_CLICK)
            }
            .build()
            .showDialog()
        sensorsTrack(popupTitle = title, point = SensorsConstant.V3700.KYCVERIFYPOPUP_VIEW)
    }

    /**
     * 神策自定义埋点(v3700)
     */
    private fun sensorsTrack(buttonStr: String? = null, popupTitle: String, point: String) {
        val properties = JSONObject()
        if (!buttonStr.isNullOrBlank()) {
            properties.put(SensorsConstant.Key.BUTTON_NAME, buttonStr)
        }
        properties.put("popup_title", popupTitle)
        properties.put(
            SensorsConstant.Key.ACCOUNT_LEVEL, when (UserDataUtil.kycLevel().toIntCatching(-1)) {
                LEVEL_1 -> "LV1"
                LEVEL_2 -> "LV2"
                LEVEL_3 -> "LV3"
                0, -1 -> "LV0"
                else -> "Advanced"
            }
        )
        SensorsDataUtil.track(point, properties)
    }

    /**
     * 外部调用 补全 用户信息半模态弹窗
     * 需校验是否有virtualId ，如果没有的话，需要调用接口请求 ，再展示
     */
    fun showSetupTradingAccountDialog(activity: Activity, content: String?) {
        noRepeat {
            showTradingAccountDialog(activity, content)
        }
    }

    /**
     * 展示 补全用户信息弹窗的具体实现
     */
    private fun showTradingAccountDialog(activity: Activity, content: String?) {
        BottomActionWithIconDialog.Builder(activity)
            .setLottieIcon(R.raw.lottie_dialog_warning)
            .setTitle(activity.getString(R.string.setup_your_trading_account))
            .setContent(content)
            .setSingleButton(true)
            .setSingleButtonText(activity.getString(R.string.trading_account_setup))
            .setOnDismissListener {
                EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_KYC_CLOSE, true))
            }
            .setOnSingleButtonListener {
                showKycDialog(activity, mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_COMPLETE_INFO))
            }
            .setLinkText(buildSpannedString { underline { append(activity.getString(R.string.maybe_later)) } })
            .build()
            .showDialog()
    }

    /**
     * 展示kyc验证url弹窗
     * @param context 上下文
     * @param dataMap 数据
     */
    fun showKycDialog(context: Context?, dataMap: Map<String, Any>, endFlow: (() -> Unit)? = null) {
        val map = dataMap.toMutableMap().apply {
            put("threat_session_id", SpManager.getTmxSessionId(""))
            put("isIB", UserDataUtil.isIB().toString())
        }
        LogUtil.w("showKycDialog : $map")

        NewHtmlActivity.openActivity(context, UrlConstants.URL_GOLD_OPEN_ACCOUNT, dataMap = map, endFlow = endFlow)
    }

    /**
     * 设置返回键监听，点击返回键时，取消请求，隐藏加载框
     */
    private fun setBackCancelRequest(activity: AppCompatActivity, viewModel: KycVerifyViewModel) {
        createLoadingDialog(activity)
        loadingDialog?.setOnBackPressedListener {
            viewModel.cancelRequest()
        }
    }

    private fun showLoading(activity: AppCompatActivity) {
        createLoadingDialog(activity)
        if (loadingDialog?.isShowing == true || activity.isFinishing || activity.isDestroyed) {
            return
        }
        if (loadingDialog?.isShowing == false) {
            loadingDialog?.show()
        }
    }

    private fun createLoadingDialog(activity: AppCompatActivity) {
        if (loadingDialog == null) {
            loadingDialog = CommonProcessDialog(activity).apply {
                setBackPropagation(true)
            }
        }
    }

    private fun hideLoading() {
        if (loadingDialog != null && loadingDialog?.isShowing == true)
            loadingDialog?.dismiss()
    }

    class KycVerifyViewModel : BaseViewModel() {

        private var userGuidanceLevelJob: Job? = null

        /**
         * 用户查询用户等级
         */
        fun userGuidanceLevel(nextLevel: Int, callback: (KycGuidanceLevelObj?) -> Unit) {
            userGuidanceLevelJob = requestNet({ baseService.userGuidanceLevel(nextLevel) }, {
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                }
                callback.invoke(it.data?.obj)
            }, onError = {
                callback.invoke(null)
            })
        }

        fun cancelRequest() {
            userGuidanceLevelJob?.cancel()
            userGuidanceLevelJob = null
        }
    }
}