package cn.com.vau.util.tracking;

/**
 * 埋点表
 * Cmd + Shift + U
 * https://www.notion.so/b9af5e0735ba4e0d979c4e8f07dba6b1?v=25fc2a5b8e7c44b49d7f9865e6511ca4
 */
public class BuryPointConstant {

    /**
     * 分享埋点来源，字段
     */
    public static class PositionType {
        
        public static final String KEY_POSITION = "Position";
        
        public static final String KEY_ELIGIBILITY = "Eligibility";

        //(展示图1) View信号源profile页
        public static final String SP_DISCOVER = "SP_discover";

        //(展示图2) 信号源本身的个人profile页
        public static final String SP_PROFILE = "SP_profile";

        //(展示图3) 自主交易的持仓订单弹窗 (跟单的自主交易+非跟单的自主交易)(position,history)
        public static final String MANUAL_ORDERS = "Manual_orders";

        //(展示图4) 跟单交易的持仓页(position,history)
        public static final String CT_ORDERS = "CT_orders";

        //(展示图5) k线页
        public static final String K_LINE = "K-line";

        //个人页面， 邀请有礼
        public static final String PROFILE = "Profile";

        //邀请页面 添加领取
        public static final String WELCOME_BUNDLE = "Welcome Bundle";
    }

    /**
     * 分享埋点，分享类型字段
     */
    public static class ShareType {
        public static final String KEY_MEDIA_SOURCE = "Media_source";
        public static final String REFERRAL_CODE = "Referral_code";
        public static final String SAVE = "Save";
        public static final String FB = "FB";
        public static final String TWITTER = "Twitter";
        public static final String INSTAGRAM = "Instagram";
        public static final String MESSENGER = "Messenger";
        public static final String WHATSAPP = "WhatsApp";
        public static final String TELEGRAM = "Telegram";
        public static final String MORE = "More";
        public static final String SHARE_ACCOUNT = "Share_account";
        public static final String COPY_LINK = "Copy_link";
        public static final String SAVE_IMAGE = "Save_image";
    }

    /**
     * InApp 查看更多类型字段
     */
    public static class ViewMoreType {
        public static final String TRADES = "Trades";
        public static final String ORDERS = "Orders";
        public static final String MSG_ANNOUNCEMENT = "Msg_announcement";
    }

    /**
     * 消息类型字段
     */
    public static class MessageType {
        public static final String ACCOUNT = "Account";
        public static final String ANNOUCEMENT = "Annoucement";
        public static final String OTHER = "Other";
    }

    /**
     * Discover页标题字段
     */
    public static class DiscoverPageType {
        public static final String COPY_TRADING = "Copy_trading";
        public static final String COMMUNITY = "Community";
        public static final String ACADEMY = "Academy";
        public static final String TWENTY_FOUR_AND_SEVEN = "24/7";
        public static final String ECONOMIC_CALENDAR = "Economic_calendar";
        public static final String LIVE = "Live";
        public static final String ANALYSIS = "Analysis";
        public static final String NEWSLETTER = "Newsletter";
        public static final String FX_TV = "FX_TV";
    }

    /**
     * 登录类型字段
     */
    public static class AccountType {
        public static final String DEMO = "Demo";
        public static final String LIVE = "Live";
        public static final String COPY_TRADING = "Copy_trading";
        public static final String NOLOGIN = "-";
    }

//------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    public static class V330 {
        /**
         * 跟单者点击小感叹号的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_COPIERS_MORE_INFO = "ct_profit_sharing_copiers_more_info";
        /**
         * 点击修改分润比例的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_RATIO_EDIT_1 = "ct_profit_sharing_ratio_edit_1";

        /**
         * 点击修改分润比例的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_RATIO_EDIT_2 = "ct_profit_sharing_ratio_edit_2";

        /**
         * 点击不同分润比例的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_RATIO_UPDATE = "ct_profit_sharing_ratio_update";

        /**
         * 信号源点击小感叹号的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_SIGNAL_PROVIDER_MORE_INFO = "ct_profit_sharing_signal_provider_more_info";

        /**
         * 信号源/跟单者卡片曝光次数
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_CARD_VIEW = "ct_profit_sharing_summary_card_view";

        /**
         * 跟单者的结算页面 Shared Profits Statement 曝光次数
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_COPIER_STATEMENT_VIEW = "ct_profit_sharing_summary_copier_statement_view";

        /**
         * 用户点击button的次数(点击数)
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_SET_AS_PUBLIC_BUTTON_CLICK = "ct_profit_sharing_summary_set_as_public_button_click";

        /**
         * 如果用户的public trade没有打开且没有跟单者，则设计效果如右图，看曝光次数（包括自然滑动）
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_SET_AS_PUBLIC_BUTTON_VIEW = "ct_profit_sharing_summary_set_as_public_button_view";

        /**
         * 信号源的结算页面 Shared profits statement 曝光次数
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_SIGNAL_PROVIDER_STATEMENT_VIEW = "ct_profit_sharing_summary_signal_provider_statement_view";

        /**
         * 信号源激励模式设置页 Profit Sharing Summary 曝光次数
         */
        public static final String CT_PROFIT_SHARING_SUMMARY_SIGNAL_PROVIDER_VIEW_MORE = "ct_profit_sharing_summary_signal_provider_view_more";

        /**
         * 个人中心页面的曝光数
         */
        public static final String PROFILE_PAGE_VIEW = "profile_page_view";

        /**
         * 活动h5页面的曝光数
         */
        public static final String PROMO_TRAFFIC_BUTTON_CLICK = "promo_traffic_button_click";

        /**
         * * (1) “我的”页面Demo账号引导卡片(点击数)
         * * (2) 评估从“我的”页面开设新demo的人数与频率，作为后期按钮是否放在二级菜单； 对比 New Live Account的频率(点击数)
         */
        public static final String REGISTER_TRAFFIC_DEMO_BUTTON_CLICK = "register_traffic_demo_button_click";

        /**
         * “我的”页面Live账号引导卡片（Start Copy），在没有开live情况下(点击数)
         */
        public static final String CT_REGISTER_LIVE_BUTTON_CLICK = "ct_register_live_button_click";

        /**
         * * (1) “我的”页面Live账号引导卡片（Open Live Account）
         * * (2) 评估从“我的”页面开设同名账户的人数与频率； 对比 New Demo Account的频率
         */
        public static final String REGISTER_TRAFFIC_LIVE_BUTTON_CLICK = "register_traffic_live_button_click";

        /**
         * 重置demo账户的人数：查看重置demo账户的频率与用户体量(点击数)
         */
        public static final String PROFILE_ACC_MGMT_RESET_DEMO_BUTTON_CLICK = "profile_acc_mgmt_reset_demo_button_click";

        /**
         * 评估新功能demo切换的使用率，只取成功切换demo account的点击数（点击切换+选择了demo account）(点击数)
         */
        public static final String PROFILE_ACC_MGMT_DEMO_SWITCH_BUTTON_CLICK = "profile_acc_mgmt_demo_switch_button_click";

        /**
         * 账户管理页面的曝光数
         */
        public static final String PROFILE_ACC_MGMT_PAGE_VIEW = "profile_acc_mgmt_page_view";

        /**
         * Account Management Try Again 未加载的点击数
         */
        public static final String PROFILE_ACC_MGMT_TRY_AGAIN_BUTTON_CLICK = "profile_acc_mgmt_try_again_button_click";

        /**
         * Account Management ID Verification Unsuccessful - Upload (点击数)
         */
        public static final String PROFILE_ACC_MGMT_REUPLOAD_BUTTON_CLICK = "profile_acc_mgmt_reupload_button_click";

        /**
         * Account Management ID Verification Required - Upload (点击数)
         */
        public static final String PROFILE_ACC_MGMT_UPLOAD_BUTTON_CLICK = "profile_acc_mgmt_upload_button_click";

        /**
         * Account Management Verification Required - Modify (点击数)
         */
        public static final String PROFILE_ACC_MGMT_MODIFY_BUTTON_CLICK = "profile_acc_mgmt_modify_button_click";
    }

    public static class V331 {
        /**
         * Vantage Rewards h5 Overview页面的浏览量，不同来源+对应的展示图
         */
        public static final String PROMO_VANTAGE_REWARDS_PAGE_VIEW = "promo_vantage_rewards_page_view";

        /**
         * Vantage Rewards Mission Center页面的浏览量，不同来源+对应的展示图
         */
        public static final String PROMO_VANTAGE_REWARDS_MISSION_CENTER_PAGE_VIEW = "promo_vantage_rewards_mission_center_page_view";

        /**
         * 进入live 页面的button click入口(点击数)
         */
        public static final String REGISTER_TRAFFIC_LIVE_BUTTON_CLICK = "register_traffic_live_button_click";

        /**
         * 不同入口的入金(点击数)
         */
        public static final String DEPOSIT_TRAFFIC_BUTTON_CLICK = "deposit_traffic_button_click";
    }

    public static class V334 {
        
        // 进入开户Lv1-account opening页面的入口 各种乱埋 ( 点击数 )
        public static final String REGISTER_LIVE_LVL1_BUTTON_CLICK = "register_live_lvl1_button_click";

        // 进入开户Lv2-account opening页面的入口 ( 点击数 )
        public static final String REGISTER_LIVE_LVL2_BUTTON_CLICK = "register_live_lvl2_button_click";

        // 进入开户Lv3-account opening页面的入口 ( 点击数 )
        public static final String REGISTER_LIVE_LVL3_BUTTON_CLICK = "register_live_lvl3_button_click";

        // 开户各页面埋点 ( 开户流程的页面曝光数 )
        public static final String REGISTER_LIVE_PAGE_VIEW = "register_live_page_view";

        // 入金/出金 银行严重，弹窗 next ( 点击数 )
        public static final String DEPOSIT_BANK_WIRE_TRANSFER_VERIFY_BUTTON_CLICK = "deposit_bank_wire_transfer_verify_button_click";
    }

    public static class V341 {
        /**
         * 点击Settings页下的Login Details ( 点击数 )
         */
        public static final String PROFILE_SETTINGS_LOGIN_DETAILS_BUTTON_CLICK = "profile_settings_login_details_button_click";

        /**
         * 点击Login Details后出现的弹窗，点击Send Now按钮 ( 点击数 )
         */
        public static final String PROFILE_SETTINGS_LOGIN_DETAILS_SEND_NOW_BUTTON_CLICK = "profile_settings_login_details_send_now_button_click";

        /**
         * New Order 进入下单页的曝光次数（区分buy/sell，挂单/市场价）
         */
        public static final String TRADES_PAGE_VIEW = "trades_page_view";

        /**
         * 点击Reset按钮的次数
         */
        public static final String ORDERS_RESET_BUTTON_CLICK = "orders_reset_button_click";

        /**
         * 点击Reset出现弹窗后点击confirm
         */
        public static final String ORDERS_RESET_CONFIRM_BUTTON_CLICK = "orders_reset_confirm_button_click";

        /**
         * 点击Reset出现弹窗后点击cancel
         */
        public static final String ORDERS_RESET_CANCEL_BUTTON_CLICK = "orders_reset_cancel_button_click";

        /**
         * Reset按钮出现的次数
         */
        public static final String ORDERS_RESET_BUTTON_PAGE_VIEW = "orders_reset_button_page_view";

        /**
         * 用户提交完POA后的弹窗信息
         */
        public static final String LIVE_REGISTER_SUCCESS = "live_register_success";
    }

    public static class V342 {
        /**
         * 邀请有礼
         */
        public static final String PROMO_REFERRAL_BONUS_PAGE_VIEW = "promo_referral_bonus_page_view";

        /**
         * 从邀请有礼h5页面入口（Invite Now），点击不同分享渠道的次数
         */
        public static final String PROMO_REFERRAL_BONUS_SHARE_MEDIA_SOURCE_BUTTON_CLICK = "promo_referral_bonus_share_media_source_button_click";

        /**
         * 不同分享入口
         */
        public static final String GENERAL_SHARE_BUTTON_CLICK = "general_share_button_click";

        /**
         * 从不同分享入口，点击不同分享渠道的次数
         */
        public static final String GENERAL_SHARE_MEDIA_SOURCE_BUTTON_CLICK = "general_share_media_source_button_click";

        /**
         * IB分享，点击不同分享渠道的次数：
         */
        public static final String GENERAL_IB_SHARE_MEDIA_SOURCE_BUTTON_CLICK = "general_ib_share_media_source_button_click";

        /**
         * 搜索入口：(点击数)
         */
        public static final String GENERAL_SEARCH_BUTTON_CLICK = "general_search_button_click";

        /**
         * 用户点击搜索结果 (点击数)
         */
        public static final String GENERAL_SEARCH_ACTION_BUTTON_CLICK = "general_search_action_button_click";

        /**
         * 用户点击加入watchlist (点击数)
         */
        public static final String GENERAL_SEARCH_WATCHLIST_BUTTON_CLICK = "general_search_watchlist_button_click";
    }

    public static class V343 {
        /**
         * 点击不同入口的置顶消息/公告栏view more进行跳转
         */
        public static final String GENERAL_PINNED_MESSAGES_VIEW_MORE_BUTTON_CLICK = "general_pinned_messages_view_more_button_click";

        /**
         * 点击不同入口的置顶消息关闭按钮
         */
        public static final String GENERAL_PINNED_MESSAGES_CLOSE_BUTTON_CLICK = "general_pinned_messages_close_button_click";

        /**
         * 从首页点击信息栏查看账号相关/公告消息/其它（展示图1）+ 点击页面数量（展示图2）+从置顶消息跳转进入公告消息或其它
         */
        public static final String GENERAL_MESSAGES_PAGE_VIEW = "general_messages_page_view";
        /**
         * 一级入金页所有曝光数，包括点击deposit按钮后，推送/跳转带来，二级页面返回etc
         */
        public static final String DEPOSIT_LVL1_PAGE_VIEW = "deposit_lvl1_page_view";

        /**
         * 用户在 "Frequently Used "和 "More Options"选项卡之间的切换频率，只取点击数（包括自然曝光，一进入一级入金页面的数据）
         */
        public static final String DEPOSIT_LVL1_PAYMENT_METHOD_PAGE_VIEW = "deposit_lvl1_payment_method_page_view";

        /**
         * 一级入金页切换账户的使用率。(lvl1一级；lvl2二级；lvl3三方支付）
         */
        public static final String DEPOSIT_LVL1_ACCOUNT_SWITCH_BUTTON_CLICK = "deposit_lvl1_account_switch_button_click";

        /**
         * 二级入金页所有曝光数，包括从一级页面带进来，或者三方支付页面返回
         */
        public static final String DEPOSIT_LVL2_PAGE_VIEW = "deposit_lvl2_page_view";

        /**
         * 用户点击二级页面的已选择入金渠道，回到一级页面重新进行选择
         */
        public static final String DEPOSIT_LVL2_PAYMENT_METHOD_BUTTON_CLICK = "deposit_lvl2_payment_method_button_click";

        /**
         * 二级页面输入信息后点击Pay Now进行入金+报错信息（如果有）
         */
        public static final String DEPOSIT_LVL2_PAY_NOW_BUTTON_CLICK = "deposit_lvl2_pay_now_button_click";

        /**
         * 用户点击返回出现弹窗后，点击确认返回/保留在当前页面的按钮
         */
        public static final String DEPOSIT_LVL2_RETURN_POP_UP_BUTTON_CLICK = "deposit_lvl2_return_pop_up_button_click";

        /**
         * 用户进入三方支付页后点击返回键
         */
        public static final String DEPOSIT_LVL3_RETURN_BUTTON_CLICK = "deposit_lvl3_return_button_click";

        /**
         * 不同的支付情况/使用不同支付渠道下，用户点击重新尝试的次数
         */
        public static final String DEPOSIT_LVL3_RETRY_BUTTON_CLICK = "deposit_lvl3_retry_button_click";

        /**
         * 用户浏览不同的Discover页面（包括自然曝光数）
         */
        public static final String GENERAL_DISCOVER_PAGE_VIEW = "general_discover_page_view";
    }

    public static class V344 {
        /**
         * 点击首页广告位/banner的关闭按钮
         */
        public static final String GENERAL_TRADES_BANNER_CLOSE_BUTTON_CLICK = "general_trades_banner_close_button_click";

        /**
         * 未登录用户点击首页卡片的Sign Up / Log In按钮
         */
        public static final String GENERAL_TRADES_TOP_SIGNUP_LOGIN_BUTTON_CLICK = "general_trades_top_signup_login_button_click";

        /**
         * 未登录用户点击底部的Demo悬浮图标
         */
        public static final String GENERAL_TRADES_BOTTOM_DEMO_BUTTON_CLICK = "general_trades_bottom_demo_button_click";

        /**
         * 未登录用户点击首页卡片的Log In按钮
         */
        public static final String GENERAL_TRADES_TOP_LOGIN_BUTTON_CLICK = "general_trades_top_login_button_click";

        /**
         * 未登录用户点击底部的Log In悬浮图标
         */
        public static final String GENERAL_TRADES_BOTTOM_LOGIN_BUTTON_CLICK = "general_trades_bottom_login_button_click";

        /**
         * 未开户用户点击首页卡片的Live按钮
         */
        public static final String GENERAL_TRADES_TOP_LIVE_BUTTON_CLICK = "general_trades_top_live_button_click";

        /**
         * 未开户用户点击底部的Live悬浮图标
         */
        public static final String GENERAL_TRADES_BOTTOM_LIVE_BUTTON_CLICK = "general_trades_bottom_live_button_click";

        /**
         * 未入金用户点击首页卡片的Deposit按钮
         */
        public static final String GENERAL_TRADES_TOP_DEPOSIT_BUTTON_CLICK = "general_trades_top_deposit_button_click";

        /**
         * 点击底部的入金&钱包悬浮图标
         */
        public static final String GENERAL_TRADES_BOTTOM_DEPOSIT_BUTTON_CLICK = "general_trades_bottom_deposit_button_click";

        /**
         * 已入金用户点击钱包悬浮图标弹窗内的按钮
         */
        public static final String GENERAL_TRADES_BOTTOM_DEPOSIT_POPUP_BUTTON_CLICK = "general_trades_bottom_deposit_popup_button_click";

        /**
         * 从profile页面点击Security后，点击Device History的页面曝光数
         */
        public static final String GENERAL_DEVICE_HISTORY_PAGE_VIEW = "general_device_history_page_view";

        /**
         * 在Device History点击change your account password
         */
        public static final String GENERAL_DEVICE_HISTORY_CHANGE_PASSWORD_BUTTON_CLICK = "general_device_history_change_password_button_click";
    }

    public static class V345 {
        /**
         * 新功能引导弹窗页面的曝光次数
         */
        public static final String GENERAL_USER_GUIDE_PAGE_VIEW = "general_user_guide_page_view";

        /**
         * k线页引导弹窗页面的曝光次数
         */
        public static final String TRADE_KLINE_GUIDE_PAGE_VIEW = "trade_kline_guide_page_view";

        /**
         * 点击产品k-line图进入横屏
         */
        public static final String TRADE_KLINE_HORIZONTAL_BUTTON_CLICK = "trade_kline_horizontal_button_click";

        /**
         * 定焦触发k-line图
         */
        public static final String TRADE_KLINE_FOCUS_BUTTON_CLICK = "trade_kline_focus_button_click";

        /**
         * 选择Pro Mode/ Lite Mode
         */
        public static final String TRADE_KLINE_DISPLAY_MODE_BUTTON_CLICK = "trade_kline_display_mode_button_click";

        /**
         * 点击k-line的设置按钮
         */
        public static final String TRADE_KLINE_SETTINGS_BUTTON_CLICK = "trade_kline_settings_button_click";

        /**
         * 点击选择k-line上的主线展示
         */
        public static final String TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK = "trade_kline_settings_line_button_click";

        /**
         * 点击选择k-line上不同的主指标/次指标展示
         */
        public static final String TRADE_KLINE_INDICATORS_BUTTON_CLICK = "trade_kline_indicators_button_click";

        /**
         * 不同模式下，点击不同k-line的时间
         */
        public static final String TRADE_KLINE_TIMELINE_BUTTON_CLICK = "trade_kline_timeline_button_click";

        /**
         * 点击各种绘图工具
         */
        public static final String TRADE_KLINE_PRO_HORIZONTAL_TOOLS_BUTTON_CLICK = "trade_kline_pro_horizontal_tools_button_click";

        /**
         * 用户打开这个窗户，不管有没有改值，在关闭后都会回传给trading view进行刷新调整
         */
        public static final String TRADE_KLINE_PRO_HORIZONTAL_TOOLS_EDIT_BUTTON_CLICK = "trade_kline_pro_horizontal_tools_edit_button_click";
    }

    public static class V346 {
        /**
         * 用户点击首页顶部栏按钮进入账户列表
         */
        public static final String GENERAL_TRADES_ACC_MGMT_BUTTON_CLICK = "general_trades_acc_mgmt_button_click";
    }

    public static class V347 {
        /**
         * 在Profile > Account & Security > 2-Factor Authentication 点击Enable按钮
         *
         * 区分场景:
         * 1. 用户自动点击acc security进入这个页面
         * 2. 出金页面跳转这个页面
         * 3. 登录时候强制带入这个页面
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_PAGE_VIEW = "profile_account_security_2FA_enable_page_view";

        /**
         * 在2-Factor Authentication页面点击Enable按钮
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_BUTTON_CLICK = "profile_account_security_2FA_enable_button_click";

        /**
         * Enable状态结果页面曝光数
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_ENABLE_STATUS_PAGE_VIEW = "profile_account_security_2FA_enable_status_page_view";

        /**
         * 在Profile > Account & Security > 2-Factor Authentication 点击disable按钮
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_DISABLE_BUTTON_CLICK = "profile_account_security_2FA_disable_button_click";

        /**
         * Disable状态结果页面曝光数
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_DISABLE_STATUS_PAGE_VIEW = "profile_account_security_2FA_disable_status_page_view";

        /**
         * 在Profile > Account & Security > 2-Factor Authentication 点击reset按钮
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_RESET_BUTTON_CLICK = "profile_account_security_2FA_reset_button_click";

        /**
         * Reset状态结果页面曝光数
         */
        public static final String PROFILE_ACCOUNT_SECURITY_2FA_RESET_STATUS_PAGE_VIEW = "profile_account_security_2FA_reset_status_page_view";

        /**
         * 用户已经有live账户，在开通同名账户的情况下选择跟单账号并且点击submit button
         */
        public static final String ST_REGISTER_3 = "st_register_3";
    }

    public static class V3474 {
        /**
         * 点击功能引导弹窗的Explore Now按钮
         */
        public static final String GENERAL_USER_GUIDE_EXPLORE_NOW_BUTTON_CLICK = "general_user_guide_explore_now_button_click";

        /**
         * k线页面的曝光数（区分交易产品名称）
         */
        public static final String TRADE_KLINE_PAGE_VIEW = "trade_kline_page_view";

        /**
         * 点击k线页的教程按钮，不同入口：k线图的设置弹窗，pro横屏模式，lite横屏模式
         */
        public static final String TRADE_KLINE_USER_GUIDE_BUTTON_CLICK = "trade_kline_user_guide_button_click";

        /**
         * k线页的教程页面曝光次数 * 只取每一个页面的首次曝光（无论是滑动或者点击next去到下个页面都算；但重复的不算）
         */
        public static final String TRADE_KLINE_USER_GUIDE_PAGE_VIEW = "trade_kline_user_guide_page_view";
    }

    public static class V348 {
        /**
         * 所有策略页面的浏览数量，区分不同入口：（1）trades首页（2）discover页面（3）community页面（4）k线页面（5）信号源详情页（6）热门搜索页面
         * Category只是（1）（2）有；其它的不需要“-” ** 抓不到的Position可以放“Others”包括推送/弹窗等其它入口
         */
        public static final String CT_STRATEGY_PAGE_VIEW = "ct_strategy_page_view";

        /**
         * 所有信号源页面的浏览数量，区分不同入口：（1）trades首页的signal_provider组合（2）discover页面的signal_provider组合（3)热门搜索页面（4）每个策略卡片策略名字下的信号源名字
         * 其它的“-” ** 抓不到的Position可以放“Others”包括推送/弹窗等其它入口
         */
        public static final String CT_SP_PAGE_VIEW = "ct_sp_page_view";

        /**
         * Trades页面的跟单首页，点击任意一个组合的view more按钮  *组合的标题、箭头、View More按钮都算点击view more
         */
        public static final String CT_TRADES_VIEW_MORE_BTN_CLICK = "ct_trades_view_more_btn_click";

        /**
         * Discover下的跟单页面，点击任意一个组合的view more按钮  *组合的标题、箭头、View More按钮都算点击view more
         */
        public static final String CT_DISCOVER_VIEW_MORE_BTN_CLICK = "ct_discover_view_more_btn_click";

        /**
         * 进入任何产品的k线页面，点击策略卡片下的view more按钮
         */
        public static final String CT_KLINE_VIEW_MORE_BTN_CLICK = "ct_kline_view_more_btn_click";

        /**
         * Discover下的Community页面，点击排序按钮
         */
        public static final String CT_COMMUNITY_SORT_BTN_CLICK = "ct_community_sort_btn_click";

        /**
         * Discover下的Community页面，点击筛选按钮
         */
        public static final String CT_COMMUNITY_FILTER_BTN_CLICK = "ct_community_filter_btn_click";

        /**
         * Discover下的Community页面，搜索页面的浏览数量
         */
        public static final String CT_COMMUNITY_SEARCH_PAGE_VIEW = "ct_community_search_page_view";

        /**
         * Discover下的Community页面搜索功能，点击热门搜索任何一个信号源 * 如果没有信号源ID则抓取信号源名称
         */
        public static final String CT_COMMUNITY_SEARCH_SP_BTN_CLICK = "ct_community_search_sp_btn_click";

        /**
         * Discover下的Community页面搜索功能，搜索请求次数  *因为自动匹配，所以有输入就算一个请求，例子：输入“USD“则参数回传”USD“，无需个别字母都回传
         */
        public static final String CT_COMMUNITY_SEARCH_BTN_CLICK = "ct_community_search_btn_click";

        /**
         * Discover下的Community页面搜索功能，点击的分类+搜索结果 *如果是搜产品名称，搜索结果点击要算点击策略的
         */
        public static final String CT_COMMUNITY_SEARCH_RESULT_BTN_CLICK = "ct_community_search_result_btn_click";

        /**
         * 点击策略详情页的分享按钮
         */
        public static final String CT_STRATEGY_DETAILS_SHARE_BTN_CLICK = "ct_strategy_details_share_btn_click";

        /**
         * 点击订单详情页的分享按钮
         */
        public static final String CT_ORDER_SHARE_BTN_CLICK = "ct_order_share_btn_click";

        /**
         * 点击信号源中心页的分享按钮
         */
        public static final String CT_PROFILE_SP_CENTER_SHARE_BTN_CLICK = "ct_profile_sp_center_share_btn_click";

        /**
         * 点击策略详情页的收藏按钮 * 如果没有策略ID则抓取策略名称
         */
        public static final String CT_STRATEGY_FAVOURITE_BTN_CLICK = "ct_strategy_favourite_btn_click";

        /**
         * 点击订单详情页的收藏按钮 * 如果没有策略ID则抓取策略名称
         */
        public static final String CT_ORDER_FAVOURITE_BTN_CLICK = "ct_order_favourite_btn_click";

        /**
         * 点击策略详情页的Copy按钮 * 如果没有策略ID则抓取策略名称；区分用户account（在没有跟单账户的情况下，点击copy会引导开户）
         */
        public static final String CT_STRATEGY_COPY_BTN_CLICK = "ct_strategy_copy_btn_click";

        /**
         * Orders页面没有持仓的情况下，点击Discover Strategies按钮
         */
        public static final String CT_ORDER_DISCOVER_STRATEGY_BTN_CLICK = "ct_order_discover_strategy_btn_click";

        /**
         * Orders页面Profit Sharing Tab，点击View Statement按钮
         */
        public static final String CT_ORDER_VIEW_STATEMENT_BTN_CLICK = "ct_order_view_statement_btn_click";

        /**
         * Orders页面Manual Trading，点击Manage按钮
         */
        public static final String CT_ORDER_MANUAL_TRADE_MANAGE_BTN_CLICK = "ct_order_manual_trade_manage_btn_click";

        /**
         * （不是信号源）点击信号源中心的Become a Signal Provider按钮
         */
        public static final String CT_PROFILE_BECOME_SP_BTN_CLICK = "ct_profile_become_sp_btn_click";

        /**
         * （不是信号源）点击submit按钮成为信号源
         */
        public static final String CT_PROFILE_BECOME_SP_SUBMIT_BTN_CLICK = "ct_profile_become_sp_submit_btn_click";

        /**
         * （已经是信号源）点击信号源中心的卡片or箭头
         */
        public static final String CT_PROFILE_SP_CENTER_BTN_CLICK = "ct_profile_sp_center_btn_click";

        /**
         * 点击创建策略按钮
         */
        public static final String CT_SP_CENTER_CREATE_STRATEGY_BTN_CLICK = "ct_sp_center_create_strategy_btn_click";

        /**
         * 点击创建策略publish按钮
         */
        public static final String CT_SP_CENTER_PUBLISH_BTN_CLICK = "ct_sp_center_publish_btn_click";

        /**
         * 点击创建策略save按钮
         */
        public static final String CT_SP_CENTER_SAVE_BTN_CLICK = "ct_sp_center_save_btn_click";

        /**
         * （分润结算页入口）点击策略卡片的Total Historical Payout按钮
         */
        public static final String CT_SP_CENTER_HISTORICAL_PAYOUT_BTN_CLICK = "ct_sp_center_historical_payout_btn_click";

        /**
         * 点击策略卡片的...按钮后，点击不同的选项：（1）Profit Sharing Statement 分润结算页入口（2）Follower List（3）Strategy Homepage
         */
        public static final String CT_SP_CENTER_MANAGE_MENU_BTN_CLICK = "ct_sp_center_manage_menu_btn_click";
    }
}
