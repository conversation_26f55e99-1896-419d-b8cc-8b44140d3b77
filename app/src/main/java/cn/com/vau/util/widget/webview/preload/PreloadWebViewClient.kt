package cn.com.vau.util.widget.webview.preload

import android.graphics.Bitmap
import android.net.http.SslError
import android.webkit.SslErrorHandler
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import cn.com.vau.util.widget.webview.base.BaseWebViewClient
import cn.com.vau.util.widget.webview.utils.WebViewLogUtil

@Suppress("unused")
open class PreloadWebViewClient : BaseWebViewClient {

    constructor() : super()

    constructor(webViewClient: WebViewClient?) : super(webViewClient)

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
        super.onPageStarted(view, url, favicon)
        clearHistory(view, url, false)
    }

    override fun onReceivedError(
        view: WebView?,
        request: WebResourceRequest?,
        error: WebResourceError?
    ) {
        super.onReceivedError(view, request, error)
        clearHistory(view, null)
    }

    override fun onReceivedHttpError(
        view: WebView?,
        request: WebResourceRequest?,
        errorResponse: WebResourceResponse?
    ) {
        super.onReceivedHttpError(view, request, errorResponse)
        clearHistory(view, null)
    }

    override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
        super.onReceivedSslError(view, handler, error)
        clearHistory(view, null)
    }

    override fun onPageFinished(view: WebView?, url: String?) {
        super.onPageFinished(view, url)
        clearHistory(view, url)
    }


    /**
     * 当页面开始加载时,需要清空当前页面之前的所有页面
     */
    private fun clearHistory(view: WebView?, url: String?, resetCache: Boolean = true) {
        if (view == null) {
            return
        }
        if (view is PreloadWebView && view.isCached()) {
            WebViewLogUtil.e("${view} onPage_clearHistory url:$url ")
            if (resetCache) {
                view.resetCached()
            }
            view.clearHistory()
        }
    }
}
