package cn.com.vau.util

import android.database.ContentObserver
import android.net.Uri
import android.os.SystemClock
import android.provider.MediaStore
import cn.com.vau.common.storage.SpManager

/**
 * Filename: ScreenCaptureObserver
 * Author: GG
 * Date: 2025/3/5
 * Description:
 */
class ScreenCaptureObserver : ContentObserver(null) {
    private var lastDetectTime = 0L
    private val debounceThreshold = 1000L // 1秒防抖

    private var listener: ((Uri) -> Unit)? = null

    fun setOnScreenCaptureListener(listener: ((Uri) -> Unit)?) {
        this.listener = listener
    }

    override fun onChange(selfChange: Boolean, uri: Uri?) {
        super.onChange(selfChange, uri)
        uri ?: return

        // 不启用截屏分享功能的话 直接拦截
        if (!SpManager.getScreenshotShareEnable()) return
        // 分享弹窗正在展示，拦截不进行截屏分享
        if (isShareDialogShow) return
        // 截屏分享弹窗正在展示，拦截不进行截屏分享
        if (isShareScreenCaptureDialogShow) return
        // 防抖处理 以及 分享弹窗以及存在的情况下，拦截不进行截屏分享
        if (isDebounceThreshold()) return
        lastDetectTime = SystemClock.elapsedRealtime()
        listener?.invoke(uri)
    }

    /**
     * 防抖处理：2秒内不重复检测
     */
    private fun isDebounceThreshold(): Boolean = SystemClock.elapsedRealtime() - lastDetectTime < debounceThreshold

    fun start() {
        UtilApp.getApp().contentResolver.registerContentObserver(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            true,
            this
        )
    }

    fun stop() {
        UtilApp.getApp().contentResolver.unregisterContentObserver(this)
    }

    companion object {
        /**
         * 当前是否展示了截屏分享弹窗
         */
        var isShareScreenCaptureDialogShow = false

        /**
         * 当前是否展示了分享弹窗
         */
        var isShareDialogShow = false
    }
}
