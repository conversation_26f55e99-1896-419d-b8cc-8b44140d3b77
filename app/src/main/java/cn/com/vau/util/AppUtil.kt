package cn.com.vau.util

import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.Build
import android.text.TextUtils
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.storage.SpManager
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.snail.antifake.jni.EmulatorDetectUtil
import java.io.IOException
import java.util.*

/**
 * Created by roy on 2018/10/15.
 */
object AppUtil {

    // 判断是否是模拟器
    @JvmStatic
    val isEmulator: Boolean by lazy { runCatching { EmulatorDetectUtil.isEmulatorFromAll(VauApplication.context) }.getOrDefault(false) }

    // 获取设备id
    @JvmStatic
    val uniqueID: String by lazy { getDeviceId() }

    @JvmStatic
    fun getTimeZoneRawOffsetToHour(): Int {
        return TimeZone.getDefault().getOffset(System.currentTimeMillis()) / (3600 * 1000)
    }

    fun getPackageName(): String {
        return VauApplication.context.packageName
    }

    @JvmStatic
    fun getSystemVersion(): String {
        return Build.VERSION.RELEASE
    }

    /**
     * 获取 SDK 版本号
     */
    fun getSdkVersion(): Int {
        return Build.VERSION.SDK_INT
    }

    @JvmStatic
    fun getVersionName(): String {
        val packageManager = UtilApp.getApp().packageManager
        try {
            val packageInfo = packageManager.getPackageInfo(getPackageName(), 0)
            return packageInfo.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return ""
    }

    @JvmStatic
    fun getSystemModel(): String {
        return Build.MODEL
    }

    @JvmStatic
    fun getGoogleAdvertisingId(): String? {
        var gaid: String? = ""
        try {
            val advertisingIdInfo = AdvertisingIdClient.getAdvertisingIdInfo(VauApplication.context)
            gaid = advertisingIdInfo.id
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: GooglePlayServicesNotAvailableException) {
            e.printStackTrace()
        } catch (e: GooglePlayServicesRepairableException) {
            e.printStackTrace()
        }
        return gaid
    }

    /**
     * 得到全局唯一UUID
     */
    @JvmStatic
    fun getUUID(): String {
        var uuid = SpManager.getUuid("")
        if (TextUtils.isEmpty(uuid)) {
            uuid = UUID.randomUUID().toString()
            SpManager.putUuid(uuid)
        }
        return uuid
    }

    private fun getDeviceId(): String {
        var deviceId = SpManager.getDeviceId("")
        if (TextUtils.isEmpty(deviceId)) {
            deviceId = DeviceUtil.getDeviceId(VauApplication.context)
            SpManager.putDeviceId(deviceId)
        }
        return deviceId
    }

    /**
     * 系统是否是深色主题
     */
    fun isNightMode(): Boolean {
        val mode = VauApplication.context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return mode == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 是否是浅色主题
     */
    @JvmStatic
    fun isLightTheme(): Boolean {
        return SpManager.getStyleState(0) == 0 //0 是明的 1 是暗的
    }

    /**
     * app 不同监管下显示的email ， au 是通过不同监管展示不同的email地址
     */
    fun getSuperviseEmail() = when (SpManager.getSuperviseNum()) {
        // asic
        "1" -> "<EMAIL>"
        // v1 v2
        "8", "14" -> "<EMAIL>"
        // fca
        "13" -> "<EMAIL>"
        // 未登录时不知道什么监管，使用默认的 .com邮箱
        else -> "<EMAIL>"
    }
}