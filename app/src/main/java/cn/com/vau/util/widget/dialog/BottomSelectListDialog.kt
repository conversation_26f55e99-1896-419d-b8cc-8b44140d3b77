package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.text.TextUtils
import android.view.LayoutInflater
import android.widget.TextView
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.DialogBottomSelectListBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.screenHeight
import cn.com.vau.util.widget.dialog.base.*
import com.lxj.xpopup.core.BottomPopupView

@SuppressLint("ViewConstructor")
class BottomSelectListDialog private constructor(
    context: Context,
    private var title: String? = null,
    private var dataList: ArrayList<String> = ArrayList(0),
    private var selectIndex: Int = -1,
    private var itemType: Int = 0,
    private var onItemClickListener: ((position: Int) -> Unit)? = null
) : BottomPopupView(context), IDialog<DialogBottomSelectListBinding> {
    private val inflater = LayoutInflater.from(context)

    private var mContentBinding =
        DialogBottomSelectListBinding.inflate(inflater, bottomPopupContainer, false)

    private var adapter: BottomSelectListAdapter? = null

    private var tvTitle: TextView? = null

    override fun addInnerContent() {
        bottomPopupContainer.addView(mContentBinding.root)
    }

    override fun getMaxHeight(): Int = (screenHeight * 0.75).toInt()

    override fun onCreate() {
        super.onCreate()
        initView()
        fixNavigationBarPadding()
        processBackPress()
    }

    private fun processBackPress() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && dialog != null) {
            dialog.onBackInvokedDispatcher.registerOnBackInvokedCallback(0) {
                if (onBackPressed()) {
                    return@registerOnBackInvokedCallback
                }
                if (popupInfo.isDismissOnBackPressed &&
                    (popupInfo.xPopupCallback == null || !popupInfo.xPopupCallback.onBackPressed(this))) {
                    dismissOrHideSoftInput()
                }
            }
        }
    }

    private fun initView() {
        setTitleInner(title)
        setRecyclerView()
    }

    private fun setRecyclerView() {
        adapter = BottomSelectListAdapter(context, dataList)
        setOnItemClickListener()
        mContentBinding.recyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mContentBinding.recyclerView.adapter = adapter
        checkSelectIndex(this.selectIndex)
        adapter?.updateData(dataList, selectIndex, itemType)
    }

    fun setTitle(title: String?): BottomSelectListDialog {
        this.title = title
        setTitleInner(this.title)
        return this
    }

    private fun setTitleInner(title: String?) {
        val params = mContentBinding.recyclerView.layoutParams as? MarginLayoutParams
        if (!TextUtils.isEmpty(title)) {
            mContentBinding.tvTitle.isVisible = true
            tvTitle = mContentBinding.root.findViewById(R.id.tvTitle)
            tvTitle?.text = title
            params?.topMargin = 4.dp2px()
        } else {
            mContentBinding.tvTitle.isVisible = false
            params?.topMargin = 18.dp2px()
        }
        mContentBinding.recyclerView.layoutParams = params
    }

    fun setData(
        dataList: List<String>?,
        selectIndex: Int,
        title: String?,
        itemType: Int
    ): BottomSelectListDialog {
        setTitle(title)
        checkDataList(dataList)
        checkSelectIndex(selectIndex)
        this.itemType = itemType
        adapter?.updateData(this.dataList, this.selectIndex, this.itemType)
        return this
    }

    private fun checkSelectIndex(selectIndex: Int) {
        if (selectIndex < 0 || selectIndex >= dataList.size) {
            this.selectIndex = -1
        } else {
            this.selectIndex = selectIndex
        }
    }

    /**
     * 外部主动调用此方法，不会有事件的会调
     */
    fun setSelectIndex(selectIndex: Int): BottomSelectListDialog {
        checkSelectIndex(selectIndex)
        adapter?.updateSelectIndex(this.selectIndex)
        return this
    }

    fun setData(
        dataList: List<String>?,
        selectIndex: Int,
        title: String?
    ): BottomSelectListDialog {
        setTitle(title)
        checkDataList(dataList)
        checkSelectIndex(selectIndex)
        itemType = 0
        adapter?.updateData(this.dataList, this.selectIndex, this.itemType)
        return this
    }

    private fun checkDataList(dataList: List<String>?) {
        if (dataList.isNullOrEmpty()) {
            this.dataList.clear()
        } else {
            this.dataList.clear()
            this.dataList.addAll(dataList)
        }
    }

    fun setData(
        dataList: List<String>?,
        title: String? = null,
        itemType: Int
    ): BottomSelectListDialog {
        setTitle(title)
        checkDataList(dataList)
        this.selectIndex = -1
        this.itemType = itemType
        adapter?.updateData(this.dataList, this.selectIndex, this.itemType)
        return this
    }

    fun setOnItemClickListener(onItemClickListener: ((position: Int) -> Unit)): BottomSelectListDialog {
        this.onItemClickListener = onItemClickListener
        setOnItemClickListener()
        return this
    }

    fun setOnItemClickListener() {
        adapter?.setOnItemClickListener { position ->
            onItemClickListener?.invoke(position)
            dismissDialog()
        }
    }


    override fun showDialog() {
        if (popupInfo == null) {
            return
        }
        super.show()
    }

    override fun dismissDialog() {
        super.dismiss()
    }

    override fun getContentViewBinding(): DialogBottomSelectListBinding {
        return mContentBinding
    }

    override fun isShowDialog(): Boolean {
        return super.isShow()
    }


    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomSelectListBinding, Builder>(activity) {
        private var dataList: ArrayList<String> = ArrayList(0)
        private var title: String? = null
        private var selectIndex: Int = -1
        private var itemType: Int = 0
        private var onItemClickListener: ((position: Int) -> Unit)? = null

        /**
         * DataList
         *
         */
        fun setDataList(dataList: List<String>?) = apply {
            if (dataList.isNullOrEmpty()) {
                this.dataList.clear()
            } else {
                this.dataList.clear()
                this.dataList.addAll(dataList)
            }
            return this
        }

        /**
         *  设置标题
         */
        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        /**
         * 设置选中的item,默认未选中
         */
        fun setSelectIndex(selectIndex: Int): Builder {
            this.selectIndex = selectIndex
            return this
        }

        /**
         * 设置item类型，右侧是是CheckBox还是Arrow,默认是0
         */
        fun setItemType(itemType: Int): Builder {
            this.itemType = itemType
            return this
        }

        /**
         * 点击的回调
         */
        fun setOnItemClickListener(onItemClickListener: ((position: Int) -> Unit)): Builder {
            this.onItemClickListener = onItemClickListener
            return this
        }

        override fun build(): BottomSelectListDialog {
            return super.build() as BottomSelectListDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomSelectListBinding> {
            return BottomSelectListDialog(
                context,
                title,
                dataList,
                selectIndex,
                itemType,
                onItemClickListener
            )
        }
    }
}