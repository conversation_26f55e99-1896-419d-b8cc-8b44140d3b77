package cn.com.vau.util

import android.annotation.SuppressLint
import cn.com.vau.util.AppUtil.getTimeZoneRawOffsetToHour
import java.text.*
import java.time.*
import java.util.*
import kotlin.math.abs

/**
 * 时间比较工具类
 */
object TimeUtil {
    /**
     * 定义一个 ThreadLocal 对象，用于存储每个线程独有的可变 Map。
     * Map 的键是日期格式的字符串，值是 SimpleDateFormat 对象。
     */
    private val SDF_THREAD_LOCAL: ThreadLocal<MutableMap<String, SimpleDateFormat>> = object : ThreadLocal<MutableMap<String, SimpleDateFormat>>() {
        /**
         * 当线程第一次访问此 ThreadLocal 变量时，提供一个空的 Map 作为初始值。
         *
         * @return 一个空的 MutableMap，用于存储日期格式字符串和对应的 SimpleDateFormat 对象。
         */
        override fun initialValue(): MutableMap<String, SimpleDateFormat> {
            return mutableMapOf()
        }
    }

    /**
     * 定义一个只读属性 defaultFormat，返回一个默认的 SimpleDateFormat 对象。
     *
     * @return 一个格式为 "yyyy-MM-dd HH:mm:ss" 的 SimpleDateFormat 对象。
     */
    private val defaultFormat: SimpleDateFormat
        get() = getSafeDateFormat("yyyy-MM-dd HH:mm:ss")

    /**
     * 获取线程安全的 SimpleDateFormat 对象。
     *
     * @param pattern 日期格式的字符串，例如 "yyyy-MM-dd"。
     * @return 一个线程安全的 SimpleDateFormat 对象，用于日期格式化。
     */
    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getSafeDateFormat(pattern: String?): SimpleDateFormat {
        // 获取当前线程对应的 Map，存储格式化模式与 SimpleDateFormat 对象的映射
        val sdfMap = SDF_THREAD_LOCAL.get()

        // 尝试从 Map 中获取指定模式的 SimpleDateFormat 对象
        var simpleDateFormat = sdfMap?.get(pattern)

        // 如果 Map 中没有对应的 SimpleDateFormat 对象，创建一个新的，并将其存入 Map 中
        if (simpleDateFormat == null) {
            simpleDateFormat = SimpleDateFormat(pattern.ifNull(), Locale.ENGLISH)
            sdfMap?.set(pattern.ifNull(), simpleDateFormat)
        }

        // 返回找到或新创建的 SimpleDateFormat 对象
        return simpleDateFormat
    }

    fun millis2String(millis: Long): String {
        return millis2String(millis, defaultFormat)
    }

    /**
     * 将毫秒值转换为指定格式的字符串。
     *
     * @param millis 毫秒值（从1970年1月1日 00:00:00 UTC的偏移量）。
     * @param pattern 日期格式的字符串，例如 "yyyy-MM-dd HH:mm:ss"。
     * @return 转换后的日期字符串。
     */
    fun millis2String(millis: Long, pattern: String): String {
        return millis2String(millis, getSafeDateFormat(pattern))
    }

    /**
     * 将毫秒值转换为指定格式的字符串。
     *
     * @param millis 毫秒值（从1970年1月1日 00:00:00 UTC的偏移量）。
     * @param format 日期格式对象，用于格式化日期。
     * @return 转换后的日期字符串。
     */
    private fun millis2String(millis: Long, format: DateFormat): String {
        return format.format(Date(millis))
    }

    /**
     * 将日期字符串转换为 Date 对象。
     *
     * @param time 日期字符串，例如 "2023-08-20 14:30:00"。
     * @param pattern 日期格式的字符串，例如 "yyyy-MM-dd HH:mm:ss"。
     * @return 转换后的 Date 对象，如果解析失败则返回 null。
     */
    fun string2Date(time: String?, pattern: String): Date? {
        return string2Date(time, getSafeDateFormat(pattern))
    }

    /**
     * 将日期字符串从一种格式转换为另一种格式。
     * @param time 日期字符串，例如 "2023-08-20 14:30:00"。
     * @param inPattern 输入日期格式的字符串，例如 "yyyy-MM-dd HH:mm:ss"。
     * @param outPattern 输出日期格式的字符串，例如 "dd/MM/yyyy"。
     */
    fun string2String(time: String?, inPattern: String, outPattern: String): String? {
        val sdf = getSafeDateFormat(inPattern)
        val date = string2Date(time, sdf)
        return date2String(date, getSafeDateFormat(outPattern))
    }

    /**
     * 将日期字符串转换为 Date 对象。
     *
     * @param time 日期字符串，例如 "2023-08-20 14:30:00"。
     * @param format 日期格式对象，用于解析日期字符串。
     * @return 转换后的 Date 对象，如果解析失败则返回 null。
     */
    private fun string2Date(time: String?, format: DateFormat): Date? {
        if (time.isNullOrBlank())
            return null
        try {
            return format.parse(time)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 将 Date 对象转换为指定格式的字符串。
     *
     * @param date Date 对象。
     * @param pattern 日期格式的字符串，例如 "yyyy-MM-dd HH:mm:ss"。
     * @return 转换后的日期字符串。
     */
    fun date2String(date: Date?, pattern: String): String? {
        if (date == null)
            return null
        return getSafeDateFormat(pattern).format(date)
    }

    /**
     * 将 Date 对象转换为指定格式的字符串，提供默认格式选项。
     *
     * @param date Date 对象。
     * @param format 日期格式对象，默认使用 `defaultFormat`。
     * @return 转换后的日期字符串。
     */
    @JvmOverloads
    fun date2String(date: Date?, format: DateFormat = defaultFormat): String? {
        if (date == null)
            return null
        return format.format(date)
    }

    /**
     * 生成一个格式化的 HTTP 请求头时间戳字符串。
     *
     * 此方法使用线程安全的 `SimpleDateFormat` 对象，将当前时间格式化为
     * "yyyy-MM-dd HH:mm:ss SSS" 的字符串形式，精确到毫秒。
     *
     * @return 格式化后的 HTTP 请求头时间戳字符串。
     */
    @JvmStatic
    fun formatHttpHeader(): String {
        // 获取线程安全的日期格式化对象，格式为 "yyyy-MM-dd HH:mm:ss SSS"
        val sdf = getSafeDateFormat("yyyy-MM-dd HH:mm:ss SSS")

        // 使用格式化对象将当前时间转换为字符串并返回
        return sdf.format(Date(System.currentTimeMillis()))
    }

    /**
     * 将日期以yyyy-MM-dd HH:mm:ss格式化
     */
    @JvmStatic
    fun formatDateTime(dateL: Long, formater: String): String {
        val sdf = getSafeDateFormat(formater)
        return sdf.format(Date(dateL))
    }

    /**
     * 根据时间戳转成指定的format格式
     *
     * @param timeMillis 1
     * @param format     2
     * @return 3
     */
    fun formatDate(timeMillis: Long, format: String): String {
        if (timeMillis <= 0) return ""

        val date = Date(timeMillis)
        val formatter = getSafeDateFormat(format)

        var result = ""
        try {
            result = formatter.format(date)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return result
    }

    /**
     * 时间戳转字符串
     *
     * @param timeStamp
     * @param pattern
     * @return
     */
    @JvmStatic
    fun getStrTime(timeStamp: String?, pattern: String): String {
        val sdf = getSafeDateFormat(pattern)
        val l = timeStamp.toLongCatching()
        return sdf.format(Date(l))
    }

    /**
     * 字符串转时间戳
     *
     * @param timeString
     * @return
     */
    @JvmStatic
    fun getTimeStr(timeString: String?, pattern: String): String? {
        var timeStamp: String? = null
        val sdf = getSafeDateFormat(pattern)
        val d: Date?
        try {
            d = sdf.parse(timeString ?: "")
            val l = d.time
            timeStamp = l.toString()
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return timeStamp
    }

    /**
     * 获取当前时间字符串
     *
     * @param pattern
     * @return
     */
    private fun getNowDate(pattern: String): String? {
        var format: String?
        try {
            format = getSafeDateFormat(pattern).format(Date().time)
        } catch (e: Exception) {
            e.printStackTrace()
            format = ""
        }
        return format
    }

    /**
     * 财经日历月份简称 June 07, 2018
     *
     * @param date
     * @return
     */
    fun getDateFormatEN(date: Date?): String {
        var dateStr = ""
        try {
            val dateMon = String.format("%tb", date) //月
            val dateDay = String.format("%td", date) //日
            val dateYear = String.format("%tY", date) //年
            dateStr = "$dateMon $dateDay, $dateYear"
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return dateStr
    }

    /**
     * 将日期字符串转换为时间戳（毫秒）。
     *
     * 使用格式为 "yyyy-MM-dd HH:mm:ss" 的线程安全 SimpleDateFormat 对象将给定的日期字符串解析为 Date 对象。
     * 如果解析失败，则返回当前时间的时间戳。
     *
     * @param time 日期字符串，例如 "2024-08-20 14:30:00"。
     * @return 对应的时间戳（毫秒），如果解析失败则返回当前时间的时间戳。
     */
    fun getTime(time: String): Long {
        // 获取线程安全的日期格式化对象，格式为 "yyyy-MM-dd HH:mm:ss"
        val sdf = getSafeDateFormat("yyyy-MM-dd HH:mm:ss")
        var date = Date()
        try {
            // 尝试将字符串解析为 Date 对象，如果解析失败，则返回当前时间
            date = sdf.parse(time) ?: Date()
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        // 返回 Date 对象的时间戳（毫秒）
        return date.time
    }

    /**
     * 将时间戳字符串转换为格式化的时间字符串。
     *
     * 此方法将给定的时间戳字符串（以秒为单位）转换为 "HH:mm:ss" 格式的时间字符串。
     * 如果时间戳为 "0"，则返回空字符串。
     *
     * @param time 时间戳字符串（以秒为单位），例如 "1692530400"。
     * @return 转换后的时间字符串，格式为 "HH:mm:ss"。
     */
    @JvmStatic
    fun getSocketDateToString(time: String): String {
        // 如果时间戳为 "0"，则返回空字符串
        if ("0" == time) return ""

        // 获取线程安全的日期格式化对象，格式为 "HH:mm:ss"
        val simpleDateFormat = getSafeDateFormat("HH:mm:ss")

        // 由于时区问题可能导致时间不准确，此处需处理时区偏移（大坑）
        // simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"))

        // 从时间戳中减去当前时区的时差（单位为秒），然后将其转换为毫秒
        var timeStr = time.mathSub((getTimeZoneRawOffsetToHour() * 3600).toString() + "")
        timeStr += "000"

        // 将调整后的时间字符串转换为 Long 类型的毫秒值
        val date = Date(timeStr.toLongCatching(0L))

        // 返回格式化后的时间字符串
        return simpleDateFormat.format(date)
    }

    /**
     * k线页面交易时间
     */
    @JvmStatic
    fun getKlineTradeDate(time: String, pattern: String): String {
        var timeStr = time.mathSub((getTimeZoneRawOffsetToHour() * 3600).toString() + "")
        if ("0" == timeStr) {
            timeStr = System.currentTimeMillis().toString()
        } else {
            timeStr += "000"
        }
        val socketCurrentData = timeStr.toLongCatching(0L)
        return millis2String(socketCurrentData, getSafeDateFormat(pattern))
    }

    /**
     * 计算从给定起始日期开始，向前推移指定分钟数后的时间戳。
     *
     * 如果 `minute` 小于 0，则返回 `startDate`。
     * 如果 `minute` 大于 `startDate` 对应的分钟数，则返回一个默认值 `1000000000`。
     *
     * @param minute 要向前推移的分钟数。
     * @param startDate 起始日期的时间戳（以秒为单位）。
     * @return 计算后的时间戳（以秒为单位）。
     */
    @JvmStatic
    fun frontDateLongWithStartDate(minute: Int, startDate: Long): Long {
        // 如果分钟数小于 0，则直接返回起始日期的时间戳
        if (minute < 0) {
            return startDate
        }
        // 如果分钟数超过了起始日期的分钟数，则返回默认值 1000000000
        if (minute > startDate / 60) {
            return 1000000000
        }

        // 将时间戳转换为 LocalDateTime 对象
        val dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startDate * 1000), ZoneId.systemDefault())

        // 获取该日期是星期几
        val dayOfWeek = dateTime.dayOfWeek

        // 判断该日期是否为周末
        val isWeekend = (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY)

        // 如果是周末，则在减去指定分钟数的基础上再减去两天的秒数
        val l = if (isWeekend) {
            startDate - minute * 60L - (2 * 24 * 60 * 60L)
        } else {
            // 否则只减去指定分钟数的秒数
            startDate - minute * 60L
        }

        // 返回绝对值，以确保时间戳为正数
        return abs(l.toDouble()).toLong()
    }

    /**
     * 获取当前时间
     *
     * @param pattern
     * @return
     */
    fun getNowTime(pattern: String): String {
        val simpleDateFormat = getSafeDateFormat(pattern)
        val date = Date(System.currentTimeMillis())
        return simpleDateFormat.format(date)
    }

    fun getBeforeYearTime(beforeYearCount: Int): Long {
        val year = getNowDate("yyyy").toIntCatching(0) - beforeYearCount
        val currentDate = getNowDate("MM-dd HH:mm:ss")
        //    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return getTime("$year-$currentDate")
    }

    /**
     * 根据指定日期往前/后推移 N 天
     *
     * @param timestamp 指定日期的时间戳（毫秒）
     * @param days 正数：要往前推移的天数  负数：要往后推移的天数
     * @return 推移后的新时间戳（毫秒）
     */
    fun frontDateByDays(timestamp: Long, days: Long): Long {
        // 将时间戳转换为 LocalDateTime 对象
        val dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())

        // 加减指定天数
        val newDateTime = dateTime.minusDays(days)

        // 转换回时间戳（毫秒）
        return newDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }


    /**
     * 将时间戳转换成fx street时间
     *
     * @param timeStamp 时间戳
     * @return 描述性日期
     */
    fun descriptiveData(timeStamp: Long): String {
        val curTimeMillis = System.currentTimeMillis()
        val curDate = Date(curTimeMillis)
        val todayHoursSeconds = curDate.hours * 60 * 60
        val todayMinutesSeconds = curDate.minutes * 60
        val todaySeconds = curDate.seconds
        val todayMillis = (todayHoursSeconds + todayMinutesSeconds + todaySeconds) * 1000
        val todayStartMillis = curTimeMillis - todayMillis
        if (timeStamp >= todayStartMillis) {
            val sdf = getSafeDateFormat("HH:mm")
            return sdf.format(Date(timeStamp))
        }
        val sdf = getSafeDateFormat("dd/MM/yyyy  HH:mm")
        return sdf.format(Date(timeStamp))
    }

}
