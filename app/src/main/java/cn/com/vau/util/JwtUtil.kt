package cn.com.vau.util

import cn.com.vau.common.http.HttpUrl
import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jws
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import java.nio.charset.StandardCharsets
import javax.crypto.SecretKey

/**
 * author：lvy
 * date：2025/05/15
 * desc：
 */
object JwtUtil {

    private const val BRAND = "AU" // 品牌，子品牌注意修改

    private val secretKey: SecretKey by lazy {
        Keys.hmacShaKeyFor(HttpUrl.X_SOURCE_SECRET_KEY.toByteArray(StandardCharsets.UTF_8))
    }

    /**
     * 生成JWT
     */
    @JvmStatic
    fun createToken(): String {
        runCatching {
            val curMillis = System.currentTimeMillis()
            Jwts.builder()
                .claim("iss", "app") // 平台
                .claim("ct", curMillis) // 时间戳
                .claim("b", BRAND) // 品牌
                .signWith(secretKey, Jwts.SIG.HS256)
                .compact()
        }.onSuccess {
            return it
        }.onFailure {
            return ""
        }
        return ""
    }

    /**
     * 解析JWT
     */
    @JvmStatic
    fun parseToken(token: String): Jws<Claims>? {
        runCatching {
            Jwts.parser()
                .verifyWith(secretKey)
                .build()
                .parseSignedClaims(token)
        }.onSuccess {
            return it
        }.onFailure {
            return null
        }
        return null
    }
}