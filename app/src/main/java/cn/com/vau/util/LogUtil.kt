package cn.com.vau.util

import android.util.Log
import cn.com.vau.BuildConfig
import java.util.*
import kotlin.math.min

/**
 * 简单日志输出类
 * 默认输出日志到控制台，不输出可配置logSwitch为false
 *
 * <AUTHOR>
 */
object LogUtil {

    /**
     * 默认的tag
     */
    private const val DEFAULT_TAG = "Log"
    private const val MAX_LEN = 3 * 1024
    private const val LEFT_MARK = "["
    private const val RIGHT_MARK = "]"
    private const val PLACEHOLDER = " "
    private val LINE_SEP = System.getProperty("line.separator") ?: "\n"
    private const val NULL = "null"

    /**
     * 是否打印堆栈信息，默认不打印
     */
    var isShowStackDeep = false
    var STACK_DEEP = 3

    /**
     * 日志输出开关
     */
    var logSwitch = BuildConfig.DEBUG

    const val V = Log.VERBOSE
    const val D = Log.DEBUG
    const val I = Log.INFO
    const val W = Log.WARN
    const val E = Log.ERROR
    const val A = Log.ASSERT

    @JvmStatic
    @JvmOverloads
    fun v(contents: Any?, showStack: Boolean = false) {
        log(V, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun v(tag: String, contents: Any?, showStack: Boolean = false) {
        log(V, tag, contents, showStack)
    }

    @JvmStatic
    fun d(contents: Any?, showStack: Boolean = false) {
        log(D, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun d(tag: String, contents: Any?, showStack: Boolean = false) {
        log(D, tag, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun i(contents: Any?, showStack: Boolean = false) {
        log(I, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun i(tag: String, contents: Any?, showStack: Boolean = false) {
        log(I, tag, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun w(contents: Any?, showStack: Boolean = false) {
        log(W, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun w(tag: String, contents: Any?, showStack: Boolean = false) {
        log(W, tag, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun e(contents: Any?, showStack: Boolean = false) {
        log(E, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun e(tag: String, contents: Any?, showStack: Boolean = false) {
        log(E, tag, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun a(contents: Any?, showStack: Boolean = false) {
        log(A, DEFAULT_TAG, contents, showStack)
    }

    @JvmStatic
    @JvmOverloads
    fun a(tag: String, contents: Any?, showStack: Boolean = false) {
        log(A, tag, contents, showStack)
    }

    private fun log(type: Int, tag: String, contents: Any?, showStack: Boolean) {
        if (!logSwitch) return
        val stringBuilder = StringBuilder()
        if (isShowStackDeep || showStack) {
            val head = processHead()
            stringBuilder.append(LEFT_MARK).append(head).append(RIGHT_MARK).append("\n")
        }
        stringBuilder.append(contents as? String ?: contents.json)
        printMsg(type, tag, stringBuilder.toString())
    }

    /**
     * 获取堆栈信息
     */
    private fun processHead(): String {
        val stackTrace = Throwable().stackTrace
        val stackIndex = 3
        val targetElement = stackTrace[stackIndex]
        val className = getFileName(targetElement)
        val tName = Thread.currentThread().name
        val head = Formatter().format(
            "%s, %s.%s(%s:%d)", tName, targetElement.className, targetElement.methodName, className, targetElement.lineNumber
        ).toString()

        return if (STACK_DEEP <= 1) {
            head
        } else {
            val consoleHead = Array(min(STACK_DEEP, stackTrace.size - stackIndex)) { "" }
            consoleHead[0] = head
            val spaceLen = tName.length + 2
            val space = String.format("%" + spaceLen + "s", "")
            for (i in 1 until consoleHead.size) {
                val element = stackTrace[i + stackIndex]
                consoleHead[i] = Formatter().format(
                    "%s%s.%s(%s:%d)", space, element.className, element.methodName, getFileName(element), element.lineNumber
                ).toString()
            }
            consoleHead.joinToString(separator = "\n")
        }
    }

    /**
     * 获取堆栈里类的信息
     */
    private fun getFileName(targetElement: StackTraceElement): String {
        var fileName = targetElement.fileName
        if (fileName != null) return fileName
        var className = targetElement.className
        val classNameInfo = className.split(".")
        if (classNameInfo.isNotEmpty()) {
            className = classNameInfo.last()
        }
        val index = className.indexOf('$')
        if (index != -1) {
            className = className.substring(0, index)
        }
        return "$className.java"
    }

    /**
     * 打印日志的方法
     */
    private fun printMsg(type: Int, tag: String, msg: String) {
        val len = msg.length
        val countOfSub = len / MAX_LEN
        if (countOfSub > 0) {
            Log.println(type, tag, msg.substring(0, MAX_LEN))
            var index = MAX_LEN
            for (i in 1 until countOfSub) {
                Log.println(type, tag, "$PLACEHOLDER$LINE_SEP${msg.substring(index, index + MAX_LEN)}")
                index += MAX_LEN
            }
            if (index != len) {
                Log.println(type, tag, "$PLACEHOLDER$LINE_SEP${msg.substring(index, len)}")
            }
        } else {
            Log.println(type, tag, msg)
        }
    }
}