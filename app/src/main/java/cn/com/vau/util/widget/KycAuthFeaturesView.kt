package cn.com.vau.util.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.LayoutKycAuthFeaturesViewBinding
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_DEPOSIT
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_LIVE_TRADING
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_OPEN_ADDITIONAL_ACCOUNT
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_OPEN_LIVE_TRADING_ACCOUNT
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_WALLET_CONVERT
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_WALLET_DEPOSIT
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_WALLET_WITHDRAWAL
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.FEATURES_WITHDRAWAL
import cn.com.vau.util.dp2px
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: AuthenticationLimitView
 * Author: GG
 * Date: 2025/3/12
 * Description:
 */
class KycAuthFeaturesView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding: LayoutKycAuthFeaturesViewBinding by lazy(LazyThreadSafetyMode.NONE) {
        LayoutKycAuthFeaturesViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    private val liveTradingAdapter: ItemAdapter by lazy(LazyThreadSafetyMode.NONE) {
        ItemAdapter()
    }
    private val cryptoWalletAdapter: ItemAdapter by lazy(LazyThreadSafetyMode.NONE) {
        ItemAdapter()
    }

    init {
        mBinding.rvLiveTrading.layoutManager = GridLayoutManager(context, 2)
        mBinding.rvCryptoWallet.layoutManager = GridLayoutManager(context, 2)
        mBinding.rvLiveTrading.adapter = liveTradingAdapter
        mBinding.rvLiveTrading.addItemDecoration(DividerItemDecoration(12.dp2px()))
        mBinding.rvCryptoWallet.adapter = cryptoWalletAdapter
        mBinding.rvCryptoWallet.addItemDecoration(DividerItemDecoration(12.dp2px()))
    }

    fun setLiveTradingAccountState(data: List<Int>? = null) {
        mBinding.tvLiveTradingAccountTitle.isVisible = !data.isNullOrEmpty()
        mBinding.rvLiveTrading.isVisible = !data.isNullOrEmpty()
        liveTradingAdapter.setList(data)
        checkViewVisible()
    }

    fun setCryptoWalletAccountState(data: List<Int>? = null) {
        mBinding.tvCryptoWalletAccountTitle.isVisible = !data.isNullOrEmpty()
        mBinding.rvCryptoWallet.isVisible = !data.isNullOrEmpty()
        cryptoWalletAdapter.setList(data)
        checkViewVisible()
    }

    private fun checkViewVisible() {
        mBinding.root.isGone = mBinding.rvLiveTrading.isGone && mBinding.rvCryptoWallet.isGone
    }

    private class ItemAdapter : BaseQuickAdapter<Int, BaseViewHolder>(R.layout.item_kyc_feature_textview) {
        override fun convert(holder: BaseViewHolder, item: Int) {
            holder.setText(R.id.tv, getFeatureStr(item.toString()))
        }

        private fun getFeatureStr(key: String) =
            context.getString(
                when (key) {
                    FEATURES_DEPOSIT -> R.string.deposit
                    FEATURES_WITHDRAWAL -> R.string.withdrawal
                    FEATURES_LIVE_TRADING -> R.string.live_trading
                    FEATURES_OPEN_LIVE_TRADING_ACCOUNT -> R.string.open_live_trading_account
                    FEATURES_OPEN_ADDITIONAL_ACCOUNT -> R.string.open_additional_account
                    FEATURES_WALLET_CONVERT -> R.string.wallet_convert
                    FEATURES_WALLET_DEPOSIT -> R.string.wallet_deposit
                    FEATURES_WALLET_WITHDRAWAL -> R.string.wallet_withdrawal
                    else -> R.string.wallet_withdrawal
                }
            )
    }
}