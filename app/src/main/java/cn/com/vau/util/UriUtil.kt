package cn.com.vau.util

import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import java.io.*

/**
 * @description:
 * @author: GG
 * @createDate: 2024 9月 21 17:08
 * @updateUser:
 * @updateDate: 2024 9月 21 17:08
 */
object UriUtil {

    fun file2Uri(file: File?): Uri? {
        if (file == null || !FileUtil.isFileExists(file)) return null
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val authority = "${UtilApp.getApp().packageName}.installapkdemor"
            FileProvider.getUriForFile(UtilApp.getApp(), authority, file)
        } else {
            Uri.fromFile(file)
        }
    }

    fun writeTxtToFile(strcontent: String, fileName: String) {
        val file = File(fileName)
        try {
            file.createNewFile()
        } catch (e: IOException) {
            e.printStackTrace()
            LogUtil.e("TestFile", " file.createNewFile ---- Error on write File:" + e)
        }

        try {
            val raf = RandomAccessFile(file, "rwd")
            raf.seek(file.length())
            raf.write(strcontent.toByteArray())
            raf.close()
        } catch (e: Exception) {
            LogUtil.e("TestFile", "RandomAccessFile Error on write File:" + e)
        }
    }

    /**
     * 通过uri 检查文件类型
     */
    fun checkFileType(uri: Uri): String? {
        val mimeType = UtilApp.getApp().contentResolver.getType(uri)?.lowercase()
        return when {
            mimeType?.contains("png") == true -> "png"
            mimeType in setOf("image/jpeg", "image/jpg") -> "jpg"
            mimeType == "image/bmp" || mimeType == "image/x-ms-bmp" -> "bmp"
            mimeType == "application/pdf" -> "pdf"
            mimeType == "application/msword" -> "doc"
            mimeType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" -> "docx"
            else -> null
        }
    }

    /**
     * 获取文件大小
     */
    fun getFileSize(uri: Uri): Long {
        return try {
            // 添加权限检查
            UtilApp.getApp().contentResolver.takePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION
            )

            UtilApp.getApp().contentResolver.openFileDescriptor(uri, "r")?.use { pfd ->
                pfd.statSize
            } ?: 0L
        } catch (e: FileNotFoundException) {
            LogUtil.e("UriUtil", "File not found: ${uri.path}")
            0L
        } catch (e: SecurityException) {
            LogUtil.e("UriUtil", "Permission denied for: ${uri.path}")
            0L
        }
    }

}