package cn.com.vau.util.widget

import android.animation.AnimatorSet
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.animation.addListener
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @description:
 * @author: GG
 * @createDate: 2025 6月 12 15:08
 */
class SwitchButton : View {

    private val gestureDetector: GestureDetector by lazy { GestureDetector(context, GestureListener()) }

    /**
     * true 和 false 状态回调
     */
    private var listener: ((Boolean) -> Unit)? = null

    /**
     * loading 状态回调
     */
    private var loadingListener: (() -> Unit)? = null

    /**
     * 背景半径
     */
    private var viewRadius = 0f

    /**
     * 按钮半径
     */
    private var buttonRadius = 0f

    /**
     * 背景高
     */
    private var height = 0f

    /**
     * 背景宽
     */
    private var width = 0f

    /**
     * 背景位置
     */
    private var left = 0f
    private var top = 0f
    private var right = 0f
    private var bottom = 0f
    private var centerX = 0f
    private var centerY = 0f
    private var ringLeft = 0f
    private var ringRight = 0f
    private var ringTop = 0f
    private var ringBottom = 0f

    /**
     * 是否可以loading ，默认false
     */
    private var isCanLoading = false

    /**
     * 是否自动切换状态 , 默认true
     */
    private var isAutoChange = true

    /**
     * button在end的 背景颜色
     */
    private var endColor = 0

    /**
     * button在start的 背景颜色
     */
    private var startColor = 0

    /**
     * 边框宽度px
     */
    private var borderWidth = 0

    /**
     * 按钮最左边
     */
    private var buttonMinX = 0f

    /**
     * 按钮最右边
     */
    private var buttonMaxX = 0f

    /**
     * 按钮画笔
     */
    private val buttonPaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            style = Paint.Style.FILL
        }
    }

    /**
     * 背景画笔
     */
    private val paint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG)
    }

    // 圆环背景颜色
    private var ringBgColor = 0

    // 进度颜色
    private var progressColor = 0

    private val buttonDuration = 100L
    private val loadingDuration = 1000L

    /**
     * 当前状态
     */
    private var viewState: ViewState = ViewState()
    private var afterState: ViewState = ViewState()
    private var beforeState: ViewState = ViewState()

    private var state: SwitchState? = null

    private var isButtonAnimating = false
    private val loadingAnimator by lazy {
        ValueAnimator.ofFloat(0f, 1f).apply {
            duration = loadingDuration
            repeatCount = ValueAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
        }
    }

    private val colorAnimator by lazy {
        ValueAnimator.ofFloat(0f, 1f).apply {
            duration = buttonDuration
            interpolator = AccelerateDecelerateInterpolator()
        }
    }

    private var animator: ValueAnimator? = null
    private val animatorSet by lazy { AnimatorSet() }

    private val argbEvaluator by lazy { ArgbEvaluator() }

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context, attrs)
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        super.setPadding(0, 0, 0, 0)
    }

    /**
     * 初始化参数
     */
    private fun init(context: Context, attrs: AttributeSet?) {
        var typedArray: TypedArray? = null
        if (attrs != null) {
            typedArray = context.obtainStyledAttributes(attrs, R.styleable.SwitchButton)
        }
        isCanLoading = optBoolean(typedArray, R.styleable.SwitchButton_switch_can_loading, false)
        isAutoChange = optBoolean(typedArray, R.styleable.SwitchButton_switch_auto_change, true)
        endColor = optColor(typedArray, R.styleable.SwitchButton_switch_end_color, ContextCompat.getColor(context, R.color.c15b374))
        startColor = optColor(typedArray, R.styleable.SwitchButton_switch_start_color, AttrResourceUtil.getColor(context, R.attr.color_c1f1e1e1e_c1fffffff))
        ringBgColor = optColor(typedArray, R.styleable.SwitchButton_switch_ring_color, AttrResourceUtil.getColor(context, R.attr.color_c1f1e1e1e_c1fffffff))
        progressColor = optColor(typedArray, R.styleable.SwitchButton_switch_progress_color, ContextCompat.getColor(context, R.color.c007fff))
        borderWidth = optPixelSize(typedArray, R.styleable.SwitchButton_switch_border_width, 3.dp2px())
        val stateInt = optInt(typedArray, R.styleable.SwitchButton_switch_state, 0)
        afterState.state = when (stateInt) {
            0 -> SwitchState.True
            1 -> SwitchState.False
            else -> SwitchState.Loading
        }
        viewState.copy(afterState)
        typedArray?.recycle()
        super.setClickable(true)
        setPadding(0, 0, 0, 0)
    }

    override fun onMeasure(widthMeasureSpecParam: Int, heightMeasureSpecParam: Int) {
        var widthMeasureSpec = widthMeasureSpecParam
        var heightMeasureSpec = heightMeasureSpecParam
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        if (widthMode == MeasureSpec.UNSPECIFIED || widthMode == MeasureSpec.AT_MOST) {
            widthMeasureSpec = MeasureSpec.makeMeasureSpec(DEFAULT_WIDTH, MeasureSpec.EXACTLY)
        }
        if (heightMode == MeasureSpec.UNSPECIFIED || heightMode == MeasureSpec.AT_MOST) {
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(DEFAULT_HEIGHT, MeasureSpec.EXACTLY)
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val viewPadding = borderWidth.toFloat()
        height = h - viewPadding - viewPadding
        width = w - viewPadding - viewPadding
        viewRadius = height * 0.5f
        buttonRadius = viewRadius - borderWidth
        left = viewPadding
        top = viewPadding
        right = w - viewPadding
        bottom = h - viewPadding
        centerX = (left + right) * 0.5f
        centerY = (top + bottom) * 0.5f
        ringLeft = centerX - buttonRadius - borderWidth
        ringRight = centerX + buttonRadius + borderWidth
        ringTop = centerY - buttonRadius - borderWidth
        ringBottom = centerY + buttonRadius + borderWidth
        buttonMinX = left + viewRadius
        buttonMaxX = right - viewRadius
        updateViewState(false)
        viewState.copy(afterState)
        invalidate()
    }

    private fun updateViewState(withAnimator: Boolean) {
        when (afterState.state) {
            SwitchState.True -> setEndViewState(withAnimator)
            SwitchState.False -> setStartViewState(withAnimator)
            else -> setLoadingViewState(withAnimator)
        }
    }

    private fun setStartViewState(withAnimator: Boolean) {
        afterState.checkStateColor = startColor
        afterState.buttonX = buttonMinX
        loadingAnimator.cancel()
        if (withAnimator) {
            setButtonProgress()
        }
    }

    private fun setEndViewState(withAnimator: Boolean) {
        afterState.checkStateColor = endColor
        afterState.buttonX = buttonMaxX
        loadingAnimator.cancel()
        if (withAnimator) {
            setButtonProgress()
        }
    }

    private fun setLoadingViewState(withAnimator: Boolean) {
        afterState.checkStateColor = ringBgColor
        afterState.buttonX = centerX
        if (withAnimator) {
            setLoadingProgress()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (afterState.state == SwitchState.Loading && !isButtonAnimating) {
            drawLoading(canvas)
            return
        }
        // 绘制开启背景色
        paint.style = Paint.Style.FILL
        paint.color = viewState.checkStateColor
        paint.strokeWidth = borderWidth.toFloat()
        if (isButtonAnimating) {
            if (beforeState.state == SwitchState.False) {
                drawRoundRect(canvas, viewState.buttonX - viewRadius - borderWidth, top, right - viewState.buttonX + viewRadius + borderWidth, bottom, viewRadius, paint)
            } else {
                drawRoundRect(canvas, centerX * 2 - viewState.buttonX - viewRadius, top, viewState.buttonX + viewRadius + borderWidth, bottom, viewRadius, paint)
            }
        } else {
            if (beforeState.state == SwitchState.Loading) {
                if (afterState.state == SwitchState.False) {
                    drawRoundRect(canvas, viewState.buttonX - viewRadius, top, right - viewState.buttonX + viewRadius + borderWidth, bottom, viewRadius, paint)
                } else {
                    drawRoundRect(canvas, centerX * 2 - viewState.buttonX - viewRadius, top, viewState.buttonX + viewRadius, bottom, viewRadius, paint)
                }
            } else {
                drawRoundRect(canvas, left, top, right, bottom, viewRadius, paint)
            }
        }
        // 绘制按钮
        drawButton(canvas, viewState.buttonX, centerY)
    }

    private fun drawLoading(canvas: Canvas) {
        paint.color = ringBgColor
        paint.strokeWidth = borderWidth.toFloat()
        canvas.drawArc(ringLeft, ringTop, ringRight, ringBottom, 0f, 360f, false, paint)
        paint.color = progressColor
        canvas.drawArc(ringLeft, ringTop, ringRight, ringBottom, -120f + viewState.progress * 360f, 80f, true, paint)
        drawButton(canvas, centerX, centerY)
    }

    private fun drawRoundRect(canvas: Canvas, left: Float, top: Float, right: Float, bottom: Float, backgroundRadius: Float, paint: Paint) {
        canvas.drawRoundRect(left, top, right, bottom, backgroundRadius, backgroundRadius, paint)
    }

    private fun drawButton(canvas: Canvas, x: Float, y: Float) {
        canvas.drawCircle(x, y, buttonRadius, buttonPaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        try {
            if (!isAutoChange) {
                return super.onTouchEvent(event)
            }
            gestureDetector.onTouchEvent(event)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true
    }

    /**
     * 外部设置状态，只允许设置 true 和 false , withAnimator 为true时，会有动画效果
     */
    fun setState(isCheck: Boolean?) {
        if ((if (isCheck == true) SwitchState.True else SwitchState.False) == viewState.state) {
            return
        }
        MainScope().launch {
            // 只有之前view的状态是loading时，才会延迟500ms执行动画，防止网络请求过快导致loading动画看不见
            if (beforeState.state == SwitchState.Loading) {
                delay(500)
            }
            beforeState.copy(viewState)
            afterState.state = if (isCheck == true) SwitchState.True else SwitchState.False
            updateViewState(true)
//            if (!withAnimator) {
//                beforeState.state = null
//            }
        }
    }

    /**
     * 获取当前状态, 当状态为loading时，返回null,仅获取view当前的状态，有可能因为获取时间以及动画执行时间导致获取状态不尽人意，该状态仅供参考
     */
    fun getState(): Boolean? = when (viewState.state) {
        SwitchState.True -> true
        SwitchState.False -> false
        else -> null
    }

    /**
     * 设置状态改变监听 , 只会返回 true 和 false 状态
     */
    fun setStateChangeListener(listener: (Boolean) -> Unit) {
        this.listener = listener
    }

    /**
     * 切换 loading 状态监听
     */
    fun setOnLoadingListener(loadingListener: () -> Unit) {
        this.loadingListener = loadingListener
    }

    /**
     * 是否展示loading
     */
    fun setIsCanLoading(isCanLoading: Boolean) {
        this.isCanLoading = isCanLoading
    }

    /**
     * 是否可以自动切换状态，如果需要自己控制状态，可以设置为false
     */
    fun setIsAutoChange(isAutoChange: Boolean) {
        this.isAutoChange = isAutoChange
    }

    private fun setPrivateState(state: SwitchState, withAnimator: Boolean = true) {
        if (beforeState.state == viewState.state) {
            return
        }
        beforeState.copy(viewState)
        afterState.state = state
        updateViewState(withAnimator)
    }

    /**
     * 保存动画状态
     */
    private class ViewState {

        /**
         * 按钮x位置[buttonMinX-buttonMaxX]
         */
        var buttonX = 0f

        /**
         * 状态背景颜色
         */
        var checkStateColor = 0

        var progress = 1f

        var state: SwitchState? = null

        fun copy(source: ViewState) {
            buttonX = source.buttonX
            checkStateColor = source.checkStateColor
            progress = source.progress
            state = source.state
        }
    }

    inner class GestureListener : GestureDetector.SimpleOnGestureListener() {

        override fun onDown(e: MotionEvent): Boolean {
            // 如果状态相同，则不做任何操作
            if (beforeState.state == viewState.state || viewState.state == SwitchState.Loading) {
                return true
            }
            if (isCanLoading) {
                isButtonAnimating = true
                setPrivateState(SwitchState.Loading)
//                GlobalScope.launch {
//                    delay(1500)
//                    MainScope().launch {
//                        if (beforeState.state == SwitchState.True) {
//                            setPrivateState(SwitchState.False)
//                        } else {
//                            setPrivateState(SwitchState.True)
//                        }
//                    }
//                }
            } else {
                if (viewState.state == SwitchState.True) {
                    setPrivateState(SwitchState.False)
                } else {
                    setPrivateState(SwitchState.True)
                }
            }
            return true
        }
    }

    /**
     * 设置进度，带动画
     */
    private fun setLoadingProgress() {
        loadingAnimator.addUpdateListener { animation ->
            viewState.progress = animation.animatedValue as Float
            invalidate()
            if (beforeState.state == SwitchState.Loading && viewState.state == afterState.state) {
                loadingAnimator.cancel()
            }
        }
        setButtonProgress {
            isButtonAnimating = false
            loadingAnimator.start()
        }
    }

    /**
     * 设置进度，带动画
     */
    private fun setButtonProgress(endFlow: (() -> Unit)? = null) {
        colorAnimator.addUpdateListener { animation ->
            viewState.checkStateColor = argbEvaluator.evaluate(
                animation.animatedValue as Float,
                beforeState.checkStateColor,
                afterState.checkStateColor
            ) as Int
        }
        animator = ValueAnimator.ofFloat(beforeState.buttonX, afterState.buttonX).apply {
            duration = buttonDuration
            interpolator = AccelerateDecelerateInterpolator()
            addUpdateListener { animation ->
                viewState.buttonX = animation.animatedValue as Float
                invalidate()
            }
        }
        animatorSet.addListener(onEnd = {
            endFlow?.invoke()
            viewState.copy(afterState)
            // 动画完毕，清除旧view的状态为null
            beforeState.state = null
            if (state != viewState.state) {
                state = viewState.state
                when (state) {
                    SwitchState.Loading -> loadingListener?.invoke()
                    else -> listener?.invoke(SwitchState.True == state)
                }
            }
        })
        animatorSet.playTogether(animator, colorAnimator)
        animatorSet.start()
    }

    private enum class SwitchState {
        True,
        False,
        Loading
    }

    companion object {

        private val DEFAULT_WIDTH = 58.dp2px()
        private val DEFAULT_HEIGHT = 32.dp2px()

        private fun optInt(typedArray: TypedArray?, index: Int, def: Int): Int = typedArray?.getInt(index, def) ?: def

        private fun optPixelSize(typedArray: TypedArray?, index: Int, def: Int): Int = typedArray?.getDimensionPixelOffset(index, def) ?: def

        private fun optColor(typedArray: TypedArray?, index: Int, def: Int): Int = typedArray?.getColor(index, def) ?: def

        private fun optBoolean(typedArray: TypedArray?, index: Int, def: Boolean): Boolean = typedArray?.getBoolean(index, def) ?: def
    }
}