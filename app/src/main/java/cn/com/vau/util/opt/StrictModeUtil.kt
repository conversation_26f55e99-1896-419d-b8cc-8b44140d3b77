package cn.com.vau.util.opt

import android.app.Application
import android.os.StrictMode
import android.os.StrictMode.ThreadPolicy
import android.os.StrictMode.VmPolicy
import cn.com.vau.BuildConfig

class StrictModeUtil {

    companion object{

        fun initStrictMode(context:Application){
            if(BuildConfig.DEBUG){
                StrictMode.setThreadPolicy(
                    ThreadPolicy.Builder()
                        .detectDiskReads()
                        .detectDiskWrites()
                        .detectNetwork() // or .detectAll() for all detectable problems
                        .penaltyLog()
                        .build()
                )
                StrictMode.setVmPolicy(
                    VmPolicy.Builder()
                        .detectLeakedSqlLiteObjects()
                        .detectLeakedClosableObjects()
                        .penaltyLog()
                        .build()
                )

            }
        }
    }
}