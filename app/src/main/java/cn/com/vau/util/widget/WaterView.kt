package cn.com.vau.util.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull

/**
 * 水印view
 */
class WaterView : View {
    private var waterName: String = "aaaaaa" //需要回传的值
    private val paint by lazy {
        Paint().apply {
            textSize = 12f.dp2px()
            style = Paint.Style.FILL
            isAntiAlias = true
            color = ContextCompat.getColor(context, R.color.c1fffffff)
        }
    }

    constructor(context: Context?, waterName: String?) : super(context) {
        this.waterName = waterName.ifNull()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setMeasuredDimension(widthMeasureSpec, heightMeasureSpec)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes)

    override fun onDraw(canvas: Canvas) {
        canvas.save()
        // 以文字中心轴旋转
        canvas.rotate(-20f, this.width / 2f, this.height / 2f)

        for (i in 0..4) {
            canvas.drawText(waterName + "", this.width / 4.5f - (this.width / 12f * i), this.height / 5f * i + (this.height / 4f) / 2, paint)
        }
        for (i in 0..5) {
            canvas.drawText(waterName + "", this.width / 2f - (this.width / 12f * i), this.height / 5f * i + (this.height / 4f) / 2 * 2, paint)
        }
        for (i in 0..4) {
            canvas.drawText(waterName + "", this.width / 1.25f - (this.width / 12f * i), this.height / 5f * i + (this.height / 4f) / 2 * 3, paint)
        }
        for (i in 0..5) {
            canvas.drawText(waterName + "", this.width - (this.width / 12f * i), this.height / 5f * i + (this.height / 4f) / 2 * 4, paint)
        }
        for (i in 0..5) {
            canvas.drawText(waterName + "", this.width * 1.5f - (this.width / 12f * i), this.height / 5f * i + (this.height / 4f) / 2 * 6, paint)
        }

        super.onDraw(canvas)
    }

    fun setWaterName(waterName: String?) {
        this.waterName = waterName.ifNull()
//        invalidate()
    }
}