package cn.com.vau.util.widget.webview.base

import android.graphics.Bitmap
import android.net.Uri
import android.os.Message
import android.view.View
import android.webkit.ConsoleMessage
import android.webkit.GeolocationPermissions
import android.webkit.JsPromptResult
import android.webkit.JsResult
import android.webkit.PermissionRequest
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebStorage.QuotaUpdater
import android.webkit.WebView

open class BaseWebChromeClient : WebChromeClient {
    protected var originClient: WebChromeClient? = null

    constructor()

    constructor(webChromeClient: WebChromeClient?) {
        this.originClient = webChromeClient
    }

    override fun onProgressChanged(view: WebView?, newProgress: Int) {
        if (originClient != null) {
            originClient?.onProgressChanged(view, newProgress)
            return
        }
        super.onProgressChanged(view, newProgress)
    }

    override fun onReceivedTitle(view: WebView?, title: String?) {
        if (originClient != null) {
            originClient?.onReceivedTitle(view, title)
            return
        }
        super.onReceivedTitle(view, title)
    }


    override fun onReceivedIcon(view: WebView?, icon: Bitmap?) {
        if (originClient != null) {
            originClient?.onReceivedIcon(view, icon)
            return
        }
        super.onReceivedIcon(view, icon)
    }


    override fun onReceivedTouchIconUrl(view: WebView?, url: String?, precomposed: Boolean) {
        if (originClient != null) {
            originClient?.onReceivedTouchIconUrl(view, url, precomposed)
            return
        }
        super.onReceivedTouchIconUrl(view, url, precomposed)
    }


    override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
        if (originClient != null) {
            originClient?.onShowCustomView(view, callback)
            return
        }
        super.onShowCustomView(view, callback)
    }

    override fun onShowCustomView(
        view: View?,
        requestedOrientation: Int,
        callback: CustomViewCallback?
    ) {
        if (originClient != null) {
            originClient?.onShowCustomView(view, requestedOrientation, callback)
            return
        }
        super.onShowCustomView(view, requestedOrientation, callback)
    }

    override fun onHideCustomView() {
        if (originClient != null) {
            originClient?.onHideCustomView()
            return
        }
        super.onHideCustomView()
    }

    override fun onCreateWindow(
        view: WebView?,
        isDialog: Boolean,
        isUserGesture: Boolean,
        resultMsg: Message?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onCreateWindow(view, isDialog, isUserGesture, resultMsg)?: false
        }
        return super.onCreateWindow(view, isDialog, isUserGesture, resultMsg)
    }

    override fun onRequestFocus(view: WebView?) {
        if (originClient != null) {
            originClient?.onRequestFocus(view)
            return
        }
        super.onRequestFocus(view)
    }

    override fun onCloseWindow(window: WebView?) {
        if (originClient != null) {
            originClient?.onCloseWindow(window)
            return
        }
        super.onCloseWindow(window)
    }

    override fun onJsAlert(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onJsAlert(view, url, message, result) ?: false
        }
        return super.onJsAlert(view, url, message, result)
    }

    override fun onJsConfirm(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onJsConfirm(view, url, message, result) ?: false
        }
        return super.onJsConfirm(view, url, message, result)
    }

    override fun onJsPrompt(
        view: WebView?,
        url: String?,
        message: String?,
        defaultValue: String?,
        result: JsPromptResult?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onJsPrompt(view, url, message, defaultValue, result) ?: false
        }
        return super.onJsPrompt(view, url, message, defaultValue, result)
    }

    override fun onJsBeforeUnload(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onJsBeforeUnload(view, url, message, result) ?: false
        }
        return super.onJsBeforeUnload(view, url, message, result)
    }

    override fun onExceededDatabaseQuota(
        url: String?,
        databaseIdentifier: String?,
        quota: Long,
        estimatedDatabaseSize: Long,
        totalQuota: Long,
        quotaUpdater: QuotaUpdater?
    ) {
        if (originClient != null) {
            originClient?.onExceededDatabaseQuota(
                url,
                databaseIdentifier,
                quota,
                estimatedDatabaseSize,
                totalQuota,
                quotaUpdater
            )
            return
        }
        super.onExceededDatabaseQuota(
            url,
            databaseIdentifier,
            quota,
            estimatedDatabaseSize,
            totalQuota,
            quotaUpdater
        )
    }

    override fun onGeolocationPermissionsShowPrompt(
        origin: String?,
        callback: GeolocationPermissions.Callback?
    ) {
        if (originClient != null) {
            originClient?.onGeolocationPermissionsShowPrompt(origin, callback)
            return
        }
        super.onGeolocationPermissionsShowPrompt(origin, callback)
    }

    override fun onGeolocationPermissionsHidePrompt() {
        if (originClient != null) {
            originClient?.onGeolocationPermissionsHidePrompt()
            return
        }
        super.onGeolocationPermissionsHidePrompt()
    }

    override fun onPermissionRequest(request: PermissionRequest?) {
        if (originClient != null) {
            originClient?.onPermissionRequest(request)
            return
        }
        super.onPermissionRequest(request)
    }

    override fun onPermissionRequestCanceled(request: PermissionRequest?) {
        if (originClient != null) {
            originClient?.onPermissionRequestCanceled(request)
            return
        }
        super.onPermissionRequestCanceled(request)
    }

    override fun onJsTimeout(): Boolean {
        if (originClient != null) {
            return originClient?.onJsTimeout()?: false
        }
        return super.onJsTimeout()
    }

    override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
        if (originClient != null) {
            return originClient?.onConsoleMessage(consoleMessage) ?: false
        }
        return super.onConsoleMessage(consoleMessage)
    }

    override fun onConsoleMessage(message: String?, lineNumber: Int, sourceID: String?) {
        if (originClient != null) {
            originClient?.onConsoleMessage(message, lineNumber, sourceID)
            return
        }
        super.onConsoleMessage(message, lineNumber, sourceID)
    }

    override fun getDefaultVideoPoster(): Bitmap? {
        if (originClient != null) {
            return originClient?.defaultVideoPoster
        }
        return super.getDefaultVideoPoster()
    }

    override fun getVideoLoadingProgressView(): View? {
        if (originClient != null) {
            return originClient?.videoLoadingProgressView
        }
        return super.getVideoLoadingProgressView()
    }

    override fun getVisitedHistory(callback: ValueCallback<Array<String>>?) {
        if (originClient != null) {
            originClient?.getVisitedHistory(callback)
            return
        }
        super.getVisitedHistory(callback)
    }


    override fun onShowFileChooser(
        webView: WebView?,
        filePathCallback: ValueCallback<Array<Uri>>?,
        fileChooserParams: FileChooserParams?
    ): Boolean {
        if (originClient != null) {
            return originClient?.onShowFileChooser(webView, filePathCallback, fileChooserParams) ?: false
        }
        return super.onShowFileChooser(webView, filePathCallback, fileChooserParams)
    }
}
