package cn.com.vau.util.widget.dialog.anim

object PopupAnimationStrategyFactory {

    fun getStrategy(animationType: PopupAnimationType?): IPopupAnimationStrategy? {
        return when (animationType) {
            PopupAnimationType.ScaleAlphaFromCenter -> ScaleAlphaFromCenterStrategy()
            PopupAnimationType.ScaleAlphaFromLeftTop -> ScaleAlphaFromLeftTopStrategy()
            PopupAnimationType.ScaleAlphaFromRightTop -> ScaleAlphaFromRightTopStrategy()
            PopupAnimationType.ScaleAlphaFromLeftBottom -> ScaleAlphaFromLeftBottomStrategy()
            PopupAnimationType.ScaleAlphaFromRightBottom -> ScaleAlphaFromRightBottomStrategy()
            PopupAnimationType.TranslateAlphaFromLeft -> TranslateAlphaFromLeftStrategy()
            PopupAnimationType.ScaleAlphaFromTopRight -> TranslateAlphaFromRightStrategy()
            PopupAnimationType.ScaleAlphaFromBottomLeft -> TranslateAlphaFromTopStrategy()
            PopupAnimationType.TranslateAlphaFromBottom -> TranslateAlphaFromBottomStrategy()
            PopupAnimationType.TranslateFromLeft -> TranslateFromLeftStrategy()
            PopupAnimationType.TranslateFromRight -> TranslateFromRightStrategy()
            PopupAnimationType.TranslateFromTop -> TranslateFromTopStrategy()
            PopupAnimationType.TranslateFromBottom -> TranslateFromBottomStrategy()
            PopupAnimationType.NoAnimation -> NoAnimationStrategy()
            else -> null
        }
    }
}