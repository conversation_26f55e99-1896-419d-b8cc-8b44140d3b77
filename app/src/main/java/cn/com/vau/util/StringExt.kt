package cn.com.vau.util

import android.content.*
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import androidx.annotation.ColorInt
import cn.com.vau.R
import cn.com.vau.util.language.LanguageHelper
import java.util.*
import java.util.regex.Pattern

/**
 * @description: string 相关的拓展函数
 * @author: GG
 * @createDate: 2024 11月 29 15:06
 * @updateUser:
 * @updateDate: 2024 11月 29 15:06
 */

/**
 * 文本复制，可设置成功后的toast，默认为空 不弹出
 */
fun CharSequence?.copyText(toast: String? = null) {
    val cm = UtilApp.getApp().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager ?: return ToastUtil.showToast(StringUtil.getString(R.string.failed))
    // 创建普通字符型ClipData
    val mClipData = ClipData.newPlainText(UtilApp.getApp().packageName, this)
    // 将ClipData内容放到系统剪贴板里。
    cm.setPrimaryClip(mClipData)
    toast?.let {
        ToastUtil.showToast(it)
    }
}

private const val POSITIVE = "+"
private const val NEGATIVE = "-"
private const val GREATER_EQUAL = ">="
private const val LESS_EQUAL = "<="
private const val GREATER = ">"
private const val LESS = "<"

/**
 * 适配阿拉伯语
 *
 * 支持这样的字符串
 *  -0.1 ， +80  ， <100 ,  >100  , <=30  ， -$200， USDJPY+
 *
 * 不支持这样的字符串：
 *  不支持：“-2.0 USD”    如果想这样的，你可以这样做
 *                      val s = “-2.0”.arabicText()
 *                      val result = "$s USD"
 */
fun String?.arabicText(locale: Locale = LanguageHelper.getAppLocale()): String? {
    if (this == null) {
        return null
    }
    if (TextUtils.isEmpty(this)) {
        return this
    }
    if (this.length <= 1) {
        return this
    }
    val isArabic = locale.language == "ar" || locale.language == "ara"
    if (!isArabic) {
        //非Arabic不处理
        return this
    }
    try {
        val prev0 = this.substring(0, 1)
        if (prev0 == POSITIVE) { //正数
            return "${this.removePrefix(POSITIVE)}$POSITIVE"
        } else if (prev0 == NEGATIVE) { //负数
            return "${this.removePrefix(NEGATIVE)}$NEGATIVE"
        }

        //该判断必须要放在判断 >  < 的前面
        val prev1 = this.substring(0, 2)
        if (prev1 == GREATER_EQUAL) {
            return "=<${this.removePrefix(GREATER_EQUAL)}"
        } else if (prev1 == LESS_EQUAL) {
            return "=>${this.removePrefix(LESS_EQUAL)}"
        }

        if (prev0 == GREATER) {
            return "<${this.removePrefix(GREATER)}"
        } else if (prev0 == LESS) {
            return ">${this.removePrefix(LESS)}"
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return this
}

/**
 * 适配阿拉伯语时间
 * 例如  2024/01/10 - 2024/01/15    那么你应该这样使用 "2024/01/10 - 2024/01/15".arabicReverseTextByFlag(flag = "-")
 *
 */
fun String?.arabicReverseTextByFlag(flag: String, locale: Locale = LanguageHelper.getAppLocale()): String? {
    if (this == null) {
        return null
    }
    if (TextUtils.isEmpty(this)) {
        return this
    }
    if (this.length <= 1) {
        return this
    }
    val isArabic = locale.language == "ar" || locale.language == "ara"
    if (!isArabic) {
        //非Arabic不处理
        return this
    }
    try {
        // 通过空格分隔字符串
        val words = this.split(flag)
        // 将单词列表倒序
        val reversedWords = words.reversed()
        // 将倒序后的单词列表拼接成新的字符串
        return reversedWords.joinToString(flag)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return this
}


/**
 * 匹配搜索条件
 */
fun String?.matcherSearchText(keyword: String, @ColorInt color: Int): SpannableString {
    if (this.isNullOrBlank()) {
        return SpannableString("")
    }
    val spannableString = SpannableString(this)
    val pattern = Pattern.compile(Pattern.quote(keyword), Pattern.CASE_INSENSITIVE)
    val matcher = pattern.matcher(spannableString)
    while (matcher.find()) {
        val start = matcher.start()
        val end = matcher.end()
        spannableString.setSpan(ForegroundColorSpan(color), start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE)
    }
    return spannableString
}
