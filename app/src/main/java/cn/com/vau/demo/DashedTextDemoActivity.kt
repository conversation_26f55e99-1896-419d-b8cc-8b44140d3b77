package cn.com.vau.demo

import android.content.res.Resources
import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.util.*
import cn.com.vau.util.span.DashedUnderlineSpan
import cn.com.vau.util.widget.DashLineTextView

/**
 * 将dp转换为px
 */
private fun Float.dpToPx(): Float = this * Resources.getSystem().displayMetrics.density

/**
 * 虚线下划线TextView演示Activity
 * 
 * 演示功能：
 * 1. 整个TextView设置虚线下划线
 * 2. 局部文字设置虚线下划线
 * 3. 不同对齐方式的虚线效果
 * 4. 自定义虚线样式
 */
class DashedTextDemoActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_dashed_text_demo)
        
        setupDemoViews()
    }

    private fun setupDemoViews() {
        // 示例1：整个TextView设置虚线下划线
        setupWholeTextDashedExample()
        
        // 示例2：局部文字设置虚线下划线
        setupPartialTextDashedExample()
        
        // 示例3：不同对齐方式的虚线效果
        setupAlignmentExample()
        
        // 示例4：自定义虚线样式
        setupCustomStyleExample()
        
        // 示例5：使用DSL构建复杂文本
        setupDSLExample()

        // 示例6：点击冲突处理演示
        setupClickConflictExample()


    }

    private fun setupWholeTextDashedExample() {
        // 使用DashLineTextView
        val dashLineTextView = findViewById<DashLineTextView>(R.id.tvEnhancedDashed)
        dashLineTextView?.apply {
            text = "这是一个完整的虚线下划线文本示例（点击虚线试试）"
            setDashColor(ContextCompat.getColor(this@DashedTextDemoActivity, R.color.colorPrimary))
            setDashLength(6f.dpToPx())
            setDashGap(3f.dpToPx())
            setDashOffsetFromBaseline(2f.dpToPx()) // DESCENT对齐下的合理偏移量

            // 设置整个TextView的点击事件
            setOnClickListener {
                Toast.makeText(this@DashedTextDemoActivity, "整个TextView被点击了！", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupPartialTextDashedExample() {
        val textView = findViewById<TextView>(R.id.tvPartialDashed)
        textView?.apply {
            text = "这里有一些普通文字，这里是虚线文字，还有更多普通文字"

            // 先设置TextView的整体点击事件
            setOnClickListener {
                Toast.makeText(this@DashedTextDemoActivity, "TextView被点击了！", Toast.LENGTH_SHORT).show()
            }

            // 设置虚线文字的样式
            setDashedUnderline(
                targetText = "虚线文字",
                dashColor = Color.RED,
                dashLength = 4f.dpToPx(),
                dashGap = 2f.dpToPx(),
                offsetFromBaseline = 2f.dpToPx(), // DESCENT对齐下的合理偏移量
                alignment = DashedUnderlineSpan.DashAlignment.DESCENT // 使用DESCENT对齐
            )
        }
    }

    private fun setupAlignmentExample() {
        // Baseline对齐（推荐默认）
        val tvBaseline = findViewById<TextView>(R.id.tvBaselineAlignment)
        tvBaseline?.apply {
            text = "基线对齐的虚线下划线（推荐默认）"
            setDashedUnderline(
                targetText = "基线对齐",
                dashColor = Color.BLUE,
                alignment = DashedUnderlineSpan.DashAlignment.BASELINE
            )
        }

        // Descent对齐（适用于包含下降字符）
        val tvDescent = findViewById<TextView>(R.id.tvDescentAlignment)
        tvDescent?.apply {
            text = "Typography gjpqy - Descent对齐避免重叠"
            setDashedUnderline(
                targetText = "Typography gjpqy",
                dashColor = Color.GREEN,
                alignment = DashedUnderlineSpan.DashAlignment.DESCENT
            )
        }

        // Bottom对齐（适用于需要更大间距）
        val tvBottom = findViewById<TextView>(R.id.tvBottomAlignment)
        tvBottom?.apply {
            text = "Bottom对齐提供更大间距效果"
            setDashedUnderline(
                targetText = "Bottom对齐",
                dashColor = Color.MAGENTA,
                alignment = DashedUnderlineSpan.DashAlignment.BOTTOM
            )
        }
    }

    private fun setupCustomStyleExample() {
        val textView = findViewById<TextView>(R.id.tvCustomStyle)
        textView?.apply {
            text = "自定义样式：粗虚线、长间隔、圆角端点"
            
            setDashedUnderline(
                targetText = "自定义样式",
                dashColor = Color.parseColor("#FF6B35"),
                dashStrokeWidth = 3f.dpToPx(),
                dashLength = 8f.dpToPx(),
                dashGap = 6f.dpToPx(),
                offsetFromBaseline = 4f.dpToPx()
            )
        }
    }

    private fun setupDSLExample() {
        val textView = findViewById<TextView>(R.id.tvDSLExample)
        textView?.apply {
            text = buildDashedText {
                text("这是")
                dashedText(
                    text = "第一段虚线",
                    dashColor = Color.RED,
                    dashLength = 3f.dpToPx(),
                    dashGap = 2f.dpToPx()
                )
                text("，这是")
                dashedText(
                    text = "第二段虚线",
                    dashColor = Color.BLUE,
                    dashLength = 6f.dpToPx(),
                    dashGap = 4f.dpToPx(),
                    alignment = DashedUnderlineSpan.DashAlignment.DESCENT
                )
                text("，结束。")
            }
        }
    }

    // 动态修改示例
    private fun setupDynamicExample() {
        val enhancedTextView = findViewById<DashLineTextView>(R.id.tvEnhancedDashed)
        
        // 可以动态修改虚线属性
        enhancedTextView?.apply {
            // 修改虚线颜色
            setDashColor(Color.CYAN)
            
            // 修改虚线样式
            setDashLength(10f.dpToPx())
            setDashGap(5f.dpToPx())
            
            // 修改对齐方式
            setDashAlignment(DashLineTextView.DashAlignment.DESCENT)
            
            // 启用/禁用虚线
            setDashEnabled(true)
            
            // 修改端点样式
            setDashCap(Paint.Cap.SQUARE)
        }
    }

    // 多个文字片段设置虚线的示例
    private fun setupMultipleTargetsExample() {
        val textView = findViewById<TextView>(R.id.tvMultipleTargets)
        textView?.apply {
            text = "第一个虚线文字，第二个虚线文字，第三个虚线文字"
            
            setDashedUnderlineMultiple(
                targets = listOf("第一个", "第二个", "第三个"),
                dashColor = Color.parseColor("#4CAF50"),
                dashLength = 4f.dpToPx(),
                dashGap = 3f.dpToPx()
            )
        }
    }

    private fun setupClickConflictExample() {
        // 演示简单的TextView点击
        val textView = findViewById<TextView>(R.id.tvClickConflict)
        textView?.apply {
            text = "这是一个带虚线的TextView示例，点击整个TextView会触发点击事件"

            // 设置TextView的整体点击事件
            setOnClickListener {
                Toast.makeText(this@DashedTextDemoActivity, "带虚线的TextView被点击了！", Toast.LENGTH_SHORT).show()
            }

            // 为"虚线"设置虚线下划线样式
            setDashedUnderline(
                targetText = "虚线",
                dashColor = Color.parseColor("#FF9800")
            )
        }
    }



}
