package cn.com.vau.common.http

import cn.com.vau.BuildConfig
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.page.setting.activity.SwitchLineActivity
import cn.com.vau.util.LogUtil

/**
 * Created by roy on 2018/10/15.
 */
object HttpUrl {

    // 正式环境开关
    const val official = true

    @JvmField
    var isSwitchTradingView = true

    // 默认为 true，只有直接给予客户安装包时为 false
    var isAppStore = true

    // 行情刷新频率
    const val MARKET_HZ = 400L

    var Md5Salt = "1457929724" + "SJHKJ1590405016hytech"

    // AppsFlyer appsKey
    const val appsFlyerKey = "uiVRqgqpLLfTJ7B6bd3gB5"

    // 获取测试环境 非交易切换线路 索引
    private val noTradeUrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_NO_TRADE, 0)
    }

    private val tradeUrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_TRADE, 0)
    }

    private val stTradeUrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_ST_TRADE, 0)
    }

    private val wsUrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_WS, 0)
    }

    private val stWsUrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_ST_WS, 0)
    }

    private val h5UrlSelectedIndex: Int by lazy {
        SpManager.getSwitchHttpUrlIndex(SwitchLineActivity.TYPE_H5, 0)
    }

    // AU 非交易线路集合
    val testNoTradeUrls = listOf(
        "https://hgw-au-one.app-alpha.com:18008/",
        "https://hgw-au-two.app-alpha.com:18008/",
        "https://hgw-au-three.app-alpha.com:18008/",
        "https://hgw-au-four.app-alpha.com:18008/",
        "https://hgw-au-five.app-alpha.com:18008/",
        "https://hgw-au-six.app-alpha.com:18008/",
        "https://hgw-au-seven.app-alpha.com:18008/",
        "https://hgw-au-eight.app-alpha.com:18008/",
        "https://hgw-au-nine.app-alpha.com:18008/",
        "https://hgw-au-ten.app-alpha.com:18008/",
        "https://hgw-au-eleven.app-alpha.com:18008/",
        "https://hgw-sgp-au1.app-alpha.com:18008/",
        "https://hgw-au-pcs01.app-alpha.com:18008/"
    )

    val testTradeUrls = listOf(
        "https://au-one.app-alpha.com:18008/",
        "https://sgp-au1.app-alpha.com:18008/"
    )

    val testStTradeUrls = listOf(
        "https://st-app.app-alpha.com:8088/stTradeApp/",
        "http://stapp-sg.app-alpha.com/stTradeApp/"
    )

    val testWsUrls = listOf(
        "wss://au-one.app-alpha.com:18008/websocket",
        "wss://sgp-au1.app-alpha.com:18008/websocket",
    )

    val testStWsUrls = listOf(
        "wss://st-app.app-alpha.com:18443/websocket",
        "wss://st-sgp-app.app-alpha.com:18443/websocket",
    )

    val testH5Urls = listOf(
        "https://au-one.app-alpha.com:18008/",
        "https://au-two.app-alpha.com:18008/",
        "https://au-three.app-alpha.com:18008/",
        "https://sgp-au1.app-alpha.com:18008/",
    )

    // h5 (355 cdn加速 替换h5主域名，只修改了生产环境)
    var BaseHtmlUrl = if (official) "https://h5.vantagemarketapp.com/" else getTestH5Url()

    // h5 地址前缀
    const val htmlUrlPrefix = "h5/feature"

    // 非交易
    var BaseUrl = if (official) "https://app.vttechfx.com:18008/vau/" else "${getTestNoTradeUrl()}vau/"

    // 交易
    var BaseTradingUrl = if (official) "https://app.vttechfx.com:18008/" else getTestTradeUrl()

    // WebSocket
    var WebSocketUrl = if (official) "wss://app.vttechfx.com:18008/websocket" else getWsUrl()

    // StWebSocket
    var StWebSocketUrl = if (official) "wss://stapp.vttechfx.com:16443/websocket" else getStWsUrl()

    // 跟单
    var BaseStTradingUrl = if (official) "https://stapp.vttechfx.com:16443/stTradeApp/" else getTestStTradeUrl()

    var stWsApiKey = if (official) {
        "mK9fz2Fhk10N3jP3zo0Q5"
    } else {
        "NVinBh7e0JbPka7G0gfEhHNHGGWg2RkvwXXyGxO7"
    }

    var officialWebUrl = "https://www.vantagemarkets.com/"

    // 易盾
    var netEaseID = if (official) "de7c8d31766f4aaa9790cbe95b067b11" else "4c1673902c2a4664b84eacc55a41f1bb"

    //神策埋点上报地址
    var SA_SERVER_URL = if (official) "https://data.theloudclan.com/sa?project=Vantage" else "https://test-data.clouddashboard123.com/sa?project=default"

    // 风控orgId
    var TmxOrgId = if (official) "96eziru5" else "9552phxn"

    // x-source 秘钥
    var X_SOURCE_SECRET_KEY = if (official) "503905cf15f041d68dcca2c3d0775791c933a9df72aa4be0883f6f68d3a08a46" else "146584f90d574e5bb8aaf951ad1a8b61724121cc7a624a9a82a839c174903c09"

    // AU 应用测试环境非交易线路（只切换非交易 1~6）
    private fun getTestNoTradeUrl(): String {
        LogUtil.w("noTradeUrlSelectedIndex----?$noTradeUrlSelectedIndex")
        return testNoTradeUrls.getOrElse(noTradeUrlSelectedIndex) { "https://au-one.app-alpha.com:18008/vau/" }
    }

    // AU 应用测试环境非交易线路（只切换非交易 1~6）
    fun getTestH5Url(): String {
        return when (h5UrlSelectedIndex) {
            -1 -> SpManager.getCustomH5Url()
            else -> testH5Urls.getOrElse(h5UrlSelectedIndex) { "https://au-one.app-alpha.com:18008/" }
        }
    }

    private fun getTestTradeUrl(): String {
        return testTradeUrls.getOrElse(tradeUrlSelectedIndex) { "https://au-one.app-alpha.com:18008/" }
    }

    private fun getTestStTradeUrl(): String {
        return testStTradeUrls.getOrElse(stTradeUrlSelectedIndex) { "https://st-app.app-alpha.com:8088/stTradeApp/" }
    }

    private fun getWsUrl(): String {
        return testWsUrls.getOrElse(wsUrlSelectedIndex) { "wss://au-one.app-alpha.com:18008/websocket" }
    }

    private fun getStWsUrl(): String {
        return testStWsUrls.getOrElse(stWsUrlSelectedIndex) { "wss://st-app.app-alpha.com:18443/websocket" }
    }

    // 应用双域名（测试环境  不开放）
    fun applyBaseUrl() {
        if (!official) return
        val serverBaseUrlData = if (official) SpManager.getServerBaseUrlProd() else SpManager.getServerBaseUrlTest()

        if (serverBaseUrlData != null && serverBaseUrlData.data?.obj != null) {
            val info = serverBaseUrlData.data?.obj
            val bean = when (info?.recommend) {
                "abroad" -> info.abroad
                "domestic" -> info.domestic
                else -> null
            }
            val isSt = UserDataUtil.isStLogin()
            val isMt5 = UserDataUtil.isMT5()
            bean?.h5?.let { BaseHtmlUrl = it }
            bean?.tradingSt?.let { BaseStTradingUrl = it }
            if (isSt) {
                //                LogUtil.d("wj", "applyBaseUrl: 更新了跟单相关url");
                bean?.nontradingSt?.let { BaseUrl = it }
                bean?.tradingMt4?.let { BaseTradingUrl = it }
                bean?.websocketapikeyStmts?.let { stWsApiKey = it }
            } else if (isMt5) {
                //                LogUtil.d("wj", "applyBaseUrl: 更新了mt5相关url");
                bean?.nontradingMt5?.let { BaseUrl = it }
                bean?.tradingMt5?.let { BaseTradingUrl = it }
                bean?.websocketNost?.let { WebSocketUrl = it }
            } else {
                //                LogUtil.d("wj", "applyBaseUrl: 更新了mt4相关url");
                bean?.nontradingMt4?.let { BaseUrl = it }
                bean?.tradingMt4?.let { BaseTradingUrl = it }
                bean?.websocketNost?.let { WebSocketUrl = it }
            }
        }
    }

}