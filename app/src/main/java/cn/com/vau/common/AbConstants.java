package cn.com.vau.common;

public class AbConstants {

    //交易（首页）模块
    public static class Trade {

    }

    public static class Order {
    }

    public static class App {
        public static final String AB_KEY_OPT_PRODUCTLIST_WEBSOCKET_PARALLEL = "opt_productlist_websocket_parallel";
        public static final String AB_KEY_OPT_EXPAND_TO_THREAD_LOCAL= "opt_expand_to_thread_local";

    }

    public static class Kline {
        public static final String AB_KEY_OPT_ENABLE_NEW_KLINE= "opt_enable_new_kline";
    }

    public static class Account {
        public static final String AB_KEY_SWITCH_ACCOUNT_CLEAR_ORDERLIST = "switch_account_clear_orderlist";
        public static final String AB_KEY_SWITCH_ACCOUNT_CLEAR_ACCOUNT = "switch_account_clear_account";
    }

    public static class WebView {
        public static final String AB_KEY_GLOBAL_WEBVIEW_POOL = "global_webview_pool_v3";
        public static final String AB_KEY_PRELOAD_URL = "is_preload_url";
        public static final String AB_KEY_OFFLINE_RESOURCE = "is_offline_resource";
    }

}
