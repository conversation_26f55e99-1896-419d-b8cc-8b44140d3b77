package cn.com.vau.common.utils.network

import android.net.ConnectivityManager.NetworkCallback
import android.net.Network
import android.net.NetworkCapabilities
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.utils.HandlerUtil
import cn.com.vau.common.utils.network.NetworkConnectMonitor.getNetworkStatus
import org.greenrobot.eventbus.EventBus

class NetworkConnectListener : NetworkCallback() {

    // 当前网络连接状态 默认为true  网络连接成功 在初始化的时候 会设置当前状态
    var currentState = true

    // 网络连接成功回调
    override fun onAvailable(network: Network) {
        super.onAvailable(network)
        val status = getNetworkStatus()
        if (status != -1 && !currentState) {
            currentState = true
//            LogUtil.d("wj", "网络连接可用: ${if (status == 1) "wifi" else "蜂窝"}")
            EventBus.getDefault().post(NoticeConstants.NETWORK_AVAILABLE)
        }
    }

    // 网络已断开连接
    override fun onLost(network: Network) {
        super.onLost(network)
        HandlerUtil().postDelayTask(500) {
            val status = getNetworkStatus()
            if (status == -1 && currentState) {
//                LogUtil.d("wj", "网络连接不可用")
                currentState = false
                EventBus.getDefault().post(NoticeConstants.NETWORK_LOST)
            }
        }
    }

    // 网络状态变化
    override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
        super.onCapabilitiesChanged(network, networkCapabilities)
        if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
            when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
//                    LogUtil.d("wj", "网络连接改变为：wifi")
                }

                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
//                    LogUtil.d("wj", "网络连接改变为：蜂窝")
                }

                else -> {
//                    LogUtil.d("wj", "网络连接改变为：其他")
                }
            }
        }

    }
}