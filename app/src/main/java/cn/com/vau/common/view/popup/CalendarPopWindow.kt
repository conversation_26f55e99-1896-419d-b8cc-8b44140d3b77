package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.DbManager
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.databinding.PopCalendarDealLogBinding
import cn.com.vau.ui.common.CalendarPopData
import cn.com.vau.ui.common.DateEntity
import cn.com.vau.util.*
import java.util.Calendar
import java.util.Date

/**
 * Created by roy on 2018/12/5.
 * 账户活动 -- 选择日期
 */
class CalendarPopWindow(val context: Context) : PopupWindow() {

    private var adapter: PopCalendarRcyAdapter? = null
    private val dataList by lazy { arrayListOf<CalendarPopData>() }
    private var selectDateStr = ""
    private val popView by lazy { PopCalendarDealLogBinding.inflate(LayoutInflater.from(context)) }

    init {
        initParam()
        initView()
        initListener()
    }

    private fun initParam() {
        this.contentView = popView.root
        this.width = screenWidth
        this.height = ViewGroup.LayoutParams.MATCH_PARENT
        this.isClippingEnabled = false
        this.isFocusable = false
        this.animationStyle = R.style.popupAnimStyleRight

        initDateData()

    }

    private fun initDateData() {

        dataList.clear()

        val currentDateStr = PickerDateUtil.longTimeToString(System.currentTimeMillis(), "dd/MM/yyyy")
        val twoDateBean = CalendarUtil.getInstance().getMonthDiffBefore(currentDateStr, -2)
        val tLastDay = CalendarUtil.getInstance().getMonthLastDay(twoDateBean.year, twoDateBean.mon)
        val tList = getWeekList()

        val tFirstDayWeek = CalendarUtil.getInstance().getDayOfWeek(twoDateBean.likeDate, "dd/MM/yyyy")
        if (tFirstDayWeek != 1) {
            for (weekIndex in 1 until tFirstDayWeek) tList.add(DateEntity(""))
        }
        for (index in twoDateBean.day..tLastDay) {
            val likeDateStr = "${index.fill0()}/${twoDateBean.mon.fill0()}/${twoDateBean.year}"
            val dataList = DbManager.getInstance().getDealLogList(likeDateStr)
            tList.add(DateEntity(if (dataList.size > 0) 1 else 0, index.toString(), likeDateStr))
        }
        val tLastDayWeek = CalendarUtil.getInstance().getDayOfWeek("$tLastDay/${twoDateBean.mon.fill0()}/${twoDateBean.year}", "dd/MM/yyyy")
        if (tLastDayWeek != 7) {
            var fillDate = 1
            for (weekIndex in tLastDayWeek + 1..7) {
                tList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }
        dataList.add(CalendarPopData(CalendarUtil.getInstance().getFormatYearMon(twoDateBean), tList))

        val oneDateBean = CalendarUtil.getInstance().getMonthDiffBefore(currentDateStr, -1)
        val oLastDay = CalendarUtil.getInstance().getMonthLastDay(oneDateBean.year, oneDateBean.mon)

        val oList = getWeekList()
        val oFirstDayWeek = CalendarUtil.getInstance().getDayOfWeek("01/${oneDateBean.mon.fill0()}/${oneDateBean.year}", "dd/MM/yyyy")
        if (oFirstDayWeek != 1) {
            var fillDate = tLastDay - oFirstDayWeek + 1 + 1
            for (weekIndex in 1 until oFirstDayWeek) {
                oList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }

        for (index in 1..oLastDay) {
            val likeDateStr = "${index.fill0()}/${oneDateBean.mon.fill0()}/${oneDateBean.year}"
            val dataList = DbManager.getInstance().getDealLogList(likeDateStr)
            oList.add(DateEntity(if (dataList.size > 0) 1 else 0, index.toString(), likeDateStr))
        }
        val oLastDayWeek = CalendarUtil.getInstance().getDayOfWeek("$oLastDay/${oneDateBean.mon.fill0()}/${oneDateBean.year}", "dd/MM/yyyy")
        if (oLastDayWeek != 7) {
            var fillDate = 1
            for (weekIndex in oLastDayWeek + 1..7) {
                oList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }
        dataList.add(CalendarPopData(CalendarUtil.getInstance().getFormatYearMon(oneDateBean), oList))

        val currDateBean = CalendarUtil.getInstance().getCurrentMonthFirstDay()
        val currentMonthFirstDayOfWeek = CalendarUtil.getInstance().getCurrentMonthFirstDayOfWeek()

        val cList = getWeekList()
        if (currentMonthFirstDayOfWeek != 1) {
            var fillDate = oLastDay - currentMonthFirstDayOfWeek + 1 + 1
            for (weekIndex in 1 until currentMonthFirstDayOfWeek) {
                cList.add(DateEntity(-1, fillDate.toString()))
                fillDate++
            }
        }
        for (index in 1..Calendar.getInstance().get(Calendar.DAY_OF_MONTH)) {
            val likeDateStr = "${index.fill0()}/${currDateBean.mon.fill0()}/${currDateBean.year}"
            val dataList = DbManager.getInstance().getDealLogList(likeDateStr)
            cList.add(DateEntity(if (dataList.size > 0) 1 else 0, index.toString(), likeDateStr))
        }
        cList.last().type = 2
        selectDateStr = cList.last().likeDate

        dataList.add(CalendarPopData(CalendarUtil.getInstance().getFormatYearMon(currDateBean), cList))

    }

    private fun getWeekList() = arrayListOf<DateEntity>().apply {
        add(DateEntity("M"))
        add(DateEntity("T"))
        add(DateEntity("W"))
        add(DateEntity("T"))
        add(DateEntity("F"))
        add(DateEntity("S"))
        add(DateEntity("S"))
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun initView() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            val layoutParams = ConstraintLayout.LayoutParams(-1, context.getStatusHeight())
            popView.mStatusBarView.layoutParams = layoutParams
        }
        popView.tvTitle.text = context.getString(R.string.select_date)
        popView.tvSelectDate.text = TimeUtil.getDateFormatEN(Date(System.currentTimeMillis()))
        popView.tvSelectDate.text = TimeUtil.getDateFormatEN(Date(System.currentTimeMillis()))
        val linearLayoutManager = WrapContentLinearLayoutManager(context)
        linearLayoutManager.stackFromEnd = true
        popView.rcvCalendar.layoutManager = linearLayoutManager
        adapter = PopCalendarRcyAdapter(context, dataList)
        popView.rcvCalendar.adapter = adapter
        popView.rcvCalendar.scrollToPosition(adapter?.itemCount ?: 1 - 1)

    }

    private fun initListener() {
        popView.ivLeft.setOnClickListener {
            dismiss()
        }
        adapter?.setOnItemClickListener(object : PopCalendarRcyAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, itemPosition: Int) {

                out@ for (value in dataList) {
                    for (data in value.dateList) {
                        if (data.type == 2) {
                            data.type = 1
                            break@out
                        }
                    }
                }
                val selectData = dataList.elementAtOrNull(position)?.dateList?.elementAtOrNull(itemPosition)
                selectData?.type = 2
                val split = selectData?.likeDate?.split("/")
                popView.tvSelectDate.text = TimeUtil.getDateFormatEN(Date(PickerDateUtil.dateStrToLong("${split?.elementAtOrNull(2)}-${split?.elementAtOrNull(1)}-${split?.elementAtOrNull(0)} 01:02:03").toLong()))
                adapter?.notifyDataSetChanged()

            }
        })
        popView.tvConfirm.setOnClickListener {
            out@ for (value in dataList) {
                for (data in value.dateList) {
                    if (data.type == 2) {
                        if (data.likeDate != selectDateStr) {
                            selectDateStr = data.likeDate
                            mOnPopClickListener?.onConfirm(data.likeDate)
                        }
                        dismiss()
                        break@out
                    }
                }
            }
        }
    }

    private var mOnPopClickListener: OnPopClickListener? = null

    interface OnPopClickListener {
        fun onConfirm(dateStr: String)
    }

    fun setOnPopClickListener(mOnPopClickListener: OnPopClickListener) {
        this.mOnPopClickListener = mOnPopClickListener
    }

}