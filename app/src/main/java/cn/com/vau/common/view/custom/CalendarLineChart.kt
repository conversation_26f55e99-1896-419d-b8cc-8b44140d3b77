package cn.com.vau.common.view.custom

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.os.CountDownTimer
import android.util.*
import android.view.*
import cn.com.vau.R
import cn.com.vau.util.*
import java.util.*
import kotlin.math.abs

/**
 * Created by roy on 2018/3/19 0019.
 * 财经日历详情
 */
class CalendarLineChart @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : View(context, attrs, defStyleAttr) {

    private var startDateStr = ""
    private var endDateStr = ""
    private val lineDataList = ArrayList<String>()
    private val lineBrokenDataList = ArrayList<Float>()

    private var mPaint: Paint? = null
    private var lineStraightColor: Int = 0
    private var textDataColor: Int = 0
    private var textDateColor: Int = 0
    private var lineBrokenColor: Int = 0
    private var textDataSize: Float = 0.0f
    private var textDateSize: Float = 0.0f
    private var lineBrokenWidth: Float = 0.0f
    private var max: Float = 0.0f
    private var min: Float = 0.0f

    private var endDateRect: Rect? = null
    private var lineAllHeight: Float = 0f
    private var allDataWidth: Float = 0f
    private var allDataHeight: Float = 0f

    private var marginRightWidthBroken: Float = 160f
    private var marginBottomHeidht: Float = 45f
    private var marginLeftWidth: Float = 20f
    private var marginRightWidth: Float = 20f

    private var weekPosition = -1

    private var weekScale = 0

    private var timer: CountDownTimer? = null

    private val dateList = ArrayList<String>()

    private var unit = ""

    init {
        initXmlAttrs(context, attrs)
        initPaint()
    }

    private fun initPaint() {
        mPaint = Paint()
        mPaint?.isAntiAlias = true
        mPaint?.style = Paint.Style.STROKE
        mPaint?.strokeWidth = 2f
    }

    @SuppressLint("CustomViewStyleable", "Recycle")
    private fun initXmlAttrs(context: Context, attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CalendarLineChart)
        marginLeftWidth = 10f.dp2px()
        marginRightWidth = 10f.dp2px()
        marginRightWidthBroken = 86f.dp2px()
        // 坐标系经线颜色
        lineStraightColor = typedArray.getColor(R.styleable.CalendarLineChart_line_straight_color, AttrResourceUtil.getColor(context, R.attr.color_c1f1e1e1e_c1fffffff))
        // 右侧刻度颜色
        textDataColor = typedArray.getColor(R.styleable.CalendarLineChart_text_data_color, AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
        textDataSize = typedArray.getDimension(R.styleable.CalendarLineChart_text_data_size, 32f)
        // 底部日期颜色
        textDateColor = typedArray.getColor(R.styleable.CalendarLineChart_text_date_color, AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
        textDateSize = typedArray.getDimension(R.styleable.CalendarLineChart_text_date_size, 30f)
        // 折线颜色
        lineBrokenColor = typedArray.getColor(R.styleable.CalendarLineChart_line_broken_color, resources.getColor(R.color.ce35728))
        lineBrokenWidth = typedArray.getDimension(R.styleable.CalendarLineChart_line_broken_width, 4f)
        typedArray.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        drawDate(canvas)
        drawDataLine(canvas)
        drawBrokenLine(canvas)
        drawClickData(canvas)
    }

    private fun drawDate(canvas: Canvas) {

        mPaint?.strokeWidth = 1f
        mPaint?.textSize = textDateSize
        mPaint?.color = textDateColor
        mPaint?.style = Paint.Style.FILL

        val startDateRect = Rect()
        mPaint?.textAlign = Paint.Align.LEFT
        mPaint?.getTextBounds(startDateStr, 0, startDateStr.length, startDateRect)
        marginBottomHeidht = (26 + startDateRect.height() + 36).toFloat()
        mPaint?.let {
            canvas.drawText(
                if (layoutDirection == LayoutDirection.LTR) startDateStr else reverseWords(startDateStr),
                0f + marginLeftWidth,
                (height - 26).toFloat(),
                it
            )
        }
        endDateRect = Rect()
        mPaint?.textAlign = Paint.Align.RIGHT
        mPaint?.getTextBounds(endDateStr, 0, endDateStr.length, endDateRect)
        mPaint?.let {
            canvas.drawText(
                if (layoutDirection == LayoutDirection.LTR) endDateStr else reverseWords(endDateStr),
                width.toFloat() - marginRightWidth,
                (height - 26).toFloat(),
                it
            )
        }
    }

    private fun reverseWords(input: String): String {
        // 通过空格分隔字符串
        val words = input.split(" ")
        // 将单词列表倒序
        val reversedWords = words.reversed()
        // 将倒序后的单词列表拼接成新的字符串
        return reversedWords.joinToString(" ")
    }

    private fun drawDataLine(canvas: Canvas) {

        mPaint?.color = lineStraightColor
        mPaint?.strokeWidth = 0.5f

        val imaginaryPaint = Paint()
        imaginaryPaint.style = Paint.Style.FILL
        imaginaryPaint.color = lineStraightColor
        imaginaryPaint.strokeWidth = 0.5f
        val effect = DashPathEffect(floatArrayOf(10f, 10f), 0f)
        imaginaryPaint.pathEffect = effect
        imaginaryPaint.isAntiAlias = true

        lineAllHeight = height - marginBottomHeidht
        for (i in 0..5) {
            canvas.drawLine(
                0f + marginLeftWidth, lineAllHeight / 6 * (i + 1),
                width.toFloat() - marginRightWidth, lineAllHeight / 6 * (i + 1),
                imaginaryPaint
            )
        }

        mPaint?.textSize = textDataSize
        mPaint?.color = textDataColor
        mPaint?.textAlign = Paint.Align.LEFT
        for (i in lineDataList.indices) {
            val dataRect = Rect()
            val dataStr = lineDataList[i].toString()
            mPaint?.getTextBounds(dataStr, 0, dataStr.length, dataRect)
            mPaint?.textAlign = Paint.Align.RIGHT
            mPaint?.let {
                canvas.drawText(
                    dataStr,
                    width.toFloat() - marginRightWidth,
                    lineAllHeight / 6 * (i + 1) - 5,
                    it
                )
            }
        }

    }

    private fun drawBrokenLine(canvas: Canvas) {

        mPaint?.pathEffect = CornerPathEffect(10F)

        allDataWidth = width - marginLeftWidth - marginRightWidthBroken
        allDataHeight = height - lineAllHeight / 6 - marginBottomHeidht

        mPaint?.style = Paint.Style.STROKE
        mPaint?.color = lineBrokenColor
        mPaint?.strokeWidth = lineBrokenWidth
        val path = Path()
        if (lineBrokenDataList.size > 0)
            path.moveTo(
                marginLeftWidth,
                lineAllHeight / 6 + allDataHeight / (max - min) * (max - lineBrokenDataList[0])
            )

        for (i in 1 until lineBrokenDataList.size) {
            path.lineTo(
                marginLeftWidth + allDataWidth / (lineBrokenDataList.size - 1) * i,
                lineAllHeight / 6 + allDataHeight / (max - min) * (max - lineBrokenDataList[i])
            )
        }

        mPaint?.let {
            canvas.drawPath(path, it)
        }

        mPaint?.color = Color.RED
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        try {
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    selectWeekScaleData(event)
                }

                MotionEvent.ACTION_MOVE -> {
                    selectWeekScaleData(event)
                    if (event.y < 0 || event.y > height) {
                        parent.requestDisallowInterceptTouchEvent(false)
                    } else {
                        parent.requestDisallowInterceptTouchEvent(true)
                    }
                }

                MotionEvent.ACTION_CANCEL,
                MotionEvent.ACTION_UP -> {
                    if (timer != null)
                        timer?.cancel()
                    startCountDownTime(4)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true
    }

    private fun startCountDownTime(time: Long) {

        timer = object : CountDownTimer(time * 1000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
            }

            override fun onFinish() {
                weekPosition = -1
                invalidate()
            }
        }
        timer?.start()
    }

    private fun drawClickData(canvas: Canvas) {

        if (weekPosition == -1) return

        val weekPaint = Paint()
        weekPaint.isAntiAlias = true
        weekPaint.strokeWidth = 3f
        weekPaint.textSize = 30f
        weekPaint.color = resources.getColor(R.color.cffffff)

        val topDataStr = lineBrokenDataList[weekPosition].toString() + unit
        val bottomDataStr = dateList[weekPosition]

        val topDataRect = Rect()
        weekPaint.getTextBounds(topDataStr, 0, topDataStr.length, topDataRect)

        val bottomDataRect = Rect()
        weekPaint.getTextBounds(bottomDataStr, 0, bottomDataStr.length, bottomDataRect)

        weekPaint.color = Color.parseColor("#d72d2b")

        val weekPointX = marginLeftWidth + allDataWidth / (lineBrokenDataList.size - 1) * weekPosition
        val weekPointY = (lineAllHeight / 6 + (height - lineAllHeight / 6 - (endDateRect?.height() ?: 0) * 2) / (max - min) * (max - lineBrokenDataList[weekPosition])).toInt()

        val tempWidth = if (topDataRect.width() > bottomDataRect.width()) topDataRect.width() else bottomDataRect.width()

        val weekBgRect = RectF(
            marginLeftWidth,
            marginLeftWidth,
            marginLeftWidth + marginLeftWidth + bottomDataRect.width() + marginRightWidth,
            marginLeftWidth + 5.dp2px() + bottomDataRect.height() + 5.dp2px() +
                    bottomDataRect.height() + 5.dp2px()
        )

        weekPaint.style = Paint.Style.STROKE
        canvas.drawRoundRect(weekBgRect, 12f, 12f, weekPaint)

        weekPaint.strokeWidth = 1f
        weekPaint.style = Paint.Style.FILL
        weekPaint.alpha = 100
        canvas.drawRoundRect(weekBgRect, 12f, 12f, weekPaint)

        weekPaint.alpha = 0
        weekPaint.color = Color.WHITE

        val fontMetrics = weekPaint.fontMetricsInt
        val baseline = ((weekBgRect.bottom + weekBgRect.top - fontMetrics.bottom.toFloat() - fontMetrics.top.toFloat()) / 2).toInt()

        weekPaint.textAlign = Paint.Align.RIGHT
        canvas.drawText(
            topDataStr,
            weekBgRect.right - marginRightWidth,
            weekBgRect.top + 5.dp2px() + bottomDataRect.height(),
            weekPaint
        )
        weekPaint.textAlign = Paint.Align.CENTER
        canvas.drawText(
            bottomDataStr,
            weekBgRect.centerX(),
            weekBgRect.bottom - 5.dp2px() - bottomDataRect.height() / 4,
            weekPaint
        )

        weekPaint.color = lineBrokenColor
        weekPaint.style = Paint.Style.FILL
        canvas.drawCircle(
            marginLeftWidth + allDataWidth / (lineBrokenDataList.size - 1) * weekPosition,
            lineAllHeight / 6 + allDataHeight / (max - min) * (max - lineBrokenDataList[weekPosition]),
            8f,
            weekPaint
        )
        weekPaint.color = Color.BLACK
        weekPaint.style = Paint.Style.FILL
        canvas.drawCircle(
            marginLeftWidth + allDataWidth / (lineBrokenDataList.size - 1) * weekPosition,
            lineAllHeight / 6 + allDataHeight / (max - min) * (max - lineBrokenDataList[weekPosition]),
            4f,
            weekPaint
        )
        mPaint?.color = Color.BLACK
    }

    fun setDataView(timeList: List<String>, actuaList: List<Float>) {
        startDateStr = timeList.first()
        endDateStr = timeList.last()

        this.dateList.clear()
        this.dateList.addAll(timeList)
        this.unit = unit

        max = Collections.max(actuaList)
        min = Collections.min(actuaList)

        if (max == min) max += max

        lineDataList.clear()
        lineDataList.add(max.toString())
        val count = actuaList.maxOfOrNull { it.toString().split(".").getOrNull(1)?.length.ifNull() }.ifNull()
        for (i in 1..5) {
            lineDataList.add((max - (max - min) / 5 * i).numFormat(count, false))
        }
        lineBrokenDataList.clear()
        for (data in actuaList)
            lineBrokenDataList.add(data)
        weekScale = ((width - marginLeftWidth - marginRightWidthBroken) / (lineBrokenDataList.size - 1)).toInt()

        invalidate()
    }

    private fun selectWeekScaleData(event: MotionEvent) {
        if (lineBrokenDataList.isEmpty()) return
        var selectIndex = 0
        if (event.x < marginLeftWidth) {
            selectIndex = 0
        }
        if (event.x > width - marginRightWidthBroken) {
            selectIndex = lineBrokenDataList.size - 1
        }
        for (i in lineBrokenDataList.indices) {
            if (abs(event.x - (marginLeftWidth + weekScale * i)) < weekScale / 2) {
                selectIndex = i
                break
            }
        }
        if (weekPosition == selectIndex) return

        weekPosition = selectIndex

        invalidate()

    }

}

