package cn.com.vau.common.utils.initializer

import android.app.Application
import android.content.Context
import androidx.startup.Initializer
import cn.com.vau.common.application.VauApplication
import com.facebook.appevents.AppEventsLogger

class AppEventsLoggerInitializer : Initializer<Unit> {

    override fun create(context: Context) {
        AppEventsLogger.activateApp(VauApplication())
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        dependencies().add(FaceBookInitializer::class.java)
        return dependencies()
    }

}