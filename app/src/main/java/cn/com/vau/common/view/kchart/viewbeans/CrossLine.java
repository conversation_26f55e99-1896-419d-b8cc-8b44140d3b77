package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.MotionEvent;

import androidx.core.content.res.ResourcesCompat;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.constants.Constants;
import cn.com.vau.common.view.kchart.interfaces.UnabelFocusedsView;
import cn.com.vau.common.view.kchart.views.ChartViewImp;
import cn.com.vau.data.init.ShareProductData;
import cn.com.vau.trade.kchart.ChartUIParamUtil;
import cn.com.vau.trade.kchart.KLineDataUtils;
import cn.com.vau.util.AttrResourceUtil;
import cn.com.vau.util.DistKt;
import cn.com.vau.util.ExpandKt;

/**
 * 描述：十字线
 */
public class CrossLine extends ZoomMoveViewContainer<String> implements UnabelFocusedsView {
    private Context ctx;
    //画笔
    private Paint mLinePaint = null;
    private Paint mPointPaint = null;
    private Paint mTextPaint;
    private Paint mTextBgPaint;
    //当前下标
    private int mIndex = 0;
    //颜色
    private int mLineColor = Color.BLACK;
    private int mPointColor = Color.BLACK;
    private int mTextColor = Color.WHITE;
    private int mTextBackgroundColor = Color.BLACK;
    //焦点
    private PointF mFingerPointF = new PointF();
    private PointF mCrossPointF = new PointF();
    //每个点的偏移量,保证十字在每个点的中间
    private float mSinglePointOffset = 0f;
    //点的半径
    private int mRadius = 10;
    //是否显示绘制点
    private boolean isShowPoint = false;
    //是否显示经线
    private boolean isShowLatitude = true;
    //是否显示纬线
    private boolean isShowLongitude = true;
    //经线是否允许超出边界
    private boolean isAllowOverLimit = true;
    //经线是否超出边界
    private boolean isLatitudeOverLimit = false;
    //是否显示文字背景
    private boolean isShowTextBackground = true;
    //经线是否对焦数据点显示
    private boolean isLatitudeFollowData = true;
    private boolean isLongitudeFollowData = true;
    //每个点的宽度
    private float mPointWidth = 0;
    //圆角半径
    private float mCornerRoundRadius = DistKt.dp2px(ChartUIParamUtil.INSTANCE.getCrossLineRadiusDp());
    //十字拖动监听器
    private OnCrossLineMoveListener mOnCrossLineMoveListener = null;
    //当前聚焦的View
    private ViewContainer mFocusedView;
    //左右padding
    private float mPaddingHorizontal = 0;
    private float mPaddingVertical = 0;

    private Typeface textTypeface;
    private RectF latitudeScaleRF = new RectF();
    private RectF longitudeScaleRF = new RectF();
    private ShareProductData symbolData = null;

    public CrossLine(Context context) {
        super(context);
        this.ctx = context;
        this.isShow = false;
        initPaint();
    }

    //初始化画笔
    private void initPaint() {
        textTypeface = ResourcesCompat.getFont(ctx, R.font.gilroy_medium);

        mLinePaint = new Paint();
        mLinePaint.setAntiAlias(true);
        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setStrokeWidth(getPixelDp(0.5f));
        mLinePaint.setPathEffect(new DashPathEffect(new float[]{10, 10}, 0));
        mLinePaint.setColor(mLineColor);

        mPointPaint = new Paint();
        mPointPaint.setAntiAlias(true);
        mPointPaint.setStyle(Paint.Style.FILL);
        mPointPaint.setColor(mPointColor);

        mTextPaint = new Paint();
        mTextPaint.setColor(mTextColor);
        mTextPaint.setStyle(Paint.Style.FILL);
        mTextPaint.setAntiAlias(true);
        mTextPaint.setTypeface(textTypeface);
        mTextPaint.setTextSize(getPixelSp(8));
        mTextPaint.setTextAlign(Paint.Align.CENTER);

        mTextBgPaint = new Paint();
        mTextBackgroundColor = AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff);
        mTextBgPaint.setColor(mTextBackgroundColor);
        mTextBgPaint.setStyle(Paint.Style.FILL);
        mTextBgPaint.setAntiAlias(true);

        mPaddingHorizontal = getPixelDp(ChartUIParamUtil.INSTANCE.getCrossLineTextPaddingHorizontalDp());
        mPaddingVertical = getPixelDp(ChartUIParamUtil.INSTANCE.getCrossLineTextPaddingVerticalDp());
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        try {
            if (isShow) {
                checkParameter();
                if (initFocusedView()) return;
                //计算点的宽度
                mPointWidth = (mCoordinateWidth - mCoordinateMarginLeft - mCoordinateMarginRight) / mShownPointNums;
                //计算触摸的
                mIndex = (int) ((mFingerPointF.x - mCoordinateMarginLeft) / mPointWidth);
                if (mIndex >= mShownPointNums) {
                    mIndex = mShownPointNums - 1;
                }

                //尽在显示区域内绘制十字线
                if (mDrawPointIndex + mIndex < mDrawPointIndex + mShownPointNums) {
                    float currentValue = mFocusedView.transDataToCrossDataFromDataList(mIndex, mIndex + mDrawPointIndex);
                    if (isShowLatitude) {
                        String indicateValue = mOnCrossLineMoveListener.onCrossIndicateYScale(mIndex, mDrawPointIndex, mShownPointNums, mCrossPointF, mYMin, mYMax);
                        if (TextUtils.isEmpty(indicateValue)) {
                            return;
                        }
                        // 3.56.0版本K线需求 更新十字线涨跌幅公式: (十字线价格 - bid) / bid * 100%
                        double rate = symbolData == null || symbolData.getBid() == 0 ?
                                0.0 : (ExpandKt.toDoubleCatching(indicateValue, 0.0) - symbolData.getBid()) / symbolData.getBid();
                        String rateStr = (rate > 0 ? "+" : "") + ExpandKt.formatProductPrice(rate * 100, 2, false, Constants.DOUBLE_LINE) + "%";
                        float indicateWidth = mTextPaint.measureText(indicateValue);
                        float rateWidth = mTextPaint.measureText(rateStr);
                        float maxWidth = Math.max(indicateWidth, rateWidth);
                        // 虚线结束的x坐标
                        float latitudeXEnd;
                        int top = getChartView().getTopHeight();
                        if (top == 0) {
                            latitudeXEnd = mCoordinateWidth - mPaddingHorizontal * 2 - maxWidth;
                        } else {
                            latitudeXEnd = mCoordinateWidth - mPaddingHorizontal * 2 - indicateWidth;
                        }
                        //绘制经线
                        drawLatitude(canvas, latitudeXEnd, currentValue);
                        drawLatitudeScaleText(canvas, latitudeXEnd, indicateValue, rateStr, currentValue);
                    }
                    if (isShowLongitude) {
                        //绘制纬线
                        drawLongitude(canvas, mIndex);
                        drawLongitudeScaleText(canvas, mIndex);
                    }
                    if (isShowPoint) {
                        //绘制点
                        drawCircle(canvas, mIndex, currentValue);
                    }

                    if (mOnCrossLineMoveListener != null) {
                        mOnCrossLineMoveListener.onCrossLineMove(mIndex, mDrawPointIndex, mCrossPointF, mFingerPointF);
                    }
                }
            }
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
    }

    private boolean initFocusedView() {
        ChartView chartView = getChartView();
        if (chartView == null) {
            return true;
        }

        mFocusedView = chartView.getFocusedView();
        if (mFocusedView == null) {
            return true;
        }
        return false;
    }

    private void checkParameter() {
        if (this.mShownPointNums < 0) {
            throw new IllegalArgumentException("maxPointNum must be larger than 0");
        }
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }
        if (mFingerPointF.x < 0f && mFingerPointF.y < 0f) {
            throw new IllegalArgumentException("mFingerPointF.x mFingerPointF.y,must bigger than -1");
        }
    }

    //绘制经线
    private void drawLatitude(Canvas canvas, float latitudeXEnd, float currentValue) {
        float y = mFingerPointF.y;
        ChartViewImp chart = getChartView();
        int top = chart.getTopHeight();
        int subTop = chart.getSubViewTopHeight();
        if (isLatitudeFollowData) {
            try {
                y = (1f - (currentValue - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
            } catch (NumberFormatException e) {
                y = mFingerPointF.y;
            }
        }
        if (KLineDataUtils.startWithSubCross) {
            if (top == 0) {
                if (y < -subTop) {
                    y = -subTop;
//                    LogUtil.i("wj", "主图到头了");
                } else if (y > -(subTop - mCoordinateHeight)) {
//                    LogUtil.i("wj", "主图超出了");
                    isLatitudeOverLimit = true;
                    return;
                }
                isLatitudeOverLimit = false;
                //              y = y < 0 ? 0 : (y > mCoordinateHeight ? mCoordinateHeight : y);
//                LogUtil.i("wj", "主图在范围内显示， mCrossPointF.y="+(y + subTop));
//                LogUtil.i("wj", "[startWithSubCross]  top = 0， mCrossPointF.y="+(y + subTop));
                mCrossPointF.y = y + subTop;
                canvas.drawLine(mCoordinateMarginLeft, y + subTop, latitudeXEnd, y + subTop, mLinePaint);
            } else {
                if (y < 0) {
//                    LogUtil.i("wj", "副图超出了");
                    isLatitudeOverLimit = true;
                    return;
                } else {
                    if (y > mCoordinateHeight) {
                        y = mCoordinateHeight;
//                        LogUtil.i("wj", "副图到头了");
                    }
                }
                isLatitudeOverLimit = false;
                //              y = y < 0 ? 0 : (y > mCoordinateHeight ? mCoordinateHeight : y);
//                LogUtil.i("wj", "副图在范围内显示， mCrossPointF.y="+y);
//                LogUtil.i("wj", "[startWithSubCross]  top != 0， mCrossPointF.y="+y);
                mCrossPointF.y = y;
                canvas.drawLine(mCoordinateMarginLeft, y, latitudeXEnd, y, mLinePaint);
            }
        } else {
            if (top == 0) {
                if (y < 0) {
                    y = 0;
                } else {
                    if (y > mCoordinateHeight) {
                        if (isAllowOverLimit) {
                            isLatitudeOverLimit = true;
                            return;
                        } else {
                            y = mCoordinateHeight;
                        }
                    }
                }
                isLatitudeOverLimit = false;
                //              y = y < 0 ? 0 : (y > mCoordinateHeight ? mCoordinateHeight : y);
//                LogUtil.i("wj", "top = "+top+"， mCoordinateHeight:"+mCoordinateHeight+"   mCrossPointF.y="+y);
                mCrossPointF.y = y;
                canvas.drawLine(mCoordinateMarginLeft, y, latitudeXEnd, y, mLinePaint);
            } else {
                if (y > mCoordinateHeight + top) {
                    y = mCoordinateHeight + top;
                } else if (y < top) {
                    isLatitudeOverLimit = true;
                    return;
                }
                isLatitudeOverLimit = false;
                //              y = y < 0 ? 0 : (y > mCoordinateHeight ? mCoordinateHeight : y);
//                LogUtil.i("wj", "top != "+top+"， mCoordinateHeight:"+mCoordinateHeight+"   mCrossPointF.y="+(y - top));
                mCrossPointF.y = y - top;
                canvas.drawLine(mCoordinateMarginLeft, y - top, latitudeXEnd, y - top, mLinePaint);
            }
        }
    }

    //绘制纬线
    private void drawLongitude(Canvas canvas, int index) {
        float x = mFingerPointF.x;
        if (isLongitudeFollowData) {
            x = index * mPointWidth + mCoordinateMarginLeft + mSinglePointOffset;
        }
        x = x < mCoordinateMarginLeft ? mCoordinateMarginLeft : (x > mCoordinateWidth ? mCoordinateWidth : x);
        mCrossPointF.x = x;
        canvas.drawLine(x, 0, x, mCoordinateHeight, mLinePaint);
    }

    //绘制圆
    private void drawCircle(Canvas canvas, int index, float currentValue) {
        float x;
        float y;
        try {
            x = index * mPointWidth + mCoordinateMarginLeft + mSinglePointOffset;
        } catch (NumberFormatException e) {
            x = mFingerPointF.x;
        }
        try {
            y = (1f - (currentValue - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        } catch (NumberFormatException e) {
            y = mFingerPointF.y;
        }
        y = y < 0 ? 0 : (y > mCoordinateHeight ? mCoordinateHeight : y);
        x = x < mCoordinateMarginLeft ? mCoordinateMarginLeft : (x > mCoordinateWidth ? mCoordinateWidth : x);

        mCrossPointF.y = y;
        mCrossPointF.x = x;

        canvas.drawCircle(x, y, mRadius, mPointPaint);
    }

    /**
     * 绘制经线刻度,不更新mCrossPoint
     */
    private void drawLatitudeScaleText(Canvas canvas, float latitudeXEnd, String indicateValue, String rateStr, float currentValue) {
        if (mOnCrossLineMoveListener == null) {
            return;
        }
        if (!isLatitudeOverLimit) {
            float y = mFingerPointF.y;
            ChartViewImp chart = getChartView();
            int top = chart.getTopHeight();
            int subTop = chart.getSubViewTopHeight();
            if (isLatitudeFollowData) {
                try {
                    y = (1f - (currentValue - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
                } catch (NumberFormatException e) {
                    y = mFingerPointF.y;
                }
            }
            float indicateWidth = mTextPaint.measureText(indicateValue);
            float rateWidth = mTextPaint.measureText(rateStr);
            if (KLineDataUtils.startWithSubCross) {
                if (top == 0) {
                    float textHeight = getTextHeight();
                    float minHeight = 2 * textHeight + mPaddingVertical * 3;
                    minHeight -= minHeight / 2f;
                    float maxHeight = -(subTop - mCoordinateHeight - minHeight);
//                    LogUtil.i("wj", "minHeight:" + minHeight + "   subTop:" + subTop + "   maxHeight:" + maxHeight + "   y:" + y);
                    y = y < (-subTop + minHeight) ? (-subTop + minHeight) : (y > maxHeight ? maxHeight : y);

                    //保证线在这个矩形中间
                    latitudeScaleRF.left = latitudeXEnd;
                    latitudeScaleRF.top = y + subTop - textHeight - 3 * mPaddingVertical / 2;
                    latitudeScaleRF.right = mCoordinateWidth;
                    latitudeScaleRF.bottom = latitudeScaleRF.top + 2 * textHeight + 3 * mPaddingVertical;

//                    LogUtil.i("wj", "startWithSubCross   top = 0   roundBg.top:" + roundBg.top);

                    //绘制背景
                    if (isShowTextBackground) {
                        canvas.drawRoundRect(latitudeScaleRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
                    }
                    canvas.drawText(rateStr, latitudeScaleRF.right - rateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + 2 * mPaddingVertical + 2 * textHeight, mTextPaint);
                    canvas.drawText(indicateValue, latitudeScaleRF.right - indicateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + mPaddingVertical + textHeight, mTextPaint);
                } else {
                    float textHeight = getTextHeight();
                    float minHeight = 2 * textHeight + mPaddingVertical * 3;
                    minHeight -= minHeight / 2f;
                    float maxHeight = mCoordinateHeight - minHeight;
//                    LogUtil.i("wj", "minHeight:" + minHeight + "   maxHeight:" + maxHeight + "   y:" + y);
                    y = y < minHeight ? minHeight : (y > maxHeight ? maxHeight : y);

                    // 保证线在这个矩形中间
                    latitudeScaleRF.left = latitudeXEnd;
                    latitudeScaleRF.top = y - mPaddingVertical - textHeight / 2;
                    latitudeScaleRF.right = mCoordinateWidth;
                    latitudeScaleRF.bottom = latitudeScaleRF.top + textHeight  + mPaddingVertical * 2;

                    // 绘制背景
                    if (isShowTextBackground) {
                        canvas.drawRoundRect(latitudeScaleRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
                    }
                    canvas.drawText(indicateValue, latitudeScaleRF.right - indicateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + mPaddingVertical + textHeight, mTextPaint);
                }
            } else {
                if (top == 0) {
                    float textHeight = getTextHeight();
                    float minHeight = 2 * textHeight + mPaddingVertical * 3;
                    minHeight -= minHeight / 2f;
                    float maxHeight = mCoordinateHeight - minHeight;
                    y = y < minHeight ? minHeight : (y > maxHeight ? maxHeight : y);

                    //保证线在这个矩形中间
                    latitudeScaleRF.left = latitudeXEnd;
                    latitudeScaleRF.top = y - textHeight - (mPaddingVertical * 3) / 2;  // 包括上、中、下三部分空隙，所以 (mPaddingVertical * 3)
                    latitudeScaleRF.right = mCoordinateWidth;
                    latitudeScaleRF.bottom = y + textHeight + (mPaddingVertical * 3) / 2;

                    //绘制背景
                    if (isShowTextBackground) {
                        canvas.drawRoundRect(latitudeScaleRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
                    }
                    canvas.drawText(rateStr, latitudeScaleRF.right - rateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + 2 * mPaddingVertical + 2 * textHeight, mTextPaint);
                    canvas.drawText(indicateValue, latitudeScaleRF.right - indicateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + mPaddingVertical  + textHeight, mTextPaint);
                } else {
                    float textHeight = getTextHeight();
                    float minHeight = 2 * textHeight + mPaddingVertical * 3;
                    minHeight -= minHeight / 2f;
                    float maxHeight = mCoordinateHeight + top - minHeight;
                    y = y < (minHeight + top) ? minHeight : (y > maxHeight ? maxHeight - top : y - top);

                    // 保证线在这个矩形中间
                    latitudeScaleRF.left = latitudeXEnd;
                    latitudeScaleRF.top = y - mPaddingVertical - textHeight / 2;
                    latitudeScaleRF.right = mCoordinateWidth;
                    latitudeScaleRF.bottom = latitudeScaleRF.top + textHeight  + mPaddingVertical * 2;

                    // 绘制背景
                    if (isShowTextBackground) {
                        canvas.drawRoundRect(latitudeScaleRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
                    }
                    canvas.drawText(indicateValue, latitudeScaleRF.right - indicateWidth / 2 - mPaddingHorizontal, latitudeScaleRF.top + mPaddingVertical  + textHeight, mTextPaint);
                }
            }
        }
    }

    /**
     * 绘制纬线刻度,不更新mCrossPoint
     */
    private void drawLongitudeScaleText(Canvas canvas, int index) {
        if (mOnCrossLineMoveListener == null) {
            return;
        }
        float x = mFingerPointF.x;
        ChartViewImp chart = getChartView();
        if (chart.isSubChart() || chart.isVolumeChart()) {
            return;
        }
        if (isLongitudeFollowData) {
            x = index * mPointWidth + mCoordinateMarginLeft + mSinglePointOffset;
        }
        String indicateValue = mOnCrossLineMoveListener.onCrossIndicateXScale(index, mDrawPointIndex, mShownPointNums);
        if (TextUtils.isEmpty(indicateValue)) {
            return;
        }
        float textHeight = getTextHeight();
        float textWidth = mTextPaint.measureText(indicateValue);
        float rectWidth = (textWidth + mPaddingHorizontal * 2);
        float minLeft = rectWidth / 2f;
        float maxLeft = mCoordinateWidth - minLeft;
        x = x < minLeft ? minLeft : (x > maxLeft ? maxLeft : x);
        float recfHeight = textHeight + mPaddingVertical * 2;
        //计算背景宽高
        longitudeScaleRF.left = x;
        longitudeScaleRF.top = mCoordinateHeight - recfHeight;
        longitudeScaleRF.right = x + rectWidth;
        longitudeScaleRF.bottom = mCoordinateHeight;

        float recWidth = longitudeScaleRF.width();
        longitudeScaleRF.left -= recWidth / 2;
        longitudeScaleRF.right -= recWidth / 2;

        if (isShowTextBackground) {
            canvas.drawRoundRect(longitudeScaleRF, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
        }
        Paint.FontMetrics fm = mTextPaint.getFontMetrics();
        float distance = (fm.bottom - fm.top) / 2 - fm.bottom;
        canvas.drawText(indicateValue, x, longitudeScaleRF.centerY() + distance , mTextPaint);
    }

    /**
     * 文字高度
     */
    private float getTextHeight() {
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);
        return Math.abs(fm.ascent);     // 文字的绘制点其实是baseline，详细参见：https://www.jb51.net/article/176780.htm
    }

    @Override
    public void move(MotionEvent event) {
        try {
            int index;
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (initFocusedView()) return;
                    mFingerPointF.x = event.getX();
                    mFingerPointF.x = mFingerPointF.x < mCoordinateMarginLeft ? mCoordinateMarginLeft : mFingerPointF.x;
                    mPointWidth = (mCoordinateWidth - mCoordinateMarginLeft) / mShownPointNums;
                    index = (int) ((mFingerPointF.x - mCoordinateMarginLeft) / mPointWidth);
                    if (mFocusedView.getDataListSize() > 0 && index > mFocusedView.getDataListSize() - 1) {
                        index = mFocusedView.getDataListSize() - 1;
                        mFingerPointF.x = index * mPointWidth + mCoordinateMarginLeft;
                    }

                    mFingerPointF.y = event.getY();
                    setShow(true);
                    break;
                case MotionEvent.ACTION_MOVE:
                    mFingerPointF.x = event.getX();
                    mFingerPointF.x = mFingerPointF.x < mCoordinateMarginLeft ? mCoordinateMarginLeft : mFingerPointF.x;
                    mFingerPointF.x = mFingerPointF.x > mCoordinateWidth ? mCoordinateWidth : mFingerPointF.x;
                    index = (int) ((mFingerPointF.x - mCoordinateMarginLeft) / mPointWidth);
                    if (mFocusedView.getDataListSize() > 0 && index > mFocusedView.getDataListSize() - 1) {
                        index = mFocusedView.getDataListSize() - 1;
                        mFingerPointF.x = index * mPointWidth + mCoordinateMarginLeft;
                    }
                    mFingerPointF.y = event.getY();
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    setShow(false);
                    if (mOnCrossLineMoveListener != null) {
                        mOnCrossLineMoveListener.onCrossLineDismiss();
                    }
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public interface OnCrossLineMoveListener {
        void onCrossLineMove(int crossIndexInScreen, int drawPointIndex, PointF crossPointF, PointF fingerPointF);

        void onCrossLineDismiss();

        String onCrossIndicateYScale(int crossIndexInScreen, int drawPointIndex, int showPointNums, PointF crossPointF, float yMin, float yMax);

        String onCrossIndicateXScale(int crossIndexInScreen, int drawPointIndex, int showPointNums);
    }

    public void setOnCrossLineMoveListener(OnCrossLineMoveListener lineMoveListener) {
        this.mOnCrossLineMoveListener = lineMoveListener;
    }

    public void setTextColor(int textColor) {
        mTextColor = textColor;
        mTextPaint.setColor(textColor);
    }

    public void setTextBgPaint(Paint textBgPaint) {
        mTextBgPaint = textBgPaint;
    }

    public void setTextBackgroundColor(int textBackgroundColor) {
        mTextBackgroundColor = textBackgroundColor;
        mTextBgPaint.setColor(mTextBackgroundColor);
    }

    public void crossDismiss() {
        if (mOnCrossLineMoveListener != null) {
            mOnCrossLineMoveListener.onCrossLineDismiss();
        }
    }

    @Override
    public void setDataList(List<String> dataList) {
        //该组件不需要数据
    }

    public int getRadius() {
        return mRadius;
    }

    public void setRadius(int radius) {
        this.mRadius = radius;
    }

    public PointF getFingerPointF() {
        return mFingerPointF;
    }

    public void setFingerPointF(PointF fingerPointF) {
        this.mFingerPointF = fingerPointF;
    }

    public int getLineColor() {
        return mLineColor;
    }

    public void setLineColor(int lineColor) {
        this.mLineColor = lineColor;
        mLinePaint.setColor(lineColor);
    }

    public int getPointColor() {
        return mPointColor;
    }

    public void setPointColor(int pointColor) {
        this.mPointColor = pointColor;
        mPointPaint.setColor(this.mPointColor);
    }

    public boolean isShowPoint() {
        return isShowPoint;
    }

    public void setShowPoint(boolean isShowPoint) {
        this.isShowPoint = isShowPoint;
    }

    public boolean isShowLatitude() {
        return isShowLatitude;
    }

    public void setShowLatitude(boolean isShowLatitude) {
        this.isShowLatitude = isShowLatitude;
    }

    public boolean isShowLongitude() {
        return isShowLongitude;
    }

    public void setTextSize(float sp) {
        mTextPaint.setTextSize(getPixelSp(sp));
    }

    public void setShowLongitude(boolean isShowLongitude) {
        this.isShowLongitude = isShowLongitude;
    }

    @Override
    public void setShownPointNums(int shownPointNums) {
        mShownPointNums = shownPointNums;
    }

    @Override
    public float getSingleDataWidth() {
        return mSinglePointOffset;
    }

    public void setSinglePointOffset(float singlePointOffset) {
        this.mSinglePointOffset = singlePointOffset;
    }

    public void setAllowOverLimit(boolean allowed) {
        this.isAllowOverLimit = allowed;
    }

    public void setLatitudeFollowData(boolean latitudeFollowData) {
        isLatitudeFollowData = latitudeFollowData;
    }

    public void setLongitudeFollowData(boolean longitudeFollowData) {
        isLongitudeFollowData = longitudeFollowData;
    }

    public void setData(ShareProductData dataBean) {
        this.symbolData = dataBean;
    }
}
