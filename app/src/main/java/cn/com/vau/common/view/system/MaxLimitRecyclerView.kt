package cn.com.vau.common.view.system

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R

class MaxLimitRecyclerView @JvmOverloads constructor(
    val mContext: Context,
    val attrs: AttributeSet? = null,
    defStyle: Int = 0
) : RecyclerView(mContext, attrs, defStyle) {

    private var mMaxHeight = 0
    private var mMaxWidth = 0

    init {
        initParam(attrs)
    }

    private fun initParam(attrs: AttributeSet?) {
        if (attrs != null) {
            var typedArray: TypedArray? = null
            try {
                typedArray =
                    mContext.obtainStyledAttributes(attrs, R.styleable.MaxLimitRecyclerView)
                if (typedArray.hasValue(R.styleable.MaxLimitRecyclerView_limit_maxHeight)) {
                    mMaxHeight = typedArray.getDimensionPixelOffset(
                        R.styleable.MaxLimitRecyclerView_limit_maxHeight,
                        -1
                    )
                }
                if (typedArray.hasValue(R.styleable.MaxLimitRecyclerView_limit_maxWidth)) {
                    mMaxWidth = typedArray.getDimensionPixelOffset(
                        R.styleable.MaxLimitRecyclerView_limit_maxWidth,
                        -1
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace();
            } finally {
                typedArray?.recycle()
            }
        }
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        super.onMeasure(widthSpec, heightSpec)
        if (mMaxHeight >= 0 || mMaxWidth >= 0) {    // need limit
            var limitHeight = measuredHeight
            var limitWith = measuredWidth
            if (measuredHeight > mMaxHeight) {
                limitHeight = mMaxHeight
            }
            if (measuredWidth > mMaxWidth) {
                limitWith = mMaxWidth
            }
            setMeasuredDimension(limitWith, limitHeight)
        }
    }

}