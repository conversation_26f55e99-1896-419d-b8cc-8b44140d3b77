package cn.com.vau.common.view.custom

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.isRtl
import cn.com.vau.util.vibrate
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * author：lvy
 * date：2024/12/03
 * desc：数量滑动条
 */
class VolSeekBar : View {

    // 进度
    private var min = 0 // 最小值，当为0时，用最小值代替
    private var max = 100 // 最大值
    private var progress = 0 // 进度值

    // 轨道 track
    private var trackBackgroundColor = AttrResourceUtil.getColor(context, R.attr.color_c0a1e1e1e_c262930) // 背景色
    private var trackProgressColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) // 前景色（进度色）
    private var dotTrackProgressColor = AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e) // 前景色（进度色）
    private var trackWidth = 5f.dp2px() // 轨道宽度

    // 平分进度的小圆点
    private var dotCount = 6 // 小圆点数量
    private var dotMargin= 1f.dp2px()
    private var dotRadius = 2f.dp2px() // 小圆点半径
    private var dotStrokeWidth = 2f.dp2px() // 小圆点边框宽度
    private var dotStrokeColor = AttrResourceUtil.getColor(context, R.attr.color_cffffff_c1a1d20) // 小圆点边框颜色
    private var dotSolidBackColor = AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff) // 小圆点背景色
    private var dotIsShow = true // 是否显示小圆点
    private var isNeedVibrate = true // 是否需要震动

    // 滑块
    private var thumbRadius = 7f.dp2px() // 滑块半径大小
    private var thumbStrokeWidth = 1f.dp2px() // 滑块边框大小
    private var centerThumbRadius = 4f.dp2px() // 滑块边框大小
//    private var thumbSolidColor = AttrResourceUtil.getColor(context, R.attr.color_cffffff_c262930) // 滑块填充颜色
    private var thumbSolidColor = context.getColor(R.color.cffffff) // 滑块填充颜色
    private var thumbStrokeColor = context.getColor(R.color.c1f1e1e1e) // 滑块边框颜色
    private var centerStrokeColor = context.getColor(R.color.c1e1e1e) // 滑块中心颜色

    // 底部百分比文字
    private var percentIsShow = false // 是否显示底部百分比数字
    private var percentTextSize = 12f.dp2px() // 底部百分比数字文字大小
    private var percentTextColor = AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff) // 底部百分比数字文字颜色
    private var percentUnit = "%" // 底部百分比单位(默认"%")
    private var percentMargin = 8f.dp2px() // 底部百分比数字与滑块的间距
    private var percentTypeface = ResourcesCompat.getFont(context, R.font.gilroy_medium)

    // 气泡
    private var isShowBubble = true // 是否显示气泡
    private var bubbleHeight = 20f.dp2px() // 气泡高度
    private var bubbleRadius = 4f.dp2px() // 气泡圆角
    private var bubbleMargin = 3f.dp2px() // 气泡与滑块的间距
    private var bubbleHorizontalPadding = 8f.dp2px() // 气泡横向内间距
    private var bubbleTextSize = 12f.dp2px() // 气泡文字大小
    private var bubbleTextColor = AttrResourceUtil.getColor(context, R.attr.color_cebffffff_c1e1e1e) // 气泡文字颜色
    private var bubbleColor = AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) // 气泡的背景色
    private var bubbleTypeface = ResourcesCompat.getFont(context, R.font.gilroy_medium)

    // 画笔
    private val paintTrack = Paint(Paint.ANTI_ALIAS_FLAG) // 轨道
    private val paintDot = Paint(Paint.ANTI_ALIAS_FLAG) // 小圆点
    private val paintThumb = Paint(Paint.ANTI_ALIAS_FLAG) // 滑块
    private val paintPercent = Paint(Paint.ANTI_ALIAS_FLAG) // 底部百分比文字
    private val paintBubble = Paint(Paint.ANTI_ALIAS_FLAG) // 气泡

    private val textRect = Rect()
    private var realProgressWidth = 0f // 实际进度值所在范围的宽度（宽度-两边滑块的半径）*
    private var cxMin = 0f // 圆心x轴最小值(去掉滑块的半径)*
    private var cxMax = 0f // 圆心x轴最大值(去掉滑块的半径)*
    private var currX = 0f // 进度值所在位置
    private var dotPositionList = arrayListOf<Int>() // 记录小圆点进度位置
    private var adsorption = false // 点击吸附效果是否可用
    private var vibrationTriggered = false // 记录震动是否已被触发
    private var isDragging = false // 记录滑动拖动状态
    private var touchYMin = cxMin
    private var touchYMax = cxMin

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.VolSeekBar)
        // 进度
        min = a.getInteger(R.styleable.VolSeekBar_vsb_min, min)
        max = a.getInteger(R.styleable.VolSeekBar_vsb_max, max)
        progress = a.getInteger(R.styleable.VolSeekBar_vsb_progress, progress)
        // 轨道 track
        trackBackgroundColor = a.getColor(R.styleable.VolSeekBar_vsb_track_backgroundColor, trackBackgroundColor)
        trackProgressColor = a.getColor(R.styleable.VolSeekBar_vsb_track_progressColor, trackProgressColor)
        trackWidth = a.getDimension(R.styleable.VolSeekBar_vsb_track_width, trackWidth)
        // 平分进度的小圆点
        dotCount = a.getInteger(R.styleable.VolSeekBar_vsb_dot_count, dotCount)
        dotRadius = a.getDimension(R.styleable.VolSeekBar_vsb_dot_radius, dotRadius)
        dotStrokeWidth = a.getDimension(R.styleable.VolSeekBar_vsb_dot_strokeWidth, dotStrokeWidth)
        dotStrokeColor = a.getColor(R.styleable.VolSeekBar_vsb_dot_strokeColor, dotStrokeColor)
        dotSolidBackColor = a.getColor(R.styleable.VolSeekBar_vsb_dot_solidBackColor, dotSolidBackColor)
        dotIsShow = a.getBoolean(R.styleable.VolSeekBar_vsb_dot_isShow, dotIsShow)
        isNeedVibrate = a.getBoolean(R.styleable.VolSeekBar_vsb_isNeedVibrate, isNeedVibrate)
        // 滑块
        thumbRadius = a.getDimension(R.styleable.VolSeekBar_vsb_thumb_radius, thumbRadius)
        thumbStrokeWidth = a.getDimension(R.styleable.VolSeekBar_vsb_thumb_strokeWidth, thumbStrokeWidth)
        thumbSolidColor = a.getColor(R.styleable.VolSeekBar_vsb_thumb_solidColor, thumbSolidColor)
        // 底部百分比文字
        percentIsShow = a.getBoolean(R.styleable.VolSeekBar_vsb_percent_isShow, percentIsShow)
        percentTextSize = a.getDimension(R.styleable.VolSeekBar_vsb_percent_textSize, percentTextSize)
        percentTextColor = a.getColor(R.styleable.VolSeekBar_vsb_percent_textColor, percentTextColor)
        percentUnit = a.getString(R.styleable.VolSeekBar_vsb_percent_unit) ?: percentUnit
        percentMargin = a.getDimension(R.styleable.VolSeekBar_vsb_percent_margin, percentMargin)
        // 气泡
        isShowBubble = a.getBoolean(R.styleable.VolSeekBar_vsb_bubble_isShow, isShowBubble)
        bubbleHeight = a.getDimension(R.styleable.VolSeekBar_vsb_bubble_height, bubbleHeight)
        bubbleRadius = a.getDimension(R.styleable.VolSeekBar_vsb_bubble_radius, bubbleRadius)
        bubbleMargin = a.getDimension(R.styleable.VolSeekBar_vsb_bubble_margin, bubbleMargin)
        bubbleHorizontalPadding =
            a.getDimension(R.styleable.VolSeekBar_vsb_bubble_horizontalPadding, bubbleHorizontalPadding)
        bubbleTextSize = a.getDimension(R.styleable.VolSeekBar_vsb_bubble_textSize, bubbleTextSize)
        bubbleTextColor = a.getColor(R.styleable.VolSeekBar_vsb_bubble_textColor, bubbleTextColor)
        bubbleColor = a.getColor(R.styleable.VolSeekBar_vsb_bubble_color, bubbleColor)
        a.recycle()
        isLongClickable = true // 不准长按

        paintPercent.typeface = percentTypeface
        paintBubble.typeface = bubbleTypeface
    }

    @Synchronized
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = getFinalSize(widthMeasureSpec, paddingStart + paddingEnd)

        cxMin = thumbRadius + thumbStrokeWidth
        cxMax = width.toFloat() - (thumbRadius + thumbStrokeWidth)
        realProgressWidth =cxMax - cxMin
        currX = getCurrentXFromProgress()

        // val height = getFinalSize(heightMeasureSpec, paddingTop + paddingBottom)
        var height = if (percentIsShow) {
            (cxMin * 2 + paddingTop + paddingBottom + percentTextSize + percentMargin).toInt()
        } else {
            (cxMin * 2 + paddingTop + paddingBottom).toInt()
        }

        // 显示气泡预留高度
        if (isShowBubble) {
            height += (bubbleHeight + bubbleMargin).toInt()
        }

        // 是否震动
        if (isNeedVibrate) {
            dotPositionList.clear()
            for (i in 0 until dotCount) {
                dotPositionList.add(i * (max - min) / (dotCount - 1))
            }
        }

        setMeasuredDimension(width, height)
    }

    private fun getFinalSize(measureSpec: Int, padding: Int): Int {
        val specMode = MeasureSpec.getMode(measureSpec)
        return if (specMode == MeasureSpec.EXACTLY) {
            MeasureSpec.getSize(measureSpec)
        } else {
            ((thumbRadius * 2).coerceAtLeast(dotRadius * 2) + padding).toInt()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        cxMin = thumbRadius + thumbStrokeWidth
        cxMax = measuredWidth.toFloat() - thumbRadius - thumbStrokeWidth

        // 两个点之间的间隔
        val space = (cxMax- cxMin - 2 * dotMargin - 2* dotRadius) / (dotCount - 1)
//        var cx = cxMin    // 圆心x轴
        val baseCy = cxMin + paddingTop
        val cy = if (isShowBubble) {
            baseCy + bubbleHeight + bubbleMargin
        } else {
            baseCy
        }
        var progressX = getCurrentXFromProgress()  // 进度值在x轴的位置
        progressX = progressX.coerceAtLeast(cxMin)
        progressX = progressX.coerceAtMost(cxMax)

        canvas.save()
        getDraggingRange()

        // 画轨道
        drawTrack(canvas, cy, progressX)
        // 画圆点和数字
        if (isRtl()) {
            for (i in dotCount - 1 downTo 0) {
                val cx = cxMax - i * space - dotMargin - dotRadius
                // 小圆点
                if (dotIsShow) {
                    drawDot(canvas, cx, cy, progressX)
                }
                // 画百分比数字
                if (percentIsShow) {
                    drawPercentText(canvas, cx, cy, i)
                }
            }
        } else {
            for (i in 0 until dotCount) {
                var cx = i * space + (cxMin + dotMargin + dotRadius)
                // 小圆点
                if (dotIsShow) {
                    drawDot(canvas, cx, cy, progressX)
                }
                // 画百分比数字
                if (percentIsShow) {
                    drawPercentText(canvas, cx, cy, i)
                }
            }
        }
        // 画滑块
        drawThumb(canvas, cy, progressX)
        // 画气泡
        if (isDragging) {
            drawChatBubble(canvas, progressX, cy)
        }

        canvas.restore()
    }

    /**
     * 画轨道
     */
    private fun drawTrack(canvas: Canvas, cy: Float, progressX: Float) {
        // 背景
        paintTrack.color = trackBackgroundColor
        paintTrack.strokeWidth = trackWidth
        canvas.drawRoundRect(
            cxMin, cy - trackWidth / 2, cxMax, cy + trackWidth / 2, trackWidth, trackWidth, paintTrack
        )
        //进度
        paintTrack.color = trackProgressColor
        if (isRtl()) {
            canvas.drawRoundRect(
                progressX, cy - trackWidth / 2, cxMax, cy + trackWidth / 2, trackWidth, trackWidth, paintTrack
            )
        } else {
            canvas.drawRoundRect(
                cxMin, cy - trackWidth / 2, progressX, cy + trackWidth / 2, trackWidth, trackWidth, paintTrack
            )
        }
    }

    /**
     * 画小圆点
     */
    private fun drawDot(canvas: Canvas, cx: Float, cy: Float, progressX: Float) {
        // stroke
//        paintDot.style = Paint.Style.STROKE
//        paintDot.strokeWidth = dotStrokeWidth
        paintDot.color = dotSolidBackColor
//        canvas.drawCircle(cx, cy, dotRadius, paintDot)
        // solid
        paintDot.style = Paint.Style.FILL
        if (isRtl()) {
            paintDot.color = if (cx <= progressX) dotSolidBackColor else dotTrackProgressColor
        } else {
            paintDot.color = if (cx >= progressX) dotSolidBackColor else dotTrackProgressColor
        }
        canvas.drawCircle(
            cx, cy, dotRadius, paintDot
        )
    }

    /**
     * 画底部百分比文字
     */
    private fun drawPercentText(canvas: Canvas, cx: Float, cy: Float, index: Int) {
        val avg = max.toDouble() / (dotCount - 1)
        val percentageText = if (index == 0 && min > 0) {
            // 如果设置了最小值，首位0使用最小值代替
            "${min}$percentUnit"
        } else {
            "${(index * avg).toInt()}$percentUnit"
        }
        paintPercent.textSize = percentTextSize // 文本大小
        paintPercent.color = percentTextColor
        paintPercent.getTextBounds(percentageText, 0, percentageText.length, textRect)
//        val textY = cy + percentMargin + textRect.height()
        val textX = if (isRtl()) {
            when (index) {
                0 -> measuredWidth - paintPercent.measureText(percentageText)
                dotCount - 1 -> 0f
                else -> cx - paintPercent.measureText(percentageText) / 2
            }
        } else {
            when (index) {
                0 -> 0f
                dotCount - 1 -> measuredWidth - paintPercent.measureText(percentageText)
                else -> cx - paintPercent.measureText(percentageText) / 2
            }
        }

        val fm = paintPercent.getFontMetrics()
        val baseline = cy + percentMargin + textRect.height() + (fm.descent - fm.ascent) / 2f - fm.descent
        canvas.drawText(percentageText, textX, baseline, paintPercent)
    }

    /**
     * 画滑块
     */
    private fun drawThumb(canvas: Canvas, cy: Float, progressX: Float) {
        // solid
        paintThumb.style = Paint.Style.FILL
        paintThumb.color = thumbSolidColor
        canvas.drawCircle(progressX, cy, thumbRadius + thumbStrokeWidth, paintThumb)
        // stroke
        paintThumb.style = Paint.Style.STROKE
        paintThumb.strokeWidth = thumbStrokeWidth
        paintThumb.color = thumbStrokeColor
        canvas.drawCircle(progressX, cy, thumbRadius + thumbStrokeWidth/2, paintThumb)

        paintThumb.style = Paint.Style.FILL
        paintThumb.color = centerStrokeColor
        canvas.drawCircle(progressX, cy, centerThumbRadius, paintThumb)
    }

    /**
     * 画气泡
     */
    private fun drawChatBubble(canvas: Canvas, thumbX: Float, thumbY: Float) {
        // 画布宽度
        val canvasWidth = canvas.width.toFloat()

        // 获取文本宽度用于气泡左右的空间计算
        val progressText = "$progress%"
        paintBubble.textSize = bubbleTextSize // 文本大小
        paintBubble.textAlign = Paint.Align.CENTER
        paintBubble.getTextBounds(progressText, 0, progressText.length, textRect)

        // 计算bubble里的percent宽度，调整bubble的左右空间
        val bubbleWidth = textRect.width() + bubbleHorizontalPadding * 2

        // 计算气泡的位置
        val adjustedThumbY = thumbY + paddingTop  // 调整 thumbY 以考虑 paddingTop
        val bubbleLeft = thumbX - bubbleWidth / 2
        val bubbleBottom = adjustedThumbY - thumbRadius - bubbleMargin
        val bubbleTop = bubbleBottom - bubbleHeight

        // 使用 min 和 max 函数调整气泡位置以限制在画布范围内
        val adjustedBubbleLeft = max(0f, min(bubbleLeft, canvasWidth - bubbleWidth))  // 限制左边界在画布内
        val adjustedBubbleRight = adjustedBubbleLeft + bubbleWidth // 根据调整后的左边界重新计算右边界

        // 绘制气泡矩形
        val bubbleRect = RectF(adjustedBubbleLeft, bubbleTop, adjustedBubbleRight, bubbleBottom)
        paintBubble.color = bubbleColor // 气泡的背景颜色
        paintBubble.style = Paint.Style.FILL
        canvas.drawRoundRect(bubbleRect, bubbleRadius, bubbleRadius, paintBubble)

        // 计算气泡矩形的中心以定位文本
        paintBubble.color = bubbleTextColor // 文本颜色
        val textX = adjustedBubbleLeft + (bubbleWidth / 2) // 水平中心
        val textY = bubbleTop + (bubbleHeight / 2) + (paintBubble.textSize / 3) // 垂直中心
        canvas.drawText(progressText, textX, textY, paintBubble)
    }

    /**
     * 根据进度值计算X轴位置
     */
    private fun getCurrentXFromProgress(): Float {
        if (isRtl()) {
            return cxMax - realProgressWidth / max.toFloat() * progress.toFloat()
        }
        return realProgressWidth / max.toFloat() * progress.toFloat() + cxMin
    }

    private fun getDraggingRange() {
        touchYMin = cxMin + paddingTop + bubbleHeight + bubbleMargin - thumbRadius- thumbStrokeWidth - 4f.dp2px()
        touchYMax = cxMin + paddingTop +  bubbleHeight + bubbleMargin + thumbRadius + thumbStrokeWidth + 8f.dp2px()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.actionMasked == MotionEvent.ACTION_DOWN && (event.y > touchYMax || event.y < touchYMin)) {
            return false
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                parent.requestDisallowInterceptTouchEvent(true)
                progress = getProgressFromX(event.x, adsorption)
                invalidate()
                vibrate()
                callProgress(true)
            }

            MotionEvent.ACTION_MOVE -> {
                isDragging = true
                progress = getProgressFromX(event.x, false)
                invalidate()
                vibrate()
                callProgress(false)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false
//                progress = getProgressFromX(event.x, false)
                invalidate()
//                callProgress()
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        return true
    }

    /**
     * 震动
     */
    private fun vibrate() {
        if (isNeedVibrate) {
            if (progress in dotPositionList) {
                if (!vibrationTriggered) {
                    dotPositionList.firstOrNull {
                        progress == it
                    }?.apply {
                        vibrationTriggered = true
                        context.vibrate()
                    }
                }
            } else {
                vibrationTriggered = false
            }
        }
    }

    // 修正最小刻度，模拟类似四舍五入
    private fun fixX(x: Float): Float {
        val minUnit = realProgressWidth / max.toFloat()
        val value = x % minUnit
        return if (value > minUnit / 2) {
            x - value + minUnit
        } else {
            x - value + minUnit
        }
    }

    /**
     * 回调
     */
    private fun callProgress(isStart: Boolean) {
        onSeekBarChangeListener.invoke(this, progress, isStart)
    }

    private var onSeekBarChangeListener: (seekBar: VolSeekBar, progress: Int, isStart: Boolean) -> Unit? = { _, _, _ -> }

    fun setOnSeekBarChangeListener(e: (seekBar: VolSeekBar, progress: Int, isStart: Boolean) -> Unit) {
        this.onSeekBarChangeListener = e
    }

    /**
     * 根据X轴位置计算进度值。
     * @param x 手指触摸的x轴
     * @param adsorption 是否使用吸附，点击时，如果使用吸附，则时progress吸附到最近的一个点
     */
    private fun getProgressFromX(x: Float, adsorption: Boolean): Int {
        currX = fixX(x)
        if (currX < cxMin) currX = cxMin
        if (currX > cxMax) currX = cxMax
//        val unit = max / (dotCount - 1)
//        if (adsorption) {
//            // 两个点之间的间隔
//            val space = realProgressWidth / (dotCount - 1)
//            for (i in 0 until dotCount - 1) {
//                val l = i * space + cxMin
//                val r = l + space
//                if (currX > l && currX <= r) {
//                    // 如果使用吸附，并且值靠近右侧，则使用右侧的值
//                    return if (currX - l < r - currX) {
//                        currX = l
//                        i * unit
//                    } else {
//                        currX = r
//                        (i + 1) * unit
//                    }
//                }
//            }
//        }
        val progress = if (isRtl()) {
            when {
                currX <= cxMin -> max
                currX >= cxMax -> min
                else -> ((cxMax - currX) / realProgressWidth * max).roundToInt()
            }
        } else {
            when {
                currX <= cxMin -> min
                currX >= cxMax -> max
                else -> ((currX - cxMin) / realProgressWidth * max).roundToInt()
            }
        }

        // 如果设置了最小值min，则当进度progress=0时，返回最小值min
        if (progress == 0 && min > 0) {
            return min
        }
        return progress
    }

    /**
     * ============================== 对外暴露的函数 ==============================
     */
    fun getMin(): Int = min

    fun getMax(): Int = max

    fun setMin(min: Int): VolSeekBar {
        if (this.min != min && min >= 0 && min < max) {
            this.min = min
            invalidate()
            requestLayout()
        }
        return this
    }

    fun setMax(max: Int): VolSeekBar {
        if (this.max != max && max > 0 && max > min) {
            this.max = max
            invalidate()
            requestLayout()
        }
        return this
    }

    fun getProgress(): Int {
        return progress
    }

    fun setProgress(progress: Int): VolSeekBar {
        return setProgress(progress, true)
    }

    fun setProgress(progress: Int, callback: Boolean): VolSeekBar {
        if (this.progress != progress) {
            this.progress = progress
            requestLayout()
            if (callback) {
                callProgress(false)
            }
        }
        return this
    }

    fun setTrackBackgroundColor(@ColorInt backColor: Int): VolSeekBar {
        if (trackBackgroundColor != backColor) {
            trackBackgroundColor = backColor
            invalidate()
        }
        return this
    }

    fun setTrackProgressColor(@ColorInt progressLineColor: Int): VolSeekBar {
        if (trackProgressColor != progressLineColor) {
            trackProgressColor = progressLineColor
            invalidate()
        }
        return this
    }

    /**
     * 点击吸附最近圆点开关
     */
    fun setAdsorption(enable: Boolean): VolSeekBar {
        adsorption = enable
        return this
    }

    fun setPercentTypeface(typeface: Typeface): VolSeekBar {
        if (percentTypeface != typeface) {
            percentTypeface = typeface
            invalidate()
        }
        return this
    }

    fun setBubbleTypeface(typeface: Typeface): VolSeekBar {
        if (bubbleTypeface != typeface) {
            bubbleTypeface = typeface
            invalidate()
        }
        return this
    }
}