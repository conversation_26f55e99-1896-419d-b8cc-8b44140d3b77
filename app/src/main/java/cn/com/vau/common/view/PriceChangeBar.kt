package cn.com.vau.common.view

import android.animation.ObjectAnimator
import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.util.numFormat
import cn.com.vau.util.setTextDiff


open class PriceChangeBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mTvMin: TextView
    private var mTvMax: TextView
    private var mTvUnit: TextView
    private var mIvCurrent: ImageView

    //是否显示动画
    private var isShowAnim = true

    //动画持续时间
    private var animDuration = 150L

    //可移动的宽度
    private var canMoveWith = 0
    private var lastMoveX = 0.0f

    //最小值和最大值float类型
    private var min = 0f
    private var max = 0f
    // 保留小数位
    private var digit = 3

    private var unit: String? = ""

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        canMoveWith = w - mIvCurrent.measuredWidth
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_price_change_view, this, true)

        mTvMin = findViewById<TextView>(R.id.mTvMin)
        mTvMax = findViewById<TextView>(R.id.mTvMax)
        mTvUnit = findViewById<TextView>(R.id.mTvUnit)
        mIvCurrent = findViewById(R.id.mIvCurrent)
    }

    /**
     * 是否显示动画
     */
    fun isShowAnim(showAnim: Boolean) {
        isShowAnim = showAnim
    }

    /**
     * 动画时间
     */
    fun setAnimDuration(duration: Long) {
        animDuration = duration
    }

    fun setMax(max: Float) {
        this.max = max
        mTvMax.setTextDiff(max.numFormat(digit, false))
    }

    fun setMin(min: Float) {
        this.min = min
        mTvMin.setTextDiff(min.numFormat(digit, false))
    }

    fun setDigit(digit: Int) {
        this.digit = digit
    }

    fun setUnit(unit: String?) {
        (if (TextUtils.isEmpty(unit)) "--" else unit)?.let { mTvUnit.setTextDiff(it) }
        this.unit = unit
    }

    open fun updateCurrent(current: Float) {
        if (min >= max) {
            return
        }
        val percent = computePercent(min, max, current)

        if (canMoveWith == 0) {
            this.post {
                scrollToCurrent(percent)
            }
        } else {
            scrollToCurrent(percent)
        }
    }

    //计算百分比
    private fun computePercent(min: Float, max: Float, current: Float): Float {
        if (max <= min) {
            return 0f
        }
        if (current <= min) {
            return 0f
        }
        if (current >= max) {
            return 1f
        }
        return (current - min) / (max - min)
    }

    private fun scrollToCurrent(percent: Float) {
        val moveToX = if(isRtl()) {
            //当RTL时，x坐标需要为负数
            0 - (canMoveWith * percent)
        } else {
            (canMoveWith * percent)
        }
        if (isShowAnim) {
            doMoveAnim(lastMoveX, moveToX)
        } else {
            mIvCurrent.translationX = moveToX
        }
        lastMoveX = moveToX
    }

    private var objAnimator: ObjectAnimator? = null
    private fun doMoveAnim(startX: Float, endX: Float) {
        if (objAnimator == null) {
            objAnimator = ObjectAnimator.ofFloat(mIvCurrent, "translationX", startX, endX)
            objAnimator?.setDuration(animDuration)
            objAnimator?.start()
        } else {
            if (objAnimator?.isRunning == true) {
                objAnimator?.cancel()
            }
            objAnimator?.setFloatValues(startX, endX)
            objAnimator?.start()
        }
    }

    fun getMin(): Float = min
    fun getMax(): Float = max

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (objAnimator?.isRunning == true) {
            objAnimator?.cancel()
        }
    }
}