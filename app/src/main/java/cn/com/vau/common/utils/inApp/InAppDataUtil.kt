package cn.com.vau.common.utils.inApp

import androidx.core.view.isGone
import androidx.viewbinding.ViewBinding
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.init.InAppBean

/**
 * Filename: InAppDataUtils.kt
 * Author: GG
 * Date: 2023/11/29
 * Description:
 */
object InAppDataUtil : InAppDataImpl() {

    /**
     * 内部应用数据
     */
    override var data: InAppBean.InAppData? = null

    override val viewList = mutableListOf<ViewBinding?>()

    /**
     * 是否可以显示内部应用数据的标志。
     */
    override var isCanShow: Boolean
        get() = SpManager.getMessageInAppIsShow(false)
        set(value) {
            // 保存进入应用的状态，内部应用需要使用，进入应用仅展示一次
            SpManager.putMessageInAppIsShow(value)
        }

    /**
     * 保存内部应用数据。
     *
     * @param list 要保存的内部应用数据。
     */
    override fun saveData(list: MutableList<InAppBean.InAppData>?) {
        this.data = list?.getOrNull(0)
    }

    override fun saveViewBinding(viewBinding: ViewBinding) {
        viewList.add(viewBinding)
    }

    /**
     * 显示下一个要展示的内部应用数据。
     *
     * @return 要展示的下一个内部应用数据，如果没有则返回 null。
     */
    override fun showData(): InAppBean.InAppData? = data

    /**
     * 清除内部应用数据。
     */
    override fun clearData() {
        data = null
        viewList.forEach { binding ->
            binding?.root?.isGone = true
        }
    }
}