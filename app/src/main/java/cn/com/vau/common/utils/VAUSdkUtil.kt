package cn.com.vau.common.utils

import android.content.Context
import android.os.Looper
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.data.init.*
import cn.com.vau.trade.fragment.deal.TestLog
import cn.com.vau.util.*
import cn.com.vau.util.opt.xhLoge
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.*

/**
 * Created by roy on 2018/10/13.
 */
object VAUSdkUtil {

    private val shareGoodList by lazy { CopyOnWriteArrayList<ShareProductGroupsData>() }

    private val symbolList by lazy { CopyOnWriteArrayList<ShareProductData>() }

    // 多品牌/跟单自主 持仓列表
    private val shareOrderList by lazy { CopyOnWriteArrayList<ShareOrderData>() }
    private val shareAccountBean by lazy { ShareAccountInfoData() }

    private val stShareStrategyList by lazy { CopyOnWriteArrayList<StShareStrategyData>() }
    private val stShareAccountBean by lazy { StShareAccountInfoData() }

    // 自选
    @JvmStatic
    val collectSymbolList by lazy { CopyOnWriteArrayList<ShareProductData>() }

    // 服务器时间戳 （ 更新：接口+WS ）
    var serverTimeMillis = "0"

    // 服务器时间（减去时差和时令后的时间）
    var socketCurrentData = -1L

    /**
     * 产品组列表
     */
    fun shareGoodList(): CopyOnWriteArrayList<ShareProductGroupsData> {
        return shareGoodList
    }

    /**
     * 产品列表
     */
    @JvmStatic
    fun symbolList(): CopyOnWriteArrayList<ShareProductData> {
        return symbolList
    }

    /**
     * 持仓列表
     */
    @JvmStatic
    fun shareOrderList(): CopyOnWriteArrayList<ShareOrderData> {
        return shareOrderList
    }

    /**
     * 资产信息 [ 多品牌 ]
     */
    fun shareAccountBean(): ShareAccountInfoData {
        return shareAccountBean
    }

    /**
     * 正在跟随策略
     */
    fun stShareStrategyList(): CopyOnWriteArrayList<StShareStrategyData> {
        return stShareStrategyList
    }

    /**
     * 资产信息 [ 跟单 ]
     */
    fun stShareAccountBean(): StShareAccountInfoData {
        return stShareAccountBean
    }

    /**
     * 接收行情后更新产品属性
     */
    fun updateQuotation(socketList: List<SocketSymbolData>?) {

        for (socketData in socketList ?: arrayListOf()) {

            // 过滤是否在产品列表
            val shareData = getQuotesData(socketData.symbol.ifNull()) ?: continue

            shareData.lasttime = socketData.lasttime.ifNull()

            if (shareData.originalAsk == socketData.ask && shareData.originalBid == socketData.bid) continue

            var ask = socketData.ask.ifNull()
            var bid = socketData.bid.ifNull()

            shareData.originalAsk = ask.ifNull()
            shareData.originalBid = bid.ifNull()

            if (shareData.pips > 0) {
                ask += shareData.askPips
                bid -= shareData.bidPips
            }

            val lastAsk = shareData.ask
            val lastBid = shareData.bid

            shareData.refresh = true

            // TODO xinhuan
            TestLog.printBean(-1, shareData)

            if(lastBid != 0f) {
                if(bid > lastBid) {
                    shareData.bidType = 1
                } else if(bid < lastBid) {
                    shareData.bidType = 2
                }
            }

            shareData.bid = bid

            if(lastAsk != 0f) {
                if(ask > lastAsk) {
                    shareData.askType = 1
                } else if(ask < lastAsk) {
                    shareData.askType = 2
                }
            }

            shareData.ask = ask

            shareData.minprice = socketData.low.ifNull()
            shareData.maxprice = socketData.high.ifNull()

            // 处理耗时数据，供 adapter 直接使用
            updateShareProductUiData(shareData)

        }

        updateAccountInfo()

    }

    /**
     * 更新用户交易信息 （ 持仓 && 账户信息 ）
     */
    fun updateAccountInfo() {

        if(!UserDataUtil.isLogin()) return

        if(UserDataUtil.isStLogin()) {
            updateStAccountInfo()
            return
        }

        updateOrderProfit()

        var profit = 0.0

        for (orderBean in shareOrderList) {
            if(TextUtils.isEmpty(orderBean.order)) continue
            profit = profit.mathAdd(orderBean.totalProfit.ifNullToDouble())
        }

        // 1
        shareAccountBean.profit = profit
        // 净值 = 余额 + 盈亏 + 信用金
        shareAccountBean.equity = shareAccountBean.balance + profit + shareAccountBean.credit

        // 可用预付款 = 净值 - 已用保证金
        shareAccountBean.freeMargin = shareAccountBean.equity - shareAccountBean.margin

        shareAccountBean.marginLevel = if(shareAccountBean.margin == 0.0)
            0.0
        else
            shareAccountBean.equity / shareAccountBean.margin * 100

    }

    /**
     * 更新订单盈亏
     */
    fun updateOrderProfit() {

        if(shareOrderList.size == 0) return

        for (orderBean in shareOrderList) {

            val symbolData = symbolList.firstOrNull { it.symbol == orderBean.symbol } ?: return

            orderBean.bid = symbolData.bid
            orderBean.ask = symbolData.ask
            orderBean.bidType = symbolData.bidType
            orderBean.askType = symbolData.askType
            orderBean.lasttime = symbolData.lasttime
            orderBean.closePrice = if (OrderUtil.isBuyOfOrder(orderBean.cmd)) "${orderBean.bid}" else "${orderBean.ask}"
            orderBean.priceCurrency = symbolData.priceCurrency

            if (0f == orderBean.bid && 0f == orderBean.ask) {
                orderBean.profit = 0.0
                orderBean.totalProfit = "0"
                orderBean.isRefresh = true
                continue
            }

            // 盈亏
            val profitLoss = getProfitLoss(
                symbolData,
                orderBean.openPrice.ifNull(),
                orderBean.volume.ifNull(),
                orderBean.cmd.ifNull()
            )
            // 2
            orderBean.profit = profitLoss.toDouble()

            val totalProfit = profitLoss.toString().mathAdd(orderBean.profitCommission)
//            // 总收益 = 盈亏 + 仓息 + 佣金
//            var totalProfit = profitLoss.toString().mathAdd(orderBean.swap ?: "0")
//
//            // mt4 加 佣金，因为 mt4 平台下单时余额不扣除佣金，mt5平台订单成交时就会扣除佣金
//            if (!UserDataUtil.isMT5()) {
//                totalProfit = totalProfit.mathAdd(orderBean.commission ?: "0")
//            }

            orderBean.totalProfit = totalProfit
            orderBean.volumeUI = orderBean.volume

            // TODO: 可移除
            orderBean.currentPriceUI = orderBean.closePrice.numFormat(orderBean.digits)
            orderBean.profitUI = orderBean.profit.numCurrencyFormat()

            orderBean.isRefresh = true

        }

    }

    private fun updateStAccountInfo() {

        // 跟单自主交易
        updateStOrderProfit()
        // 跟单跟随策略
        updateStFollowStrategyProfit()

        var profit = 0.0

        for (orderBean in shareOrderList) {
            profit = profit.mathAdd(orderBean.totalProfit.ifNullToDouble())
        }

        stShareAccountBean.profit = profit

        stShareAccountBean.equity = stShareAccountBean.balance + profit + stShareAccountBean.credit

        val tempFreeMargin = stShareAccountBean.equity - stShareAccountBean.margin

        stShareAccountBean.freeMargin = tempFreeMargin

        stShareAccountBean.marginLevel = if (stShareAccountBean.margin == 0.0)
            0.0
        else
            stShareAccountBean.equity / stShareAccountBean.margin * 100

        val followCredit = stShareAccountBean.followCredit.toDoubleCatching()
        // 净值 ： 跟单余额 + 订单盈亏 + 信用金
        stShareAccountBean.followEquity = stShareAccountBean.followBalance + stShareAccountBean.followFloatingPl + followCredit

    }

    /**
     * 更新持仓订单 【跟单自主】
     * todo 在 stInitPosition中没有调用，会导致下拉刷新新最新价和手数闪一下
     */
    private fun updateStOrderProfit() {

        if (shareOrderList.size == 0) return

        for (orderBean in shareOrderList) {

            val orderSymbol = orderBean.symbol

            for (dataBean in symbolList) {

                if (orderSymbol != dataBean.symbol) continue

                orderBean.bid = dataBean.bid
                orderBean.bidType = dataBean.bidType
                orderBean.ask = dataBean.ask
                orderBean.askType = dataBean.askType
                orderBean.lasttime = dataBean.lasttime
                orderBean.closePrice = "${if (OrderUtil.isBuyOfOrder(orderBean.cmd)) orderBean.bid else orderBean.ask}"
                orderBean.priceCurrency = dataBean.priceCurrency

                val profitLoss = getProfitLoss(
                    dataBean,
                    orderBean.openPrice.ifNull(),
                    orderBean.volume.ifNull(),
                    orderBean.cmd.ifNull()
                )

                orderBean.profit = profitLoss.toDouble()
                orderBean.totalProfit = profitLoss.toString().mathAdd(orderBean.profitCommission)

                orderBean.currentPriceUI = orderBean.closePrice.numFormat(orderBean.digits)
                // 跟单接口已处理，不用APP端二次格式化，为了适配器统一
                orderBean.volumeUI = orderBean.volume
                orderBean.profitUI = orderBean.profit.numCurrencyFormat()

                orderBean.isRefresh = true

                break

            }
        }
    }

    /**
     * 更新跟随策略信息 ( 策略信息 && 跟随策略持仓 )
     */
    fun updateStFollowStrategyProfit() {

        if (stShareStrategyList.size == 0) return

        var allProfit = 0.0

        // 跟随策略列表
        for (followBean in stShareStrategyList) {

            var followProfit = 0.0

            // 跟随策略持仓订单
            for (orderData in followBean.positions ?: arrayListOf()) {

                if (orderData.status == "PENDINGOPEN") continue

                val orderSymbol = orderData.symbol

                val shareProductData = symbolList.firstOrNull {
                    it.symbol == orderSymbol
                } ?: continue

                orderData.bid = shareProductData.bid
                orderData.bidType = shareProductData.bidType
                orderData.ask = shareProductData.ask
                orderData.askType = shareProductData.askType
                orderData.lasttime = shareProductData.lasttime
                orderData.closePrice = "${if (OrderUtil.isBuyOfOrder(orderData.cmd)) orderData.bid else orderData.ask}"
                orderData.priceCurrency = shareProductData.priceCurrency

                val profitLoss = getProfitLoss(
                    shareProductData,
                    orderData.openPrice.ifNull(),
                    orderData.volume.ifNull(),
                    orderData.cmd.ifNull()
                )

                orderData.profit = profitLoss.toDouble()
                orderData.totalProfit = profitLoss.toString().mathAdd(orderData.swap)

                orderData.currentPriceUI = orderData.closePrice.numFormat(orderData.digits)
                orderData.volumeUI = orderData.volume
                orderData.profitUI = orderData.profit.numCurrencyFormat()

                followProfit += orderData.totalProfit.toDoubleCatching()

                orderData.isRefresh = true

            }

            // 单个信号源
            followBean.profit = followProfit

            allProfit += followProfit

            // adapter 直接使用
            followBean.followTotalProfit = followBean.profit + followBean.totalHistoryProfit
            followBean.pnlUI = followBean.followTotalProfit.numCurrencyFormat()
            followBean.roi = if (followBean.investmentAmount.ifNull("0") == "0") {
                0.0
            } else {
                followBean.followTotalProfit.div(followBean.investmentAmount?.toDoubleCatching() ?: 0.0).times(100)
            }
            followBean.returnUI = followBean.roi.numCurrencyFormat("2")
            followBean.equityUI = followBean.balance.mathAdd("${followBean.profit}").mathAdd(followBean.investmentCredit).numCurrencyFormat()
            followBean.investedUI = followBean.investmentAmount?.numCurrencyFormat()
            followBean.totalShareProfitUI = followBean.totalSharedProfit.numCurrencyFormat()

            followBean.isRefresh = true

        }

        // 所有信号源
        stShareAccountBean.followFloatingPl = allProfit

    }

    /**
     * 产品属性，开盘价格，手数，买卖类型
     */
    fun getProfitLoss(
        shareProductData: ShareProductData,
        openPrice: String,
        volume: String,
        orderType: String
    ): Float {
        val direction = if (OrderUtil.isBuyOfOrder(orderType)) 1.0f else -1.0f
        // 订单止赢止损预计盈亏 所使用 closePrice 是用户手动设置的值
        val closePrice = if (1f == direction) "${shareProductData.bid}" else "${shareProductData.ask}"
        return getProfitLoss(
            shareProductData, openPrice, volume, orderType, closePrice
        )
    }

    @JvmStatic
    fun getProfitLoss(
        shareProductData: ShareProductData,
        openPrice: String,
        volume: String,
        orderType: String,
        closePrice: String
    ): Float {

        val symbol = shareProductData.symbol.ifNull()

        var currencyType = UserDataUtil.currencyType()

        val isUSC = currencyType == "USC"
        if (isUSC) currencyType = "USD"

        if ("USDT" == currencyType) currencyType = "UST"

        // 订单方向
        val direction = if (OrderUtil.isBuyOfOrder(orderType)) 1.0f else -1.0f

        /**
         * Buy单
         * A=现价*手数*合约规模*汇率    四舍五入
         * B=开仓价*手数*合约规模*汇率   四舍五入
         * 利润=A-B
         *
         * Sell单
         * A=现价*手数*合约规模*汇率     四舍五入
         * B=开仓价*手数*合约规模*汇率   四舍五入
         * 利润=B-A
         */
        // 手数 * 合约规模 * 汇率
        val plParam = volume.mathMul(shareProductData.contractsize ?: "1", direction.toString())
        // 盈亏
        var profitLoss = plParam.mathMul(closePrice).mathSub(plParam.mathMul(openPrice)).toFloatCatching()

        /**
         * 平仓-开仓
         *
         * val closeSubOpen = MathUtil.sub(orderBean.closePrice, orderBean.openPrice)
         * 手数 * （平仓-开仓）
         * val volumeMulPrice = MathUtil.mul(orderBean.volume, closeSubOpen)
         * 手数 *（现价-开仓价格）* 合约数
         * val contracMulPrice = MathUtil.mul(volumeMulPrice, orderBean.contractsize ?: "1")
         * 手数 *（现价-开仓价格）* 合约数 * 操作
         * val dirMulPrice = MathUtil.mul(contracMulPrice, direction.toString())
         * 盈亏
         * var profitLoss = dirMulPrice.toFloat()
         */
        if ("forex" == shareProductData.stoplossmodel.lowercase(Locale.getDefault())) {

            if (!symbol.contains(currencyType) || symbol.startsWith(currencyType)) {

                // XXXYYY
                val symbolYYY =
                    if (symbol.length <= 3)
                        ""
                    else if (symbol.length >= 6)
                        symbol.substring(3, 6)
                    else
                        symbol.substring(3, symbol.length)

                val suffixZZ = if (symbol.length > 6) symbol.substring(6) else ""

                var mulPrice: Float = -1f
                var divPrice: Float = -1f

                for (symbolData in symbolList) {
                    if (symbolData.symbol.contains("$symbolYYY$currencyType$suffixZZ")) {
                        mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                        profitLoss = profitLoss.mathMul(mulPrice)
                        break
                    }
                    if (symbolData.symbol.contains("$currencyType$symbolYYY$suffixZZ")) {
                        divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                        profitLoss = profitLoss.mathDiv(divPrice, 8)
                        break
                    }
                }

                // !①
                if (mulPrice == -1f && divPrice == -1f) {
                    for (symbolData in symbolList) {
                        if (symbolData.symbol.contains("USD$symbolYYY$suffixZZ")) {
                            divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            profitLoss = profitLoss.mathDiv(divPrice, 8)
                            break
                        }
                        if (symbolData.symbol.contains("${symbolYYY}USD$suffixZZ")) {
                            mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            profitLoss = profitLoss.mathMul(mulPrice)
                            break
                        }
                    }

                    // ②
                    if (mulPrice != -1f || divPrice != -1f) {
                        for (symbolData in symbolList) {
                            if (symbolData.symbol.contains("USD$currencyType")) {
                                mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                profitLoss = profitLoss.mathMul(mulPrice)
                                break
                            }
                            if (symbolData.symbol.contains("${currencyType}USD")) {
                                divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                profitLoss = profitLoss.mathDiv(divPrice, 8)
                                break
                            }
                        }
                    }

                }

            }

        } else {

            // 预付款货币
            val marginCurrency =
                if (UserDataUtil.isStLogin()) {
                    shareProductData.currency
                } else {
                    if (UserDataUtil.isMT5() || UserDataUtil.isVts())
                        shareProductData.profit_currency
                    else
                        shareProductData.margin_currency
                }

            if (marginCurrency != currencyType) {

                var mulPrice: Float = -1f
                var divPrice: Float = -1f

                for (symbolBean in symbolList) {

                    if (symbolBean.symbol.contains("$marginCurrency$currencyType")) {
                        mulPrice = if (direction == 1f) symbolBean.bid else symbolBean.ask
                        profitLoss = profitLoss.mathMul(mulPrice)
                        break
                    }
                    if (symbolBean.symbol.contains("$currencyType$marginCurrency")) {
                        divPrice = if (direction == 1f) symbolBean.bid else symbolBean.ask
                        profitLoss = profitLoss.mathDiv(divPrice, 8)
                        break
                    }

                }

                if (mulPrice == -1f && divPrice == -1f) {

                    for (symbolData in symbolList) {

                        if (symbolData.symbol.contains("USD$marginCurrency")) {
                            divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            profitLoss = profitLoss.mathDiv(divPrice, 8)
                            break
                        }
                        if (symbolData.symbol.contains("${marginCurrency}USD")) {
                            mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            profitLoss = profitLoss.mathMul(mulPrice)
                            break
                        }

                    }

                    if (mulPrice != -1f || divPrice != -1f) {
                        for (symbolData in symbolList) {

                            if (symbolData.symbol.contains("USD$currencyType")) {
                                mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                profitLoss = profitLoss.mathMul(mulPrice)
                                break
                            }
                            if (symbolData.symbol.contains("${currencyType}USD")) {
                                divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                profitLoss = profitLoss.mathDiv(divPrice, 8)
                                break
                            }

                        }
                    }

                }

            }

        }

        if (isUSC) profitLoss = profitLoss.mathMul(100f)

        return profitLoss

    }

    // 长链接推送产品开市闭市
    fun initMarketCloseState(tradeTimeList: List<MarketCloseData>) {
        for (shareGoodData in shareGoodList) {
            for (shareSymbolData in shareGoodData.symbolList ?: arrayListOf()) {
                for (tradeTimeData in tradeTimeList) {
                    val (state, symbol) = tradeTimeData
                    if (shareSymbolData.symbol == symbol) shareSymbolData.marketClose = "0" == state
                }
            }
        }
        EventBus.getDefault().post(NoticeConstants.Init.MARKET_CLOSE_CHECK)
    }

    // ~消耗 160ms
    fun initMarketClose() {

        var serverTimeLong = if (serverTimeMillis == "0") {
            System.currentTimeMillis().toString()
        } else {
            serverTimeMillis
        }.toLongCatching()

        socketCurrentData = serverTimeLong - (AppUtil.getTimeZoneRawOffsetToHour() - Constants.season) * 60 * 60 * 1000L

        val isWeekend = CalendarUtil.getInstance().isWeekend(socketCurrentData)
        EventBus.getDefault().post(if (isWeekend) NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_WEEKEND else NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_NOT_WEEKEND)

        val week = PickerDateUtil.getWeek(socketCurrentData)

        if (week == -1) return
        // 因为系统广播过来的时间不是正分钟是提前了半秒左右 类似于xx:59.590左右 所以需要加上一定的偏移量，这样格式化以后会是正确的小时分钟数，增加的秒数会被格式化掉所以10s也可以
        val testTime = PickerDateUtil.formatHhMm(socketCurrentData + 10 * 1000)

        val currentTime = PickerDateUtil.dateToTime(testTime, "HH:mm")
        for (shareGood in shareGoodList) {
            val testSymbolList = shareGood.symbolList ?: continue

            for (shareSymbolData in testSymbolList) {
                val tradeTimeList = shareSymbolData.tradetime

                if (tradeTimeList.size < week + 1) {
                    shareSymbolData.marketClose = false
                    shareSymbolData.refresh = true
                    continue
                }

                var marketClose = true
                val timeDataList = tradeTimeList.elementAtOrNull(week)?.timeList ?: arrayListOf()

                for (timeStr in timeDataList) {
                    if (!timeStr.contains("-")) break

                    val splitArray = timeStr.split("-").toTypedArray()
                    val startTime = splitArray.getOrNull(0)
                    val endTime = splitArray.getOrNull(1)

                    if (startTime.isNullOrEmpty() || endTime.isNullOrEmpty()) break

                    if (
                        currentTime in PickerDateUtil.dateToTimeMarketClose(startTime)..(PickerDateUtil.dateToTimeMarketClose(endTime))
                    ) {
                        marketClose = false
                        break
                    }
                }

                shareSymbolData.marketClose = marketClose
                shareSymbolData.refresh = true

            }
        }
        EventBus.getDefault().post(NoticeConstants.Init.MARKET_CLOSE_CHECK)
    }

    private fun getQuotesData(symbol: String): ShareProductData? {
        for (goodList in shareGoodList) {
            for (shareData in goodList?.symbolList ?: arrayListOf()) {
                if (shareData.symbol == symbol) return shareData
            }
        }
        return null
    }

    fun getGroupNameLanguage(context: Context, groupNameEn: String): String {
        if (TextUtils.isEmpty(groupNameEn)) return ""
        return when (groupNameEn.lowercase()) {
            "forex" -> context.getString(R.string.forex)
            "crypto" -> context.getString(R.string.crypto)
            "share cfds", "share" -> context.getString(R.string.share_cfds)
            "indices" -> context.getString(R.string.indices)
            "metals" -> context.getString(R.string.metals)
            "commodities" -> context.getString(R.string.commodities)
            "ndfs" -> "NDFS"
            "etf" -> "ETF"
            "bond" -> context.getString(R.string.bond)
            else -> groupNameEn
        }
    }

    // 交易-自选  对数据进行处理
    @OptIn(DelicateCoroutinesApi::class)
    fun updateShareProductUiData(shareData: ShareProductData) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            GlobalScope.launch {
                dealSlowData(shareData)
//                withContext(Dispatchers.Main) {
//                    complete.invoke()
//                }
            }
        } else {
            dealSlowData(shareData)
//            complete.invoke()
        }
    }

    private fun dealSlowData(data: ShareProductData) {

        val closePrice = data.closePrice.toFloatCatching()
        data.diff = data.bid - closePrice
        data.rose = if (closePrice == 0f) {
            0f
        } else {
            data.diff * 100f / closePrice
        }

        // 这些方法比较耗时，因此在这里解析，用来UI显示，显示时直接取
        data.bidUI = data.bid.formatProductPrice2(data.digits, true, Constants.DOUBLE_LINE)
        data.askUI = data.ask.formatProductPrice2(data.digits, true, Constants.DOUBLE_LINE)
        if (data.bidUI == Constants.DOUBLE_LINE || data.askUI == Constants.DOUBLE_LINE) {
            data.spreadUI = Constants.DOUBLE_LINE
        } else {
            val spread: Float = (abs(data.ask - data.bid) * 10.0.pow((data.digits).toDouble())).toFloat()
            data.spreadUI = spread.numFormat2(0, true)
        }
        // 应马东的要求这里改为四舍五入
        data.roseUI = if (closePrice == 0f || data.bidUI == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else data.rose.numFormat2(2, true)
        data.diffUI = if (data.roseUI == Constants.DOUBLE_LINE) Constants.DOUBLE_LINE else data.diff.numFormat2(data.digits, true)
        TestLog.printBean(-100, data)
    }

}