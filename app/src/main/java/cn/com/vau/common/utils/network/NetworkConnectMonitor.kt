package cn.com.vau.common.utils.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import cn.com.vau.common.application.VauApplication

/**
 * 网络连接状态监听器
 * * （主要是为解决非正常手段绕过强更 / 维护页的问题）
 */
object NetworkConnectMonitor {

    private val listener: NetworkConnectListener by lazy {
        NetworkConnectListener()
    }

    fun register() {
        val builder = NetworkRequest.Builder()
        val networkRequest = builder.build()
        val connectivityManager = VauApplication.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        try {
            // 注册的时候 设置当前网络状态
            listener.currentState = getNetworkStatus() != -1
            connectivityManager.registerNetworkCallback(networkRequest, listener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun isNetworkConnected(transportType: Int): Boolean {
        val connectivityManager = VauApplication.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        if (networkCapabilities != null) {
            return networkCapabilities.hasTransport(transportType)
        }
        return false
    }

    private fun isWifiConnected(): Boolean {
        val connect = isNetworkConnected(NetworkCapabilities.TRANSPORT_WIFI)
//        LogUtil.d("wj", "isWifiConnected: $connect")
        return connect
    }

    private fun isMobileConnected(): Boolean {
        val connect = isNetworkConnected(NetworkCapabilities.TRANSPORT_CELLULAR)
//        LogUtil.d("wj", "isMobileConnected: $connect")
        return connect
    }

    /**
     * 获取当前网络连接状态
     * @return -1:无连接   1:WIFI连接    2:移动网络连接
     */
    fun getNetworkStatus(): Int {
        return when {
            isWifiConnected() -> 1
            isMobileConnected() -> 2
            else -> -1
        }
    }

    fun unregister() {
        val connectivityManager = VauApplication.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        connectivityManager.unregisterNetworkCallback(listener)
    }
}