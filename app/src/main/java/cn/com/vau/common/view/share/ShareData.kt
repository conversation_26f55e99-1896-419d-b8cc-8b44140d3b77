package cn.com.vau.common.view.share

import android.graphics.Bitmap
import androidx.annotation.Keep
import cn.com.vau.common.view.popup.bean.ShareEditData
import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * Filename: ShareLayoutItem
 * Author: GG
 * Date: 2023/9/12 0012 10:49
 * Description:
 */
data class ShareData(
    override var itemType: Int,
    /**
     * 二维码图片
     */
    var qrCodeUrl: String? = null,
    /**
     * 邀请码
     */
    var shareCode: String? = null,
    /**
     * 分享的账号 ib分享特有
     */
    var shareAccount: String? = null,
    /**
     * 分享带的url
     */
    var shareUrl: String? = null,

    /**
     * 底部描述
     */
    var description: String? = null,
    /**
     * raf分享的头图，如果有则用后台传的，没有用默认的
     */
    var topImgUrl: String? = null,

    /**
     * 产品k线图
     */
    var kLineBitmap: Bitmap? = null,

    /**
     * 截屏图片
     */
    var screenshotBitmap: Bitmap? = null,

    /**
     * ib分享的图片
     */
    var ibBitmap: Bitmap? = null,

    /**
     * 头像
     */
    var avatarUrl: String? = null,

    /**
     * 产品名
     */
    var symbol: String? = null,

    /**
     * 策略名
     */
    var strategyName: String? = null,

    /**
     * 用户昵称
     */
    var userNickname: String? = null,

    /**
     * id
     */
    var strategyNo: String? = null,

    /**
     * 跟单时间
     */
    var copyTime: String? = null,

    /**
     * 总交易量
     */
    var totalDealCount: String? = null,

    /**
     *  盈利率
     */
    var winRate: String? = null,

    /**
     *  交易信息颜色 绿涨红跌
     */
    var winRateColor: Int = 0,
    /**
     *  交易信息的list
     */
    var dealInfoList: MutableList<Pair<String, String>>? = null,

    var returnYTD: String? = null,
    var returnYTDColor: Int = 0,

    var maxMonthlyReturn: String? = null,
    var maxMonthlyReturnColor: Int = 0,

    /**
     * 3个月回报率
     */
    var returnRate3M: String? = null,
    var returnRate3MColor: Int = 0,
    /**
     * 当前跟单者数量
     */
    var currentCopiersCount: String? = null,
    /**
     * 总跟单者数量
     */
    var totalCopiersCount: String? = null,

    /**
     *  入场价
     */
    var entryPrice: String? = null,

    /**
     *  当前价
     */
    var currentPrice: String? = null,

    /**
     *  盈亏
     */
    var profit: String? = null,
    var profitColor: Int = 0,

    var profitRate: String = "",
    /**
     *  未实现盈亏标题
     */
    var profitTitle: String? = null,

    /**
     * 交易类型
     */
    var direction: String? = null,

    /**
     * 底部时间文案
     */
    var bottomTime: String? = null,

    var shareItemType: Int? = null

) : MultiItemEntity

@Keep
data class ShareSettingData(
    val shareType: Int,
    var shareTheme: Int = TYPE_THEME_LOGO,
    var editList: MutableList<ShareEditData>? = mutableListOf()
) {

    companion object {
        const val TYPE_THEME_LOGO = 0
        const val TYPE_THEME_FERRARI = 1
    }
}
