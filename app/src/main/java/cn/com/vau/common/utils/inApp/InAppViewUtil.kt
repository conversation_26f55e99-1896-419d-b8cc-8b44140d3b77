package cn.com.vau.common.utils.inApp

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.ViewStub
import androidx.core.view.isGone
import androidx.core.view.isVisible
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.SwipeGestureDetector
import cn.com.vau.data.init.InAppBean
import cn.com.vau.databinding.VsLayoutInAppBinding
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Filename: InAppUtils
 * Author: GG
 * Date: 2023/10/27
 * Description:
 */
object InAppViewUtil {

    private const val TYPE_CLOSE = "close"
    private const val TYPE_CLICK = "click"
    private const val TYPE_AUTO_DISMISS = "dismiss"

    /**
     * 显示内部应用视图。
     *
     * @param context 上下文对象。
     * @param binding 视图绑定对象。
     * @param pagePosition 页面位置。
     * @param dismissCallBack 隐藏回调函数。
     */
    fun show(context: Context, vs: ViewStub?, pagePosition: Int, dismissCallBack: ((String?) -> Unit)? = null) {
        try {
            //     InAppDataUtil.saveData(
            //         mutableListOf(
            //             GsonUtil.buildGson().fromJson(
            //                 """
            //                 {
            //     "id": 932,
            //     "eventId": 932,
            //     "startTime": "16/04/2024",
            //     "endTime": "18/04/2024",
            //     "imgUrl": "https://cdn.vauappprotech.com/news/2024/4/20240405111547.png",
            //     "imgType": 20,
            //     "eventsStatus": 0,
            //     "eventsHot": 0,
            //     "longTerm": 0,
            //     "appJumpDefModel": {
            //         "openType": "appview",
            //         "viewType": "35",
            //         "urls": {},
            //         "titles": {
            //             "en": "Share CFD Dividends Announcement"
            //         }
            //     },
            //     "eventsName": "Share CFD Dividends Announcement",
            //     "eventsDesc": "Share CFD dividends for ",
            //     "displayLocation": "1"
            // }
            //             """.trimIndent(), InAppBean.Data::class.java
            //             )
            //         )
            //     )
            InAppDataUtil.showData()?.let { item ->
                when {
                    true == item.displayLocation?.contains("1") && (pagePosition in 0..1) -> {
                        show(context, vs, pagePosition, item, dismissCallBack)
                    }

                    true == item.displayLocation?.contains("2") && (pagePosition == 0) -> {
                        show(context, vs, pagePosition, item, dismissCallBack)
                    }

                    true == item.displayLocation?.contains("3") && (pagePosition == 1) -> {
                        show(context, vs, pagePosition, item, dismissCallBack)
                    }

                    else -> {
                        vs?.isGone = true
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 显示内部应用视图的私有方法。
     *
     * @param context 上下文对象。
     * @param binding 视图绑定对象。
     * @param pagePosition 页面位置。
     * @param data 内部应用数据。
     * @param dismissCallBack 隐藏回调函数。
     */
    @SuppressLint("ClickableViewAccessibility")
    @Throws(Exception::class)
    private fun show(context: Context, vs: ViewStub?, pagePosition: Int, data: InAppBean.InAppData, dismissCallBack: ((String?) -> Unit)? = null) {
        val isCanShow = SpManager.getMessageInAppIsShow(false)
        // LogUtils.w("$isCanShow---------$pagePosition")
        // 本次打开app已经有展示过，所以跳过
        if (!isCanShow) {
            return
        }
        vs?.setOnInflateListener { stub, inflated ->
            val binding = VsLayoutInAppBinding.bind(inflated)
            binding.apply {
                InAppDataUtil.saveViewBinding(this)
                ivClose.setOnClickListener {
                    try {
                        val otherDistanceY = root.y + root.height
                        root.animate()
                            .translationYBy(-otherDistanceY)
                            .setDuration(200)
                            .withStartAction {
                                root.isEnabled = false
                            }
                            .withEndAction {
                                dismissConfig(data, pagePosition, TYPE_CLOSE, dismissCallBack)
                            }
                            .start()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                tvTitle.text = data.eventsName

                val swipeGestureDetector = SwipeGestureDetector(context, object : SwipeGestureDetector.OnSwipeGestureListener {
                    override fun onScroll(distanceY: Float) {
                        // LogUtils.w("root.y---->${root.y}----distanceY----->$distanceY----root.height---->${root.height}")
                        // 滑动事件处理逻辑
                        if (root.y < 0 || distanceY < 0) {
                            root.animate()
                                .translationYBy(distanceY)
                                .setDuration(0)
                                .start()
                        }
                    }

                    override fun up() {
                        val otherDistanceY = root.y + root.height
                        if (root.y + root.height < root.height / 2) {
                            root.animate()
                                .translationYBy(-otherDistanceY)
                                .setDuration(200)
                                .withStartAction {
                                    root.isEnabled = false
                                }
                                .withEndAction {
                                    dismissConfig(data, pagePosition, TYPE_CLOSE, dismissCallBack)
                                }
                                .start()
                        } else {
                            root.animate()
                                .translationYBy(-root.y)
                                .setDuration(200)
                                .start()
                        }
                    }

                    override fun click() {
                        performClick(data, context, pagePosition, dismissCallBack)
                    }
                })
                root.setOnTouchListener(swipeGestureDetector)
                try {
                    if ("appview" == data.appJumpDefModel?.openType || "url" == data.appJumpDefModel?.openType) {
                        tvIntro.clickNoRepeat {
                            performClick(data, context, pagePosition, dismissCallBack)
                        }
                    }
                    tvIntro.text = data.eventsDesc
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                vs.isVisible = true
                CoroutineScope(Dispatchers.Default).launch {
                    data.autoCloseTime?.let {
                        if (it != 0L) {
                            delay(it * 1000)
                            MainScope().launch {
                                vs.isGone = true
                                dismissConfig(data, pagePosition, TYPE_AUTO_DISMISS, dismissCallBack)
                            }
                        }
                    }
                }
            }
            // 弹框曝光事件
            sensorsTrackWhenPopupShow(data)
        }
        vs?.isVisible = true
    }

    private fun VsLayoutInAppBinding.performClick(data: InAppBean.InAppData, context: Context, pagePosition: Int, dismissCallBack: ((String?) -> Unit)?) {
        try {
            if ("appview" == data.appJumpDefModel?.openType || "url" == data.appJumpDefModel?.openType) {
                VAUStartUtil.openActivity(context, data.appJumpDefModel)
                dismissConfig(data, pagePosition, TYPE_CLICK, dismissCallBack)
                LogEventUtil.setLogEvent(BuryPointConstant.V343.GENERAL_PINNED_MESSAGES_VIEW_MORE_BUTTON_CLICK, Bundle().apply {
                    putString(
                        "Page", when (pagePosition) {
                            0 -> BuryPointConstant.ViewMoreType.TRADES
                            1 -> BuryPointConstant.ViewMoreType.ORDERS
                            else -> ""
                        }
                    )
                    putString("Title", data.eventsName)
                })
            }
        } catch (e: Exception) {
            e.printStackTrace()
            dismissConfig(data, pagePosition, TYPE_CLICK, dismissCallBack)
        }
        root.isGone = true
    }

    /**
     * 隐藏内部应用视图，并进行相应的清除和回调操作。
     *
     * @param data 内部应用数据。
     * @param dismissCallBack 隐藏回调函数。
     */
    private fun dismissConfig(data: InAppBean.InAppData, pagePosition: Int, closeType: String, dismissCallBack: ((String?) -> Unit)? = null) {
        if (closeType == TYPE_CLOSE) {
            LogEventUtil.setLogEvent(BuryPointConstant.V343.GENERAL_PINNED_MESSAGES_CLOSE_BUTTON_CLICK, Bundle().apply {
                putString(
                    "Page", when (pagePosition) {
                        0 -> BuryPointConstant.ViewMoreType.TRADES
                        1 -> BuryPointConstant.ViewMoreType.ORDERS
                        else -> ""
                    }
                )
                putString("Title", data.eventsName)
            })
            // 弹窗点击事件
            sensorsTrackWhenPopupClick(data)
        }
        InAppDataUtil.isCanShow = false
        InAppDataUtil.clearData()

        dismissCallBack?.invoke(data.eventId)

    }

    /**
     * 弹窗曝光事件
     */
    private fun sensorsTrackWhenPopupShow(data: InAppBean.InAppData) {
        SensorsDataUtil.track(SensorsConstant.V3610.POPUP_EXPOSURE, JSONObject().apply {
            put(SensorsConstant.Key.PLATFORM_TYPE, "Android")
            put(SensorsConstant.Key.ACTIVITY_NAME, data.promoLibraryName)
            put(SensorsConstant.Key.POPUP_NAME, data.originalEventDesc)
            put(SensorsConstant.Key.POPUP_ID, data.eventId)
            put(SensorsConstant.Key.POPUP_TYPE, data.imgType)
        })
    }

    /**
     * 弹窗点击事件
     */
    private fun sensorsTrackWhenPopupClick(data: InAppBean.InAppData) {
        SensorsDataUtil.track(SensorsConstant.V3610.POPUP_CLICK, JSONObject().apply {
            put(SensorsConstant.Key.PLATFORM_TYPE, "Android")
            put(SensorsConstant.Key.ACTIVITY_NAME, data.promoLibraryName)
            put(SensorsConstant.Key.POPUP_NAME, data.originalEventDesc)
            put(SensorsConstant.Key.POPUP_ID, data.eventId)
            put(SensorsConstant.Key.POPUP_TYPE, data.imgType)
            put(SensorsConstant.Key.BUTTON_NAME, "redirect")
        })
    }
}