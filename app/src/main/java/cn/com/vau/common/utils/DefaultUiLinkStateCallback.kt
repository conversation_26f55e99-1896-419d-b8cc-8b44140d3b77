package cn.com.vau.common.utils

import android.view.View
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.application.LinkStateManager
import cn.com.vau.util.opt.xhLoge
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 带有默认处理状态
 */
open class DefaultUiLinkStateCallback(private val tvState: TextView?, private val lifecycleOwner: LifecycleOwner) : LinkStateManager.StateChangeCallback() {
    override fun onStartInit() {
        uiShowConnecting()
    }

    override fun onProductAndWebsocketSuccess() {
        uiHideTvConnect()
    }

    override fun onNetSlow() {
        uiShowNetSlow()
    }

    override fun onHeartbeatNormal() {
        uiHeartbeatNormal()
    }

    override fun onWebsocketDisconnected() {
        uiReconnecting()
    }

    private fun uiHideTvConnect() {
        if (tvState == null) {
            return
        }
        if (tvState.visibility != View.VISIBLE) {
            return
        }
        tvState.text = tvState.context.getString(R.string.connected)
        lifecycleOwner.lifecycleScope.launch {
            delay(1000)
            tvState.visibility = View.GONE
        }
    }

    private fun uiShowNetSlow() {
        if (tvState == null) {
            return
        }
        tvState.text = tvState.context.getString(R.string.slow_connection)
        tvState.visibility = View.VISIBLE
    }

    private fun uiHeartbeatNormal() {
        if (tvState == null) {
            return
        }
        if (tvState.text != tvState.context.getString(R.string.slow_connection)) return
        tvState.visibility = View.GONE
    }

    private fun uiReconnecting() {
        if (tvState == null) {
            return
        }
        tvState.text = tvState.context.getString(R.string.reconnecting)
        tvState.visibility = View.VISIBLE
    }


    private fun uiShowConnecting() {
        if (tvState == null) {
            return
        }
        tvState.text = "${tvState.context.getString(R.string.connecting)}..."
        tvState.visibility = View.VISIBLE
    }
}