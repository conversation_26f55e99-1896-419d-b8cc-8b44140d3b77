package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.*
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.common.view.popup.adapter.PopAttchViewAdapter
import cn.com.vau.databinding.LayoutEditTextOpenAccountBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.widget.dialog.base.AttachListPopupWindow
import com.lxj.xpopup.enums.*

@SuppressLint("CustomViewStyleable")
class OpenAccountDropDown constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), VerifyComponent {

    private var parent: LayoutEditTextOpenAccountBinding
    private var onSelected: ((position: Int) -> Unit)? = null
    private var mustFill = false
    private var hintTxt = ""
    private var titleTxt = ""
    private var dataList: MutableList<String> = mutableListOf()

    init {
        parent =
            LayoutEditTextOpenAccountBinding.inflate(LayoutInflater.from(context), this, true)
        val attr = context.obtainStyledAttributes(attrs, R.styleable.OpenAccount_Option_Text)
        mustFill = attr.getBoolean(R.styleable.OpenAccount_Option_Text_must_fill, false)
        hintTxt = attr.getString(R.styleable.OpenAccount_Option_Text_hint_text) ?: ""
        titleTxt = attr.getString(R.styleable.OpenAccount_Option_Text_title_text) ?: ""   // 如果title和hint不一样的话，可以单独设置此项
        initView()
        initListener()
        attr.recycle()
    }

    private val mAdapter by lazy { PopAttchViewAdapter() }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        parent.mIfvArrow.visibility = View.VISIBLE
        parent.mEditText.isFocusable = false
        if (LanguageHelper.isRtlLanguage()) {
            parent.mEditText.setPadding(42.dp2px(), 16.dp2px(), 12.dp2px(), 16.dp2px())
        } else {
            parent.mEditText.setPadding(12.dp2px(), 16.dp2px(), 42.dp2px(), 16.dp2px())
        }
        parent.tvHint.text = "${titleTxt.ifEmpty { hintTxt }}${if (mustFill) "*" else ""}"
        parent.mEditText.hint = hintTxt
    }

    private fun initListener() {
        parent.mEditText.keyListener = null
        parent.mEditText.setOnClickListener { attachView ->
            if (dataList.isNotEmpty()) {
                val actvity = context as? AppCompatActivity
                actvity?.let {
                    val dialog = AttachListPopupWindow.Builder(it)
                        .setDestroyOnDismiss(false)
                        .hasShadowBg(false)
                        .setPopupPosition(PopupPosition.Bottom)
                        .setPopupAnimation(PopupAnimation.ScrollAlphaFromTop)
                        .borderRadius(10f.dp2px())
                        .setAdapter(mAdapter)
                        .setTargetView(attachView)
                        .setWidth(parent.mEditText.width)
                        .setOffsetY(8.dp2px())
                        .build()
                    mAdapter.setSelected(parent.mEditText.text.toString())
                    mAdapter.setNewInstance(dataList)
                    mAdapter.setOnItemClickListener { _, _, position ->
                        onSelected?.invoke(position)
                        setText(dataList.elementAtOrNull(position) ?: "")
                        dialog.dismiss()
                    }
                    dialog.showDialog()
                }
            }
        }
    }

    fun setData(data: MutableList<String>): OpenAccountDropDown {
        this.dataList.clear()
        dataList.addAll(data)
        return this
    }

    fun onSelected(listener: (position: Int) -> Unit) {
        this.onSelected = listener
    }

    fun setText(text: String) {
        parent.mEditText.setText(text)
        callback?.verify(text.isNotEmpty())
    }

    fun text(): String {
        return parent.mEditText.text.toString()
    }

    override var callback: VerifyCallBack? = null
    override fun getVerify(): Boolean = parent.mEditText.text.toString().isNotEmpty()

}