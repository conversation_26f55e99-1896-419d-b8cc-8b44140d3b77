package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.MotionEvent;

import androidx.core.content.res.ResourcesCompat;

import cn.com.vau.R;
import cn.com.vau.common.greendao.dbUtils.UserDataUtil;
import cn.com.vau.common.utils.VAUSdkUtil;
import cn.com.vau.common.view.kchart.interfaces.AboveCoordinatesView;
import cn.com.vau.common.view.kchart.interfaces.UnabelFocusedsView;
import cn.com.vau.common.view.kchart.views.ChartViewImp;
import cn.com.vau.data.init.ShareOrderData;
import cn.com.vau.data.init.ShareProductData;
import cn.com.vau.trade.kchart.ChartUIParamUtil;
import cn.com.vau.trade.kchart.KLineDataUtils;
import cn.com.vau.util.DistKt;
import cn.com.vau.util.ExpandKt;
import cn.com.vau.util.ScreenUtil;

public class MovableOrderLine extends ZoomMoveViewContainer implements UnabelFocusedsView, AboveCoordinatesView {
    private Paint mLinePaint = null;
    private Paint mTextPaint = null;
    private Paint mTextBgPaint = null;
    private Paint mScaleTextBgPaint = null;
    private Paint mScaleTextPaint = null;
    private Paint mStrokePaint = null;

    private int mTextColor = Color.WHITE;
    private float mTextPaddingDP = 4;
    private float mTextPadding = 0;
    private float mIconPadding = 43;
    private float mIconOffsetTop = 2;
    private float mIconOffsetLeft = 36;
    private int iconWidth = 40;
    private int iconHeight = 40;
    private float minHeight = 0f;
    private float maxHeight = 0f;
    private float mHScreenPaddingLeft = 0f;
    //圆角半径
    private float mCornerRoundRadius = DistKt.dp2px(ChartUIParamUtil.INSTANCE.getOrderLineRadiusDp());
    // 文字与边框的vertical padding距离
    private float mPaddingVerticalText = DistKt.dp2px(ChartUIParamUtil.INSTANCE.getOrderLineTextPaddingVerticalDp());

    private boolean isShowScale = true;
    private RectF roundBg = new RectF();
    private RectF scaleRF = new RectF();
    private RectF lineRectF;
    float circleR;

    private Typeface textTypeface;
    //左右padding
    private float mPaddingHorizontalDP = 2;
    private float mPaddingVerticalDP = 2.5f;
    private float mPaddingHorizontal = 0;
    private float mPaddingVertical = 0;

    // 当前坐标点,仅Y轴即可,X轴数据为0
    private PointF mPointF = new PointF();
    // 边距
    private float mSpace;
    private Context ctx;
    private State state = State.STANDBY;
    private OrderLine.Direction direction;
    private ShareOrderData mShareOrderData;
    private ChartViewImp.OnOrderLineMoveListener moveListener;
    private int lineType = 0;   // 0:Position Line  1:TP Line   2:SL Line
    private String currencyType;
    private ShareProductData symbolData;     // 产品数据

    public MovableOrderLine(Context context) {
        super(context);
        this.isShow = false;
        this.ctx = context;
        init();
    }

    public void setTargetLineType(int lineType) {
        this.lineType = lineType;
    }

    private void init() {
        textTypeface = ResourcesCompat.getFont(ctx, R.font.gilroy_regular);
        currencyType = UserDataUtil.currencyType();
        if (TextUtils.isEmpty(currencyType)) {
            currencyType = "";
        }

        circleR = getPixelDp(3f);

        mLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setAntiAlias(true);
        mLinePaint.setStrokeWidth(getPixelDp(1));

        mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setColor(Color.WHITE);
        mTextPaint.setStyle(Paint.Style.FILL);
        mTextPaint.setAntiAlias(true);
        mTextPaint.setTypeface(textTypeface);
        mTextPaint.setTextSize(getPixelSp(9));
//        mTextPaint.setTextAlign(Paint.Align.RIGHT);

        mTextBgPaint = new Paint();
        mTextBgPaint.setStyle(Paint.Style.FILL);
        mTextBgPaint.setAntiAlias(true);

        mStrokePaint = new Paint();
        mStrokePaint.setStyle(Paint.Style.STROKE);
        mStrokePaint.setStrokeWidth(getPixelDp(0.5f));
        mStrokePaint.setAntiAlias(true);

        mScaleTextBgPaint = new Paint();
        mScaleTextBgPaint.setStyle(Paint.Style.FILL);
        mScaleTextBgPaint.setAntiAlias(true);

        mScaleTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mScaleTextPaint.setColor(Color.WHITE);
        mScaleTextPaint.setStyle(Paint.Style.FILL);
        mScaleTextPaint.setAntiAlias(true);
        mScaleTextPaint.setTypeface(textTypeface);
        mScaleTextPaint.setTextSize(getPixelSp(9));
        mScaleTextPaint.setTextAlign(Paint.Align.CENTER);

//        mIconPaint = new Paint();
//        mIconPaint.setStyle(Paint.Style.FILL);
//        mIconPaint.setAntiAlias(true);

        mSpace = getPixelSp(2);
        mTextPadding = getPixelDp(mTextPaddingDP);
        mPaddingHorizontal = getPixelDp(mPaddingHorizontalDP);
        mPaddingVertical = getPixelDp(mPaddingVerticalDP);
        int screenWidth = KLineDataUtils.isFrontPortrait ? ScreenUtil.Companion.getAppScreenWidth() : ScreenUtil.Companion.getAppScreenHeight();
        mHScreenPaddingLeft = (screenWidth - DistKt.dp2px(12f)) * ChartUIParamUtil.INSTANCE.getOrderLineTextMarginStartPercent();
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        try {
            if (isShow) {

                checkParameter();

                if (mShareOrderData == null) return;

                // 0:持仓线副本  1:止盈线副本  2:止损线副本
                float openPrice = 0;
                if (lineType == 0) {
                    openPrice = parseFloat(mShareOrderData.getOpenPrice());
                } else if (lineType == 1) {
                    openPrice = parseFloat(mShareOrderData.getTakeProfit());
                } else if (lineType == 2) {
                    openPrice = parseFloat(mShareOrderData.getStopLoss());
                }
                if (openPrice < mYMax && openPrice > mYMin) {
                    maxHeight = mCoordinateHeight;
                    float y;
                    if (getChartView().getMovingPostionLine() && mPointF.y != 0) {
                        y = mPointF.y;
                    } else {
                        y = (1f - (openPrice - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
                    }

                    float yScale = (mYMax - mYMin) / mCoordinateHeight * (mCoordinateHeight - y) + mYMin;
                    String yScaleStr = ExpandKt.numFormat(yScale, mShareOrderData.getDigits(), true);
                    String orderType = "";

                    CandleLine.CandleLineBean lastBean = (CandleLine.CandleLineBean) mDataList.get(mDataList.size() - 1);
                    if (direction == OrderLine.Direction.BUY) {
                        if (yScale >= lastBean.getOriginalBid()) {      //价格 >= 当前卖价： 止盈
                            orderType = "TP";
                            mLinePaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                            mTextBgPaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                            mScaleTextBgPaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                        } else {                                         //价格 < 当前卖价： 止损
                            orderType = "SL";
                            mLinePaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                            mTextBgPaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                            mScaleTextBgPaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                        }
                    } else if (direction == OrderLine.Direction.SELL) {
                        if (yScale <= lastBean.getOriginalAsk()) {      //价格 <= 当前买价： 止盈
                            orderType = "TP";
                            mLinePaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                            mTextBgPaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                            mScaleTextBgPaint.setColor(mContext.getResources().getColor(R.color.c00c79c));
                        } else {                                         //价格 > 当前买价： 止损
                            orderType = "SL";
                            mLinePaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                            mTextBgPaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                            mScaleTextBgPaint.setColor(mContext.getResources().getColor(R.color.cf44040));
                        }
                    }

                    String profit = ExpandKt.numCurrencyFormat(
                            VAUSdkUtil.getProfitLoss(
                                    symbolData == null ? new ShareProductData() : symbolData,
                                    mShareOrderData.getOpenPrice(),
                                    TextUtils.isEmpty(mShareOrderData.getVolume()) ? "" : mShareOrderData.getVolume(),
                                    mShareOrderData.getCmd(),
                                    String.valueOf(yScale)
                            ), UserDataUtil.currencyType(), true
                    );
                    if (ExpandKt.toDoubleCatching(profit, 0.0) > 0) {
                        profit = "+" + profit;
                    }
//                    String textStr = orderType + "   " + yScaleStr + "   " + profit + " " + currencyType;
//                    float textRectRight = drawTextStuff(canvas, y, textStr);
                    float textRectRight = drawMoveTpSlLine(canvas,profit,orderType,y);
//                    float latitudeXEnd = mLinePaint.getStrokeWidth() - mCoordinateMarginRight + mCoordinateWidth - circleR  - getPixelDp(0.5f);
                    float latitudeXEnd = mCoordinateWidth - mTextPadding * 2 - mScaleTextPaint.measureText(yScaleStr)  - circleR - getPixelDp(0.5f);
                    drawOrderCircle(canvas,y,latitudeXEnd);
                    drawOrderLine(canvas, y, textRectRight,latitudeXEnd - circleR);
                    if (isShowScale) {
                        drawScaleText(canvas, y, yScaleStr,latitudeXEnd +  circleR + getPixelDp(0.5f));
                    }
                }
            }
        } catch (Exception ignored) {
           ignored.printStackTrace();
        }
    }

    private float drawMoveTpSlLine(Canvas canvas, String profit, String tpSlType, float y) {
        boolean isTp = "TP".equals(tpSlType);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);

        float textHeight = Math.abs(fm.ascent) + mPaddingVertical;

        float profitW = mTextPaint.measureText(profit);
        String currencyType = UserDataUtil.currencyType();
        float currencyTypeW = mTextPaint.measureText(currencyType);
        float tpSlTypeW = mTextPaint.measureText(tpSlType);

        roundBg.left = mCoordinateMarginLeft + mHScreenPaddingLeft;
        roundBg.top = y;
        roundBg.right = roundBg.left + tpSlTypeW + profitW + currencyTypeW + getPixelDp(mTextPaddingDP) * 6;
        roundBg.bottom = roundBg.top + textHeight + mTextPadding;
        if (lineRectF != null) {
            float width = lineRectF.right - lineRectF.left;
            roundBg.right = roundBg.left + width;
        }

        float recHeight = roundBg.height();
        roundBg.top -= recHeight / 2;
        roundBg.bottom -= recHeight / 2;
        minHeight = roundBg.bottom + 1f - roundBg.top;
        canvas.drawRoundRect(roundBg, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
        mStrokePaint.setColor(isTp?ChartUIParamUtil.INSTANCE.getBuyIndicatorColor():ChartUIParamUtil.INSTANCE.getSellIndicatorColor());
        canvas.drawRoundRect(roundBg, mCornerRoundRadius, mCornerRoundRadius, mStrokePaint);
        float line1X = roundBg.left + tpSlTypeW + 2 * getPixelDp(mTextPaddingDP);
        Paint linePaint = new Paint();
        linePaint.setColor(ctx.getColor(R.color.c61ffffff));
        linePaint.setStrokeWidth(getPixelDp(1f));
        canvas.drawLine(line1X, roundBg.top, line1X, roundBg.bottom, linePaint);
        float line2X = roundBg.left + tpSlTypeW + profitW + 4 * getPixelDp(mTextPaddingDP);
        canvas.drawLine(line2X, roundBg.top, line2X, roundBg.bottom, linePaint);

        canvas.drawText(tpSlType, roundBg.left + getPixelDp(mTextPaddingDP), roundBg.top + (mTextPadding / 2) + textHeight  - (mPaddingVertical/2f), mTextPaint);
        canvas.drawText(profit, roundBg.left + tpSlTypeW + 3 * getPixelDp(mTextPaddingDP), roundBg.top + (mTextPadding / 2) + textHeight  - (mPaddingVertical/2f), mTextPaint);
        canvas.drawText(currencyType, roundBg.left + tpSlTypeW + profitW + 5 * getPixelDp(mTextPaddingDP), roundBg.top + (mTextPadding / 2) + textHeight  - (mPaddingVertical/2f), mTextPaint);
        return roundBg.right;
    }

    private float drawTextStuff(Canvas canvas, float y, String showTextStr) {
        Paint.FontMetrics fm = new Paint.FontMetrics();
        mTextPaint.getFontMetrics(fm);

        float textHeight = Math.abs(fm.ascent);

        roundBg.left = mCoordinateMarginLeft + mHScreenPaddingLeft;
        roundBg.top = y;
        roundBg.right = roundBg.left + mTextPaint.measureText(showTextStr) + getPixelDp(mTextPaddingDP) * 2 + mIconPadding;
        roundBg.bottom = roundBg.top + textHeight + getPixelDp(mTextPaddingDP) * 2;
        if (lineRectF != null) {
            float width = lineRectF.right - lineRectF.left;
            roundBg.right = roundBg.left + width;
        }

        float recHeight = roundBg.height();
        roundBg.top -= recHeight / 2;
        roundBg.bottom -= recHeight / 2;
        minHeight = roundBg.bottom + 1f - roundBg.top;

        canvas.drawRoundRect(roundBg, mCornerRoundRadius, mCornerRoundRadius, mTextBgPaint);
        float roundWidth = roundBg.right - roundBg.left;
        float centerStart = (roundWidth - mTextPaint.measureText(showTextStr)) / 2;
        canvas.drawText(showTextStr, roundBg.left + centerStart, roundBg.top + 6 + getPixelDp(mTextPaddingDP) / 2 + textHeight, mTextPaint);// 此数值 6 为offset 为了让应用了app字体的文字更居中
        return roundBg.right;   // 返回矩形的右边界
    }

    // 按传入的文字矩形右边界作为起点画线
    private void drawOrderLine(Canvas canvas, float y, float startPoint,float endX) {
        Path path = new Path();
        path.moveTo(startPoint, y);
        path.lineTo(endX, y);
        canvas.drawPath(path, mLinePaint);
    }

    private void drawScaleText(Canvas canvas, float y, String scaleText,float startX) {
        float textHeight = getTextHeight(mScaleTextPaint)+mPaddingVertical;
        scaleRF.left = startX;
        scaleRF.top = y ;
        scaleRF.right = mCoordinateWidth;
        scaleRF.bottom = scaleRF.top + textHeight + mTextPadding;
        //保证线在这个矩形中间
        float recHeight = scaleRF.height();
        scaleRF.top -= recHeight / 2;
        scaleRF.bottom -= recHeight / 2;
        canvas.drawRoundRect(scaleRF, mCornerRoundRadius, mCornerRoundRadius, mScaleTextBgPaint);
        float textWidth = mScaleTextPaint.measureText(scaleText);
        float strX = mCoordinateWidth - textWidth / 2f - mPaddingHorizontal;
        canvas.drawText(scaleText, strX - mTextPadding/2f, scaleRF.top + mTextPadding / 2 + textHeight - (mPaddingVertical/2f), mScaleTextPaint);
    }

    private void drawOrderCircle(Canvas canvas, float y, float latitudeXEnd){
        float circleR = getPixelDp(3f);
        Paint paint = new Paint();
        paint.setColor(Color.WHITE);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(latitudeXEnd,y,circleR,paint);
        paint.setColor(ctx.getColor(R.color.c007fff));
        paint.setStrokeWidth(getPixelDp(1));
        paint.setStyle(Paint.Style.STROKE);
        canvas.drawCircle(latitudeXEnd,y,circleR,paint);
    }

    /**
     * 文字高度
     */
    private float getTextHeight(Paint textPaint) {
        Paint.FontMetrics fm = new Paint.FontMetrics();
        textPaint.getFontMetrics(fm);
        return Math.abs(fm.ascent);
    }

    @Override
    public void move(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
//                LogUtil.d("wj", "MovableOrderLine -> move: ")
                mPointF.x = 0;
                mPointF.y = event.getY();

                mPointF.y = mPointF.y < minHeight ? minHeight : (mPointF.y > maxHeight ? maxHeight : mPointF.y);
                if (moveListener != null) {
                    CandleLine.CandleLineBean lastBean = (CandleLine.CandleLineBean) mDataList.get(mDataList.size() - 1);
                    float yScale = (mYMax - mYMin) / mCoordinateHeight * (mCoordinateHeight - mPointF.y) + mYMin;
                    boolean isTP = false;
                    if (direction == OrderLine.Direction.BUY) {
                        isTP = yScale >= lastBean.getOriginalBid();
                    } else if (direction == OrderLine.Direction.SELL) {
                        isTP = yScale <= lastBean.getOriginalAsk();
                    }
                    moveListener.onMoving(yScale, isTP);
                }
                withRect(null);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
//                setShow(false);
                // 放下拖动的持仓线，即设置止盈/止损线
                if (moveListener != null) {
                    CandleLine.CandleLineBean lastBean = (CandleLine.CandleLineBean) mDataList.get(mDataList.size() - 1);
                    float yScale = (mYMax - mYMin) / mCoordinateHeight * (mCoordinateHeight - mPointF.y) + mYMin;
                    boolean isTP = false;
                    if (direction == OrderLine.Direction.BUY) {
                        isTP = yScale >= lastBean.getOriginalBid();
                    } else if (direction == OrderLine.Direction.SELL) {
                        isTP = yScale <= lastBean.getOriginalAsk();
                    }
                    moveListener.onMoveOver(yScale, isTP);
                }
                withRect(null);
                break;
        }
    }

    private void checkParameter() {
        if (this.mShownPointNums < 0) {
            throw new IllegalArgumentException("maxPointNum must be larger than 0");
        }
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }

    }

    public void withRect(RectF rectF) {
        this.lineRectF = rectF;
    }

    public void setLineDashPath(PathEffect pathEffect) {
        mLinePaint.setPathEffect(pathEffect);
    }

    public void setTextColor(int textColor) {
        mTextColor = textColor;
        mTextPaint.setColor(textColor);
    }

    public void setTextSize(float sp) {
        mTextPaint.setTextSize(getPixelSp(sp));
    }

    public void setLineWidth(float dp) {
        mLinePaint.setStrokeWidth(getPixelDp(dp));
    }

    public void setOrderData(ShareOrderData shareOrderData) {
        mShareOrderData = shareOrderData;
        if (mShareOrderData != null) {
            if ("0".equals(mShareOrderData.getCmd()) || "2".equals(mShareOrderData.getCmd()) || "4".equals(mShareOrderData.getCmd())) {
                direction = OrderLine.Direction.BUY;
            } else {
                direction = OrderLine.Direction.SELL;
            }
        }
    }

    public void setSymbolData(ShareProductData symbolData) {
        this.symbolData = symbolData;
    }

    public ShareProductData getSymbolData() {
        return symbolData;
    }

    public ShareOrderData getOrderData() {
        return mShareOrderData;
    }

    public void setOnOrderLineMoveListener(ChartViewImp.OnOrderLineMoveListener listener) {
        moveListener = listener;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    private float parseFloat(String floatStr) {
        int result = 0;
        if (!TextUtils.isEmpty(floatStr)) {
            try {
                return Float.parseFloat(floatStr);
            } catch (Exception e) {
                result = 0;
            }
        }
        return result;
    }

    public enum State {
        STANDBY, POSITIONED
    }
}
