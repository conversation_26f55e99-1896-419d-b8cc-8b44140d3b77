package cn.com.vau.common.view.share

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.text.inSpans
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.bean.ShareButton
import cn.com.vau.common.view.popup.bean.ShareEditData
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.databinding.PopupShareBinding
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.util.AppUtil
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.BitmapUtil
import cn.com.vau.util.CustomTypefaceSpan
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.IntentUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.PermissionUtil
import cn.com.vau.util.ScreenCaptureObserver
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.copyText
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.setNbOnItemClickListener
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.FirebaseManager
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import cn.com.vau.util.widget.dialog.base.FullScreenDialog
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.share.Sharer
import com.facebook.share.widget.ShareDialog
import com.google.common.reflect.TypeToken
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Filename: SharePopup
 * Author: GG
 * Date: 2023/9/7 0007 17:25
 * Description:
 */
@SuppressLint("ViewConstructor")
class SharePopup private constructor(
    val activity: AppCompatActivity,
    /**
     * 分享弹窗类型
     */
    private val shareType: Int,
    private val viewModel: ShareViewModel,
    private val signalId: String?,
    private val shareData: ShareData,
    private val isIB: Boolean = false,
    private val accountList: List<AccountTradeBean>? = null,
) : FullScreenDialog<PopupShareBinding>(activity, PopupShareBinding::inflate) {

    private var bitmap: Bitmap? = null

    private var callbackManager: CallbackManager? = null

    private var shareDialog: ShareDialog? = null

    private var buryShareType: String = ""

    private var positionShareType: String = ""

    private var isQrCodeLoadComplete = false

    private val shareSettingData: ShareSettingData by lazy {
        SpManager.getShareSetting(shareType.toString()) ?: ShareSettingData(shareType = shareType)
    }

    // 加载自定义字体
    private val customTypeface by lazy { ResourcesCompat.getFont(context, R.font.gilroy_medium) }

    private val shareAdapter: ShareLayoutAdapter by lazy {
        // todo 测试代码 373 版本删除
        if (null == shareSettingData.editList) {
            shareSettingData.editList = mutableListOf()
            FirebaseManager.recordException(Exception("shareSettingData.editList is null"))
        }
        ShareLayoutAdapter(
            isIB, shareTheme = shareSettingData.shareTheme,
            isShowIBAccount = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_ACCOUNT_NO }?.isSelect.ifNull(true),
            isShowUserName = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_USERNAME }?.isSelect.ifNull(false),
            isShowWatermark = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_WATERMARK }?.isSelect.ifNull(false)
        ).apply {
            // 添加稳定 ID 防止布局重绘
            setHasStableIds(true)
            // 关闭默认动画
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
        }
    }

    private val accountAdapter: SelectAccountAdapter<AccountTradeBean> by lazy {
        SelectAccountAdapter<AccountTradeBean>(isChangeSelectTextColor = false).apply {
            setList(accountList)
            selectTitle = SpManager.getInvitationLastSelectAccount()
            setOnItemClickListener { _, _, position ->
                if (accountAdapter.selectTitle == <EMAIL>(position)?.acountCd) {
                    selectAccountDialog.dismiss()
                    return@setOnItemClickListener
                }

                SpManager.putInvitationLastSelectAccount(data.getOrNull(position)?.acountCd.ifNull())

                activity.lifecycleScope.launch(CoroutineExceptionHandler { _, _ ->
                    run {
                        hideLoading(activity)
                    }
                }) {
                    showLoading(activity)
                    viewModel.getIBRefereeInfo(data.getOrNull(position)?.acountCd, signalId)?.obj?.let {
                        // 只有signalId 为空的时候 ，说明不是策略相关分享的时候 才需要缓存接口数据，方便下次使用，策略分享的数据 和signalId 也有关系，所以不能缓存
                        if (signalId.isNullOrBlank())
                            SpManager.putRefereeInfoIB(it.json)
                        isQrCodeLoadComplete = false
                        ImageLoaderUtil.preloadImage(it.qrcodeUrl)
                        shareAdapter.data.forEach { item ->
                            item.shareAccount = <EMAIL>(position)?.acountCd
                            item.shareCode = it.inviteCode
                            item.qrCodeUrl = it.qrcodeUrl
                            item.shareUrl = it.refereeUrl
                        }
                        shareAdapter.notifyItemRangeChanged(0, shareAdapter.itemCount)
                        hideLoading(activity)
                        accountAdapter.selectTitle = <EMAIL>(position)?.acountCd
                        accountAdapter.notifyItemRangeChanged(0, accountAdapter.itemCount)
                        selectAccountDialog.dismiss()
                        mContentBinding.tvSelectAccount.text = buildSpannedString {
                            customTypeface?.let { font ->
                                inSpans(CustomTypefaceSpan(font)) {
                                    append(context.getString(R.string.account))
                                    append(": ")
                                }
                            }
                            append(accountAdapter.selectTitle.ifNull())
                        }
                    }
                }
            }
        }
    }

    private val selectAccountDialog by lazy {
        BottomListDialog.Builder(activity)
            .setTitle(context.getString(R.string.select_an_account_to_share))
            .setContent(context.getString(R.string.users_who_sign_link_your_clients))
            .setContentColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
            .setAdapter(accountAdapter)
            .build()
    }

    private val manager by lazy { WrapContentLinearLayoutManager(context, RecyclerView.HORIZONTAL, false) }

    private val shareButtonList: MutableList<ShareButton> by lazy {
        mutableListOf(
            ShareButton(type = ShareButton.TYPE_SAVE, icon = AttrResourceUtil.getDrawable(context, R.attr.imgShareSave), title = context.getString(R.string.save)),
            ShareButton(type = ShareButton.TYPE_FACEBOOK, icon = R.drawable.img_share_facebook, title = "Facebook"),
            ShareButton(type = ShareButton.TYPE_MORE, AttrResourceUtil.getDrawable(context, R.attr.imgShareMore), title = context.getString(R.string.more)),
        )
    }

    private val adapter: ShareItemAdapter by lazy { ShareItemAdapter() }

    private val editAdapter: ShareEditAdapter by lazy {
        ShareEditAdapter().apply {
            // 添加稳定 ID 防止布局重绘
            setHasStableIds(true)
            // 关闭默认动画
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
        }
    }

    override fun getImplLayoutId(): Int = R.layout.popup_share

    override fun setContentView() {
        ScreenCaptureObserver.isShareDialogShow = true
        initFaceBook()

        mContentBinding.run {
            configRV()
            updateShareData()

            if (isIB) {
                groupRaf.isVisible = false
                tvSelectAccount.text = buildSpannedString {
                    customTypeface?.let {
                        inSpans(CustomTypefaceSpan(it)) {
                            append(context.getString(R.string.account))
                            append(": ")
                        }
                    }
                    append(SpManager.getInvitationLastSelectAccount().ifNull())
                }
                tvSelectAccount.isVisible = true
                tvSelectAccount.clickNoRepeat {
                    selectAccountDialog.show()
                }
                shareAdapter.notifyItemRangeChanged(0, shareAdapter.itemCount)
            } else {
                tvSelectAccount.isVisible = false
                groupRaf.isVisible = false
            }

            if (shareType == TYPE_RAF) {
                groupRaf.isVisible = true
                tvSelectAccount.isVisible = false
                tvCodeCopy.text = shareData.shareCode
                tvCodeCopy.clickNoRepeat {
                    tvCodeCopy.text.copyText(context.getString(R.string.referral_code_copied))
                    buryShareType = BuryPointConstant.ShareType.REFERRAL_CODE
                    point()
                }
            }

            configShareChannel()

            shareAdapter.setQrCodeLoadListener {
                isQrCodeLoadComplete = it
                if (shareType == TYPE_SCREENSHOT && isQrCodeLoadComplete) {
                    post {
                        val visibleItemPosition = manager.findFirstVisibleItemPosition()
                        val viewHolder = rvShare.findViewHolderForAdapterPosition(visibleItemPosition)
                        bitmap = BitmapUtil.loadBitmapFromView(viewHolder?.itemView)
                        systemShareClick()
                    }
                }
            }

            tvStart.clickNoRepeat {
                resetSetting()
                updateSetting()
                hideEditView()
            }

            tvEnd.clickNoRepeat {
                saveEditData()
                updateSetting()
                hideEditView()
            }

            tvNext.setOnClickListener {
                dismiss()
            }
        }
        sensorsTrack(SensorsConstant.V3710.SHAREPOSTER_VIEW)
    }

    /**
     * 点击取消重置设置的状态
     */
    private fun resetSetting() {
        editAdapter.setList(shareSettingData.editList?.map { it.deepCopy() }?.toMutableList())
    }

    /**
     * 根据分享类型 保存不同类型的 编辑设置的状态
     */
    private fun saveEditData() {
        test()
        shareSettingData.editList = editAdapter.data.map { it.deepCopy() }.toMutableList()
        SpManager.putShareSetting(shareType.toString(), shareSettingData)
    }

    private fun test() {
        // todo 测试代码 373版本删除
        try {
            val json = editAdapter.data.json
            if (null == GsonUtil.fromJson(json, object : TypeToken<MutableList<ShareEditData>>() {}.type)) {
                //上报为不严重类型
                FirebaseManager.recordException(Exception(json))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            FirebaseManager.recordException(Exception("${e.message}"))
        }
    }

    private fun updateSetting() {
        editAdapter.data.forEach {
            when (it.title) {
                ShareEditData.TYPE_WATERMARK -> {
                    shareAdapter.isShowWatermark = it.isSelect
                }

                ShareEditData.TYPE_USERNAME -> {
                    shareAdapter.isShowUserName = it.isSelect
                }

                ShareEditData.TYPE_ACCOUNT_NO -> {
                    shareAdapter.isShowIBAccount = it.isSelect
                }
            }
        }
        shareAdapter.notifyItemRangeChanged(0, shareAdapter.itemCount)
    }

    private fun hideEditView() {
        TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())
        mContentBinding.viewEdit.isVisible = false
        mContentBinding.viewShare.isVisible = true
    }

    private fun PopupShareBinding.configRV() {
        rvShare.layoutManager = manager
        rvShare.adapter = shareAdapter
        // 关闭默认动画
        rvShare.itemAnimator = null
        val snapHelper = PagerSnapHelper()
        snapHelper.attachToRecyclerView(rvShare)
        // 创建 RecyclerView 的滚动监听器
        val scrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // 获取当前显示的 position
                    val layoutManager = recyclerView.layoutManager
                    snapHelper.findSnapView(layoutManager)?.let { currentView ->
                        val currentPosition = layoutManager?.getPosition(currentView)
                        // 处理当前显示的 position
                        if (currentPosition != null) {
                            // 在这里执行您想要的操作，例如更新 UI、显示 Toast 等
                            tvIndicator.text = getIndicatorText(currentPosition + 1)
                        }
                    }
                }
            }
        }

        // 将滚动监听器添加到 RecyclerView
        rvShare.addOnScrollListener(scrollListener)
    }

    private fun PopupShareBinding.configShareChannel() {
        rvChannel.layoutManager = WrapContentLinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        rvChannel.addItemDecoration(DividerItemDecoration(26.dp2px(), firstDividerSize = 24.dp2px(), orientation = RecyclerView.HORIZONTAL))
        rvChannel.adapter = adapter
        adapter.setNbOnItemClickListener { _, _, position ->
            if (shareType != TYPE_RAF && isIB && tvSelectAccount.text.isNullOrBlank()) {
                ToastUtil.showToast(context.getString(R.string.please_select_an_account_to_share))
                return@setNbOnItemClickListener
            }

            if (!isQrCodeLoadComplete) {
                ToastUtil.showToast(context.getString(R.string.the_qr_code_please_again_later))
                return@setNbOnItemClickListener
            }

            val visibleItemPosition = manager.findFirstVisibleItemPosition()
            val viewHolder = rvShare.findViewHolderForAdapterPosition(visibleItemPosition)
            bitmap = BitmapUtil.loadBitmapFromView(viewHolder?.itemView)
            when (adapter.data[position].type) {
                ShareButton.TYPE_EDIT -> {
                    TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())
                    mContentBinding.viewEdit.isVisible = true
                    mContentBinding.viewShare.isVisible = false
                    sensorsTrack(SensorsConstant.V3710.SHAREEDITBUTTON_CLICK)
                }

                ShareButton.TYPE_COPY_LINK -> {
                    /**
                     * raf分享 ib分享只有url链接
                     *
                     * 其他分享不复制文案
                     */
                    shareData.shareUrl.copyText(context.getString(R.string.link_copied))
                    buryShareType = BuryPointConstant.ShareType.COPY_LINK
                    point()
                    // 神策自定义埋点(v3500)
                    sensorsTrack(shareType, true, "Copy Link")
                }

                ShareButton.TYPE_SAVE -> {
                    checkPermission {
                        ShareUtils.save(activity, bitmap)
                        buryShareType = BuryPointConstant.ShareType.SAVE_IMAGE
                        point()
                        // 神策自定义埋点(v3500)
                        sensorsTrack(shareType, true, "Save")
                    }
                }

                ShareButton.TYPE_FACEBOOK -> {
                    checkPermission {
                        ShareUtils.shareFacebook(activity, bitmap)
                        buryShareType = BuryPointConstant.ShareType.FB
                        point()
                        // 神策自定义埋点(v3500)
                        sensorsTrack(shareType, true, "Facebook")
                    }
                }

                ShareButton.TYPE_MORE -> {
                    systemShareClick()
                }
            }
            if (adapter.data[position].type != ShareButton.TYPE_EDIT)
                sensorsTrack(
                    SensorsConstant.V3710.SHAREPOSTERBUTTON_CLICK,
                    shareTopic = if (shareSettingData.shareTheme == ShareSettingData.TYPE_THEME_FERRARI) "Car" else "Default",
                    isImageEdited = isEdit(),
                    isCustomWatermarkEdited = isWatterMarkSelect(),
                    isCustomUsernameEdited = isUserNameSelect(),
                    isCustomAccountnoEdited = isAccNoSelect(),
                )
        }
    }

    /**
     * 是否和默认设置不同
     */
    private fun isEdit(): Boolean {
        var isEdit = false
        val accNoItem = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_ACCOUNT_NO }
        if (accNoItem != null) {
            isEdit = isEdit || accNoItem.isSelect != true
        }
        val userNameItem = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_USERNAME }
        if (userNameItem != null) {
            isEdit = isEdit || userNameItem.isSelect != true
        }
        val isWaterMarkItem = shareSettingData.editList?.find { it.title == ShareEditData.TYPE_WATERMARK }
        if (isWaterMarkItem != null) {
            isEdit = isEdit || isWaterMarkItem.isSelect != false
        }
        return isEdit
    }

    /**
     * 是否添加水印
     */
    private fun isWatterMarkSelect(): Boolean {
        return shareSettingData.editList?.find { it.title == ShareEditData.TYPE_WATERMARK }?.isSelect == true
    }

    /**
     * 是否添加用户名
     */
    private fun isUserNameSelect(): Boolean {
        return shareSettingData.editList?.find { it.title == ShareEditData.TYPE_USERNAME }?.isSelect == true
    }

    /**
     * 是否添加用户账号
     */
    private fun isAccNoSelect(): Boolean {
        return shareSettingData.editList?.find { it.title == ShareEditData.TYPE_ACCOUNT_NO }?.isSelect == true
    }

    private fun systemShareClick() {
        checkPermission {
            /**
             * ib分享只有url链接
             * raf分享添加其他文案
             * 其他分享不复制文案
             */
            val shareStr = when (shareType) {
                TYPE_RAF -> "GET USD 50 NOW when you download and trade on Vantage App, an award-winning, multi-asset broker with over 13 years of experience! ${shareData.shareUrl} T&Cs apply"
                else -> ""
            }
            ShareUtils.moreShare(activity, shareStr, bitmap)
            buryShareType = BuryPointConstant.ShareType.MORE
            point()
            // 神策自定义埋点(v3500)
            sensorsTrack(shareType, true, "More")
        }
    }

    override fun beforeDismiss() {
        super.beforeDismiss()
        ScreenCaptureObserver.isShareDialogShow = false
    }

    /**
     * 埋点
     */
    private fun point() {
        LogEventUtil.setLogEvent(
            positionShareType, Bundle().apply {
                putString(BuryPointConstant.ShareType.KEY_MEDIA_SOURCE, buryShareType)
            }
        )
    }

    /**
     * 神策自定义埋点
     * @param eventName 事件名称
     * @param shareTopic 分享主题
     * @param isImageEdited 是否使用过编辑功能
     * @param isCustomWatermarkEdited 是否添加水印
     * @param isCustomUsernameEdited 是否添加用户名
     * @param isCustomAccountnoEdited 是否添加Accountno
     */
    private fun sensorsTrack(
        eventName: String,
        shareTopic: String? = null,
        isImageEdited: Boolean? = null,
        isCustomWatermarkEdited: Boolean? = null,
        isCustomUsernameEdited: Boolean? = null,
        isCustomAccountnoEdited: Boolean? = null
    ) {
        val properties = JSONObject()
        properties.put(
            "share_source", when (shareType) {
                TYPE_KLINE -> "CandlestickchartPage"
                TYPE_STRATEGY_DETAIL -> "StrategiesDetailPage"
                TYPE_ORDER -> "PositionPage"
                TYPE_ORDER_HISTORY -> "PositionHistoryPage"
                TYPE_RAF -> "RafPage"
                TYPE_STRATEGY_ORDER -> "CopyTradingPage"
                TYPE_STRATEGY_ORDER_HISTORY -> "CopyTradingHistoryPage"
                TYPE_SCREENSHOT -> "SncreeshotPage"
                else -> ""

            }
        )
        shareTopic?.let {
            properties.put("share_topic", shareTopic)
        }
        isImageEdited?.let {
            properties.put("is_image_edited", isImageEdited)
        }
        isCustomWatermarkEdited?.let {
            properties.put("is_custom_watermark_edited", isCustomWatermarkEdited)
        }
        isCustomUsernameEdited?.let {
            properties.put("is_custom_username_edited", isCustomUsernameEdited)
        }
        isCustomAccountnoEdited?.let {
            properties.put("is_custom_accountno_edited", isCustomAccountnoEdited)
        }

        SensorsDataUtil.track(eventName, properties)
    }

    /**
     * 分享等操作前进行权限检查
     */
    private fun checkPermission(save: () -> Unit) {
        PermissionUtil.checkPermissionWithCallback(activity, *Constants.PERMISSION_STORAGE) {
            if (it)
                save.invoke()
            else
                CenterActionDialog.Builder(activity)
                    .setTitle(context.getString(R.string.save_failed))
                    .setContent(context.getString(R.string.to_save_images))
                    .setEndText(context.getString(R.string.go_settings))
                    .setOnEndListener {
                        IntentUtil.launchAppDetailsSettings()
                    }
                    .build()
                    .showDialog()
        }
    }

    /**
     * 根据不同类型设置分享按钮以及分享的图片类型
     */
    private fun updateShareData() {
        //默认的分享方式是下面这条，raf分享和ib分享是其他的type
        positionShareType = BuryPointConstant.V342.GENERAL_SHARE_MEDIA_SOURCE_BUTTON_CLICK
        when (shareType) {
            TYPE_KLINE -> {
                addIBEditItem()
                configList(listOf(shareData))
            }

            TYPE_ORDER, TYPE_ORDER_HISTORY -> {
                addDefaultEditItem()
                addIBEditItem()
                configList(listOf(/*shareData.copy(shareItemType = ITEM_TYPE_1), shareData.copy(shareItemType = ITEM_TYPE_2), */shareData.copy(shareItemType = ITEM_TYPE_3)))
            }

            TYPE_STRATEGY_ORDER, TYPE_STRATEGY_ORDER_HISTORY -> {
                addIBEditItem()
                configList(listOf(shareData.copy(shareItemType = ITEM_TYPE_1), shareData.copy(shareItemType = ITEM_TYPE_2)))
            }

            TYPE_STRATEGY_DETAIL -> {
                addCopyItem()
                addIBEditItem()
                configList(listOf(shareData.copy(shareItemType = ITEM_TYPE_1), shareData.copy(shareItemType = ITEM_TYPE_2), shareData.copy(shareItemType = ITEM_TYPE_3)))
            }

            TYPE_RAF -> {
                addCopyItem()
                configList(listOf(shareData))
                positionShareType = BuryPointConstant.V342.PROMO_REFERRAL_BONUS_SHARE_MEDIA_SOURCE_BUTTON_CLICK
            }

            TYPE_SCREENSHOT -> {
                mContentBinding.rootView.background = null
                mContentBinding.viewShare.isVisible = false
                mContentBinding.viewEdit.isVisible = false
                configList(listOf(shareData))
            }

        }
        if (shareSettingData.editList?.isNotEmpty()==true) {
            mContentBinding.rvEdit.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            mContentBinding.rvEdit.adapter = editAdapter
            mContentBinding.rvEdit.addItemDecoration(DividerItemDecoration(12.dp2px(), firstDividerSize = 12.dp2px(), orientation = RecyclerView.HORIZONTAL))
            editAdapter.setOnItemClickListener { _, _, position ->
                editAdapter.data.getOrNull(position)?.let {
                    it.isSelect = !it.isSelect
                }
                editAdapter.notifyItemChanged(position)
                updateSetting()
            }
            test()
            editAdapter.setList(shareSettingData.editList?.map { it.deepCopy() }?.toMutableList())
            addEditItem()
        }
        adapter.setList(shareButtonList)
        if (shareType != TYPE_STRATEGY_DETAIL && shareType != TYPE_SCREENSHOT && shareType != TYPE_KLINE && shareType != TYPE_RAF) {
            mContentBinding.groupTheme.isVisible = true
            if (shareSettingData.shareTheme == ShareSettingData.TYPE_THEME_FERRARI) {
                setThemeFerrariSelect()
            } else {
                setThemeLogoSelect()
            }
            mContentBinding.ivThemeLogo.clickNoRepeat {
                if (shareSettingData.shareTheme == ShareSettingData.TYPE_THEME_LOGO)
                    return@clickNoRepeat
                setThemeLogoSelect()
            }
            mContentBinding.ivThemeFerrari.clickNoRepeat {
                if (shareSettingData.shareTheme == ShareSettingData.TYPE_THEME_FERRARI)
                    return@clickNoRepeat
                setThemeFerrariSelect()
            }
        }
    }

    private fun setThemeLogoSelect() {
        mContentBinding.ivThemeFerrari.setImageResource(AttrResourceUtil.getDrawable(context, R.attr.imgShareThemeFerrari))
        mContentBinding.ivThemeLogo.setImageResource(AttrResourceUtil.getDrawable(context, R.attr.imgShareThemeLogoSelect))
        shareAdapter.shareTheme = ShareSettingData.TYPE_THEME_LOGO
        shareAdapter.notifyItemRangeChanged(0, shareAdapter.itemCount)
        shareSettingData.shareTheme = ShareSettingData.TYPE_THEME_LOGO
        SpManager.putShareSetting(shareType.toString(), shareSettingData)
    }

    private fun setThemeFerrariSelect() {
        mContentBinding.ivThemeFerrari.setImageResource(AttrResourceUtil.getDrawable(context, R.attr.imgShareThemeFerrariSelect))
        mContentBinding.ivThemeLogo.setImageResource(AttrResourceUtil.getDrawable(context, R.attr.imgShareThemeLogo))
        shareAdapter.shareTheme = ShareSettingData.TYPE_THEME_FERRARI
        shareAdapter.notifyItemRangeChanged(0, shareAdapter.itemCount)
        shareSettingData.shareTheme = ShareSettingData.TYPE_THEME_FERRARI
        SpManager.putShareSetting(shareType.toString(), shareSettingData)
    }

    private fun addDefaultEditItem() {
        val watermark = ShareEditData(title = ShareEditData.TYPE_WATERMARK)
        val username = ShareEditData(title = ShareEditData.TYPE_USERNAME)
        if (shareSettingData.editList?.find { it.title == ShareEditData.TYPE_WATERMARK } == null)
            shareSettingData.editList?.add(watermark)
        if (shareSettingData.editList?.find { it.title == ShareEditData.TYPE_USERNAME } == null)
            shareSettingData.editList?.add(username)
    }

    private fun addIBEditItem() {
        if (isIB) {
            val accountNo = ShareEditData(title = ShareEditData.TYPE_ACCOUNT_NO, isSelect = true)
            if (shareSettingData.editList?.find { it.title == ShareEditData.TYPE_ACCOUNT_NO } == null)
                shareSettingData.editList?.add(accountNo)
        }
    }

    /**
     * 部分分享类型需要添加复制链接按钮
     */
    private fun addCopyItem() {
        shareButtonList.add(
            0,
            ShareButton(
                type = ShareButton.TYPE_COPY_LINK,
                icon = AttrResourceUtil.getDrawable(context, R.attr.imgShareCopyLink),
                title = context.getString(R.string.copy_link)
            )
        )
    }

    /**
     * 部分分享类型需要添加编辑按钮
     */
    private fun addEditItem() {
        shareButtonList.add(
            0,
            ShareButton(
                type = ShareButton.TYPE_EDIT,
                icon = AttrResourceUtil.getDrawable(context, R.attr.imgShareEdit),
                title = context.getString(R.string.edit)
            )
        )
    }

    override fun onBackPressed(): Boolean {
        if (mContentBinding.viewEdit.isVisible) {
            mContentBinding.viewEdit.isVisible = false
            mContentBinding.viewShare.isVisible = true
            return true
        } else {
            return super.onBackPressed()
        }
    }

    /**
     * 设置分享的图片列表
     */
    private fun configList(data: List<ShareData>) {
        mContentBinding.run {
            rvShare.isVisible = true
            shareAdapter.setList(data)
            tvIndicator.isInvisible = data.size <= 1
            tvIndicator.text = getIndicatorText(1)
        }
    }

    /**
     * 获取 指示器文案
     */
    private fun getIndicatorText(index: Int) = buildSpannedString {
        if (LanguageHelper.isRtlLanguage()) {
            color(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)) {
                append("${shareAdapter.data.size}/")
            }
            color(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)) {
                append("$index")
            }
        } else {
            color(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)) {
                append("$index")
            }
            color(AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff)) {
                append("/${shareAdapter.data.size}")
            }
        }
    }

    /**
     * 初始话Facebook分享弹窗，如果要使用Facebook的sdk分享的话，需要先初始化
     */
    private fun initFaceBook() {
        if (callbackManager == null) {
            callbackManager = CallbackManager.Factory.create()
            shareDialog = ShareDialog(activity)
            callbackManager?.let {
                shareDialog?.registerCallback(it, object : FacebookCallback<Sharer.Result> {
                    override fun onSuccess(result: Sharer.Result) {
                        LogUtil.w("share------onSuccess")
                        ToastUtil.showToast(context.getString(R.string.success))
                    }

                    override fun onCancel() {
                        LogUtil.w("share------onCancel")
                    }

                    override fun onError(error: FacebookException) {
                        error.printStackTrace()
                        LogUtil.w("share------onError---->${error.message}")
                    }
                })
            }
        }
    }

    private fun showLoading(activity: AppCompatActivity) {
        when (activity) {
            is BaseMvvmActivity<*, *> -> {
                activity.showLoadDialog()
            }

            is BaseDataBindingActivity<*> -> {
                activity.showLoadDialog()
            }

            is BaseActivity -> {
                activity.showNetDialog()
            }
        }
    }

    private fun hideLoading(activity: AppCompatActivity) {
        when (activity) {
            is BaseMvvmActivity<*, *> -> {
                activity.hideLoadDialog()
            }

            is BaseDataBindingActivity<*> -> {
                activity.hideLoadDialog()
            }

            is BaseActivity -> {
                activity.hideNetDialog()
            }
        }
    }

    companion object {
        // region 分享类型
        /**
         * k线图分享
         */
        const val TYPE_KLINE = 0x1001

        /**
         * 策略详情
         */
        const val TYPE_STRATEGY_DETAIL = 0x1002

        /**
         * 自助交易 多品牌 订单交易分享
         */
        const val TYPE_ORDER = 0x1003

        /**
         *  自助交易 多品牌 订单历史
         */
        const val TYPE_ORDER_HISTORY = 0x1004

        /**
         * raf分享， 设置-邀请-invite_now-分享
         */
        const val TYPE_RAF = 0x1005

        /**
         * 策略 订单 分享
         */
        const val TYPE_STRATEGY_ORDER = 0x1006

        /**
         * 历史 策略 订单 分享
         */
        const val TYPE_STRATEGY_ORDER_HISTORY = 0x1007

        /**
         * 系统截屏分享
         */
        const val TYPE_SCREENSHOT = 0x1008

        // endregion 分享类型结束

        // 分享列表里的分享样式
        const val ITEM_TYPE_1 = 0x1111
        const val ITEM_TYPE_2 = 0x2222
        const val ITEM_TYPE_3 = 0x3333

        internal fun show(
            activity: AppCompatActivity,
            /**
             * 分享弹窗类型
             */
            shareType: Int,
            signalId: String?,
            viewModel: ShareViewModel,
            shareData: ShareData,
            isIB: Boolean = false,
            accountList: List<AccountTradeBean>? = null,
            dismissCallback: (() -> Unit)? = null
        ): SharePopup? {
            // 神策自定义埋点(v3500)
            sensorsTrack(shareType)

            return XPopup.Builder(activity)
                .apply {
                    if (AppUtil.isLightTheme()) {
                        hasStatusBarShadow(true)
                    }
                }
                .enableDrag(false)
                .setPopupCallback(object : SimpleCallback() {
                    override fun onDismiss(popupView: BasePopupView?) {
                        super.onDismiss(popupView)
                        dismissCallback?.invoke()
                    }
                })
                .navigationBarColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))
                .asCustom(
                    SharePopup(
                        activity = activity,
                        shareType = shareType,
                        signalId = signalId,
                        viewModel = viewModel,
                        shareData = shareData,
                        isIB = isIB,
                        accountList = accountList,
                    )
                ).show() as? SharePopup
        }

        /**
         * 神策自定义埋点(v3500)
         */
        private fun sensorsTrack(shareType: Int, isClickBtnTrack: Boolean = false, buttonName: String? = null) {
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "$shareType") // 当前页面名称
            properties.put(SensorsConstant.Key.IS_ACTIVITY_SHARE, "") // 是否活动分享
            properties.put(SensorsConstant.Key.ACTIVITY_ID, "") // 活动ID
            properties.put(SensorsConstant.Key.ACTIVITY_NAME, "") // 活动名称
            if (isClickBtnTrack) {
                properties.put(SensorsConstant.Key.SHARE_TYPE, buttonName) // 分享方式
                // 分享按钮点击 -> 分享按钮点击时触发
                SensorsDataUtil.track(SensorsConstant.V3500.SHARE_METHOD_CLICK, properties)
            } else {
                // 分享按钮点击 -> 分享按钮点击时触发
                SensorsDataUtil.track(SensorsConstant.V3500.SHARE_BTN_CLICK, properties)
            }
        }
    }
}