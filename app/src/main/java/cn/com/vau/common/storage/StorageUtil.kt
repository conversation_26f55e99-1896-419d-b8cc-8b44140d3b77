package cn.com.vau.common.storage

import cn.com.vau.util.language.LanguagesConfig
import com.tencent.mmkv.MMKV

object StorageUtil {

    val userStorage: Storage by lazy {
        Storage(MMKV.mmkvWithID("vau_user_data", MMKV.MULTI_PROCESS_MODE))
    }

    /**
     * 业务临时存储，业务自己清理的
     */
    val localStorage: Storage by lazy {
        Storage(MMKV.mmkvWithID("local_data", MMKV.MULTI_PROCESS_MODE))
    }

    /**
     * 退出登录时清理
     */
    val logoutClearStorage: Storage by lazy { Storage(MMKV.mmkvWithID("logout_clear_data", MMKV.MULTI_PROCESS_MODE)) }

    /**
     * 切换账户时清理
     */
    val switchAccountClearStorage: Storage by lazy { Storage(MMKV.mmkvWithID("switch_account_clear_data", MMKV.MULTI_PROCESS_MODE)) }

    /**
     * 存储用户下单产品时使用的交易量，考虑到后面可交易的产品数可能会很多所以单独使用一个文件存储。且不需要清理
     */
    val productLotsStorage: Storage by lazy { Storage(MMKV.mmkvWithID("product_lots_data",MMKV.MULTI_PROCESS_MODE)) }

    /**
     * 清空用户数据
     */
    fun clearUserData() {
        userStorage.clearAll()
    }

    /**
     *  清空业务临时数据
     */
    fun clearLocalData() {
        localStorage.clearAll()
    }

    /**
     * 退出登录清理
     */
    fun logoutClear() {
        logoutClearStorage.clearAll()
    }

    /**
     * 切换账户清理
     */
    fun switchAccountClear() {
        switchAccountClearStorage.clearAll()
    }

    /**
     * 获取不需要清理sp
     */

    fun getNeverClear(): SPUtil {
        return SPUtil
    }

    /**
     * 为了兼容老用户的语言配置，先单独处理，后续版本会删除
     */
    fun getLanguageSP(): LanguagesConfig {
        return LanguagesConfig
    }

}