package cn.com.vau.common.view.popup

import android.app.Activity
import android.content.Context
import android.graphics.Paint
import cn.com.vau.databinding.DialogBottomSelectMt4ConfirmBinding
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.buidler.ActionBuilder

/**
 * author：lvy
 * date：2024/09/26
 * desc：选择mt4平台的确认弹框
 */
class BottomSelectMt4ConfirmDialog(context: Context) :
    BottomDialog<DialogBottomSelectMt4ConfirmBinding>(context, DialogBottomSelectMt4ConfirmBinding::inflate) {

    override fun setContentView() {
        super.setContentView()
        mContentBinding.tvPlatform4.paint.flags = Paint.UNDERLINE_TEXT_FLAG

        mContentBinding.tvPlatform5.clickNoRepeat {
            mt5Listener.invoke()
        }
        mContentBinding.tvPlatform4.clickNoRepeat {
            mt4Listener.invoke()
        }
    }

    /**
     * 回调
     */
    private var mt5Listener: () -> Unit = { }
    fun switchMt5Listener(e: () -> Unit) {
        this.mt5Listener = e
    }

    private var mt4Listener: () -> Unit = { }
    fun continueMt4Listener(e: () -> Unit) {
        this.mt4Listener = e
    }

    class Builder(activity: Activity) : ActionBuilder<DialogBottomSelectMt4ConfirmBinding>(activity) {

        override fun createDialog(context: Context): BottomDialog<DialogBottomSelectMt4ConfirmBinding> {
            return BottomSelectMt4ConfirmDialog(context)
        }

        override fun build(): BottomSelectMt4ConfirmDialog {
            return super.build() as BottomSelectMt4ConfirmDialog
        }
    }
}