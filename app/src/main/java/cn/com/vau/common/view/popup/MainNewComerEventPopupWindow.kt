package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.databinding.PopupMainEventNewComerBinding
import cn.com.vau.data.init.NewerGiftActivityBean

class MainNewComerEventPopupWindow(context: Context) : PopupWindow() {

    private val popView by lazy { PopupMainEventNewComerBinding.inflate(LayoutInflater.from(context)) }

    init {
        initParam()
        initView(context)
        initData(context)
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun initView(context: Context) {
        // popView!!.textView11.text = "${context.getString(R.string.open)}: "
    }

    @SuppressLint("SetTextI18n")
    fun initData(context: Context) {
        popView?.mImageFilterView?.let {
            ImageLoaderUtil.loadImage(context,dataBean?.bottomPicUrl?:"",it)
        }
    }

    private fun initParam() {
        this.contentView = popView.root
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.WRAP_CONTENT
//        this.animationStyle = R.style.popupAnimStyleBottom
//        this.isOutsideTouchable = true
    }

    private fun initListener() {
        popView?.ivDelete?.setOnClickListener {
            dismiss()
        }

        popView?.mImageFilterView?.setOnClickListener {
            mOnPopClickListener?.onItemClick()
            dismiss()
        }
    }

    private var mOnPopClickListener: OnPopClickListener? = null

    interface OnPopClickListener {
        fun onItemClick()
    }

    fun setOnPopClickListener(mOnPopClickListener: OnPopClickListener) {
        this.mOnPopClickListener = mOnPopClickListener
    }

    private var dataBean:  NewerGiftActivityBean? = null

    fun setData(dataBean: NewerGiftActivityBean, context: Context) {
        this.dataBean = dataBean
        initData(context)
    }

}