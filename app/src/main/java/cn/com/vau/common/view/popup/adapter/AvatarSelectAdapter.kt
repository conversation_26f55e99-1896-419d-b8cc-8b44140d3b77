package cn.com.vau.common.view.popup.adapter

import cn.com.vau.R
import cn.com.vau.data.strategy.SelectAllPicBean
import cn.com.vau.util.ImageLoaderUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * @description:
 * @author: gold.guo
 * @createDate: 2024 4.4 星期四 16:34
 * @updateUser:
 * @updateDate: 2024 4.4 星期四 16:34
 */
class AvatarSelectAdapter(var selectUrl: String? = null) : BaseQuickAdapter<SelectAllPicBean.Obj, BaseViewHolder>(R.layout.item_recycler_avatar_select) {
    override fun convert(holder: BaseViewHolder, item: SelectAllPicBean.Obj) {
        if (selectUrl == item.url) {
            holder.itemView.setBackgroundResource(R.drawable.shape_stroke_c00c79c_r10)
        } else {
            holder.itemView.background = null
        }
        holder.getViewOrNull<ShapeableImageView>(R.id.ivAvatar)?.let {
            ImageLoaderUtil.loadImage(context,item.url,it,R.mipmap.ic_launcher,R.mipmap.ic_launcher)
        }

    }
}