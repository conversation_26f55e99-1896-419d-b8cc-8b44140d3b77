package cn.com.vau.common.view

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.View
import kotlin.text.indexOf
import kotlin.text.isNotEmpty

/**
 * 该组件是 SerialTextView 子类，在父类基础上做了超链接功能
 * 显示与父类相同
 */
class SerialLinkTextView constructor(context: Context, attrs: AttributeSet? = null) :
    SerialTextView(context, attrs) {

    private var spanString: SpannableString? = null

    // color资源的加载在外部实现
    // 例如：ContextCompat.getColor(context, R.color.ce35728)
    // 例如：AttrResourceUtil.getColor(context, R.attr.color_c034854_ce35728)
    fun set(span: String, color: Int = -1, click: (() -> Unit)? = null): SerialLinkTextView {
        if (getContentText().isNotEmpty() && span.isNotEmpty()) {
            spanString = getSpannableString()
            val index = getContentText().indexOf(span)
            if (index != -1) {
                spanString?.setSpan(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        click?.invoke()
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = true
                        if (color != -1) {
                            ds.color = color
                        }
                    }
                }, index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                textView()?.text = spanString
                textView()?.movementMethod = LinkMovementMethod.getInstance()
            }
        }
        return this
    }

    private fun getSpannableString(): SpannableString {
        return if (null != this.spanString) this.spanString ?: SpannableString("") else SpannableString(getContentText())
    }

}