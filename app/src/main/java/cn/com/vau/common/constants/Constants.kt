package cn.com.vau.common.constants

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi
import cn.com.vau.R
import cn.com.vau.util.StringUtil

/**
 * 常量
 */
object Constants {

    /**
     * 变量 || 常量
     */
    var offServerTime: Long = 0

    @Volatile
    var appInstanceId: String = ""
    const val finishRefreshOrMoreTime = 100
    var PromoTabTxt = true

    // 行情维护 ing
    var MARKET_MAINTAINING = false
    var MAINTENANCE_MSG = ""

    // 时令
    @JvmField
    var season = 3
    var badgeCount = 0

    // 登录失效状态
    const val TOKEN_ERROR = -1

    /**
     * param
     */
    const val DEPOSITE_FROM = "deposite_from"
    const val IS_FROM = "is_from"
    const val HANDLE_TYPE = "handle_type"
    const val DATA_MSG = "data_msg"
    const val SOUCE_OPEN_ACOUNT = "souce_open_acount"
    const val USER_EMAIL = "user_email"
    const val USER_PWD = "user_pwd"
    const val SELECT_AREA_CODE = "select_area_code"
    const val SELECT_CREDIT = "select_credit"
    const val SELECT_NATIONALITY_DATA = "select_nationality_data"
    const val NUM_STEP_OPEN_ACCOUNT = "num_step_open_account"
    const val PARAM_IS_FCM = "is_fcm"
    const val PARAM_STRATEGY_ID = "param_strategy_id"
    const val ST_HISTORY_ORDER = "st_history_order_fragment"
    const val ST_STRATEGY_ORDERS_HISTORY = "st_strategy_orders_history_fragment"

    // 产品名称
    const val PARAM_PRODUCT_NAME = "param_product_name"

    // 订单 - 数据
    const val PARAM_ORDER_DATA = "param_order_data"

    // 订单号
    const val PARAM_ORDER_NUMBER = "param_order_number"

    // 订单 - 类型
    const val PARAM_ORDER_TYPE = "param_order_type"

    // 订单 - 手数
    const val PARAM_ORDER_VOLUME = "param_order_volume"

    // 订单 - 开仓时间
    const val PARAM_ORDER_OPEN_TIME = "param_order_open_time"

    // 跟单- 获取平仓历史需要临时账号
    const val PARAM_ORDER_PORTFOLIO_ID = "param_order_portfolio_id"

    const val EVENT_TAG_CHANNELKEY = "event_tag_channelkey"
    const val EVENT_TAG_DEPARTMENT = "event_tag_department"

    // ASIC开户Event通知
    const val EVENT_TAG_ASIC_FIRST_GET_DATA = "event_tag_asic_first_get_data"
    const val EVENT_TAG_ASIC_FIRST_EXIST_EMAIL = "event_tag_asic_first_exist_email"
    const val EVENT_TAG_ASIC_FIRST_SAVE_DATA = "event_tag_asic_first_save_data"
    const val EVENT_TAG_ASIC_FIRST_SECOND_GET_DATA = "event_tag_asic_first_second_get_data"
    const val EVENT_TAG_ASIC_FIRST_SECOND_SAVE_DATA = "event_tag_asic_first_second_save_data"
    const val EVENT_TAG_ASIC_FIFTH_ERROR = "event_tag_asic_fifth_error"
    const val EVENT_TAG_ASIC_FIFTH_TO_H5 = "event_tag_asic_fifth_to_h5"

    // 主/副图参数传递，合并ViewModelView后无用
    const val PARAM_MAIN_INDICATOR_LIST = "param_main_indicator_list"
    const val PARAM_SUB_INDICATOR_LIST = "param_sub_indicator_list"
    const val PARAM_KLINE_EVENT = "param_kline_event"

    const val DOUBLE_LINE = "--"
    const val SELECT_AREA = 10000
    const val SELECT_NATION = 100
    const val SUBMIT_SUCCESS = 101
    const val REQUEST_CODE_NICKNAME = 0x1
    const val REQUEST_CODE_PHONE = 0x3
    const val RESULT_CODE_SECURITY = 0x4
    const val REQUEST_CODE_PERSONAL_INFO = 0x5
    const val REQUEST_CODE_STRATEGY_EDIT = 0x6
    const val REFRESH_PROFILE_SIGNAL_PROVIDER_CENTER = 0x8 // 刷新个人中心Signal Provider Center模块数据
    const val REQUEST_CODE_WEB_TV = 0xa
    const val RESULT_CODE = 0xff
    const val RESULT_CODE_SELECT_COUPON = 0xfe
    const val RESULT_CODE_BACK_TO_PAYMETHOD = 0xfd

    const val USER_TFA_BINDED = "_user_2fa_binded"
    const val STRATEGY_MOST_COPIED = "strategy_most_copied" // Most Copied strategies
    const val STRATEGY_HIGHEST_RETURN = "strategy_highest_return" // Highest Annual Return strategies
    const val STRATEGY_LOW_RISK_RETURN = "strategy_low_risk_return" // Low Risk and Stable Return strategies
    const val STRATEGY_HIGH_WIN_RATE = "strategy_high_win_rate" // High Win Rate strategies
    const val STRATEGY_TOP_PROVIDERS = "strategy_top_providers" // Top Signal Providers

    // 策略跟随模式
    const val STRATEGY_COPY_MODE_FORMULA = "FORMULA" // 等比例占用保证金
    const val STRATEGY_COPY_MODE_FIXED_VOLUME = "FIXED_VOLUME" // 固定手数
    const val STRATEGY_COPY_MODE_FIXED_MAGNIFICATION = "FIXED_MAGNIFICATION" // 固定倍数

    const val ST_USER_ID = "st_user_id"
    const val STRATEGY_ID = "strategy_id"
    const val STRATEGY_MIN_FOLLOW_AMOUNT = "strategy_min_follow_amount"
    const val STRATEGY_ORDER_COPY_MODE = "strategy_order_copy_mode"
    const val SP_KEY_SEARCH_HISTORY = "search_history_key"
    const val EVENT_REFRESHLAYOUT_FINISH = "event_refreshlayout_finishrefresh"

    const val PARAM_PASSKEY_CHECK = "param_passkey_check"

    const val CXD = "cxd"
    const val CID = "cid"
    const val RAF = "raf"
    const val LS = "ls"
    const val CP = "cp"
    const val AGENTACCOUNTNUM = "agentAccountNum"
    const val LIVESTREAM = "Livestream"

    const val FCM_TYPE = "fcm_type"
    const val FCM_DATA_P = "fcm_data_p"

    /**
     * h5页面顶部按钮 对应的type
     */
    const val ICON_TYPE_CLOSE = "CLOSE"
    const val ICON_TYPE_MENU = "MENU"
    const val ICON_TYPE_CUSTOMER = "CUSTOMER"
    const val ICON_TYPE_SHARE = "SHARE"
    const val ICON_TYPE_QUESTION = "QUESTION"
    const val ICON_TYPE_HISTORY = "HISTORY"

    /**
     * SUMSUB 校验poi 的 type  2
     */
    const val SUMSUB_TYPE_POI = "2"

    /**
     * SUMSUB 校验poa 的 type 3
     */
    const val SUMSUB_TYPE_POA = "3"

    /**
     * 人脸认证 朋哥最新确认 传空字符串
     * Sue Suen Mak淑璇 @Gold Guo @Ellie Huang @Zheng Quan Teng 确定了传空给宋朋，走原生支付的这块也要改
     */
    const val SUMSUB_TYPE_FACE = ""

    /**
     * SUMSUB 电汇入金 的 type 5
     */
    const val SUMSUB_TYPE_ADVANCE = "5"

    /**
     * 国家区号
     */
    val defaultCountryNum = "61"

    /**
     * 国家代码
     */
    val defaultCountryCode = "AU"

    /**
     * 国家名字
     */
    val defaultCountryName = StringUtil.getString(R.string.australia)

    /**
     * 空价格 或者 空数据的 占位显示值
     */
    const val NULL_VALUE = "--"

    /**
     * 品牌名称 ， 目前用于拦截器里给header使用 或者接口请求的传参
     */
    const val PRODUCT_NAME = "vau"

    /**
     * 默认的payload key , 目前只用于了notifyItemChanged的方法里， 没有在adapter里做对应的局部刷新，目前的默认值用的 vau ，提取出来 方便子品牌修改
     */
    const val DEFAULT_PAYLOAD_KEY = "vau_payload"

    /**
     * 存储权限 Android10 以上不需要申请
     */
    val PERMISSION_STORAGE by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            arrayOf()
        } else {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    /**
     * 相机权限
     */
    const val PERMISSION_CAMERA = Manifest.permission.CAMERA

    /**
     * 通知权限 Android12 以上需要申请
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    const val PERMISSION_NOTIFICATIONS = Manifest.permission.POST_NOTIFICATIONS

    /**
     * 录音权限
     */
    const val PERMISSION_RECORD_AUDIO = Manifest.permission.RECORD_AUDIO

    /**
     * 震动权限
     */
    const val PERMISSION_VIBRATE = Manifest.permission.VIBRATE

    /**
     * 自主交易 快速平仓 key
     */
    const val KEY_FAST_CLOSE = "us0003"

    /**
     * 跟单 快速平仓 key
     */
    const val KEY_FAST_CLOSE_ST = "us0018"

    /**
     * 订单确认 key
     */
    const val KEY_ORDER_CONFIRM = "us0019"

    /**
     * 消息免打扰的code
     */
    const val KEY_NO_NOTICE = "us0020"

    /**
     * 截屏分享的字段
     */
    const val SETTING_SCREEN_SHARE = "us0027"

    object GoldParam {

        const val CODE = "code" // 1: KYC流程  2: 开通主/同名账户  3: 补全信息
        const val NEXT_LEVEL = "nextLevel" // 期望等级（接口获取）
        const val SPECIALLEVEL = "specialLevel" // 用户强制跳转特定等级

        const val CODE_KYC = "1" // 1: KYC流程
        const val CODE_OPEN_ACCOUNT = "2" // 2: 开通主/同名账户
        const val CODE_COMPLETE_INFO = "3" // 3: 补全信息

        const val CREATE_COPY_TRADING_ACCOUNT = "createCopyTradingAccount" // 账户列表 开通跟单账号
        const val FROM_H5_NEED_SHOW_ALERT = "fromH5NeedShowAlert"
        const val CAN_ONLY_OPEN_COPY_TRADING = "canOnlyOpenCopyTrading"
        const val DEFAULT_ACCOUNT_TYPE_NUM = "defaultAccountTypeNum"
    }
}