package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.*
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.databinding.LayoutStepOpenAccountBinding
import cn.com.vau.util.dp2px

/**
 * 老开户 顶部 步数
 */
class StepOpenAccountView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {

    private var stepNum = 1
    private var stepNumTotal = 5
    private val parent by lazy { LayoutStepOpenAccountBinding.inflate(LayoutInflater.from(context), this, true) }

    private val drawable by lazy { ContextCompat.getDrawable(context, R.drawable.draw_shape_c1e1e1e_cebffffff_r100) }

    init {
        val attr = context.obtainStyledAttributes(attrs, R.styleable.step_open_account)
        stepNum = attr.getInteger(R.styleable.step_open_account_step_num, 1)
        stepNumTotal = attr.getInteger(R.styleable.step_open_account_step_num_total, 5)
        initView()
        attr.recycle()
    }

    private fun initView() {

        if (stepNumTotal == 2) {
            parent.thirdView.visibility = View.GONE
            parent.fourthView.visibility = View.GONE
            parent.fifthView.visibility = View.GONE

            val lp2 = parent.secondView.layoutParams as LinearLayout.LayoutParams
            lp2.marginStart = 20.dp2px()
            parent.secondView.layoutParams = lp2

        }

        if (stepNumTotal == 5) {
            val viewsList = ArrayList<View>().apply {
                add(parent.secondView)
                add(parent.thirdView)
                add(parent.fourthView)
                add(parent.fifthView)
            }
            for (mView in viewsList) {
                mView.let {
                    val lp2 = it.layoutParams as LinearLayout.LayoutParams
                    lp2.marginStart = 10.dp2px()
                    it.layoutParams = lp2
                }
            }
        }

        if (stepNumTotal == 6) {

            parent.sixthView.visibility = View.VISIBLE

            val viewsList = ArrayList<View>().apply {
                add(parent.secondView)
                add(parent.thirdView)
                add(parent.fourthView)
                add(parent.fifthView)
                add(parent.sixthView)
            }

            for (mView in viewsList) {
                mView.let {
                    val lp2 = it.layoutParams as LinearLayout.LayoutParams
                    lp2.marginStart = 10.dp2px()
                    it.layoutParams = lp2
                }
            }

        }

        when (stepNum) {
            2 -> {
                parent.secondView.background = drawable
            }

            3 -> {
                parent.secondView.background = drawable
                parent.thirdView.background = drawable
            }

            4 -> {
                parent.secondView.background = drawable
                parent.thirdView.background = drawable
                parent.fourthView.background = drawable
            }

            5 -> {
                parent.secondView.background = drawable
                parent.thirdView.background = drawable
                parent.fourthView.background = drawable
                parent.fifthView.background = drawable
            }

            6 -> {
                parent.secondView.background = drawable
                parent.thirdView.background = drawable
                parent.fourthView.background = drawable
                parent.fifthView.background = drawable
                parent.sixthView.background = drawable
            }
        }
    }

    fun setStepNumTotal(num: Int) {
        stepNumTotal = num
        initView()
    }

    fun setStepNum(num: Int) {
        stepNum = num
        initView()
    }

}