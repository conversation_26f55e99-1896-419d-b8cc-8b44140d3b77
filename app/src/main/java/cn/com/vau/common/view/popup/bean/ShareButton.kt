package cn.com.vau.common.view.popup.bean

import androidx.annotation.DrawableRes
import androidx.annotation.Keep

/**
 * Filename: ShareItem
 * Author: GG
 * Date: 2023/9/8 0008 15:54
 * Description:
 */
@Keep
data class ShareButton(
    val type: String,
    @DrawableRes val icon: Int,
    val title: String
) {

    companion object {
        const val TYPE_EDIT = "type_edit"
        const val TYPE_COPY_LINK = "copy_link"
        const val TYPE_SAVE = "save"
        const val TYPE_FACEBOOK = "facebook"
        const val TYPE_MORE = "more"
    }
}

@Keep
data class ShareEditData(
    var isSelect: Boolean = false,
    val title: String? = null,
) {
    fun deepCopy(): ShareEditData {
        return ShareEditData(isSelect = this.isSelect, title = this.title)
    }

    companion object {
        const val TYPE_WATERMARK = "Watermark"
        const val TYPE_USERNAME = "Username"
        const val TYPE_ACCOUNT_NO = "Account_No"
    }
}
