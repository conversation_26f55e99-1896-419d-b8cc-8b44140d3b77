package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.*
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.common.view.popup.adapter.PopAttchViewAdapter
import cn.com.vau.databinding.ViewDropDownListBinding
import cn.com.vau.profile.adapter.*
import cn.com.vau.util.dp2px
import cn.com.vau.util.widget.dialog.base.AttachListPopupWindow
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.lxj.xpopup.enums.*

@SuppressLint("CustomViewStyleable")
class DropDownListView : ConstraintLayout, VerifyComponent {

    constructor(context: Context) : super(context) {
        init(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(attrs)
    }

    private val parent: ViewDropDownListBinding by lazy { ViewDropDownListBinding.inflate(LayoutInflater.from(context), this, true) }
    private var onSelected: ((position: Int) -> Unit)? = null
    private var dataList: MutableList<String> = mutableListOf()
    private var popMode: Int = 0
    private var popTitle: String? = null
    private var hintTxt = ""
    private var showArrow = false
    private var tempIndex = -1

    private fun init(attrs: AttributeSet?) {
        val attr = context.obtainStyledAttributes(attrs, R.styleable.OpenAccount_Option_Text)
        hintTxt = attr.getString(R.styleable.OpenAccount_Option_Text_hint_text) ?: ""
        popMode = attr.getInt(R.styleable.OpenAccount_Option_Text_popMode, 0)
        showArrow = attr.getBoolean(R.styleable.OpenAccount_Option_Text_show_arrow, true)
        initView()
        initListener()
        attr.recycle()
    }

    private fun initView() {
        parent.mEditText.isFocusable = false
        parent.mImageArrow.visibility = if (showArrow) View.VISIBLE else View.GONE
        parent.mEditText.hint = hintTxt
    }

    private fun initListener() {
        parent.mEditText.keyListener = null
        parent.mEditText.setOnClickListener {
            if (dataList.isNotEmpty()) {

                if (popMode == 0) {
                    val actvity = context as? AppCompatActivity
                    actvity?.let {
                        val adapter = PopAttchViewAdapter()
                        val dialog = AttachListPopupWindow.Builder(it)
                            .setDestroyOnDismiss(false)
                            .hasShadowBg(false)
                            .setPopupPosition(PopupPosition.Bottom)
                            .setPopupAnimation(PopupAnimation.ScrollAlphaFromTop)
                            .borderRadius(8f.dp2px())
                            .setAdapter(adapter)
                            .setTargetView(parent.mEditText)
                            .setWidth(parent.mEditText.width)
                            .setOffsetY(8.dp2px())
                            .build()
                        adapter.setSelected(parent.mEditText.text.toString())
                        adapter.setNewInstance(dataList)
                        adapter.setOnItemClickListener { _, _, position ->
                            onSelected?.invoke(position)
                            setText(dataList.elementAtOrNull(position) ?: "")
                            dialog.dismiss()
                        }
                        dialog.showDialog()
                    }
                } else {
                    val adapter = SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false)
                    val activity = context as? AppCompatActivity
                    activity?.let {
                        val popup = BottomListDialog.Builder(it)
                            .setTitle(popTitle)
                            .setAdapter(adapter)
                            .build()
                        val list = mutableListOf<SelectBean>()
                        dataList.mapTo(list) { str -> SelectBean(str) }
                        adapter.setList(list)
                        adapter.selectTitle = parent.mEditText.text.toString()
                        adapter.setOnItemClickListener { _, _, position ->
                            onSelected?.invoke(position)
                            setText(dataList.elementAtOrNull(position) ?: "")
                            popup.dismissDialog()
                        }
                        popup.showDialog()
                    }
                }
            }
        }
    }

    fun setData(data: MutableList<String>, defaultSelectedIndex: Int? = null, pupTitle: String? = null): DropDownListView {
        this.dataList.clear()
        this.popTitle = pupTitle
        dataList.addAll(data)
        if (defaultSelectedIndex != null && defaultSelectedIndex <= dataList.size) {
            if (onSelected != null) {
                onSelected?.invoke(defaultSelectedIndex)
            } else {
                tempIndex = defaultSelectedIndex
            }
            setText(dataList.elementAtOrNull(defaultSelectedIndex) ?: "")
        }
        return this
    }

    fun onSelected(listener: (position: Int) -> Unit) {
        this.onSelected = listener
        if (tempIndex != -1) {
            onSelected?.invoke(tempIndex)
            tempIndex = -1  // 用过一次就置回初始值
        }
    }

    fun setText(text: String) {
        parent.mEditText.setText(text)
        callback?.verify(text.isNotEmpty())
    }

    fun text(): String {
        return parent.mEditText.text.toString()
    }

    override var callback: VerifyCallBack? = null

    override fun getVerify(): Boolean = parent.mEditText.text.toString().isNotEmpty()
}