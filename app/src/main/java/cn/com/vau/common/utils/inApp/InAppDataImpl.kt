package cn.com.vau.common.utils.inApp

import androidx.viewbinding.ViewBinding
import cn.com.vau.data.init.InAppBean

/**
 * Filename: InAppDataImpl.kt
 * Author: GG
 * Date: 2023/11/29
 * Description:
 */
abstract class InAppDataImpl {

    /**
     * 内部应用数据列表。
     */
    protected abstract var data: InAppBean.InAppData?

    /**
     * 内部应用数据与视图绑定之间的映射。
     */
    protected abstract val viewList: MutableList<ViewBinding?>

    /**
     * 是否可以显示内部应用数据的标志。
     */
    abstract var isCanShow: Boolean

    /**
     * 保存内部应用数据。
     *
     * @param list 要保存的内部应用数据。
     */
    abstract fun saveData(list: MutableList<InAppBean.InAppData>?)

    /**
     * 将视图绑定与指定的内部应用数据关联。
     *
     * @param viewBinding 要关联的视图绑定。
     */
    abstract fun saveViewBinding(viewBinding: ViewBinding)

    /**
     * 显示下一个要展示的内部应用数据。
     *
     * @return 要展示的下一个内部应用数据，如果没有则返回 null。
     */
    abstract fun showData(): InAppBean.InAppData?

    /**
     * 清除内部应用数据。
     */
    abstract fun clearData()
}