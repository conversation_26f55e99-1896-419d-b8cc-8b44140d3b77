package cn.com.vau.common.view.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.databinding.DialogCommonLoadingBinding
import cn.com.vau.util.AppUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.screenHeight
import cn.com.vau.util.screenWidth

/**
 * Created by array on 2025/2/18 14:20
 * Desc: 可以设置锚点，loading和锚点中心点重合
 */
class CommonLoadingDialog(context: Context) : Dialog(context, R.style.LoadRequestDialog) {

    private val mBinding by lazy { DialogCommonLoadingBinding.inflate(layoutInflater) }

    private var cancelable: Boolean = true

    private var canceledOnTouchOutside: Boolean = false

    private val loadingDrawable by lazy {
        if (AppUtil.isLightTheme())
            ContextCompat.getDrawable(context, R.drawable.drawable_loading)
        else
            ContextCompat.getDrawable(context, R.drawable.drawable_loading_night)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        mBinding.loadingView.setImageDrawable(loadingDrawable)
        setCanceledOnTouchOutside(canceledOnTouchOutside)
        setCancelable(cancelable)
        setFullScreen()
    }

    private fun setFullScreen() {
        val set = ConstraintSet()
        set.clone(mBinding.root)
        set.constrainWidth(mBinding.clContent.id, screenWidth)
        set.constrainHeight(mBinding.clContent.id, screenHeight)
        set.applyTo(mBinding.root)
    }

    override fun show() {
        if (context is Activity && (!(context as Activity).isFinishing || !(context as Activity).isDestroyed)) {
            return
        }
        super.show()
        mBinding.loadingView?.animate()?.rotation(360_000f)?.setDuration(900_000L)?.setInterpolator(LinearInterpolator())?.start()
    }

    override fun dismiss() {
        if (context is Activity && (!(context as Activity).isFinishing || !(context as Activity).isDestroyed)) {
            return
        }
        super.dismiss()
        mBinding.loadingView.animate().cancel()
    }

    fun setCancelAble(cancelAble: Boolean): CommonLoadingDialog {
        this.cancelable = cancelAble
        return this
    }

    fun setCanceledOutside(canceledOnTouchOutside: Boolean): CommonLoadingDialog {
        this.canceledOnTouchOutside = canceledOnTouchOutside
        return this
    }

    fun setAnchorView(anchor: View): CommonLoadingDialog {
        val location = IntArray(2)
        anchor.getLocationOnScreen(location)
        val anchorCenterY = location[1] + anchor.height / 2
        val topMargin = anchorCenterY - 60.dp2px()
        val set = ConstraintSet()
        set.clone(mBinding.clContent)
        set.clear(mBinding.loadingView.id, ConstraintSet.BOTTOM)
        set.connect(mBinding.loadingView.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, topMargin)
        set.connect(mBinding.loadingView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
        set.connect(mBinding.loadingView.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
        set.applyTo(mBinding.clContent)

        return this
    }
}