package cn.com.vau.common.mvvm.base

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import java.lang.reflect.ParameterizedType

/**
 * ViewModelActivity基类，把ViewModel注入进来了
 */
abstract class BaseMvvmActivity<VB : ViewBinding, VM : BaseViewModel> : BaseMvvmBindingActivity<VB>() {

    protected val mViewModel: VM by lazy {
        ViewModelProvider(this)[getVmClazz(this)]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //方便在VM层使用loading，因为上层基类没有VM，所以监听loading变化方法放在这个有VM的基类，继承上层基类的直接使用show和hide即可
        initLoadingChange()
    }

    /**
     * VM监听loading变化事件
     */
    private fun initLoadingChange() {
        mViewModel.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun <T : Any> getVmClazz(obj: Any): Class<T> {
        return (obj.javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[1] as Class<T>
    }

}