package cn.com.vau.common.view.popup

import android.app.Activity
import android.content.Context
import android.view.*
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.*
import cn.com.vau.page.user.accountManager.AccountTradeSecondDetail
import cn.com.vau.page.user.accountManager.adapter.SwitchDemoListAdapter
import cn.com.vau.util.setAlpha

class AccountListPop(var context: Context): PopupWindow() {

    private var listener: OnSelectItemListener? = null
    private var popView: View? = null
    private var mRecyclerView: RecyclerView? = null
    private var ivClose: ImageView? = null

    private var adapter = SwitchDemoListAdapter()

    init {
        initView(context)
        initParam()
        initListener()
    }

    private fun initParam() {
        this.contentView = popView
        this.width = ViewGroup.LayoutParams.MATCH_PARENT
        this.height = ViewGroup.LayoutParams.WRAP_CONTENT
        this.isFocusable = true
        this.isOutsideTouchable = true
        this.animationStyle = R.style.popupAnimStyleBottom
    }

    private fun initView(context: Context) {
        popView = LayoutInflater.from(context).inflate(R.layout.popup_accaount_list, null)
        mRecyclerView = popView?.findViewById(R.id.mRecyclerView)
        ivClose = popView?.findViewById(R.id.ivClose)
        mRecyclerView?.adapter = adapter
        ivClose?.setOnClickListener {
            dismiss()
        }
    }

    private fun initListener() {
        adapter.setOnItemChildClickListener { _, _, position ->
            val bean = adapter.getItem(position)
            bean.showAccountInfo = !bean.showAccountInfo
            adapter.notifyItemChanged(position)
        }
        adapter.setOnItemClickListener { _, _, position ->
            val bean = adapter.getItem(position)
            listener?.onSelectItem(convertTradeBean(bean))
            dismiss()
        }
    }

    private fun convertTradeBean(bean: DemoAccountDetail): AccountTradeBean {
        return AccountTradeBean().apply {
            accountServer = bean.accountServer
            acountCd = bean.mt4AccountId
            accountDealType = "3"
            platform = bean.platFrom
            detailData = AccountTradeSecondDetail()
            detailData?.currencyType = bean.currency
            accountType = bean.mt4AccountType
        }
    }

    fun setData(data: MutableList<DemoAccountDetail>?): AccountListPop? {
        data?.let {
            adapter.setNewInstance(it)
            return this
        }
        return null
    }

    fun setCallBack(listener: OnSelectItemListener): AccountListPop {
        this.listener = listener
        return this
    }

    fun show() {
        super.showAtLocation(popView, Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, 0, 0)
        if (context is Activity) {
            (context as Activity).setAlpha(0.2f)
        }
    }

    override fun dismiss() {
        super.dismiss()
        if (context is Activity) {
            (context as Activity).setAlpha(1f)
        }
    }

    interface OnSelectItemListener {
        fun onSelectItem(item: AccountTradeBean)
    }
}