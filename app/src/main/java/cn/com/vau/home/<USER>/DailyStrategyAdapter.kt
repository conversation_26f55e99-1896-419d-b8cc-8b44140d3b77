package cn.com.vau.home.adapter

import cn.com.vau.R
import cn.com.vau.data.discover.NewsLetterObjData
import cn.com.vau.util.ImageLoaderUtil
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * Filename: DailyStrategyAdapter
 * Author: GG
 * Date: 2024/9/5
 * Description:
 */
class DailyStrategyAdapter : BaseQuickAdapter<NewsLetterObjData, BaseViewHolder>(R.layout.item_recycler_daily_strategy) {

    override fun convert(holder: BaseViewHolder, item: NewsLetterObjData) {
        val myOptions = RequestOptions()
            .placeholder(R.drawable.shape_placeholder)
            .error(R.drawable.shape_placeholder)

        ImageLoaderUtil.loadImageWithOption(
            context,
            item.img,
            holder.getView<ShapeableImageView>(R.id.mImageView),
            myOptions
        )

        holder.setText(R.id.tvTitle, item.title)
            .setText(R.id.tvDate, item.date)
            .setText(R.id.tvViews, item.views)
    }

}