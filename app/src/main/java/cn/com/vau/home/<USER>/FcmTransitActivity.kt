package cn.com.vau.home.activity

import android.content.Intent
import android.os.Bundle
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.msg.PushParam
import cn.com.vau.page.html.*
import cn.com.vau.page.start.SplashActivity
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.opt.PerfTraceUtil

class FcmTransitActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fcm_transit)
        jump(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        jump(intent)
    }

    private fun jump(intent: Intent?) {
        val mainIsExists = ActivityManagerUtil.getInstance().isExists(MainActivity::class.java)
        val openType: String = intent?.extras?.getString("openType") ?: ""
        val type: String = intent?.extras?.getString(Constants.FCM_TYPE) ?: ""
        val parcelableData: PushParam? = intent?.extras?.getParcelable<PushParam>(Constants.FCM_DATA_P)
        PerfTraceUtil.stopStartTrace()
//        LogUtils.w("viewType----->$type  mainIsExists----->$mainIsExists")
        // MainActivity 不存在
        if (!mainIsExists) {
            when (openType) {
                "url" -> {
                    val tradeType: Int = intent?.extras?.getInt("tradeType") ?: -1
                    val tittle: String = intent?.extras?.getString("title") ?: ""
                    val url: String = intent?.extras?.getString("url") ?: ""
                    val ruleId: String = intent?.extras?.getString("ruleId") ?: ""
                    val intent0 = if (!NewHtmlViewModel.isJumpNewHtml(url)) {
                        Intent(this, HtmlActivity::class.java).apply {
                            putExtras(Bundle().apply {
                                putInt("tradeType", tradeType)
                                putString("title", tittle)
                                putString("url", url)
                                putString("ruleId", ruleId)
                            })
                        }
                    } else {
                        Intent(this, NewHtmlActivity::class.java).apply {
                            putExtras(Bundle().apply {
                                putString(NewHtmlActivity.KEY_URL, url)
                                putString(NewHtmlActivity.KEY_TITLE, tittle)
                            })
                        }
                    }
                    val intent1 = Intent(this, MainActivity::class.java) // 不需要带任何参数
                    intent1.putExtra(PerfTraceUtil.startUpTagFromFcm, PerfTraceUtil.startUpTagFromFcm)
                    val intentArray = arrayOfNulls<Intent>(2)
                    intentArray[0] = intent1
                    intentArray[1] = intent0
                    startActivities(intentArray)
                }

                else -> {
                    startActivity(Intent(this, SplashActivity::class.java).apply {
                        putExtras(Bundle().apply {
                            putString(Constants.FCM_TYPE, type)
                            putParcelable(Constants.FCM_DATA_P, parcelableData ?: PushParam())
                            putString(PerfTraceUtil.startUpTagFromFcm, PerfTraceUtil.startUpTagFromFcm)
                        })
                    })
                }
            }
        } else {
            // MainActivity 已存在
            when (openType) {
                "url" -> {
                    val tradeType: Int = intent?.extras?.getInt("tradeType") ?: -1
                    val tittle: String = intent?.extras?.getString("title") ?: ""
                    val url: String = intent?.extras?.getString("url") ?: ""
                    val ruleId: String = intent?.extras?.getString("ruleId") ?: ""
                    startActivity(Intent(this, HtmlActivity::class.java).apply {
                        putExtras(Bundle().apply {
                            putInt("tradeType", tradeType)
                            putString("title", tittle)
                            putString("url", url)
                            putString("ruleId", ruleId)
                        })
                    })
                }

                else -> {
                    startActivity(Intent(this, MainActivity::class.java).apply {
                        putExtras(Bundle().apply {
                            putString(Constants.FCM_TYPE, type)
                            putParcelable(Constants.FCM_DATA_P, parcelableData ?: PushParam())
                            putString(PerfTraceUtil.startUpTagFromFcm, PerfTraceUtil.startUpTagFromFcm)
                        })
                    })
                }
            }

        }
        finish()
    }

}