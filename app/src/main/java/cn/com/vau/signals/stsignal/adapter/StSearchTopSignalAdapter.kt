package cn.com.vau.signals.stsignal.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.strategy.StTopBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * Filename: StSearchTopSignalAdapter.kt
 * Author: GG
 * Date: 2024/3/29
 * Description:
 */
class StSearchTopSignalAdapter(private val isStrategy: Boolean) : BaseQuickAdapter<StTopBean, BaseViewHolder>(R.layout.item_recycler_signal_top) {

    override fun convert(holder: BaseViewHolder, item: StTopBean) {
        val avatar = holder.getViewOrNull<ShapeableImageView>(R.id.ivIcon)
        holder.setText(R.id.tvName, if (isStrategy) item.strategyName else item.nickname)
            .setText(R.id.tvNum, "${holder.absoluteAdapterPosition + 1}")
        if (holder.absoluteAdapterPosition <= 2) {
            holder.setTextColor(R.id.tvNum, AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
        } else {
            holder.setTextColor(R.id.tvNum, AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff))
        }
        if (isStrategy) {
            holder.setText(R.id.tvType, context.getString(R.string.return_3m))
                .setText(R.id.tvTypeNum, item.threeMonthRR.mathMul("100").numFormat(2, true) + "%")
                .setTextColor(R.id.tvTypeNum, getColor(item.threeMonthRR.toDoubleCatching()))
            avatar?.post {
                avatar.shapeAppearanceModel = avatar.shapeAppearanceModel.toBuilder().setAllCornerSizes(10f.dp2px()).build()
            }
        } else {
            holder.setText(R.id.tvType, context.getString(R.string.copiers))
                .setText(R.id.tvTypeNum, item.copiers.ifNull("0"))
                .setTextColor(R.id.tvTypeNum, AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            avatar?.post {
                val cornerSize = avatar.width / 2f
                avatar.shapeAppearanceModel = avatar.shapeAppearanceModel.toBuilder().setAllCornerSizes(cornerSize).build()
            }
        }
        ImageLoaderUtil.loadImage(context, item.avatar, avatar, R.mipmap.ic_launcher)
    }

    private fun getColor(value: Double) = when {
        value > 0 -> ContextCompat.getColor(context, R.color.c00c79c)
        value == 0.0 -> AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        else -> ContextCompat.getColor(context, R.color.ce35728)
    }
}