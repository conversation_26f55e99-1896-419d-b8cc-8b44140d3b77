package cn.com.vau.signals.viewModel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.*
import cn.com.vau.util.*

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 30 14:42
 * @updateUser:
 * @updateDate: 2024 10月 30 14:42
 */
class EconomyCalendarViewModel : BaseViewModel() {

    var queryDate = TimeUtil.formatDate(System.currentTimeMillis(), "yyyy-MM-dd")
    var importance = "all"
    var timeZone = 8
    var currentTime = TimeUtil.formatDate(System.currentTimeMillis(), "HH:mm:ss")
    var areaCode = "all"

    var dataId: String? = null
    var isRemind = -1

    val uiListLiveData: MutableLiveData<ListUIState<List<CalendarObjFinindex>?>> by lazy { MutableLiveData() }

    val detailLiveData: MutableLiveData<EconomicCalendarObjBean?> by lazy { MutableLiveData() }
    val chartLiveData: MutableLiveData<List<ChartCalendarObjData>?> by lazy { MutableLiveData() }

    /**
     * 获取财经日历列表
     */
    fun queryCalendarList() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "queryDate" to queryDate,
                "importance" to importance,
                "timeZone" to AppUtil.getTimeZoneRawOffsetToHour(),
                "currentTime" to currentTime
            )
            if (UserDataUtil.isLogin()) {
                map["userToken"] = UserDataUtil.loginToken()
            }
            if (!TextUtils.isEmpty(areaCode)) {
                map["areaCode"] = areaCode
            }
            baseService.finCalendarList(map)
        }, onSuccess = {
            if (!it.isSuccess()) {
                uiListLiveData.value = ListUIState.Error()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            uiListLiveData.value = ListUIState.RefreshSuccess(it.data?.obj?.finIndexs)
        }, onError = {
            uiListLiveData.value = ListUIState.Error()
        })
    }

    /**
     * 设置提醒
     */
    fun setUpRemind(dataId: String?) {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "userToken" to UserDataUtil.loginToken(),
                "dataId" to dataId,
            )
            baseService.finCalendarRemind(map)
        }, {

        })
    }

    /**
     * 取消提醒
     */
    fun cancelRemind(dataId: String?) {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "userToken" to UserDataUtil.loginToken(),
                "dataId" to dataId,
            )
            baseService.finCalendarCancelRemind(map)
        }, {

        })
    }

    /**
     * 获取财经日历详情
     */
    fun finCalendarDetail() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "dataId" to dataId,
                "timeZone" to AppUtil.getTimeZoneRawOffsetToHour(),
            )
            if (UserDataUtil.isLogin()) {
                map["userToken"] = UserDataUtil.loginToken()
            }
            baseService.finCalendarDetail(map)
        }, onSuccess = { dataBean ->
            if (!dataBean.isSuccess()) {
                ToastUtil.showToast(dataBean.getResponseMsg())
                return@requestNet
            }
            val objBean = dataBean.data?.obj
            isRemind = objBean?.isRemind.ifNull()
            detailLiveData.value = objBean
        }, isShowDialog = true)
    }

    /**
     * 财经日历图表数据
     */
    fun finCalendarChartData() {
        requestNet({
            val map = hashMapOf<String, Any?>(
                "dataId" to dataId,
                "timeZone" to AppUtil.getTimeZoneRawOffsetToHour(),
            )
            baseService.finCalendarChartData(map)
        }, onSuccess = { dataBean ->
            if (!dataBean.isSuccess()) {
                return@requestNet
            }
            val chartList = dataBean.data?.obj.orEmpty()
            if (chartList.size < 2) return@requestNet
            chartLiveData.value = chartList
        })
    }
}