package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.*
import android.text.*
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView.OnEditorActionListener
import androidx.appcompat.widget.AppCompatEditText
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityEditPersonalInfoBinding
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog

class EditPersonalInfoActivity : BaseMvvmBindingActivity<ActivityEditPersonalInfoBinding>() {

    private val title: String by lazy { intent.getStringExtra(KEY_TITLE) ?: getString(R.string.edit_bio) }
    private val hint: String? by lazy { intent.getStringExtra(KEY_HINT) }
    private val isNeedCheck: Boolean by lazy { intent.getBooleanExtra(KEY_IS_NEED_CHECK, false) }
    private val oldPersonalInfo: String? by lazy { intent.getStringExtra(KEY_DESCRIPTION) }

    override fun initView() {
        mBinding.mHeaderBar.setTitleText(title)
        mBinding.tvCount.text = buildString {
            append(oldPersonalInfo?.length ?: 0)
            append("/2000")
        }
        hint?.let {
            mBinding.etPersonalInfo.setHint(it)
        }
        mBinding.etPersonalInfo.setText(oldPersonalInfo)
    }

    override fun initListener() {
        mBinding.mHeaderBar.setStartBackIconClickListener {
            val newPersonalInfo = mBinding.etPersonalInfo.text?.trim().toString()
            if (!TextUtils.isEmpty(newPersonalInfo) && newPersonalInfo != oldPersonalInfo) {
                showStayDialog()
            } else {
                finish()
            }
        }
        mBinding.tvSubmit.setOnClickListener(this)
        mBinding.etPersonalInfo.requestFocus()
        mBinding.etPersonalInfo.setOnEditorActionListener(OnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE) { //imeOptions设置的动作
                //释放焦点并隐藏软键盘
                KeyboardUtil.hideSoftInput(this)
                //去自定义下一步逻辑...
                return@OnEditorActionListener true
            }
            false
        })

        mBinding.etPersonalInfo.addTextChangedListener(object : TextWatcher {
            var currentLength = 0

            @SuppressLint("SetTextI18n")
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                mBinding.tvCount.text = "$currentLength/2000"
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                val editContent = mBinding.etPersonalInfo.text?.trim()
                if (isNeedCheck && TextUtils.isEmpty(editContent)) {
                    mBinding.tvSubmit.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
                } else {
                    mBinding.tvSubmit.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                }

                if ((editContent?.length ?: 0) <= 2000) {
                    currentLength = editContent?.length ?: 0
                    mBinding.etPersonalInfo.isEnabled = true
                } else {
                    currentLength = 2000
                    mBinding.etPersonalInfo.isEnabled = false
                }
            }

            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(s: Editable) {
                mBinding.tvCount.text = "$currentLength/2000"
            }
        })
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mBinding.root.requestFocus()
                removeFocusBg(mBinding.etPersonalInfo, true)
            }
        }
        mBinding.root.viewTreeObserver.addOnGlobalFocusChangeListener { oldFocus, newFocus ->
            removeFocusBg(mBinding.etPersonalInfo)
            when (newFocus) {
                mBinding.etPersonalInfo -> addFocusBg(mBinding.etPersonalInfo)
            }
        }

    }

    private fun addFocusBg(et: AppCompatEditText) {
        // 键盘可见
//        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_stroke_ca63d3d3d_c99ffffff_r10)
    }

    private fun removeFocusBg(et: AppCompatEditText, isClear: Boolean = false) {
//        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_stroke_c1f3d3d3d_c1fffffff_r10)
        if (isClear)
            et.clearFocus()
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvSubmit -> {
                updatePersonalInfo()
            }
        }
    }

    /**
     * 弹出挽留dialog
     */
    private fun showStayDialog() {
        CenterActionDialog.Builder(this)
            .setTitle(getString(R.string.leave_this_page) + "?")
            .setContent(getString(R.string.changes_you_made_be_saved))
            .setStartText(getString(R.string.stay))
            .setEndText(getString(R.string.leave))
            .setOnEndListener {
                finish()
            }.build().showDialog()
    }

    /**
     * 返回上一级 刷新信息，请求接口
     */
    private fun updatePersonalInfo() {
        val intent = Intent()
        intent.putExtra(KEY_DESCRIPTION, mBinding.etPersonalInfo.text?.trim().toString())
        setResult(Constants.REQUEST_CODE_PERSONAL_INFO, intent)
        finish()
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.onTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {

        private const val KEY_TITLE = "title"
        private const val KEY_HINT = "hint"
        const val KEY_DESCRIPTION = "description"
        const val KEY_IS_NEED_CHECK = "isNeedCheck"

        fun createIntent(context: Context, title: String? = null, hint: String? = null, isNeedCheck: Boolean = false, description: String? = null) =
            Intent(context, EditPersonalInfoActivity::class.java).apply {
                putExtra(KEY_TITLE, title)
                putExtra(KEY_HINT, hint)
                putExtra(KEY_DESCRIPTION, description)
                putExtra(KEY_IS_NEED_CHECK, isNeedCheck)
            }
    }
}

