package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.StringBean
import cn.com.vau.data.init.StShareStrategyData
import cn.com.vau.data.strategy.StStrategyCopyLoadData
import cn.com.vau.data.strategy.StStrategyCopySubmitData
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

class StStrategyCopyViewModel : BaseViewModel() {

    private val repository by lazy { StStrategyCopyRepository() }

    var selfLeverage: String? = null        // 用户杠杆比例
    var strategyLeverage: String? = null    // 策略的杠杆比例
    var profitShieldStrategy: Boolean = false   // 是否是GS活动策略

    var currentMode = MutableLiveData<String>()
    var stoplossValue = MutableLiveData<String>()
    var takeProfitValue = MutableLiveData<String>()
    var takeProfitEnable = MutableLiveData<Boolean>()
    var strategyCopyLoadLiveData = MutableLiveData<StStrategyCopyLoadData>()
    var strategyCopySubmitLiveData = MutableLiveData<StStrategyCopySubmitData>()
    var strategySettingSubmitLiveData = MutableLiveData<StringBean>()
    var strategyCopySettingLiveData = MutableLiveData<StStrategyCopyLoadData>()

    var shareStrategyData: StShareStrategyData? = null
    var strategyOrderData: StrategyOrderBaseData? = null

    fun initShareFollowStrategyData() {
        shareStrategyData = VAUSdkUtil.stShareStrategyList().firstOrNull {
            strategyOrderData?.signalStrategyId == it.strategyId
        }
    }

    fun setCopyMode(mode: String) {
        currentMode.value = mode
    }

    fun setSL(sl: String) {
        stoplossValue.value = sl
    }

    fun setTP(tp: String) {
        takeProfitValue.value = tp
    }

    fun setTakeProfitEnabl(enabled: Boolean) {
        takeProfitEnable.value = enabled
    }

    fun strategyCopySubmitLoad(strategyId: String, accountId: String) {
        repository.strategyCopySubmitLoad(strategyId, accountId)
            .compose(applyLoading(true))
            .bindTLiveData(strategyCopyLoadLiveData)
    }

    fun strategyCopySubmit(param: HashMap<String, String>) {
        repository.strategyCopySubmit(param)
            .compose(applyLoading(true))
            .bindTLiveData(strategyCopySubmitLiveData)
    }

    fun strategyCopySettings(type: String, portfolioId: String) {
        repository.strategyCopySettings(type, portfolioId)
            .compose(applyLoading(true))
            .bindTLiveData(strategyCopySettingLiveData)
    }

    fun strategyCopyUpdate(param: HashMap<String, String>) {
        repository.strategyCopyUpdate(param)
            .compose(applyLoading(true))
            .bindTLiveData(strategySettingSubmitLiveData)
    }

    fun sensorsCopyTradingSubmitClick() {
        val bean = strategyCopyLoadLiveData.value?.data ?: return
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TARGET_NAME, bean.strategyName.ifNull())
        properties.put(SensorsConstant.Key.STRATEGY_ID, bean.strategyId.ifNull())
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGSUBMIT_CLICK, properties)
    }

    fun sensorsCopyTradingEventSubmitClick(isEndClick: Boolean) {
        val bean = strategyCopyLoadLiveData.value?.data ?: return
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TARGET_NAME, bean.strategyName.ifNull())
        properties.put(SensorsConstant.Key.STRATEGY_ID, bean.strategyId.ifNull())
        properties.put(SensorsConstant.Key.BUTTON_NAME, if (isEndClick) "Submit" else "Cancel")
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGEVENT_SUBMITCLICK, properties)
    }

    fun sensorsCopyTradingEventSubmitResult(isSuccess: Boolean) {
        val bean = strategyCopyLoadLiveData.value?.data ?: return
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.SUBMIT_RESULT, if (isSuccess) "success" else "failure")
        properties.put(SensorsConstant.Key.TARGET_NAME, bean.strategyName.ifNull())
        properties.put(SensorsConstant.Key.STRATEGY_ID, bean.strategyId.ifNull())
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGEVENT_SUBMITRESULT, properties)
    }
}