package cn.com.vau.signals.stsignal.adapter

import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.expandabletextview.ExpandableTextView
import cn.com.vau.common.view.expandabletextview.app.StatusType
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * author：lvy
 * date：2024/03/26
 * desc：信号源详情->策略列表
 */
class StSignalStrategyListAdapter :
    BaseQuickAdapter<StrategyBean, BaseViewHolder>(R.layout.item_st_signal_strategy_list) {

    override fun convert(holder: BaseViewHolder, item: StrategyBean) {
        //策略头像
        val ivAvatar = holder.getView<ShapeableImageView>(R.id.ivAvatar)
        ImageLoaderUtil.loadImage(context, item.avatar, ivAvatar, R.mipmap.ic_launcher)

        //策略名称
        holder.setText(R.id.tvNick, item.strategyName)

        /*
        策略ID或跟单者状态指示文案。
        对于跟单者，展示 ‘Copying since dd/mm/yyyy’；
        对于待审核的跟单者，展示’Applied on dd/mm/yyyy’；
        对于未跟单者，展示’ID：策略ID’
         */
        val statusStr = if (UserDataUtil.isStLogin()) {
            if (item.pendingApplyApproval) { //待审核
                context.getString(
                    R.string.applied_on_x,
                    TimeUtil.millis2String(item.appliedDate?.toLongOrNull() ?: 0L, "dd/MM/yyyy")
                )
            } else { //审核通过或拒绝等其他状态
                if (item.followerStatus == true) { //已跟单
                    context.getString(
                        R.string.copying_since_x,
                        TimeUtil.millis2String(item.copyDate?.toLongOrNull() ?: 0L, "dd/MM/yyyy")
                    )
                } else { //未跟单
                    "${context.getString(R.string.strategy_id)}：${item.strategyNo}".arabicReverseTextByFlag("：")
                }
            }
        } else {
            "${context.getString(R.string.strategy_id)}：${item.strategyNo}".arabicReverseTextByFlag("：")
        }
        holder.setText(R.id.tvStrategyId, statusStr)

        /*
        动作按钮。
        对于已经跟单的用户，展示‘Manage’；
        对于未跟单用户、未登录、Live和Demo账户用户，展示‘View’
         */
        val action = if (UserDataUtil.isStLogin()) {
            if (item.followerStatus == true || item.pendingApplyApproval) {
                context.getString(R.string.manage)
            } else {
                context.getString(R.string.view)
            }
        } else {
            context.getString(R.string.view)
        }
        holder.setText(R.id.tvAction, action)

        //策略介绍
        val tvIntro = holder.getView<ExpandableTextView>(R.id.tvIntro)
        if (!item.comments.isNullOrBlank()) {
            tvIntro.isVisible = true
            tvIntro.setContent(item.comments)
            tvIntro.setExpandOrContractClickListener({
                if (it == StatusType.STATUS_EXPAND) {
                    viewMoreClickListener.invoke(holder.layoutPosition, item.comments.ifNull())
                }
            }, false)
        } else {
            tvIntro.isVisible = false
        }

        //三个月回报率
        val returnRate = (item.returnRate ?: "0").percent()
        holder.setText(R.id.tvRoi, "$returnRate%")
            .setTextColor(R.id.tvRoi, getReturnRateColor(returnRate.toDoubleCatching()))

        //跟单人数
        holder.setText(R.id.tvCopiers, "${item.copiers}")

        //风险度
        holder.setText(R.id.tvRisk, "${item.riskLevel}")
            .setTextColorRes(
                R.id.tvRisk, when (item.riskLevel) {
                    "4", "5", "6" -> R.color.cff8e5c
                    "7", "8", "9", "10" -> R.color.ce35728
                    else -> R.color.c00c79c
                }
            )

        /*
        分润比例。
        对于已经跟单的跟单者，展示跟单者适用的分润比例；
        对于未跟单用户，展示该策略最新的分润比例
         */
        val profitRatio = item.profitShareRatio.ifNull().percent(0)
        holder.setText(R.id.tvProfitSharing, "$profitRatio%")
    }

    private fun getReturnRateColor(value: Double) = when {
        value > 0 -> ContextCompat.getColor(context, R.color.c00c79c)
        value == 0.0 -> AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        else -> ContextCompat.getColor(context, R.color.ce35728)
    }

    /**
     * 简介viewMore点击回调
     */
    private var viewMoreClickListener: (position: Int, intro: String) -> Unit = { _, _ -> }
    fun viewMoreClickListener(e: (position: Int, intro: String) -> Unit) {
        this.viewMoreClickListener = e
    }
}