package cn.com.vau.signals.activity

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityDebugH5Binding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat

class H5DebugActivity : BaseMvvmBindingActivity<ActivityDebugH5Binding>() {

    override fun initView() {
        mBinding.tvNext.clickNoRepeat {
            val h5Url = mBinding.etH5Url.text.trim().toString()
            if (TextUtils.isEmpty(h5Url)) {
                ToastUtil.showToast(getString(R.string.enter_text))
                return@clickNoRepeat
            }

            NewHtmlActivity.openActivity(this, url = h5Url)
        }
    }

    companion object {

        fun openActivity(context: Context) {
            context.startActivity(Intent(context, H5DebugActivity::class.java))
        }
    }
}