package cn.com.vau.signals.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityFiltersBinding
import cn.com.vau.signals.adapter.FiltersCountryAdapter
import cn.com.vau.signals.viewModel.FiltersViewModel
import cn.com.vau.util.*

/**
 * 筛选
 * Created by zhy on 2018/10/31.
 */
class FiltersActivity : BaseMvvmActivity<ActivityFiltersBinding, FiltersViewModel>() {

    private val filtersCountryAdapter: FiltersCountryAdapter by lazy { FiltersCountryAdapter() }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.selectedCountryLivedata.value = intent.getStringExtra(KEY_AREACODE).ifNull("all")
        mViewModel.selectedPriorityLivedata.value = intent.getStringExtra(KEY_IMPORTANCE).ifNull("all")
    }

    override fun initView() {
        filtersCountryAdapter.areaCode = mViewModel.selectedCountryLivedata.value
        mBinding.mRecyclerViewCountry.adapter = filtersCountryAdapter
    }

    private fun resetPriorityButton() {
        mBinding.tvSelectAll.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        mBinding.tvSelectAll.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
        mBinding.tvHigh.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        mBinding.tvHigh.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
        mBinding.tvMedium.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        mBinding.tvMedium.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
        mBinding.tvLow.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
        mBinding.tvLow.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
    }

    private fun selectPriorityButton(view: AppCompatTextView) {
        view.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e));
        view.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100);
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvSelectAll.setOnClickListener {
            mViewModel.selectedPriorityLivedata.value = "all"
        }
        mBinding.tvHigh.setOnClickListener {
            mViewModel.selectedPriorityLivedata.value = "high"
        }
        mBinding.tvMedium.setOnClickListener {
            mViewModel.selectedPriorityLivedata.value = "medium"
        }
        mBinding.tvLow.setOnClickListener {
            mViewModel.selectedPriorityLivedata.value = "low"
        }
        mBinding.tvNext.clickNoRepeat {
            setResult(RESULT_OK, Intent().apply {
                putExtra(KEY_AREACODE, mViewModel.selectedCountryLivedata.value)
                putExtra(KEY_IMPORTANCE, mViewModel.selectedPriorityLivedata.value)
            })
            finish()
        }
        filtersCountryAdapter.setNbOnItemClickListener { _, _, position ->
            mViewModel.selectedCountryLivedata.value = filtersCountryAdapter.getItemOrNull(position)?.areaCode
            val index = filtersCountryAdapter.data.indexOfFirst { it.areaCode == filtersCountryAdapter.areaCode }
            filtersCountryAdapter.areaCode = filtersCountryAdapter.getItemOrNull(position)?.areaCode
            if (index != -1)
                filtersCountryAdapter.notifyItemChanged(index)
            filtersCountryAdapter.notifyItemChanged(position)
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.selectedPriorityLivedata.observe(this) {
            resetPriorityButton()
            when (it) {
                "all" -> {
                    selectPriorityButton(mBinding.tvSelectAll)
                }

                "high" -> {
                    selectPriorityButton(mBinding.tvHigh)
                }

                "medium" -> {
                    selectPriorityButton(mBinding.tvMedium)
                }

                "low" -> {
                    selectPriorityButton(mBinding.tvLow)
                }
            }
        }
        mViewModel.uiListLiveData.observeUIState(this, filtersCountryAdapter)
    }

    companion object {

        const val KEY_AREACODE = "areaCode"
        const val KEY_IMPORTANCE = "importance"

        fun createIntent(context: Context, areaCode: String?, importance: String?): Intent {
            val intent = Intent(context, FiltersActivity::class.java)
            intent.putExtra(KEY_AREACODE, areaCode)
            intent.putExtra(KEY_IMPORTANCE, importance)
            return intent
        }
    }
}