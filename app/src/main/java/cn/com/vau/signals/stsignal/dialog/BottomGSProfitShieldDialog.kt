package cn.com.vau.signals.stsignal.dialog

import android.app.Activity
import android.content.Context
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import cn.com.vau.R
import cn.com.vau.databinding.LayoutGsProfitShieldTipBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.buidler.ActionBuilder

class BottomGSProfitShieldDialog private constructor(
    private val context: Context
) : BottomDialog<LayoutGsProfitShieldTipBinding>(context, LayoutGsProfitShieldTipBinding::inflate) {

    override fun setContentView() {
        super.setContentView()
        mContentBinding.tvTC.text = buildSpannedString {
            val start = length
            append(context.getString(R.string.gs_terms_and_conditions))
            val end = length
            setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    // 点击事件处理逻辑
                    NewHtmlActivity.openActivity(
                        context,
                        url = "https://webh5.vantagemarketapp.com/app-web/202505-copy-trade-growth-shield-tc-tnc/",
                        title = context.getString(R.string.copy_trade_growth_and_conditions)
                    )
                    dismiss()
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = true
                    ds.color = ContextCompat.getColor(context, R.color.ce35728)
                }
            }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            mContentBinding.tvTC.movementMethod = LinkMovementMethod.getInstance()
            mContentBinding.tvTC.highlightColor = ContextCompat.getColor(context, R.color.transparent)

            append(" ${context.getString(R.string.apply)}.")
        }
    }

    class Builder(activity: Activity) : ActionBuilder<LayoutGsProfitShieldTipBinding>(activity) {

        override fun createDialog(context: Context): BottomDialog<LayoutGsProfitShieldTipBinding> {
            return BottomGSProfitShieldDialog(context)
        }

        override fun build(): BottomGSProfitShieldDialog {
            return super.build() as BottomGSProfitShieldDialog
        }

    }
}