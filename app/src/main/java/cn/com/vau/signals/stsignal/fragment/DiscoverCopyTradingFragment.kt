package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.text.buildSpannedString
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.CustomViewPagerOnPageChangeListener
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.discover.StrategyMostCopied
import cn.com.vau.data.discover.StrategyRecommendAllData
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.databinding.FootRecyclerViewmoreBinding
import cn.com.vau.databinding.FragmentDiscoverCopyTradingBinding
import cn.com.vau.databinding.VsLayoutDiscoverCopyTradingBannerBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.dialog.BottomGSProfitShieldDialog
import cn.com.vau.signals.stsignal.viewmodel.DiscoverCopyTradingViewModel
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.trade.st.adapter.StHighestReturnAdapter
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathMul
import cn.com.vau.util.noRepeat
import cn.com.vau.util.numFormat2
import cn.com.vau.util.setVisibleWithAlphaAnim
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.bumptech.glide.request.RequestOptions
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class DiscoverCopyTradingFragment : BaseMvvmBindingFragment<FragmentDiscoverCopyTradingBinding>(), View.OnClickListener {

    private val mViewModel: DiscoverCopyTradingViewModel by viewModels(ownerProducer = { requireParentFragment() })
    private val tipsAdapter by lazy { PlatAdapter() }

    private val bottomTipPopup: BottomListDialog by lazy { BottomListDialog.Builder(requireActivity()).setAdapter(tipsAdapter).build() }

    private var vsBannerBinding: VsLayoutDiscoverCopyTradingBannerBinding? = null

    private val profitShieldFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.profitShieldRecyclerView, false)
    }
    private val mostCopiedFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.mostCopiedRecyclerView, false)
    }
    private val highReturnFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.highestReturnRecyclerView, false)
    }
    private val lowReturnFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.lowRiskReturnRecyclerView, false)
    }
    private val highRateFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.highWinRateRecyclerView, false)
    }
    private val profitShieldAdapter: StHighestReturnAdapter by lazy {
        StHighestReturnAdapter().apply {
            addFooterView(view = profitShieldFooter.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val mostCopiedAdapter: StHighestReturnAdapter by lazy {
        StHighestReturnAdapter().apply {
            addFooterView(view = mostCopiedFooter.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val highReturnAdapter: StHighestReturnAdapter by lazy {
        StHighestReturnAdapter().apply {
            addFooterView(view = highReturnFooter.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val lowReturnAdapter: StHighestReturnAdapter by lazy {
        StHighestReturnAdapter().apply {
            addFooterView(view = lowReturnFooter.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val highWinRateAdapter: StHighestReturnAdapter by lazy {
        StHighestReturnAdapter().apply {
            addFooterView(view = highRateFooter.root, orientation = LinearLayout.HORIZONTAL)
        }
    }

    override fun initView() {
        mBinding.mRefreshLayout.setOnRefreshListener {  // 下拉刷新 (刷新多策略接口及显示,广告位接口)
            request()
        }
        mBinding.mVsBanner.setOnInflateListener { _, inflated ->
            vsBannerBinding = VsLayoutDiscoverCopyTradingBannerBinding.bind(inflated)
            vsBannerBinding?.mBanner?.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
                override fun onBindView(holder: BannerImageHolder?, data: String?, position: Int, size: Int) {
                    if (isDestroyed()) return
                    val options = RequestOptions()
                        .placeholder(R.drawable.shape_placeholder)
                        .error(R.drawable.shape_placeholder)
                    ImageLoaderUtil.loadImageWithOption(requireContext(), data, holder?.imageView, options)
                }
            })?.setScrollTime(1000)
                ?.addBannerLifecycleObserver(this)

            vsBannerBinding?.mBanner?.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
                override fun onPageSelected(position: Int) {
                    vsBannerBinding?.indicator?.changeIndicator(position)
                }
            })
            vsBannerBinding?.mBanner?.setOnBannerListener { _, bannerPosition ->
                val bannerBean = mViewModel.advertImgLiveData.value?.eventsList?.elementAtOrNull(bannerPosition)
                val pushBean = bannerBean?.appJumpDefModel
                VAUStartUtil.openActivity(requireActivity(), pushBean)

                // 神策自定义埋点(v3500)
                sensorsTrack(bannerBean?.eventId.ifNull(), bannerPosition, pushBean?.urls?.def.ifNull())
            }
            vsBannerBinding?.ivBannerClose?.setOnClickListener(this)
        }

        mBinding.mVsNoDataScroll.setOnInflateListener { stub, inflated ->
            val vs = VsLayoutNoDataScrollBinding.bind(inflated)
            vs.mNoDataScrollView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mVsNoDataScroll.isVisible = true
        // Profit Shield
        mBinding.layoutCopyTrading.profitShieldRecyclerView.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.profitShieldRecyclerView.adapter = profitShieldAdapter

        // Most Copied
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.adapter = mostCopiedAdapter

        // Highest Annual Return
        mBinding.layoutCopyTrading.highestReturnRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.highestReturnRecyclerView.adapter = highReturnAdapter

        // Low Risk and Stable Return
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.adapter = lowReturnAdapter

        // High Win Rate
        mBinding.layoutCopyTrading.highWinRateRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.highWinRateRecyclerView.adapter = highWinRateAdapter
    }

    override fun createObserver() {
        super.createObserver()
        // 策略推荐接口返回 New
        mViewModel.strategyRecommendLiveData.observe(this) {

            lifecycleScope.launch(Dispatchers.Default) {
                it?.profitShield?.forEach { item ->
                    item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                    item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                }
                it?.mostCopied?.forEach { item ->
                    item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                    item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                }
                it?.highestReturn?.forEach { item ->
                    item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                    item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                }
                it?.lowRisk?.forEach { item ->
                    item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                    item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                }
                it?.highestWinRate?.forEach { item ->
                    item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                    item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                }

                MainScope().launch {
                    // 空数据页面即将去掉，开始展示banner的逻辑
                    showOperationBanner(mViewModel.bannerLivaData.value)
                    showStrategyRecommend(it)
                    mBinding.mVsNoDataScroll.visibility = View.GONE
                }
            }
        }
        // 广告位接口返回
        mViewModel.bannerLivaData.observe(this) {
            // 如果空数据页面没有展示 ，说明推荐策略列表已经展示了，所以开始banner展示的逻辑
            if (mBinding.mVsNoDataScroll.isGone)
                showOperationBanner(it)
        }
    }

    override fun initListener() {
        mBinding.layoutCopyTrading.tvProfitShield.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivProfitShield.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivArrowProfitShield.setOnClickListener(this)
        mBinding.layoutCopyTrading.tvMostCopied.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivMostCopied.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivArrowMostCopied.setOnClickListener(this)
        mBinding.layoutCopyTrading.tvHighestReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivHighestReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivArrowHighestReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.tvLowRiskReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivLowRiskReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivArrowLowRiskReturn.setOnClickListener(this)
        mBinding.layoutCopyTrading.tvHighWinRate.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivHighWinRate.setOnClickListener(this)
        mBinding.layoutCopyTrading.ivArrowHighWinRate.setOnClickListener(this)
        profitShieldFooter.root.setOnClickListener {
            gotoHtml()
        }
        mostCopiedFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_MOST_COPIED)
        }
        highReturnFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_HIGHEST_RETURN)
        }
        lowReturnFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_LOW_RISK_RETURN)
        }
        highRateFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_HIGH_WIN_RATE)
        }
        profitShieldAdapter.setOnItemChildClickListener { _, view, position ->
            val strategy = profitShieldAdapter.getItem(position)
            handleStrategySubView(-1, strategy, position)
        }
        mostCopiedAdapter.setOnItemChildClickListener { _, view, position ->
            val strategy = mostCopiedAdapter.getItem(position)
            handleStrategySubView(0, strategy, position)
        }
        highReturnAdapter.setOnItemChildClickListener { _, _, position ->
            val strategy = highReturnAdapter.getItem(position)
            handleStrategySubView(1, strategy, position)
        }
        lowReturnAdapter.setOnItemChildClickListener { _, _, position ->
            val strategy = lowReturnAdapter.getItem(position)
            handleStrategySubView(2, strategy, position)
        }
        highWinRateAdapter.setOnItemChildClickListener { _, _, position ->
            val strategy = highWinRateAdapter.getItem(position)
            handleStrategySubView(3, strategy, position)
        }
        profitShieldAdapter.setOnItemClickListener { _, _, position ->
            val strategy = profitShieldAdapter.getItem(position)
            gotoStrategyDetail(-1, strategy, position)
        }
        mostCopiedAdapter.setOnItemClickListener { _, _, position ->
            val strategy = mostCopiedAdapter.getItem(position)
            gotoStrategyDetail(0, strategy, position)
        }
        highReturnAdapter.setOnItemClickListener { _, _, position ->
            val strategy = highReturnAdapter.getItem(position)
            gotoStrategyDetail(1, strategy, position)
        }
        lowReturnAdapter.setOnItemClickListener { _, _, position ->
            val strategy = lowReturnAdapter.getItem(position)
            gotoStrategyDetail(2, strategy, position)
        }
        highWinRateAdapter.setOnItemClickListener { _, _, position ->
            val strategy = highWinRateAdapter.getItem(position)
            gotoStrategyDetail(3, strategy, position)
        }
        // 事件处理
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                when (it) {
                    Constants.EVENT_REFRESHLAYOUT_FINISH -> {
                        if (isDestroyed() || activity?.isDestroyed == true) return@collectLatest   // 判断Fragment或Activity已被销毁
                        mBinding.mRefreshLayout.finishRefresh()
                    }
                }
            }
        }
    }

    private fun request() {
        // 多策略推荐接口 New
        mViewModel.strategyDiscoverListAll()
        // 请求广告位接口
        mViewModel.mainEventImgAdvertInfo()
    }

    private fun gotoHtml() {
        context?.let {
            NewHtmlActivity.openActivity(
                it, url = UrlConstants.HTML_GS_AGGREGATION,
                title = "Copy Trade Growth Shield",
                dataMap = mapOf(
                    "accountId" to UserDataUtil.accountCd(),
                    "crmUserId" to SpManager.getCrmUserId()
                )
            )
        }
    }

    // 广告位
    private fun showOperationBanner(bannerUrlList: List<String?>?) {
        if (bannerUrlList?.isNotEmpty() == true) {
            mBinding.mVsBanner.isVisible = true
            vsBannerBinding?.ivBannerClose?.isVisible = true
            vsBannerBinding?.indicator?.isVisible = bannerUrlList.size > 1
            vsBannerBinding?.indicator?.initIndicatorCount(bannerUrlList.size)
            vsBannerBinding?.mBanner.setVisibleWithAlphaAnim(duration = 100)
            vsBannerBinding?.mBanner?.setDatas(bannerUrlList)
            vsBannerBinding?.mBanner?.start()
        } else {
            hideOperationBanner()
        }
    }

    private fun hideOperationBanner() {
        vsBannerBinding?.mBanner?.stop()
        mBinding.mVsBanner.isVisible = false
    }

    private fun showStrategyRecommend(data: StrategyRecommendAllData?) {
        if (isDestroyed()) return   // 判断Fragment或Activity已被销毁
        mBinding.mRefreshLayout.finishRefresh()
        mBinding.layoutCopyTrading.root.isVisible = data != null

        // Profit Shield
        val profitShieldAvailable = data?.profitShield?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.ivProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.ivArrowProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.tvProfitShieldTip.isVisible = profitShieldAvailable
        if (profitShieldAvailable) {
            mBinding.layoutCopyTrading.tvProfitShieldTip.text = getProfitShieldTip()
        }
        mBinding.layoutCopyTrading.profitShieldRecyclerView.isVisible = profitShieldAvailable
        if (profitShieldAvailable) {
            profitShieldAdapter.setShowShieldIcon(true)
            profitShieldAdapter.setList(data?.profitShield ?: emptyList())
        }

        // Most Copied
        val mostCopiedAvailable = data?.mostCopied?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.ivMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.ivArrowMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.isVisible = mostCopiedAvailable
        if (mostCopiedAvailable) {
            mBinding.layoutCopyTrading.mostCopiedRecyclerView.setVisibleWithAlphaAnim()
            mostCopiedAdapter.setList(data.mostCopied ?: emptyList())
        }

        // Highest Annual Return
        val highReturnAvailable = data?.highestReturn?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.ivHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.ivArrowHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.highestReturnRecyclerView.isVisible = highReturnAvailable
        if (highReturnAvailable) {
            mBinding.layoutCopyTrading.highestReturnRecyclerView.setVisibleWithAlphaAnim()
            highReturnAdapter.setList(data.highestReturn ?: emptyList())
        }

        // Low Risk and Stable Return
        val lowRiskAvailable = data?.lowRisk?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.ivLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.ivArrowLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.isVisible = lowRiskAvailable

        if (lowRiskAvailable) {
            mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.setVisibleWithAlphaAnim()
            lowReturnAdapter.setList(data.lowRisk ?: emptyList())
        }

        // High Win Rate
        val highWinAvailable = data?.highestWinRate?.isNotEmpty() == true
        mBinding.layoutCopyTrading.groupHighWinRate.isVisible = highWinAvailable
        mBinding.layoutCopyTrading.tvHighWinRate.isVisible = highWinAvailable
        mBinding.layoutCopyTrading.ivHighWinRate.isVisible = highWinAvailable
        mBinding.layoutCopyTrading.ivArrowHighWinRate.isVisible = highWinAvailable
        mBinding.layoutCopyTrading.highWinRateRecyclerView.isVisible = highWinAvailable

        if (highWinAvailable) {
            mBinding.layoutCopyTrading.highWinRateRecyclerView.setVisibleWithAlphaAnim()
            highWinRateAdapter.setList(data.highestWinRate ?: emptyList())
        }
    }

    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.ivBannerClose -> {
                // 关闭广告位
                mViewModel.mainEventImgClose(getEventIdList())
                hideOperationBanner()
            }

            R.id.tvProfitShield, R.id.ivArrowProfitShield -> {
                gotoHtml()
            }

            R.id.tvMostCopied, R.id.ivArrowMostCopied -> {          // Most Copied
                gotoDiscoverWithCondition(Constants.STRATEGY_MOST_COPIED)
            }

            R.id.tvHighestReturn, R.id.ivArrowHighestReturn -> {    // Highest Annual Return
                gotoDiscoverWithCondition(Constants.STRATEGY_HIGHEST_RETURN)
            }

            R.id.tvLowRiskReturn, R.id.ivArrowLowRiskReturn -> {    // Low Risk and Stable Return
                gotoDiscoverWithCondition(Constants.STRATEGY_LOW_RISK_RETURN)
            }

            R.id.tvHighWinRate, R.id.ivArrowHighWinRate -> {        // High Win Rate
                gotoDiscoverWithCondition(Constants.STRATEGY_HIGH_WIN_RATE)
            }

            R.id.ivProfitShield -> BottomGSProfitShieldDialog.Builder(requireActivity()).build().showDialog()
            R.id.ivMostCopied -> bottomTips(Constants.STRATEGY_MOST_COPIED)
            R.id.ivHighestReturn -> bottomTips(Constants.STRATEGY_HIGHEST_RETURN)
            R.id.ivLowRiskReturn -> bottomTips(Constants.STRATEGY_LOW_RISK_RETURN)
            R.id.ivHighWinRate -> bottomTips(Constants.STRATEGY_HIGH_WIN_RATE)
        }
    }

    private fun gotoDiscoverWithCondition(condition: String) {
        noRepeat {
            // 跳转Discover页 Community
            EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY, condition))
            LogEventUtil.setLogEvent(
                BuryPointConstant.V348.CT_DISCOVER_VIEW_MORE_BTN_CLICK, bundleOf(
                    "Category" to when (condition) {
                        Constants.STRATEGY_MOST_COPIED -> "Most_copied"
                        Constants.STRATEGY_HIGHEST_RETURN -> "Highest_annual_return"
                        Constants.STRATEGY_LOW_RISK_RETURN -> "Low_risk_stable_return"
                        else -> "High_win_rate"
                    }
                )
            )
        }
    }

    private fun gotoStrategyDetail(typeForm: Int = -1, data: StrategyMostCopied?, position: Int) {
        data?.let {
            StStrategyDetailsActivity.open(requireContext(), it.strategyId.ifNull())
            LogEventUtil.setLogEvent(
                BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                    "Type_of_account" to if (UserDataUtil.isLiveAccount()) "Live" else "Demo",
                    "Position" to "Discover",
                    "Category" to when (typeForm) {
                        0 -> "Most_copied"
                        1 -> "Highest_annual_return"
                        2 -> "Low_risk_stable_return"
                        3 -> "High_win_rate"
                        else -> ""
                    },
                    "Strategy_ID" to it.strategyId.ifNull()
                )
            )
            // 神策自定义埋点(v3500)
            sensorsTrack(data.strategyId.ifNull(), position, "")
            // 进策略详情页埋点
            mViewModel.sensorsCopyTradingStrategyClick(data, typeForm)
        }
    }

    private fun manageToOrder(data: StrategyMostCopied?) {
        if (data?.pendingApplyApproval == true) { //审核通过，跳转策略订单详情页 默认 tab
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.PENDING_REVIEW
                    this.signalStrategyId = data.strategyId
                    this.portfolioId = data.portfolioId
                    this.followRequestId = data.followRequestId
                })
            })
        } else { //待审核，点击跳转策略订单详情页 setting tab
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.OPEN
                    this.signalStrategyId = data?.strategyId
                    this.portfolioId = data?.portfolioId
                    this.followRequestId = data?.followRequestId
                })
            })
        }
    }

    private fun bottomTips(type: String) {
        val tips = arrayListOf<HintLocalData>()
        LogUtil.w("type: $type")
        when (type) {
            Constants.STRATEGY_MOST_COPIED -> {
                tips.add(HintLocalData(getString(R.string.most_copied), getString(R.string.strategies_with_the_accumulated_copiers)))
            }

            Constants.STRATEGY_HIGHEST_RETURN -> {
                tips.add(HintLocalData(getString(R.string.highest_annual_return), getString(R.string.strategies_with_the_12_months)))
            }

            Constants.STRATEGY_LOW_RISK_RETURN -> {
                tips.add(HintLocalData(getString(R.string.low_risk_and_stable_return), getString(R.string.strategies_with_risk_than_50_percent)))
                tips.add(HintLocalData(getString(R.string.risk_band), getString(R.string.the_risk_band_the_the_here_date_status)))
            }

            Constants.STRATEGY_HIGH_WIN_RATE -> {
                tips.add(HintLocalData(getString(R.string.high_win_rate), getString(R.string.strategies_with_win_3_months)))
                tips.add(HintLocalData(getString(R.string.win_rate), getString(R.string.the_percentage_of_profitable_orders)))
            }

            Constants.STRATEGY_TOP_PROVIDERS -> {
                tips.add(HintLocalData(getString(R.string.top_signal_providers), getString(R.string.the_signal_providers_cumulative_copiers)))
            }
        }
        tipsAdapter.setList(tips)
        bottomTipPopup.show()
    }

    private fun handleStrategySubView(typeForm: Int, strategy: StrategyMostCopied?, position: Int) {
        if (UserDataUtil.isStLogin()) {
            if (strategy != null) {
                if (strategy.followerStatus == true || strategy.pendingApplyApproval == true) {
                    // Manage 根据审核状态跳转相应页面
                    manageToOrder(strategy)
                } else {
                    // View 去策略详情
                    gotoStrategyDetail(typeForm, strategy, position)
                }
            }
        } else {
            // 未登录 / 非跟单 直接进策略详情
            if (strategy != null) {
                gotoStrategyDetail(typeForm, strategy, position)
            }
        }
    }

    private fun getEventIdList(): String {
        if (mViewModel.advertImgLiveData.value?.eventsList?.isNotEmpty() == true) {
            return mViewModel.advertImgLiveData.value?.eventsList?.joinToString(",") { it.eventId.ifNull() }.ifNull("")
        }
        return ""
    }

    private fun getProfitShieldTip(): CharSequence {
        return buildSpannedString {
            val upto = getString(R.string.up_to_100_p_a)
            append(getString(R.string.earn_x_of_cover_from_copying, upto))
            val start = indexOf(upto)
            val end = start + upto.length
            if (start != -1 && end <= length) {
                setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.ce35728)), start, end, 0)
                setSpan(StyleSpan(android.graphics.Typeface.BOLD), start, end, 0)
            }
            append(" ")
            append(getString(R.string.tnc_apply))
        }
    }

    override fun onResume() {
        super.onResume()
        if (vsBannerBinding?.mBanner?.isVisible == true && mViewModel.advertImgLiveData.value != null) {
            vsBannerBinding?.mBanner?.start()
        }
    }

    override fun onStop() {
        super.onStop()
        vsBannerBinding?.mBanner?.stop()
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}