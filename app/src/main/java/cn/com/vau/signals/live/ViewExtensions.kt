package cn.com.vau.signals.live

import android.graphics.SurfaceTexture
import android.view.Surface
import android.view.TextureView
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior
import java.util.*
import java.util.concurrent.TimeUnit


fun BottomSheetBehavior<View>.isShowing() = state == BottomSheetBehavior.STATE_EXPANDED

fun BottomSheetBehavior<View>.show() {
    if (!isShowing()) {
        state = BottomSheetBehavior.STATE_EXPANDED
    }
}

fun BottomSheetBehavior<View>.hide() {
    if (state != BottomSheetBehavior.STATE_HIDDEN) {
        state = BottomSheetBehavior.STATE_HIDDEN
    }
}

fun TextureView.onReady(onReady: (surface: Surface) -> Unit) {
    if (surfaceTexture != null) {
        onReady(Surface(surfaceTexture))
        return
    }
    surfaceTextureListener = object : TextureView.SurfaceTextureListener {
        override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
            surfaceTextureListener = null
            onReady(Surface(surfaceTexture))
        }

        override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
            /* Ignored */
        }

        override fun onSurfaceTextureDestroyed(surface: SurfaceTexture) = false

        override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
            /* Ignored */
        }
    }
}

/**
 * 时间格式化
 */
fun getDurationString(milliseconds: Long): String? {
    val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
    var time = milliseconds - TimeUnit.HOURS.toMillis(hours)
    val minutes = TimeUnit.MILLISECONDS.toMinutes(time)
    time -= TimeUnit.MINUTES.toMillis(minutes)
    val seconds = TimeUnit.MILLISECONDS.toSeconds(time)
    val var10000 = time - TimeUnit.SECONDS.toMillis(seconds)
    val sb = StringBuilder()
    if (hours > 0L) {
        sb.append(String.format(Locale.getDefault(), "%02d", hours))
        sb.append(":")
    }
    sb.append(String.format(Locale.getDefault(), "%02d", minutes))
    sb.append(":")
    sb.append(String.format(Locale.getDefault(), "%02d", seconds))
    return sb.toString()
}


