package cn.com.vau.signals.stsignal.center.fragment

import android.os.Bundle
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.signals.stsignal.center.adapter.StCopierReviewPendingAdapter
import cn.com.vau.signals.stsignal.center.vm.StCopierReviewViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView

/**
 * author：lvy
 * date：2024/05/11
 * desc：信号源中心->Copier Review->Approved、Rejected
 */
class StCopierReviewApprovedFragment :
    BaseMvvmFragment<FragmentRefreshBinding, StCopierReviewViewModel>() {

    private var mType = 1
    private val mAdapter by lazy {
        StCopierReviewPendingAdapter(mType).apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        arguments?.let {
            mType = it.getInt("type", mType)
        }
    }

    override fun initView() {
        mAdapter.registerAdapterDataObserver(adapterDataObserver)
        initRecyclerView()
    }

    override fun lazyLoadData() {
        refreshData()
    }

    override fun initListener() {
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            refreshData(isRefresh = false)
        }
        // 跟单审核列表
        mViewModel.copyPageListLiveData.observe(this) {
            if (mViewModel.pageNum == 1) {
                mAdapter.setList(it?.content)
            } else {
                it?.content?.let { list ->
                    mAdapter.addData(list)
                }
            }
            if (mAdapter.itemCount >= (it?.totalElements ?: 0)) { // 加载结束
                mBinding.mRefreshLayout.finishLoadMoreWithNoMoreData()
            } else { // 加载完成还有下一页
                mBinding.mRefreshLayout.finishLoadMore(true)
            }
        }
        // 请求失败
        mViewModel.reqErrLiveData.observe(this) {
            ToastUtil.showToast(it)
            if (mViewModel.pageNum == 1) {
                mBinding.mRefreshLayout.finishRefresh()
            } else {
                mBinding.mRefreshLayout.finishLoadMore(Constants.finishRefreshOrMoreTime, false, true)
            }
        }
    }

    private fun initRecyclerView() {
        mBinding.mRecyclerView.adapter = mAdapter
    }

    /**
     * 刷新数据
     */
    fun refreshData(isRefresh: Boolean = true) {
        if(isDestroyed()) return
        mViewModel.stProfileCopyPageListApi(isRefresh, if (mType == 1) "APPROVED" else "REJECTED")
    }

    /**
     * 监听adapter数据变化
     */
    private val adapterDataObserver = object : RecyclerView.AdapterDataObserver() {
        override fun onChanged() {
            if (mAdapter.data.isEmpty()) {
                mBinding.mRefreshLayout.setEnableRefresh(false) // 禁用下拉刷新
                mBinding.mRefreshLayout.setEnableLoadMore(false) // 禁用上拉加载
            } else {
                mBinding.mRefreshLayout.setEnableRefresh(false)
                mBinding.mRefreshLayout.setEnableLoadMore(true)
                // 添加分隔线
                if (mBinding.mRecyclerView.itemDecorationCount == 0) {
                    mBinding.mRecyclerView.addItemDecoration(
                        DividerItemDecoration(
                            8.dp2px(),
                            dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c0a1e1e1e_c0affffff)
                        )
                    )
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mAdapter.unregisterAdapterDataObserver(adapterDataObserver)
    }

    companion object {
        // type=1 APPROVED  type=2 REJECTED
        fun newInstance(type: Int): StCopierReviewApprovedFragment {
            val fragment = StCopierReviewApprovedFragment()
            val bundle = Bundle()
            bundle.putInt("type", type)
            fragment.arguments = bundle
            return fragment
        }
    }
}