package cn.com.vau.signals.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.discover.AddAWSData
import cn.com.vau.data.discover.FilterChartData
import cn.com.vau.data.discover.HistoryMessageData
import cn.com.vau.data.discover.LiveInfoData
import cn.com.vau.data.discover.LiveLikes
import cn.com.vau.data.discover.LivePromoData
import cn.com.vau.signals.presenter.LiveContract
import io.reactivex.disposables.Disposable
import java.util.HashMap

/**
 * Created by liyang.
 * live
 */
class LiveModel : LiveContract.Model {

    override fun addAWSLive(userId : String ,roomArn: String, channelId:Long,baseObserver: BaseObserver<AddAWSData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().addAwsWatchUser(userId,roomArn,channelId), baseObserver)
        return baseObserver.disposable
    }

    override fun getWatchCount(channelId: Long, baseObserver: BaseObserver<LiveInfoData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getChannelById(channelId), baseObserver)
        return baseObserver.disposable
    }

    override fun filterChatContent(userId: String,roomId: Long, chatContent: String, baseObserver: BaseObserver<FilterChartData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().filter(userId, Constants.PRODUCT_NAME,roomId,chatContent), baseObserver)
        return baseObserver.disposable
    }

    override fun giveLikes(isLike: Int, channelId:Long,baseObserver: BaseObserver<LiveLikes>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().addAndCancelLike(isLike,channelId), baseObserver)
        return baseObserver.disposable
    }

    override fun getChatContent(channelId: Long, baseObserver: BaseObserver<HistoryMessageData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getChatRecord(channelId), baseObserver)
        return baseObserver.disposable
    }

    override fun getChartToken(userId : String,roomArn: String, baseObserver: BaseObserver<DataObjStringBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getToken(userId,
            roomArn), baseObserver)
        return baseObserver.disposable
    }

    override fun queryLivePromo(baseObserver: BaseObserver<LivePromoData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().eventsGetListByListStream(), baseObserver)
        return baseObserver.disposable
    }

    override fun exitLive(userId: String,channelId: Long, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().awsWatchUserSignOutLiveStream(userId ,channelId), baseObserver)
        return baseObserver.disposable
    }

    override fun queryMT4AccountType(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().crmGetMt4AccountApplyType(map), baseObserver)
        return baseObserver.disposable
    }

    override fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().crmGetMt4AccountApplyType(map), baseObserver)
        return baseObserver.disposable
    }
}