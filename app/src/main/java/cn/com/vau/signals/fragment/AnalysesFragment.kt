package cn.com.vau.signals.fragment

import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.CustomViewPagerOnPageChangeListener
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.discover.NewsLetterObjData
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.databinding.HeaderRecyclerSignalsBannerBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.adapter.AnalysesRecyclerAdapter
import cn.com.vau.signals.viewModel.AnalysesViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.observeUIState
import cn.com.vau.util.setNbOnItemClickListener
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataScrollView
import com.bumptech.glide.request.RequestOptions
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import org.json.JSONObject

/**
 * 分析师观点
 */
class AnalysesFragment : BaseMvvmBindingFragment<FragmentRefreshBinding>() {

    private val mViewModel: AnalysesViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private val headerBanner by lazy { HeaderRecyclerSignalsBannerBinding.inflate(layoutInflater) }

    private val mAdapter by lazy {
        AnalysesRecyclerAdapter().apply {
            addHeaderView(headerBanner.root)
            setEmptyView(NoDataScrollView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    override fun initView() {
        headerBanner.mBanner.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
            override fun onBindView(holder: BannerImageHolder?, data: String?, position: Int, size: Int) {
                val options = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(requireContext(), data, holder?.imageView, options)
            }
        }).setScrollTime(1000)
            .addBannerLifecycleObserver(this)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(requireContext(), R.attr.color_c1f1e1e1e_c1fffffff), firstShowCount = 1))
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMore()
        }

        headerBanner.mBanner.setOnBannerListener { _, position ->
            val selectData = mViewModel.bannerLiveData.value?.elementAtOrNull(position)
            jumpToHtml(selectData, position)
        }
        headerBanner.mBanner.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                if (!mViewModel.bannerLiveData.value.isNullOrEmpty()) {
                    mViewModel.bannerLiveData.value?.elementAtOrNull(position)?.let {
                        headerBanner.tvTime.text = buildString {
                            append(it.date)
                            append(" ")
                            append(it.time)
                        }
                        headerBanner.tvTitle.text = it.product ?: ""
                        headerBanner.tvContentTitle.text = it.intro ?: ""
                    }
                }
                headerBanner.mIndicator.changeIndicator(position)
            }
        })

        mAdapter.setNbOnItemClickListener { _, _, position ->
            val selectData = mAdapter.data.elementAtOrNull(position)
            jumpToHtml(selectData, position)
            LogEventUtil.setLogEvent("signals", Bundle().apply {
                putString("categroy_title", "ns+${selectData?.title}+${selectData?.date}")
            })
        }
    }

    private fun jumpToHtml(selectData: NewsLetterObjData?, position: Int) {
        val htmlUrl = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/analysis/${selectData?.id.ifNull()}"
        NewHtmlActivity.openActivity(requireContext(), url = htmlUrl)
        // 神策自定义埋点(v3500)
        sensorsTrack(selectData?.id.ifNull(), position, htmlUrl)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.bannerLiveData.observe(this) {
            updateBannerData(it)
        }
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, mAdapter, mBinding.mRefreshLayout)
    }

    private fun updateBannerData(dataList: List<NewsLetterObjData>?) {
        if (dataList.isNullOrEmpty()) {
            headerBanner.mBanner.visibility = View.GONE
            headerBanner.mIndicator.visibility = View.GONE
        } else {
            headerBanner.mIndicator.isVisible = dataList.size > 1
            headerBanner.mIndicator.initIndicatorCount(dataList.size)
            headerBanner.tvTitle.text = mViewModel.bannerLiveData.value?.elementAtOrNull(0)?.product.ifNull()
            headerBanner.tvContentTitle.text = mViewModel.bannerLiveData.value?.elementAtOrNull(0)?.title.ifNull()
            headerBanner.mBanner.visibility = View.VISIBLE
            headerBanner.mBanner.setDatas(dataList.map { it.img.ifNull() })
            headerBanner.mBanner.start()
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}
