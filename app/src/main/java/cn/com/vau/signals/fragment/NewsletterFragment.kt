package cn.com.vau.signals.fragment

import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.*
import cn.com.vau.data.discover.NewsLetterObjData
import cn.com.vau.databinding.*
import cn.com.vau.home.adapter.DailyStrategyAdapter
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.viewModel.NewsLetterViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.NoDataScrollView
import cn.com.vau.util.widget.NoDataView
import com.bumptech.glide.request.RequestOptions
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import org.json.JSONObject

/**
 * NewsLetter
 */
class NewsletterFragment : BaseMvvmBindingFragment<FragmentRefreshBinding>() {

    private val mViewModel: NewsLetterViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private val headerBanner by lazy { HeaderRecyclerSignalsBannerBinding.inflate(layoutInflater) }

    private val mAdapter by lazy {
        DailyStrategyAdapter().apply {
            addHeaderView(headerBanner.root)
            setEmptyView(NoDataScrollView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }

    override fun initView() {
        headerBanner.mBanner.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
            override fun onBindView(holder: BannerImageHolder?, data: String?, position: Int, size: Int) {
                val options = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(requireContext(), data, holder?.imageView, options)
            }
        }).setScrollTime(1000)
            .addBannerLifecycleObserver(this)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(requireContext(), R.attr.color_c1f1e1e1e_c1fffffff), firstShowCount = 1))
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.refresh()
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mViewModel.loadMore()
        }

        headerBanner.mBanner.setOnBannerListener { _, bannerPosition ->
            val selectData = mViewModel.bannerLiveData.value?.elementAtOrNull(bannerPosition)
            jumpHtmlActivity(selectData, bannerPosition)
        }

        headerBanner.mBanner.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                if (!mViewModel.bannerLiveData.value.isNullOrEmpty()) {
                    val data = mViewModel.bannerLiveData.value?.getOrNull(position)
                    headerBanner.tvTime.text = data?.date.ifNull()
                    headerBanner.tvTitle.text = data?.title.ifNull()
                    headerBanner.tvContentTitle.text = data?.subtitle.ifNull()
                    headerBanner.mIndicator.changeIndicator(position)
                }
            }
        })

        mAdapter.setNbOnItemClickListener { _, _, position ->
            val selectData = mAdapter.data.elementAtOrNull(position)
            jumpHtmlActivity(selectData, position)
        }
    }

    private fun jumpHtmlActivity(selectData: NewsLetterObjData?, position: Int) {
        val url = HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix + "/nativeTitle/newsletter/${selectData?.id.ifNull()}"
        NewHtmlActivity.openActivity(requireContext(), url = url)

        mViewModel.newsLetterAddrecord(selectData?.id ?: "")
        LogEventUtil.setLogEvent("signals", Bundle().apply {
            putString("categroy_title", "ns+${selectData?.title}+${selectData?.date}")
        })
        // 神策自定义埋点(v3500)
        sensorsTrack(selectData?.id.ifNull(), position, url)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.bannerLiveData.observe(this) {
            updateBannerData(it)
        }
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, mAdapter, mBinding.mRefreshLayout)
    }

    private fun updateBannerData(dataList: List<NewsLetterObjData>?) {
        if (dataList.isNullOrEmpty()) {
            headerBanner.mBanner.visibility = View.GONE
            headerBanner.mIndicator.visibility = View.GONE
        } else {
            headerBanner.mIndicator.isVisible = dataList.size > 1
            headerBanner.mIndicator.initIndicatorCount(dataList.size)
            headerBanner.mBanner.visibility = View.VISIBLE
            headerBanner.mBanner.setDatas(dataList.map { it.img.ifNull() })
            val data = mViewModel.bannerLiveData.value?.getOrNull(0)
            headerBanner.tvTime.text = data?.date.ifNull()
            headerBanner.tvTitle.text = data?.title.ifNull()
            headerBanner.tvContentTitle.text = data?.subtitle.ifNull()
            headerBanner.mBanner.start()
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}
