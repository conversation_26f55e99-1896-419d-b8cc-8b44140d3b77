package cn.com.vau.signals.stsignal.viewmodel

import cn.com.vau.common.base.mvvm.BaseRepository
import cn.com.vau.common.http.ServiceManager
import cn.com.vau.data.StringBean
import cn.com.vau.data.strategy.StStrategyCopyLoadData
import cn.com.vau.data.strategy.StStrategyCopySubmitData
import cn.com.vau.util.GsonUtil
import io.reactivex.Flowable

class StStrategyCopyRepository : BaseRepository() {

    private val service by lazy { ServiceManager.getInstance().baseStTradingUrlService }

    //----------------------------------------------------------- 下单 ------------------------------------------------------------------
    fun strategyCopySubmitLoad(strategyId: String, accountId: String): Flowable<StStrategyCopyLoadData> {
        val map = hashMapOf("strategyId" to strategyId, "accountId" to accountId)
        return service.strategyCopySubmitLoad(GsonUtil.buildGson().toJsonTree(map).asJsonObject)
            .compose(threadToMain())
    }

    fun strategyCopySubmit(param: HashMap<String, String>): Flowable<StStrategyCopySubmitData> {
        return service.strategyCopySubmit(GsonUtil.buildGson().toJsonTree(param).asJsonObject)
            .compose(threadToMain())
    }
//------------------------------------------------------------------------------------------------------------------------------------

    //----------------------------------------------------------- 改单 ------------------------------------------------------------------
    fun strategyCopySettings(type: String, portfolioId: String): Flowable<StStrategyCopyLoadData> {
        return service.strategyCopySettings(type, portfolioId)
            .compose(threadToMain())
    }

    fun strategyCopyUpdate(param: HashMap<String, String>): Flowable<StringBean> {
        return service.strategyCopyUpdate(GsonUtil.buildGson().toJsonTree(param).asJsonObject)
            .compose(threadToMain())
    }
//------------------------------------------------------------------------------------------------------------------------------------
}