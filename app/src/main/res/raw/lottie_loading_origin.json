{"v": "5.12.2", "fr": 25, "ip": 0, "op": 40, "w": 1080, "h": 1080, "nm": "下拉 Loading_Light", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "空 5", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": -15, "ix": 10}, "p": {"a": 0, "k": [540.125, 502.125, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50.125, 50.125, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [500, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 16, "s": [400, 400, 100]}, {"t": 30, "s": [500, 500, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: 缩放')(1), 200);\n    freq = $bm_div(effect('Elastic: 缩放')(2), 30);\n    decay = $bm_div(effect('Elastic: 缩放')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 缩放", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "ip": 0, "op": 125, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "右", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 15, "ix": 10}, "p": {"a": 0, "k": [73.222, 51.585, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-1399.165, 2.958, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [500, 500, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 22, "nm": "描边", "np": 13, "mn": "ADBE Stroke", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "路径", "mn": "ADBE Stroke-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "所有蒙版", "mn": "ADBE Stroke-0010", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "顺序描边", "mn": "ADBE Stroke-0011", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 2, "nm": "颜色", "mn": "ADBE Stroke-0002", "ix": 4, "v": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}}, {"ty": 0, "nm": "画笔大小", "mn": "ADBE Stroke-0003", "ix": 5, "v": {"a": 0, "k": 2, "ix": 5}}, {"ty": 0, "nm": "画笔硬度", "mn": "ADBE Stroke-0004", "ix": 6, "v": {"a": 0, "k": 0.75, "ix": 6}}, {"ty": 0, "nm": "不透明度", "mn": "ADBE Stroke-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 0, "nm": "起始", "mn": "ADBE Stroke-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}, {"ty": 0, "nm": "结束", "mn": "ADBE Stroke-0009", "ix": 9, "v": {"a": 0, "k": 100, "ix": 9}}, {"ty": 7, "nm": "间距", "mn": "ADBE Stroke-0006", "ix": 10, "v": {"a": 0, "k": 15, "ix": 10}}, {"ty": 7, "nm": "绘画样式", "mn": "ADBE Stroke-0007", "ix": 11, "v": {"a": 0, "k": 1, "ix": 11}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1408.028, -5.254], [-1390.302, -5.254], [-1399.123, 11.171], [-1399.123, 0.289]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 30, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-351]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [-33]}, {"t": 30, "s": [0]}], "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"t": 32, "s": [0]}], "ix": 4}, "w": {"a": 0, "k": 0.8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 32, "s": [100]}], "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 125, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "左", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 15, "ix": 10}, "p": {"a": 0, "k": [21.229, 59.853, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-1408.781, 7.247, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [500, 500, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1417.153, -5.254], [-1410.5, -5.254], [-1400.41, 13.572], [-1403.726, 19.748]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 30, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [309]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [-63]}, {"t": 30, "s": [0]}], "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"t": 32, "s": [0]}], "ix": 4}, "w": {"a": 0, "k": 0.8, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 32, "s": [100]}], "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 125, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}}