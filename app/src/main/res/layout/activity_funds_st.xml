<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/funds"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 主要内容区域 -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/ctlBottom"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/mAppBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/mainLayoutBg"
            app:elevation="0dp">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llAccountFundContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_vertical_base"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_scrollFlags="scroll">

                <TextView
                    android:id="@+id/tvCurrencyType"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_top_title"
                    android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/margin_horizontal_base"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    tools:text="USD" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/accountFundViewPager2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="10dp" />

                <cn.com.vau.common.view.custom.BannerIndicatorView
                    android:id="@+id/mTopIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/indicator_margin_top"
                    android:gravity="center"
                    app:indicator_count="2" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="12dp"
                    android:background="?attr/color_c0a1e1e1e_c0affffff" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <!-- 账户信息/登陆按钮 切换显示 -->
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <TextView
                style="@style/bold_semi_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:text="@string/details"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/fundDetailViewPager2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp" />

        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvDeposit"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:layout_marginBottom="0dp"
            android:text="@string/deposit"
            app:layout_constraintBottom_toTopOf="@+id/tvWithdraw"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/margin_bottom_next_to_layout" />

        <ImageView
            android:id="@+id/ivEvent"
            android:layout_width="40dp"
            android:layout_height="22dp"
            android:contentDescription="@string/app_name"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/tvDeposit"
            app:layout_constraintTop_toTopOf="@+id/tvDeposit" />

        <TextView
            android:id="@+id/tvWithdraw"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:text="@string/withdraw"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDeposit" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
