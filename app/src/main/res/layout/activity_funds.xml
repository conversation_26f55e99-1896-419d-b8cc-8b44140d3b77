<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/funds"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@+id/ctlBottom"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <TextView
                android:id="@+id/tvCurrencyType"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_top_title"
                android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:text="USD" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llAccountInfoTop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:paddingVertical="@dimen/margin_horizontal_base"
                app:layout_constraintTop_toBottomOf="@+id/tvCurrencyType">

                <TextView
                    android:id="@+id/tvEquityTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/equity"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvFreeMarginTitle"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvEquity"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvEquityTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvEquityTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvEquityTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvFreeMarginTitle"
                    style="@style/medium_font"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"

                    android:text="@string/free_margin"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvCreditAmountTitle"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvEquityTitle"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvFreeMargin"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvFreeMarginTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvFreeMarginTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvFreeMarginTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvCreditAmountTitle"
                    style="@style/medium_font"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/credit"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvFreeMarginTitle"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvCreditAmount"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="0.00"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvCreditAmountTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvCreditAmountTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvCreditAmountTitle"
                    tools:ignore="HardcodedText,SpUsage" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llAccountInfoBottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:background="@drawable/draw_main_card"
                android:gravity="center_vertical"
                android:paddingTop="18dp"
                android:paddingBottom="18dp"
                app:layout_constraintTop_toBottomOf="@+id/llAccountInfoTop">

                <TextView
                    android:id="@+id/tvProfitTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/pnl"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvPlus"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvProfit"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvProfitTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvProfitTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvProfitTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvPlus"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tvFloatingProfitTitle"
                    app:layout_constraintStart_toEndOf="@+id/tvProfitTitle"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvFloatingProfitTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/floating_pnl"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvEqual"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvPlus"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvFloatingProfit"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvFloatingProfitTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvFloatingProfitTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvFloatingProfitTitle"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvEqual"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="="
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tvTotalProfitTitle"
                    app:layout_constraintStart_toEndOf="@+id/tvFloatingProfitTitle"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="HardcodedText,SpUsage" />

                <TextView
                    android:id="@+id/tvTotalProfitTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/total_profit"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvEqual"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvTotalProfit"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvTotalProfitTitle"
                    app:layout_constraintStart_toStartOf="@+id/tvTotalProfitTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvTotalProfitTitle"
                    tools:ignore="HardcodedText,SpUsage" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/mOffView"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff"
                app:layout_constraintTop_toBottomOf="@+id/llAccountInfoBottom" />

            <TextView
                android:id="@+id/tvDetails"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:text="@string/details"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/mOffView"
                tools:ignore="SpUsage" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDetails">

                <cn.com.vau.common.view.system.MyRecyclerView
                    android:id="@+id/mRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never" />

                <ViewStub
                    android:id="@+id/mVsNoData"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="75dp"
                    android:layout="@layout/vs_layout_no_data"
                    app:layout_constraintTop_toTopOf="parent" />

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvDeposit"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:layout_marginBottom="0dp"
            android:text="@string/deposit"
            app:layout_constraintBottom_toTopOf="@+id/tvWithdraw"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/margin_bottom_next_to_layout" />

        <ImageView
            android:id="@+id/ivEvent"
            android:layout_width="40dp"
            android:layout_height="22dp"
            android:contentDescription="@string/app_name"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/tvDeposit"
            app:layout_constraintTop_toTopOf="@+id/tvDeposit"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvWithdraw"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:text="@string/withdraw"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDeposit" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
