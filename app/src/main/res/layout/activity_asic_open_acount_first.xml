<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".page.user.asicOpenAccount.activity.OpenAccountFirstActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:hb_titleText="@string/open_live_account"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.StepOpenAccountView
        android:id="@+id/stepView"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:step_num="1"
        app:step_num_total="5" />

    <TextView
        android:id="@+id/tvBasicInformation"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="30dp"
        android:text="@string/basic_information"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepView" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingStart="@dimen/margin_horizontal_base"
        android:paddingEnd="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBasicInformation">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="100dp">

            <EditText
                android:id="@+id/etEmail"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/email"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvEmail"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/email"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etEmail"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <EditText
                android:id="@+id/etFirstName"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/first_name"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etEmail" />

            <TextView
                android:id="@+id/tvFirstName"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/first_name"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etFirstName"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <EditText
                android:id="@+id/etMiddleName"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/middle_name"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etFirstName" />

            <TextView
                android:id="@+id/tvMiddleName"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/middle_name"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etMiddleName"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <EditText
                android:id="@+id/etLastName"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/last_name"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etMiddleName" />

            <TextView
                android:id="@+id/tvLastName"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/last_name"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etLastName"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/etNationality"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/nationality"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etLastName" />

            <TextView
                android:id="@+id/tvNationality"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/nationality"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etNationality"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/etDateOfBirth"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:hint="@string/date_of_birth"
                android:maxLength="50"
                android:paddingStart="20dp"
                android:paddingTop="16dp"
                android:paddingEnd="20dp"
                android:paddingBottom="16dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textColorHint="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etNationality" />

            <TextView
                android:id="@+id/tvDateOfBirth"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:background="?attr/mainLayoutBg"
                android:text="@string/date_of_birth"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:translationY="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/etDateOfBirth"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <View
                android:id="@+id/marginView"
                android:layout_width="0dp"
                android:layout_height="20dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etDateOfBirth" />

            <TextView
                android:id="@+id/tvAgreement"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/the_personal_information_we_parties"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/marginView" />

            <!-- 嵌套的这一层FrameLayout是为了解决阿拉伯语相关问题，不能删 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAgreement">

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvVantageLink"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textDirection="ltr"
                    android:fontFamily="@font/gilroy_regular"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    tools:text="Vantage Global Prime Pty Ltd's Privacy Policy." />
            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>


    <TextView
        android:id="@+id/tvNext"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bitmap_icon2_next_inactive"
        android:gravity="center"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

