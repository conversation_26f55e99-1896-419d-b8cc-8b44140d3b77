<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/update_email"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTips"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/c1fe35728"
        android:lineSpacingMultiplier="1.2"
        android:padding="@dimen/padding_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:visibility="gone"
        tools:text="123"
        tools:visibility="visible" />

    <TextView
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/new_email"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp" />

    <cn.com.vau.common.view.login.LoginInputContentView
        android:id="@+id/emailView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        app:icv_hintText="@string/new_email"
        app:icv_isInputEmail="true" />

    <TextView
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/current_password"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp" />

    <cn.com.vau.common.view.login.LoginInputPwdView
        android:id="@+id/pwdView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        app:ipv_hintText="@string/current_password" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/login_tvNext_style"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:text="@string/send_otp_via_email" />
</LinearLayout>