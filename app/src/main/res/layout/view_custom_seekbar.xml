<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatSeekBar
        android:id="@+id/seekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="2dp"
        android:maxHeight="2dp"
        android:background="@null"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        tools:progressDrawable="@drawable/seekbar_progress_cff8e5c_background_c1f1e1e1e_c1fffffff"
        tools:thumb="@drawable/shape_oval_solid_cffffff_stroke_cff8e5c"
        android:thumbOffset="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvMin"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seekBar"
        tools:text="-5%" />

    <TextView
        android:id="@+id/tvMax"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seekBar"
        tools:text="-95%" />

    <TextView
        android:id="@+id/tvMostSelected"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textSize="12dp"
        tools:textColor="@color/ce35728"
        app:layout_constraintTop_toBottomOf="@id/seekBar"
        app:layout_constraintStart_toStartOf="@id/gl33"
        app:layout_constraintEnd_toEndOf="@id/gl33"
        tools:text="Most Selected"/>

    <!-- 要求Most Selected在35%(5%~95%)位置，相当于整个进度条的33% -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.333"/>

</androidx.constraintlayout.widget.ConstraintLayout>