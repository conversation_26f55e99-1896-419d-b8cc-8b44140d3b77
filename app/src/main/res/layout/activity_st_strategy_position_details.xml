<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".trade.activity.StStrategyPositionDetailsActivity">

    <data></data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.util.widget.HeaderBar
            android:id="@+id/mHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:hb_endIcon="@drawable/draw_bitmap2_favorite12x12_c1e1e1e_cebffffff"
            app:hb_endIcon1="?attr/icon1AlertPrice"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toTopOf="@+id/tvClose"
            app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvOrderType"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_marginStart="@dimen/margin_horizontal_base"
                    android:background="@drawable/shape_c1f00c79c_r100"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="8dp"
                    android:textColor="@color/c00c79c"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage"
                    tools:text="Buy" />

                <TextView
                    android:id="@+id/tvProdName"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="18dp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvOrderType"
                    app:layout_constraintStart_toEndOf="@+id/tvOrderType"
                    app:layout_constraintTop_toTopOf="@+id/tvOrderType"
                    tools:ignore="SpUsage"
                    tools:text="VAU-TEST" />

                <ImageView
                    android:id="@+id/ivKLine"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="2dp"
                    android:contentDescription="@string/app_name"
                    android:padding="2dp"
                    android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
                    app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
                    app:layout_constraintStart_toEndOf="@+id/tvProdName"
                    app:layout_constraintTop_toTopOf="@+id/tvProdName" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlTop"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/tvOrderType">

                    <TextView
                        android:id="@+id/tvPnlTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:maxLines="1"
                        android:text="@string/pnl"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintEnd_toStartOf="@+id/tvNetPnlTitle"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvNetPnlTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:maxLines="1"
                        android:text="@string/net_pnl"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvPnlTitle"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvPnl"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/ce35728"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="@+id/tvPnlTitle"
                        app:layout_constraintStart_toStartOf="@+id/tvPnlTitle"
                        app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
                        tools:ignore="SpUsage"
                        tools:text="-0.24 USD" />

                    <TextView
                        android:id="@+id/tvNetPnl"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/ce35728"
                        android:textDirection="ltr"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="@+id/tvNetPnlTitle"
                        app:layout_constraintStart_toStartOf="@+id/tvNetPnlTitle"
                        app:layout_constraintTop_toBottomOf="@+id/tvNetPnlTitle"
                        tools:ignore="SpUsage"
                        tools:text="-0.30 USD" />

                    <View
                        android:id="@+id/cutOffLineView"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/cut_off_line_height"
                        android:layout_marginTop="12dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        app:layout_constraintTop_toBottomOf="@+id/tvPnl" />

                    <TextView
                        android:id="@+id/tvVolumeTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="12dp"
                        android:text="@string/volume"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/cutOffLineView"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvVolume"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolumeTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="0.01 Lots" />

                    <TextView
                        android:id="@+id/tvOpenPriceTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="16dp"
                        android:text="@string/open_price"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvVolumeTitle"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvOpenPrice"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenPriceTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="0.1059" />

                    <TextView
                        android:id="@+id/tvCurrentPriceTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="16dp"
                        android:text="@string/current_price"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvOpenPriceTitle"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvCurrentPrice"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvCurrentPriceTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="0.1035" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/topOffView"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="?attr/color_c0a1e1e1e_c0affffff"
                    app:layout_constraintTop_toBottomOf="@+id/ctlTop" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlBottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/padding_horizontal_base"
                    android:paddingStart="@dimen/padding_horizontal_base"
                    android:paddingEnd="0dp"
                    app:layout_constraintTop_toBottomOf="@+id/topOffView">

                    <TextView
                        android:id="@+id/tvChargesTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawablePadding="4dp"
                        android:text="@string/charges"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvCharges"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvChargesTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="-0.06 USD" />

                    <TextView
                        android:id="@+id/tvSwapTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:drawablePadding="4dp"
                        android:text="@string/swap"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvChargesTitle"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvSwap"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvSwapTitle"
                        app:layout_constraintEnd_toEndOf="@+id/tvCharges"
                        tools:ignore="SpUsage"
                        tools:text="0.00 USD" />

                    <TextView
                        android:id="@+id/tvOpenTimeTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/open_time"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvSwapTitle"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvOpenTime"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenTimeTitle"
                        app:layout_constraintEnd_toEndOf="@+id/tvCharges"
                        tools:ignore="SpUsage"
                        tools:text="04/06/2024 09:59:24" />

                    <TextView
                        android:id="@+id/tvCloseTimeTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/close_time"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvOpenTimeTitle"
                        tools:ignore="SpUsage"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvCloseTime"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvCloseTimeTitle"
                        app:layout_constraintEnd_toEndOf="@+id/tvCharges"
                        tools:ignore="SpUsage"
                        tools:text="04/06/2024 09:59:24"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvReasonTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:drawablePadding="4dp"
                        android:text="@string/reason"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCloseTimeTitle"
                        tools:ignore="SpUsage"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvReason"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        android:visibility="gone"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvReasonTitle"
                        app:layout_constraintEnd_toEndOf="@+id/tvCharges"
                        tools:ignore="SpUsage"
                        tools:text="Closed by trader"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvOrderNoTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/order_number"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvReasonTitle"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvOrderNo"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:drawablePadding="8dp"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="8dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="?attr/icon2EditStrategyFunds"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderNoTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="#45634575" />

                    <TextView
                        android:id="@+id/tvCloseHistory"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/close_history"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                        app:layout_constraintEnd_toEndOf="@+id/tvCharges"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvOrderNoTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:background="?attr/color_c0a1e1e1e_c0affffff"
                    app:layout_constraintTop_toBottomOf="@+id/ctlBottom" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/tvClose"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/close__position"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="HardcodedText,SpUsage" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>