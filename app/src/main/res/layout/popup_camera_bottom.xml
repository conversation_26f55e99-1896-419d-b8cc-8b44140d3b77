<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:paddingTop="20dp"
    android:paddingBottom="36dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toStartOf="@id/iv_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Add Picture From" />

    <ImageView
        android:id="@+id/iv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:itemCount="2"
        tools:listitem="@layout/item_text_with_right_arrow" />

</androidx.constraintlayout.widget.ConstraintLayout>
