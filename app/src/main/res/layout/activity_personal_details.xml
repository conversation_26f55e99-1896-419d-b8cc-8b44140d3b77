<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:contentDescription="@string/app_name"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:shapeAppearanceOverlay="@style/circleImageStyle" />

    <TextView
        android:id="@+id/tvNickname"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingVertical="10dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/icon2_edit_12x12"
        app:drawableTint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        app:layout_constraintWidth_default="wrap"
        tools:text="Vantage569" />

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/ivAvatar" />

    <View
        android:id="@+id/line"
        style="@style/TabLayoutBottomLineStyle"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintTop_toBottomOf="@+id/mTabLayout" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line" />
</androidx.constraintlayout.widget.ConstraintLayout>