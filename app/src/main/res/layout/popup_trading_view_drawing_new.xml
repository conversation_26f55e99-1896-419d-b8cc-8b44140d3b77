<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="?attr/color_cffffff_c262930"
    android:paddingVertical="20dp"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlParent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvDrawing"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/drawing"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDrawing"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4"
            app:layout_constraintTop_toBottomOf="@id/tvDrawing"
            tools:listitem="@layout/item_trading_view_drawing"
            tools:itemCount="3"/>

        <TextView
            android:id="@+id/tvLine"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/line"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintStart_toStartOf="@id/tvDrawing"
            app:layout_constraintTop_toBottomOf="@+id/rvDrawing"
            tools:ignore="HardcodedText" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvLine"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4"
            app:layout_constraintTop_toBottomOf="@id/tvLine"
            tools:listitem="@layout/item_trading_view_drawing"
            tools:itemCount="4"/>

        <TextView
            android:id="@+id/tvShape"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/shape"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintStart_toStartOf="@id/tvLine"
            app:layout_constraintTop_toBottomOf="@+id/rvLine" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvShape"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4"
            app:layout_constraintTop_toBottomOf="@id/tvShape"
            tools:listitem="@layout/item_trading_view_drawing"
            tools:itemCount="2"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>

