<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tool:ignore="HardcodedText">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivHead"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:contentDescription="@string/app_name"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundImageStyle6" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShare"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="4dp"
        android:padding="8dp"
        android:src="@drawable/draw_bitmap2_share_c1e1e1e_cebffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvLabel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvLabel"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvLabel"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/paused_copying"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
        app:layout_constraintEnd_toStartOf="@+id/ivShare"
        app:layout_constraintTop_toTopOf="@+id/ivHead"
        tool:ignore="Smalldp"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvName"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:paddingHorizontal="8dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toTopOf="@+id/tvId"
        app:layout_constraintEnd_toStartOf="@+id/tvLabel"
        app:layout_constraintStart_toEndOf="@+id/ivHead"
        app:layout_constraintTop_toTopOf="@+id/ivHead"
        tool:text="Follow signal 1 Follow signal 1 Follow signal 1 Follow signal 1" />

    <TextView
        android:id="@+id/tvIdKey"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
        app:layout_constraintStart_toStartOf="@+id/tvName"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        tool:text="Strategy ID：" />

    <TextView
        android:id="@+id/tvId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
        app:layout_constraintStart_toEndOf="@+id/tvIdKey"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        app:layout_goneMarginStart="8dp"
        tool:text="1234567" />

    <View
        android:id="@+id/cutOffLineView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/cut_off_line_height"
        android:layout_marginTop="12dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@+id/ivHead" />

    <TextView
        android:id="@+id/tvPnlTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:text="@string/pnl"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvBalanceTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cutOffLineView" />

    <TextView
        android:id="@+id/tvBalanceTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/balance"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnlTitle"
        app:layout_constraintEnd_toStartOf="@+id/tvEquityTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvPnlTitle" />

    <TextView
        android:id="@+id/tvEquityTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|end"
        android:maxLines="1"
        android:text="@string/equity"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnlTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvBalanceTitle" />

    <TextView
        android:id="@+id/tvPnl"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:ellipsize="end"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/c00c79c"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
        tool:text="-200.00" />

    <TextView
        android:id="@+id/tvBalance"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnl"
        app:layout_constraintEnd_toStartOf="@+id/tvEquity"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvPnl"
        tool:ignore="MissingConstraints"
        tool:text="-25.00" />

    <TextView
        android:id="@+id/tvEquity"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|end"
        android:maxLines="1"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvBalance"
        tool:ignore="MissingConstraints"
        tool:text="300.00" />

    <TextView
        android:id="@+id/tvCreditTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/credit"
        android:layout_marginHorizontal="6dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintEnd_toStartOf="@id/tvPlaceholderTitle"
        app:layout_constraintStart_toEndOf="@id/tvTotalSharedProfitTitle"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvTotalSharedProfitTitle" />

    <TextView
        android:id="@+id/tvTotalSharedProfitTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:text="@string/total_shared_profit"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:textAlignment="viewStart"
        android:layout_marginTop="12dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvCreditTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvPnl" />

    <TextView
        android:id="@+id/tvPlaceholderTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCreditTitle" />

    <TextView
        android:id="@+id/tvCredit"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvPlaceholder"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tvTotalSharedProfit"
        app:layout_constraintTop_toBottomOf="@+id/tvCreditTitle"
        tool:ignore="MissingConstraints"
        tool:text="50.00" />

    <TextView
        android:id="@+id/tvTotalSharedProfit"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textAlignment="viewStart"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvCredit"
        app:layout_constraintHorizontal_weight="1"
        tool:text="0.00" />

    <TextView
        android:id="@+id/tvPlaceholder"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCredit" />

    <TextView
        android:id="@+id/tvNextStart"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:minHeight="32dp"
        android:paddingVertical="8dp"
        android:text="@string/details"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintEnd_toStartOf="@+id/tvNextEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCredit" />

    <TextView
        android:id="@+id/tvNextEnd"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:minHeight="32dp"
        android:paddingVertical="8dp"
        android:text="@string/manage"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvNextStart"
        app:layout_constraintTop_toBottomOf="@+id/tvCredit" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        tool:background="?attr/color_c0a1e1e1e_c0affffff" />

</androidx.constraintlayout.widget.ConstraintLayout>
