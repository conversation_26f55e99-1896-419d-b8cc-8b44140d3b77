<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <View
        android:id="@+id/viewLine"
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:src="?attr/imgAlertOk"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewLine"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:lineSpacingExtra="6dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon"
        tools:text="Lorem ipsum dolor"
        tools:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvDetail"
        tools:text="Button"/>

</androidx.constraintlayout.widget.ConstraintLayout>