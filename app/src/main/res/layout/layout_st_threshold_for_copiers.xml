<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvThresholdTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/threshold_for_copiers"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMinInvestmentTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/min_investment_per_copy"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvThresholdTitle"
        app:layout_goneMarginTop="@dimen/margin_vertical_button" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etMinInvestment"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/select_login_et_bg"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:inputType="number"
        android:maxLength="9"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMinInvestmentTitle"
        tools:hint="Min. 50" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrencyType"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etMinInvestment"
        app:layout_constraintEnd_toEndOf="@id/etMinInvestment"
        app:layout_constraintTop_toTopOf="@id/etMinInvestment"
        tools:text="USD" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLotsTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/min_lots_per_order"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etMinInvestment" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etLots"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/select_login_et_bg"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:hint="0.01-100"
        android:inputType="numberDecimal"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="0.01"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLotsTitle"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLotsInfo"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="@string/lots"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etLots"
        app:layout_constraintEnd_toEndOf="@id/etLots"
        app:layout_constraintTop_toTopOf="@id/etLots" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMultiplesTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/min_multiples_per_order"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etLots" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etMultiples"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="@dimen/margin_vertical_button"
        android:background="@drawable/select_login_et_bg"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:hint="0.1-50"
        android:inputType="numberDecimal"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMultiplesTitle"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMultiplesInfo"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="X"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etMultiples"
        app:layout_constraintEnd_toEndOf="@id/etMultiples"
        app:layout_constraintTop_toTopOf="@id/etMultiples"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEquivalentMarginTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/min_multiples_for_equivalent_margin"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etMultiples" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etEquivalentMargin"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="@dimen/margin_vertical_button"
        android:background="@drawable/select_login_et_bg"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:hint="1-50"
        android:inputType="numberDecimal"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvEquivalentMarginTitle"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEquivalentMarginInfo"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="X"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etEquivalentMargin"
        app:layout_constraintEnd_toEndOf="@id/etEquivalentMargin"
        app:layout_constraintTop_toTopOf="@id/etEquivalentMargin"
        tools:ignore="HardcodedText" />
</androidx.constraintlayout.widget.ConstraintLayout>