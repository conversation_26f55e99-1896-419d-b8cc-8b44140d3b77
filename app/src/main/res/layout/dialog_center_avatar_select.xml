<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="268dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:orientation="vertical"
    android:paddingBottom="20dp">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toTopOf="@id/tvLeft"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLeft"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:gravity="center"
            android:maxLines="1"
            android:paddingVertical="10dp"
            android:text="@string/cancel"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintEnd_toStartOf="@+id/tvRight"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rvList" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRight"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_weight="1"
            android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
            android:gravity="center"
            android:lineSpacingExtra="10dp"
            android:maxLines="1"
            android:paddingVertical="10dp"
            android:text="@string/confirm"
            android:textColor="?attr/color_cebffffff_c1e1e1e"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvLeft"
            app:layout_constraintTop_toTopOf="@+id/tvLeft" />
    </LinearLayout>

</LinearLayout>