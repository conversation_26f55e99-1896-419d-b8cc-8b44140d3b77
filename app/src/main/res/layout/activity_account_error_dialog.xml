<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/mainLayoutBg"
    android:paddingStart="@dimen/margin_horizontal_base"
    android:paddingEnd="@dimen/margin_horizontal_base"
    tools:context=".ui.common.activity.AccountErrorDialogActivity">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="28dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline_h30"
        tools:text="@string/your_account_has_expired" />

    <TextView
        android:id="@+id/tvMsg"
        style="@style/medium_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:padding="16dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="@string/this_account_has_please_management" />

    <TextView
        android:id="@+id/tvTopNext"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:paddingVertical="14dp"
        android:text="@string/contact_customer_service"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toTopOf="@+id/tvSwitchAccount" />

    <TextView
        android:id="@+id/tvSwitchAccount"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/margin_bottom_next_to_layout"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:paddingVertical="14dp"
        android:text="@string/switch_account"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_h30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.3" />

</androidx.constraintlayout.widget.ConstraintLayout>