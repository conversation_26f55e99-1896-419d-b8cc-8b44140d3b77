<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/sign_up" />

    <TextView
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:text="@string/country_of_residence"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/tvSelectCountry"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
        android:gravity="center_vertical"
        android:hint="@string/country_of_residence"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff" />

    <CheckBox
        android:id="@+id/cbNotUs"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="6dp"
        android:background="@null"
        android:button="@drawable/select_checkbox_agreement_new"
        android:checked="true"
        android:paddingHorizontal="8dp"
        android:paddingVertical="6dp"
        android:text="@string/i_am_not_a_us_resident"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/login_tvNext_style"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="18dp"
        android:text="@string/create_account" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvLogin"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:paddingVertical="8dp"
        android:paddingStart="0px"
        android:paddingEnd="5dp"
        android:text="@string/already_have_an_account_login"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="13dp" />
</LinearLayout>