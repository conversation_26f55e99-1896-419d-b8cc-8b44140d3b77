<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/c034854">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSkip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:paddingStart="28dp"
        android:paddingTop="10dp"
        android:paddingEnd="28dp"
        android:paddingBottom="10dp"
        android:text="@string/skip"
        android:textColor="@color/cffffff"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSkip" />

    <View
        android:id="@+id/goHome"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>