<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minWidth="38dp"
        android:paddingHorizontal="8dp"
        android:gravity="center"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1D"/>

</androidx.constraintlayout.widget.ConstraintLayout>