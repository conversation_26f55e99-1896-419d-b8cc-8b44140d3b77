<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="cn.com.vau.trade.fragment.order.OpenTradesFragment">

    <!--解决 AppBarLayout滑动问题 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHideOther"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="8dp"
                    android:paddingStart="12dp"
                    android:src="?attr/icon2CbSquareUncheck"
                    app:layout_constraintBottom_toBottomOf="@id/tvHideOther"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvHideOther"
                    tools:ignore="RtlSymmetry" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHideOther"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="8dp"
                    android:text="@string/hide_other_symbols"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    app:layout_constraintStart_toEndOf="@id/ivHideOther"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gpHideOther"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="ivHideOther,tvHideOther"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvCloseAll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="5dp"
                    android:text="@string/close_all"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    style="@style/gilroy_500" />

                <View
                    android:id="@+id/cutOffLineView"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/cut_off_line_height"
                    android:layout_marginTop="8dp"
                    android:background="?attr/color_c1f1e1e1e_c1fffffff"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/tvCloseAll"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/closeAllGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tvCloseAll,cutOffLineView"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scrollView"
        app:layout_goneMarginTop="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <cn.com.vau.common.view.system.MyRecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layoutManager=".common.view.WrapContentLinearLayoutManager" />

            <ViewStub
                android:id="@+id/mVsNoDataScroll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout="@layout/vs_layout_no_data_scroll"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>