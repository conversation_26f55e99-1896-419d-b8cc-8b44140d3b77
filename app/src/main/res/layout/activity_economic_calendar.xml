<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/economic_calendar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/margin_top_title"
        android:layout_weight="1"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/tvNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/loginTitleView">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTimeTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:gravity="center"
                android:text="@string/time"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTime"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvTimeTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvTimeTitle"
                tools:text="08/12/2019 12:30" />

            <View
                android:id="@+id/viewTime"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvTime" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvRegionTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/region"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewTime" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvRegion"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvRegionTitle"
                tools:text="美国" />

            <View
                android:id="@+id/viewRegion"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvRegion" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvEventTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/event"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewRegion" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvEvent"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:gravity="end"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:maxWidth="180dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvEventTitle"
                tools:text="美国至11月11日当周ICSC-高盛连锁店销售较上周" />

            <View
                android:id="@+id/viewEvent"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvEvent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvImportanceTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/importance"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewEvent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvImportance"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingStart="7dp"
                android:paddingTop="4dp"
                android:paddingEnd="7dp"
                android:paddingBottom="4dp"
                android:textSize="10dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvImportanceTitle"
                tools:background="@drawable/shape_cff8e5c_r100"
                tools:text="Medium" />

            <View
                android:id="@+id/viewImportance"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvImportance" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPrevTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/prev"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewImportance" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPrev"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:maxWidth="180dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvPrevTitle"
                tools:text="2.5%" />

            <View
                android:id="@+id/viewPrev"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvPrev" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFcstTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/fcst"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewPrev" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFcst"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:maxWidth="180dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvFcstTitle"
                tools:text="2.6%" />

            <View
                android:id="@+id/viewFcst"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvFcst" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvActTitle"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/act"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvTimeTitle"
                app:layout_constraintTop_toBottomOf="@+id/viewFcst" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAct"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:maxWidth="180dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="@+id/tvTime"
                app:layout_constraintTop_toTopOf="@+id/tvActTitle"
                tools:text="2.5%" />

            <View
                android:id="@+id/viewAct"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/tvAct" />
            <!-- 图表 -->

            <cn.com.vau.common.view.custom.CalendarLineChart
                android:id="@+id/lineChart"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layerType="software"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/viewAct" />

            <ViewStub
                android:id="@+id/mVsNoData"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/vs_layout_no_data"
                app:layout_constraintTop_toTopOf="@+id/lineChart" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/follow"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

</LinearLayout>