<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tool:ignore="MissingConstraints">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_c99000000_r12"
        android:layout_marginTop="7dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/gilroy_regular"
            android:lineSpacingExtra="3dp"
            android:textColor="@color/cffffff"
            android:textSize="11dp"
            android:textStyle="normal"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
