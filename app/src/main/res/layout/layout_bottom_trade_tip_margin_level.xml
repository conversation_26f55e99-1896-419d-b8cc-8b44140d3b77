<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--region Low Risk-->
    <TextView
        android:id="@+id/tvLowRiskTitle"
        style="@style/gilroy_700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/low_risk"
        android:textColor="@color/c00c79c"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tvLowRiskSubtitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="@color/c00c79c"
        android:textSize="14dp"
        tools:text="Margin Level ≥ Warning Rate+100%" />

    <TextView
        android:id="@+id/tvLowRiskContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        tools:text="@string/the_account_has_of_liquidation" />
    <!--endregion-->

    <!--region Medium Risk-->
    <TextView
        android:id="@+id/tvMediumRiskTitle"
        style="@style/gilroy_700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/medium_risk"
        android:textColor="@color/cff8e5c"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tvMediumRiskSubtitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="@color/cff8e5c"
        android:textSize="14dp"
        tools:text="Warning Rate ≤ Margin Level ≤ Warning Rate+100%" />

    <TextView
        android:id="@+id/tvMediumRiskContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        tools:text="@string/the_accounts_you_account_risk" />
    <!--endregion-->

    <!--region High Risk-->
    <TextView
        android:id="@+id/tvHighRiskTitle"
        style="@style/gilroy_700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/high_risk"
        android:textColor="@color/cf44040"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tvHighRiskSubtitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="@color/cf44040"
        android:textSize="14dp"
        tools:text="Stop out ≤ Margin Level ≤ Warning Rate" />

    <TextView
        android:id="@+id/tvHighRiskContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        tools:text="@string/the_accounts_available_to_you_some_positions" />
    <!--endregion-->

    <!--region Stop Out-->
    <TextView
        android:id="@+id/tvStopOutTitle"
        style="@style/gilroy_700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/stop_out_2"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tvStopOutSubtitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        tools:text="Margin Level ≤ Stop out" />

    <TextView
        android:id="@+id/tvStopOutContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        tools:text="@string/your_positions_will_x_starting_opened_positions" />
    <!--endregion-->
</LinearLayout>