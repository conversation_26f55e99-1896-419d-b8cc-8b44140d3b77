<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvHint"
        style="@style/medium_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="Email(*)"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />

    <EditText
        android:id="@+id/mEditText"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/select_login_et_bg"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tvHint"
        tools:ignore="Autofill,LabelFor" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mIfvArrow"
        android:layout_width="wrap_content"
        android:layout_height="6dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mEditText"
        app:layout_constraintEnd_toEndOf="@+id/mEditText"
        app:layout_constraintTop_toTopOf="@+id/mEditText"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>