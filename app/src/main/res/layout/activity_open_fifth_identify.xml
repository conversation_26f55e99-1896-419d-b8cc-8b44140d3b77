<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/c1e1e1e"
    android:paddingHorizontal="@dimen/padding_horizontal_base">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ifvLogo"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_marginTop="36dp"
        android:src="@drawable/bitmap_img_asic_right_top_logo_nav"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:paddingEnd="20dp"
        android:paddingStart="0dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/bitmap2_arrow_start_16x16_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ifvLogo"
        app:tint="@color/cebffffff" />

    <ImageView
        android:contentDescription="@string/app_name"
        android:id="@+id/ivRegisterTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_asic_passport"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ifvLogo" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="@string/passport"
        android:textSize="28dp"
        android:textColor="@color/cffffff"
        style="@style/bold_semi_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivRegisterTop"/>

    <TextView
        android:id="@+id/tvTopTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/please_take_photo_and_glare"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="20dp"
        style="@style/regular_font"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvUploadTip1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/at_least_x_months_validity"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="8dp"
        style="@style/regular_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTopTip" />

    <TextView
        android:id="@+id/tvUploadTip2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/must_show_full_clearly"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="6dp"
        style="@style/regular_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip1" />

    <TextView
        android:id="@+id/tvUploadTip3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/must_show_passport_clearly"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="6dp"
        style="@style/regular_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip2" />

    <TextView
        android:id="@+id/tvUploadTip4"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/must_show_passport_clearly"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/regular_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip3" />

    <TextView
        android:id="@+id/tvUploadTip5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/must_show_passport_clearly"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/regular_font"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip4" />

    <TextView
        android:id="@+id/tvUploadTip6"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/must_show_passport_clearly"
        android:textColor="@color/cffffff"
        android:textSize="14dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        style="@style/regular_font"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip5" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="12dp"
        android:paddingBottom="20dp"
        app:layout_constraintTop_toBottomOf="@+id/tvUploadTip6"
        app:layout_constraintBottom_toTopOf="@+id/tvNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/tvSeeExample"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/see_example"
        android:textColor="@color/cffffff"
        android:layout_marginTop="22dp"
        style="@style/bold_semi_font"
        app:layout_constraintTop_toTopOf="@+id/tvNext"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/tvNext"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bitmap_icon2_next_inactive_d"
        android:gravity="center"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>