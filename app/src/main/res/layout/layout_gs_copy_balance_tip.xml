<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <cn.com.vau.common.view.SerialTextView
        android:id="@+id/stvDesc1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:layout_constraintTop_toTopOf="parent"
        app:serial_text_color="?attr/color_ca61e1e1e_c99ffffff"
        tools:content_text="@string/gs_balance_forexample_tip1" />

    <cn.com.vau.common.view.SerialTextView
        android:id="@+id/stvDesc2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:layout_constraintTop_toBottomOf="@id/stvDesc1"
        app:serial_text_color="?attr/color_ca61e1e1e_c99ffffff"
        tools:content_text="@string/gs_balance_forexample_tip1" />

    <cn.com.vau.common.view.SerialTextView
        android:id="@+id/stvDesc3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/stvDesc2"
        app:serial_text_color="?attr/color_ca61e1e1e_c99ffffff"
        tools:visibility="visible"
        tools:content_text="@string/gs_balance_forexample_tip2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvExample"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/stvDesc3"
        tools:visibility="visible"
        tools:text="Example: If a client's eligible funds for copy trading are 15,000 USD, the profit will be calculated as: 1,000 USD × 0.39% + 9,000 USD × 0.08%, while the remaining 5,000 USD of eligible funds will not earn any interest." />


</androidx.constraintlayout.widget.ConstraintLayout>