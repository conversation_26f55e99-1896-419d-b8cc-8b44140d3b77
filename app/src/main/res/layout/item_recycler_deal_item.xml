<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llParent"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <TextView
        android:id="@+id/tvTradeType"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="center"
        android:paddingHorizontal="5dp"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:background="@drawable/shape_c1f007fff_r100"
        tools:text="Close Only"
        tools:textColor="@color/c007fff"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvName"
        style="@style/gilroy_600"
        android:layout_width="86dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="start"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@id/tvFullName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTradeType"
        app:layout_constraintVertical_chainStyle="packed"
        tools:layout_marginVertical="3dp"
        tools:text="AUDCAD.st+" />

    <TextView
        android:id="@+id/tvFullName"
        style="@style/gilroy_400"
        android:layout_width="86dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="US Dollar/Canadian US Dollar/Canadian"
        tools:visibility="visible" />

    <!--产品和UI确认 tvBid 和 tvAsk 最长7位数字，tvBid 和 tvAsk 尺寸固定68 * 22，左右内边距1dp -->
    <TextView
        android:id="@+id/tvBid"
        style="@style/gilroy_500"
        android:layout_width="100dp"
        android:layout_height="22dp"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/c00c79c"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline1"
        app:layout_constraintStart_toStartOf="@+id/guideline1"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="-99999.99999" />

    <cn.com.vau.common.view.custom.TrendLineChart
        android:id="@+id/mTrendLineChart"
        android:layout_width="60dp"
        android:layout_height="24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline4"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvSpread"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline2"
        app:layout_constraintStart_toStartOf="@+id/guideline2"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1242" />

    <TextView
        android:id="@+id/tvAsk"
        style="@style/gilroy_500"
        android:layout_width="100dp"
        android:layout_height="22dp"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/cf44040"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline3"
        app:layout_constraintStart_toStartOf="@+id/guideline3"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="-99999.99999" />

    <TextView
        android:id="@+id/tvBidClassic"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="22dp"
        android:gravity="end|center_vertical"
        android:singleLine="true"
        android:textAlignment="viewEnd"
        android:textColor="@color/c00c79c"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline5"
        app:layout_constraintStart_toStartOf="@id/guideline4"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="99999.99999" />

    <!-- 涨幅 -->
    <TextView
        android:id="@+id/tvRose"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:gravity="end|center_vertical"
        android:minWidth="72dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="-88.88%" />

    <TextView
        android:id="@+id/tvDiff"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tvRose"
        app:layout_constraintStart_toStartOf="@id/tvRose"
        tools:text="-1242" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.371" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.509" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.646" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.478" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.725" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBuySell"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvAsk,tvBid,tvSpread"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupClassic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="mTrendLineChart,tvBidClassic,tvDiff"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>