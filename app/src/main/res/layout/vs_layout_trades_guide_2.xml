<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="HardcodedText,RtlSymmetry">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="4dp"
        app:cardBackgroundColor="?attr/color_cffffff_c262930"
        app:cardCornerRadius="10dp"
        app:cardElevation="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="@dimen/margin_horizontal_base">

            <TextView
                android:id="@+id/tvClassic"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="34dp"
                android:layout_marginTop="4dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                android:drawablePadding="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:paddingStart="20dp"
                android:text="@string/classic"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableStartCompat="@drawable/img_classic_selected" />

            <TextView
                android:id="@+id/tvBuySell"
                style="@style/gilroy_400"
                android:layout_width="match_parent"
                android:layout_height="34dp"
                android:drawablePadding="@dimen/margin_horizontal_base"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:paddingStart="20dp"
                android:text="@string/buy_sell"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableStartCompat="?attr/imgAskUnselected" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">

        <View
            android:id="@+id/viewBg"
            android:layout_width="292dp"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="-2dp"
            android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c262930_solid_cffffff_c262930_r8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivTop" />

        <ImageView
            android:id="@+id/ivTop"
            android:layout_width="16dp"
            android:layout_height="8dp"
            android:layout_marginStart="20dp"
            android:contentDescription="@string/app_name"
            android:src="?attr/imgTradesGuideTop"
            app:layout_constraintStart_toStartOf="@+id/viewBg"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="start"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:text="@string/switch_list_mode"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintEnd_toEndOf="@+id/viewBg"
            app:layout_constraintStart_toStartOf="@+id/viewBg"
            app:layout_constraintTop_toTopOf="@+id/viewBg" />

        <TextView
            android:id="@+id/tvContent"
            style="@style/gilroy_400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="start"
            android:lineSpacingMultiplier="1.5"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:text="@string/select_the_desired_list_mode"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="@+id/tvTitle"
            app:layout_constraintStart_toStartOf="@+id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <TextView
            android:id="@+id/tvNext"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:layout_marginBottom="12dp"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:paddingHorizontal="14dp"
            android:paddingVertical="3dp"
            android:text="@string/ok"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvContent" />

        <TextView
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="2/2"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvNext"
            app:layout_constraintEnd_toStartOf="@+id/tvNext"
            app:layout_constraintTop_toTopOf="@+id/tvNext" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>