<?xml version="1.0" encoding="utf-8"?><!-- 账户管理 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAccountFund"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:visibility="visible">

        <LinearLayout
            android:id="@+id/llSTAccountInfoTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/padding_horizontal_base">

                <TextView
                    android:id="@+id/tvAccountTitle"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/manual_trading"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tvSt"
                    style="@style/bold_semi_font"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_marginStart="5dp"
                    android:background="@drawable/shape_c007fff_r100"
                    android:gravity="center"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:text="Copy Trading"
                    android:textColor="@color/cffffff"
                    android:textSize="8dp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/margin_horizontal_base">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvEquityTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@string/equity"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="10dp" />

                    <TextView
                        android:id="@+id/tvEquity"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="- -"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@string/free_margin"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="10dp" />

                    <TextView
                        android:id="@+id/tvFreeMargin"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="- -"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCredit"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@string/credit"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="10dp"
                        tools:ignore="SpUsage" />

                    <TextView
                        android:id="@+id/tvCreditAmount"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="0.00"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        tools:ignore="HardcodedText,SpUsage" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llStAccountProfit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:background="@drawable/draw_main_card"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="3"
            app:layout_constraintTop_toBottomOf="@+id/llSTAccountInfoTop">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="18dp"
                android:paddingBottom="18dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="@string/pnl"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvProfit"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    tools:ignore="HardcodedText,SpUsage"
                    tools:text="-0.5" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="+"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="16dp"
                tools:ignore="HardcodedText,Smalldp,SpUsage" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="18dp"
                android:paddingBottom="18dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/floating_pnl"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp"
                    tools:ignore="SpUsage" />

                <TextView
                    android:id="@+id/tvFloatingProfit"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    tools:ignore="HardcodedText,SpUsage" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="="
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="16dp"
                tools:ignore="HardcodedText,Smalldp,SpUsage" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="18dp"
                android:paddingBottom="18dp">

                <TextView
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/total_profit"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/tvTotalProfit"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="- -"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textDirection="ltr"
                    android:textSize="14dp"
                    tools:ignore="HardcodedText" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
