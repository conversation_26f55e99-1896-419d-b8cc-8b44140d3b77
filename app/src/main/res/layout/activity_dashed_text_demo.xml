<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".demo.DashedTextDemoActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="虚线下划线TextView演示"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- 示例1：整个TextView设置虚线下划线 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例1：整个TextView设置虚线下划线"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvEnhancedDashed"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="这是一个完整的虚线下划线文本示例"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:dlDashAlignment="descent"
            app:dlDashColor="@color/colorPrimary"
            app:dlDashEnabled="true"
            app:dlDashGap="4dp"
            app:dlDashLength="6dp"
            app:dlDashOffsetFromBaseline="2dp"
            app:dlDashStrokeWidth="1dp" />

        <!-- 示例2：局部文字设置虚线下划线 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例2：局部文字设置虚线下划线"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPartialDashed"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="这里有一些普通文字，这里是虚线文字，还有更多普通文字"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 示例3：不同对齐方式的虚线效果 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例3：不同对齐方式的虚线效果"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvBaselineAlignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:text="基线对齐的虚线下划线（推荐默认）"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvDescentAlignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:padding="12dp"
            android:text="Typography gjpqy - Descent对齐避免重叠"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvBottomAlignment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="Bottom对齐提供更大间距效果"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 示例4：自定义虚线样式 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例4：自定义虚线样式"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvCustomStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="自定义样式：粗虚线、长间隔、圆角端点"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 示例5：使用DSL构建复杂文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例5：使用DSL构建复杂文本"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDSLExample"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 示例6：多个目标文字设置虚线 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例6：多个目标文字设置虚线"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvMultipleTargets"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="第一个虚线文字，第二个虚线文字，第三个虚线文字"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 示例7：简单的TextView点击演示 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="示例7：简单的TextView点击演示"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvClickConflict"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:text="这是一个带虚线的TextView示例，点击整个TextView会触发点击事件"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 使用说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@color/colorAccent"
            android:padding="16dp"
            android:text="使用说明：\n\n1. DashLineTextView：支持整个TextView设置虚线下划线\n2. 扩展函数：支持为普通TextView的局部文字设置虚线\n3. 对齐方式：支持基线、descent、bottom三种对齐方式\n4. 自定义样式：支持设置虚线颜色、粗细、长度、间隔等\n5. DSL构建：支持使用DSL风格构建复杂的虚线文本\n6. 点击事件：通过TextView的setOnClickListener设置整体点击"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

</ScrollView>
