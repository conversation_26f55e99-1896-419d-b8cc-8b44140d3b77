<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <View
        android:id="@+id/viewLine"
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:background="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r4" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/glossary"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toStartOf="@id/ivClose"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <TextView
                android:id="@+id/tvLink"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/more"
                android:textColor="@color/ce35728"
                android:textSize="14dp"
                android:visibility="gone"
                tools:ignore="SpUsage"
                tools:visibility="visible" />

            <View
                android:layout_width="0dp"
                android:layout_height="24dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
