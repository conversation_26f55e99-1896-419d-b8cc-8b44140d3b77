<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
    android:padding="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawablePadding="4dp"
        android:gravity="start|center_vertical"
        android:paddingVertical="4dp"
        android:paddingEnd="4dp"
        android:text="@string/live_trading_account_limit"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeposit"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/deposit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvDepositNumberDesc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvDepositNumber" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDepositNumber"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/unlimited"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDepositNumberDesc"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="4dp"
        android:text="@string/fiat_only"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvDepositNumber" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWithdrawal"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/withdraw"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvWithdrawalNumberDesc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvWithdrawalNumber" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWithdrawalNumber"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/unlimited"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvDepositNumberDesc" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWithdrawalNumberDesc"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="4dp"
        android:text="@string/fiat_only"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvWithdrawalNumber"
        tools:text="$400 Fiat + $600 Crypto" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupDeposit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvDeposit,tvDepositNumber,tvDepositNumberDesc" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupWithdraw"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvWithdrawal,tvWithdrawalNumber,tvWithdrawalNumberDesc" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTriangle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvWithdrawalNumberDesc"
        app:srcCompat="@drawable/img_kyc_feature_triangle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBottom"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_c1f007fff_r6"
        android:lineSpacingExtra="4dp"
        android:padding="@dimen/padding_horizontal_base"
        android:text="@string/note_this_limit_account_activity"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintTop_toBottomOf="@+id/ivTriangle" />

</androidx.constraintlayout.widget.ConstraintLayout>