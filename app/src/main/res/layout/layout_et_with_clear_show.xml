<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et"
        style="@style/regular_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:paddingEnd="30dp"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="ceshiceshiceshiceshiceshiceshiceshiceshiceshiceshiceshiceshiceshi" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClear"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginEnd="4dp"
        android:padding="4dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivShowPwd"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="8dp"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowPwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:contentDescription="@string/app_name"
        android:padding="4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/et"
        app:layout_constraintEnd_toEndOf="@+id/et"
        app:layout_constraintTop_toTopOf="@+id/et"
        app:srcCompat="@drawable/draw_bitmap2_password_hide_c731e1e1e_c61ffffff"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>