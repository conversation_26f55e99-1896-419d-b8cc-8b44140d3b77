<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPending"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_solid_c0a1e1e1e_c262930_r6"
        android:gravity="center"
        android:text="@string/fill_at_market_price"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ViewStub
            android:id="@+id/vsAtPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout="@layout/vs_order_input"
            tools:visibility="visible" />

        <ViewStub
            android:id="@+id/vsStopLimit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_weight="1"
            android:layout="@layout/vs_order_input"
            tools:visibility="visible" />
    </LinearLayout>

</merge>