<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="8dp"
        android:backgroundTint="@color/transparent"
        android:background="@drawable/draw_shape_c731e1e1e_c61ffffff_r100"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clUpload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivAddIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_source2_add"
                app:layout_constraintBottom_toBottomOf="parent"
                android:tint="?attr/color_c731e1e1e_c61ffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivUploadPreview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:visibility="gone"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:padding="5dp"
        android:src="@drawable/icon2_close_circle_16x16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvPhotoName"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@id/cardView"
        app:layout_constraintStart_toStartOf="@id/cardView"
        app:layout_constraintTop_toBottomOf="@id/cardView"
        tools:text="Photo 1"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>