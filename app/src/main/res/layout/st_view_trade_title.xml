<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:contentDescription="@string/app_name"
        android:src="?attr/imgLogoMark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvAccountStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="Copy Trading"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@id/ivLogo"
        app:layout_constraintStart_toEndOf="@id/ivLogo"
        app:layout_constraintTop_toTopOf="@id/ivLogo"
        tools:ignore="HardcodedText,SpUsage"
        tools:style="@style/gilroy_600" />

    <TextView
        android:id="@+id/tvAccountId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@id/tvAccountStatus"
        app:layout_constraintStart_toEndOf="@id/tvAccountStatus"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:text="*********" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="3dp"
        android:src="@drawable/bitmap2_expand_down12x12_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/tvAccountStatus"
        app:layout_constraintStart_toEndOf="@id/tvAccountId"
        app:layout_constraintTop_toTopOf="@id/tvAccountStatus" />

    <TextView
        android:id="@+id/tvConnecting"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:background="?attr/mainLayoutBg"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:style="@style/gilroy_500"
        tools:text="Connecting..." />

    <ImageView
        android:id="@+id/ivMessage"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:contentDescription="@string/app_name"
        android:padding="12dp"
        android:src="?attr/icon1Msg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivSettingTrade"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivRedDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@color/ce35728"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivMessage"
        app:layout_constraintCircle="@id/ivMessage"
        app:layout_constraintCircleAngle="45"
        app:layout_constraintCircleRadius="11dp"
        app:layout_constraintEnd_toEndOf="@id/ivMessage"
        app:layout_constraintStart_toStartOf="@id/ivMessage"
        app:layout_constraintTop_toTopOf="@id/ivMessage"
        app:shapeAppearanceOverlay="@style/circleImageStyle"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivSettingTrade"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:contentDescription="@string/app_name"
        android:padding="12dp"
        android:src="?attr/icon1OrderSetting"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</merge>