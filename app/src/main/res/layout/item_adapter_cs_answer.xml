<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/padding_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@id/ivArrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="What will happen to an open position before the market closes?" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:srcCompat="@drawable/draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="All open contracts at the time of the close will be automatically saved to the next business day. All customers will be paid or receive the corresponding interest or swap when the market opens the following day."
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>