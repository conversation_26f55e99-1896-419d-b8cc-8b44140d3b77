<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.util.widget.HeaderBar
            android:id="@+id/mHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/mSmartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/tvSl"
            app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlHead"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_top_title">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/ivHead"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:contentDescription="@string/app_name"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/ic_launcher"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:shapeAppearanceOverlay="@style/roundImageStyle10" />

                    <TextView
                        android:id="@+id/tvName"
                        style="@style/bold_semi_font"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:maxLines="1"
                        android:paddingHorizontal="8dp"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="16dp"
                        app:layout_constraintBottom_toTopOf="@+id/tvId"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/ivHead"
                        app:layout_constraintTop_toTopOf="@+id/ivHead"
                        tools:text="Follow signal 1 Follow signal 1 Follow signal 1 Follow signal 1" />

                    <TextView
                        android:id="@+id/tvId"
                        style="@style/gilroy_400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textDirection="ltr"
                        android:textSize="12dp"
                        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
                        app:layout_constraintStart_toStartOf="@+id/tvName"
                        app:layout_constraintTop_toBottomOf="@+id/tvName"
                        tools:text="Strategy ID：123456" />

                    <TextView
                        android:id="@+id/tvReturnTitle"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="@dimen/margin_vertical_base"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:maxLines="1"
                        android:text="@string/signal_filter_detail_return"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_hide_v33"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/ivHead" />

                    <TextView
                        android:id="@+id/tvReturn"
                        style="@style/gilroy_600"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="4dp"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:maxLines="1"
                        android:textAlignment="viewStart"
                        android:textColor="@color/c00c79c"
                        android:textDirection="ltr"
                        android:textSize="18dp"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_hide_v33"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvReturnTitle"
                        tools:text="200.00" />

                    <LinearLayout
                        android:id="@+id/llProfitSharingTitle"
                        android:layout_width="0px"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_hide_v66"
                        app:layout_constraintStart_toEndOf="@+id/guideline_hide_v33"
                        app:layout_constraintTop_toTopOf="@+id/tvReturnTitle">

                        <TextView
                            android:id="@+id/tvProfitSharingTitle"
                            style="@style/gilroy_500"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="6dp"
                            android:drawablePadding="4dp"
                            android:ellipsize="end"
                            android:gravity="center_horizontal"
                            android:maxLines="1"
                            android:text="@string/profit_sharing"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvProfitSharing"
                        style="@style/gilroy_600"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:ellipsize="end"
                        android:gravity="center_horizontal"
                        android:maxLines="1"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textDirection="ltr"
                        android:textSize="18dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvReturn"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_hide_v66"
                        app:layout_constraintStart_toEndOf="@+id/guideline_hide_v33"
                        tools:ignore="MissingConstraints"
                        tools:text="25.00%" />

                    <TextView
                        android:id="@+id/tvSettlementTitle"
                        style="@style/gilroy_500"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:drawableEnd="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:maxLines="1"
                        android:text="@string/settlement"
                        android:textAlignment="viewEnd"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvReturnTitle"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/guideline_hide_v66"
                        tools:ignore="UseCompatTextViewDrawableXml" />

                    <TextView
                        android:id="@+id/tvSettlement"
                        style="@style/gilroy_600"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textAlignment="viewEnd"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="18dp"
                        app:layout_constraintBaseline_toBaselineOf="@+id/tvReturn"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/guideline_hide_v66"
                        tools:ignore="MissingConstraints"
                        tools:text="Weekly" />

                    <View
                        android:id="@+id/headOffLine"
                        style="@style/cut_off_line"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="12dp"
                        app:layout_constraintTop_toBottomOf="@+id/tvReturn" />

                    <TextView
                        android:id="@+id/tvMoneyAllocatedTitle"
                        style="@style/bold_semi_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="12dp"
                        android:text="@string/money_allocated"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="18dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/headOffLine" />

                    <cn.com.vau.common.view.CurrencyFormatEditText
                        android:id="@+id/etMoney"
                        style="@style/gilroy_500"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                        android:ellipsize="end"
                        android:gravity="center_vertical|start"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingStart="@dimen/padding_horizontal_base"
                        android:paddingEnd="48dp"
                        android:singleLine="true"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="14dp"
                        app:layout_constraintTop_toBottomOf="@+id/tvMoneyAllocatedTitle"
                        tools:ignore="Autofill,LabelFor"
                        tools:text="50.00" />

                    <TextView
                        android:id="@+id/tvCurrency"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:maxLength="10"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@+id/etMoney"
                        app:layout_constraintEnd_toEndOf="@+id/etMoney"
                        app:layout_constraintTop_toTopOf="@+id/etMoney"
                        tools:text="USD" />

                    <TextView
                        android:id="@+id/tvAvailableInvestment"
                        style="@style/gilroy_400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="8dp"
                        android:drawablePadding="4dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textDirection="ltr"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/etMoney"
                        tools:text="Max Removable Investment: 1000.00 USD" />

                    <TextView
                        android:id="@+id/tvUsedCredit"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="12dp"
                        android:drawablePadding="4dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvAvailableInvestment"
                        tools:text="@string/used_credit" />

                    <TextView
                        android:id="@+id/tvUsedCreditCurrency"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@id/tvUsedCredit"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tvUsedCredit"
                        tools:text="USD" />

                    <TextView
                        android:id="@+id/tvUsedCreditAmount"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@id/tvUsedCreditCurrency"
                        app:layout_constraintEnd_toStartOf="@id/tvUsedCreditCurrency"
                        app:layout_constraintTop_toTopOf="@id/tvUsedCreditCurrency"
                        tools:text="5.00" />

                    <TextView
                        android:id="@+id/tvAvailableCredit"
                        style="@style/gilroy_400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:layout_marginTop="8dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textDirection="ltr"
                        android:textSize="12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvUsedCredit"
                        tools:text="Available Credit: 1000.00 USD" />

                    <TextView
                        android:id="@+id/tvUsedBalance"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginTop="12dp"
                        android:drawablePadding="4dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvAvailableCredit"
                        tools:text="@string/used_balance" />

                    <TextView
                        android:id="@+id/tvUsedBalanceCurrency"
                        style="@style/gilroy_600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@id/tvUsedBalance"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tvUsedBalance"
                        tools:text="USD" />

                    <TextView
                        android:id="@+id/tvUsedBalanceAmount"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@id/tvUsedBalanceCurrency"
                        app:layout_constraintEnd_toStartOf="@id/tvUsedBalanceCurrency"
                        app:layout_constraintTop_toTopOf="@id/tvUsedBalanceCurrency"
                        tools:text="5.00" />

                    <TextView
                        android:id="@+id/tvAvailableBalance"
                        style="@style/gilroy_400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:layout_marginTop="8dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textDirection="ltr"
                        android:textSize="12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvUsedBalance"
                        tools:text="Available Balance: 1000.00 USD" />

                    <TextView
                        android:id="@+id/tvGSUserBalanceTip"
                        style="@style/gilroy_400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:layout_marginTop="8dp"
                        android:drawablePadding="4dp"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        android:visibility="gone"
                        android:textAlignment="viewStart"
                        android:gravity="start"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvAvailableBalance"
                        tools:text="@string/to_qualify_for_the_x_other_currencies"
                        tools:visibility="visible" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_hide_v33"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.33" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_hide_v66"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.66" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.core.widget.NestedScrollView>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <TextView
            android:id="@+id/tvSl"
            style="@style/gilroy_600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:gravity="center"
            android:paddingBottom="16dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@+id/tvNext"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Stop Loss: 60%" />

        <TextView
            android:id="@+id/tvNext"
            style="@style/main_bottom_button_theme"
            android:layout_width="match_parent"
            android:layout_height="@dimen/height_button_main"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/update"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
