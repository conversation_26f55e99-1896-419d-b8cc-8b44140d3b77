<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:paddingVertical="16dp">

    <cn.com.vau.common.view.login.LoginInputMobileView
        android:id="@+id/mobileView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.login.LoginInputContentView
        android:id="@+id/emailView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:visibility="gone"
        app:icv_hintText="@string/email"
        app:icv_isInputEmail="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="mobileView,emailView" />

    <cn.com.vau.common.view.login.LoginInputPwdView
        android:id="@+id/pwdView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="16dp"
        app:ipv_hintText="@string/_8_16_characters"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrier" />

    <TextView
        android:id="@+id/tvPwdErrTips"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/the_password_format_is_invalid"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/pwdView"
        tools:visibility="visible" />

    <include
        android:id="@+id/layoutPasswordCheck"
        layout="@layout/include_password_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPwdErrTips"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvReferralTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingVertical="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutPasswordCheck"
        tools:text="Referral (optional)" />

    <ImageView
        android:id="@+id/ivReferral"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginStart="4dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/draw_bitmap2_triangle_down_tab_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvReferralTitle"
        app:layout_constraintStart_toEndOf="@+id/tvReferralTitle"
        app:layout_constraintTop_toTopOf="@+id/tvReferralTitle" />

    <cn.com.vau.common.view.login.LoginInputContentView
        android:id="@+id/referralView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:visibility="gone"
        app:icv_hintText="@string/referral_code_email_account_no"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvReferralTitle"
        tools:visibility="visible" />

    <CheckBox
        android:id="@+id/cbAgreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:background="@null"
        android:button="@drawable/select_checkbox_agreement_new"
        android:checked="true"
        android:paddingStart="0dp"
        android:paddingEnd="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/referralView"
        app:layout_goneMarginTop="16dp" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvAgreementTip"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/gilroy_regular"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/by_click_sign_up_you_privacy_policy"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cbAgreement"
        app:layout_constraintTop_toTopOf="@id/cbAgreement" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/login_tvNext_style"
        android:layout_marginTop="24dp"
        android:text="@string/sign_up"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAgreementTip" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvLogin"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:paddingVertical="8dp"
        android:paddingStart="0px"
        android:paddingEnd="5dp"
        android:text="@string/already_have_an_account_login"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNext" />
</androidx.constraintlayout.widget.ConstraintLayout>