<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:maxWidth="280dp"
    android:minHeight="172dp"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:paddingBottom="12dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:text="@string/allow_x_to_verification_function"
        android:textColor="@color/ce35728"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:lineSpacingExtra="8dp"
        android:text="@string/x_sumsub_dialog_detail"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <CheckBox
        android:id="@+id/cbAgree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/icon2_cb_tick_circle_c15b374"
        android:checked="true"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="@id/tvAgree"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvAgree" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAgree"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="@string/i_agree_to_identity_verification"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cbAgree"
        app:layout_constraintTop_toBottomOf="@+id/tvDetail" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLeft"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="30dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="10dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_stroke_c731e1e1e_c61ffffff_r8"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="@string/back"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@+id/tvRight"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAgree"
        app:layout_goneMarginBottom="8dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRight"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="30dp"
        android:layout_weight="1"
        android:background="@drawable/shape_ce35728_r8"
        android:gravity="center"
        android:lineSpacingExtra="10dp"
        android:maxLines="1"
        android:padding="10dp"
        android:text="@string/agree"
        android:textColor="@color/cffffff"
        android:textSize="16dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvLeft"
        app:layout_constraintTop_toTopOf="@+id/tvLeft" />
</androidx.constraintlayout.widget.ConstraintLayout>