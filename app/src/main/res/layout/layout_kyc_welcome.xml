<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle1"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="start"
        android:text="@string/features"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle2"
        app:layout_constraintEnd_toStartOf="@+id/tvTitle2"
        app:layout_constraintHorizontal_weight="137"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle2"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingVertical="@dimen/padding_horizontal_base"
        android:text="@string/before_verification"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/tvTitle3"
        app:layout_constraintHorizontal_weight="104"
        app:layout_constraintStart_toEndOf="@id/tvTitle1"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle3"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/after_verification"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="104"
        app:layout_constraintStart_toEndOf="@id/tvTitle2"
        app:layout_constraintTop_toTopOf="@id/tvTitle2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDeposit"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base_new"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:text="@string/deposit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="?attr/imgWelcomeDeposit"
        app:layout_constraintEnd_toStartOf="@+id/ivDepositBefore"
        app:layout_constraintStart_toStartOf="@id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@id/tvTitle2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLiveTrading"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base_new"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:text="@string/live_trading"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="?attr/imgWelcomeTrades"
        app:layout_constraintEnd_toStartOf="@+id/ivLiveTradingBefore"
        app:layout_constraintStart_toStartOf="@id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@id/tvDeposit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWithdrawal"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base_new"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="8dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:text="@string/withdraw"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="?attr/imgWelcomeWithdraw"
        app:layout_constraintEnd_toStartOf="@+id/ivWithdrawBefore"
        app:layout_constraintStart_toStartOf="@id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@id/tvLiveTrading" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDepositBefore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvDeposit"
        app:layout_constraintEnd_toEndOf="@id/tvTitle2"
        app:layout_constraintStart_toStartOf="@id/tvTitle2"
        app:layout_constraintTop_toTopOf="@+id/tvDeposit"
        app:srcCompat="?attr/icon2CloseCircle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLiveTradingBefore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvLiveTrading"
        app:layout_constraintEnd_toEndOf="@id/tvTitle2"
        app:layout_constraintStart_toStartOf="@id/tvTitle2"
        app:layout_constraintTop_toTopOf="@+id/tvLiveTrading"
        app:srcCompat="?attr/icon2CloseCircle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivWithdrawBefore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvWithdrawal"
        app:layout_constraintEnd_toEndOf="@id/tvTitle2"
        app:layout_constraintStart_toStartOf="@id/tvTitle2"
        app:layout_constraintTop_toTopOf="@+id/tvWithdrawal"
        app:srcCompat="?attr/icon2CloseCircle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvDeposit"
        app:layout_constraintEnd_toEndOf="@id/tvTitle3"
        app:layout_constraintStart_toStartOf="@id/tvTitle3"
        app:layout_constraintTop_toTopOf="@+id/tvDeposit"
        app:srcCompat="@drawable/icon2_cb_tick_circle_c15b374" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvLiveTrading"
        app:layout_constraintEnd_toEndOf="@id/tvTitle3"
        app:layout_constraintStart_toStartOf="@id/tvTitle3"
        app:layout_constraintTop_toTopOf="@+id/tvLiveTrading"
        app:srcCompat="@drawable/icon2_cb_tick_circle_c15b374" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvWithdrawal"
        app:layout_constraintEnd_toEndOf="@id/tvTitle3"
        app:layout_constraintStart_toStartOf="@id/tvTitle3"
        app:layout_constraintTop_toTopOf="@+id/tvWithdrawal"
        app:srcCompat="@drawable/icon2_cb_tick_circle_c15b374" />

</androidx.constraintlayout.widget.ConstraintLayout>