<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp">

    <TextView
        android:id="@+id/tvProductName"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="USDCAD" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProductName" />

</androidx.constraintlayout.widget.ConstraintLayout>