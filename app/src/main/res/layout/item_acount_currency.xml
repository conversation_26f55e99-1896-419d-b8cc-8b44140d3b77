<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/llAccountType"
        android:layout_width="0dp"
        android:layout_height="65dp"
        android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/ivAccountCurrency"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:contentDescription="@string/app_name"
            android:paddingTop="12dp"
            android:paddingBottom="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/shape_placeholder"/>

        <TextView
            android:id="@+id/tvCurrency"
            style="@style/regular_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivIcon"
            tools:text="USBJPY" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>