<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/voucher_details"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/ctlCoupon"
        layout="@layout/item_recycler_coupon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <TextView
        android:id="@+id/tvAccountManagerDesc"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingStart="@dimen/margin_horizontal_base"
        android:paddingTop="16dp"
        android:paddingEnd="120dp"
        android:paddingBottom="16dp"
        android:text="@string/account"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ctlCoupon" />

    <TextView
        android:id="@+id/tvAccountManager"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawableEnd="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        android:drawablePadding="8dp"
        android:gravity="center_vertical|end"
        android:maxWidth="160dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvAccountManagerDesc"
        app:layout_constraintEnd_toEndOf="@+id/tvAccountManagerDesc"
        app:layout_constraintTop_toTopOf="@+id/tvAccountManagerDesc"
        tools:text="24526124fdsfsdfsdfsd5" />

    <TextView
        android:id="@+id/tvTransferCountDesc"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:paddingStart="@dimen/margin_horizontal_base"
        android:paddingTop="16dp"
        android:paddingEnd="20dp"
        android:paddingBottom="16dp"
        android:text="@string/transfer_amount"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAccountManagerDesc" />

    <TextView
        android:id="@+id/tvCurrency"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTransferCountDesc"
        app:layout_constraintEnd_toEndOf="@+id/tvTransferCountDesc"
        app:layout_constraintTop_toTopOf="@+id/tvTransferCountDesc"
        tools:text="AUD" />

    <TextView
        android:id="@+id/tvTransferCount"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:gravity="center_vertical"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTransferCountDesc"
        app:layout_constraintEnd_toStartOf="@+id/tvCurrency"
        app:layout_constraintTop_toTopOf="@+id/tvTransferCountDesc"
        tools:text="1000" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/submit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.26" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v70"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.73" />

</androidx.constraintlayout.widget.ConstraintLayout>