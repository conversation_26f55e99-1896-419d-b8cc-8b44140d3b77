<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/margin_horizontal_base"
    android:paddingEnd="@dimen/margin_horizontal_base"
    tools:context=".page.user.openAccount.OpenAccountSuccessAsicActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBack"
        android:layout_width="26dp"
        android:layout_height="28dp"
        android:layout_marginTop="40dp"
        android:paddingStart="0dp"
        android:paddingTop="5dp"
        android:paddingEnd="4dp"
        android:paddingBottom="5dp"
        android:scaleType="fitStart"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

<!--    保证内容在父布局居中-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/mImageFilterView"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:src="?attr/imgAlertOk"
            app:layout_constraintBottom_toTopOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center_horizontal"
            android:text="@string/successful_submission"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tvOpenAccountHint"
            style="@style/gilroy_500"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="10dp"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:text="@string/your_live_account_application_it_be_approved"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginBottom="@dimen/margin_bottom_next_to_layout"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="@string/deposit_now" />

</androidx.constraintlayout.widget.ConstraintLayout>