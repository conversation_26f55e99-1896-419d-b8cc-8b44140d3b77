<?xml version="1.0" encoding="utf-8"?><!--【跟单/非跟单】 订单持仓 / K线底部持仓 / 策略订单详情Positions / 策略订单详情Pending Close -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/padding_card_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProdName"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:text="VAU-TEST" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivKLine"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="2dp"
        android:contentDescription="@string/app_name"
        android:padding="2dp"
        android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toEndOf="@+id/tvProdName"
        app:layout_constraintTop_toTopOf="@+id/tvProdName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDelete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r4"
        android:gravity="center_horizontal"
        android:minWidth="52dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/close__position"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:ignore="SpUsage"
        tool:layout_constraintEnd_toStartOf="@id/tvPnlTitle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShare"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="6dp"
        android:padding="6dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvProdName"
        app:srcCompat="@drawable/draw_bitmap2_share_c1e1e1e_cebffffff"
        tool:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOrderType"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/shape_c1f00c79c_r100"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:textColor="@color/c00c79c"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="@id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:text="Buy" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOrderId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvOrderType"
        app:layout_constraintStart_toEndOf="@+id/tvOrderType"
        app:layout_constraintTop_toTopOf="@+id/tvOrderType"
        tool:text="#4535356565" />

    <!-- 手数 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVolumeTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/volume"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@id/tvProdName"
        app:layout_constraintTop_toTopOf="@id/tvOpenPriceTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVolume"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tvVolumeTitle"
        app:layout_constraintTop_toBottomOf="@id/tvVolumeTitle"
        tool:text="0.20 Lots" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOpenPriceTitle"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/open_price"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v66"
        app:layout_constraintStart_toEndOf="@id/guideline_v33"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderType" />

    <!-- 开单价 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOpenPrice"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toStartOf="@id/guideline_v66"
        app:layout_constraintStart_toEndOf="@id/guideline_v33"
        app:layout_constraintTop_toBottomOf="@+id/tvOpenPriceTitle"
        tool:text="1.2345" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentPriceTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:text="@string/current_price"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenPriceTitle"
        app:layout_constraintEnd_toEndOf="@id/tvPnl" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentPrice"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOpenPrice"
        app:layout_constraintEnd_toEndOf="@id/tvPnl"
        tool:text="1.3456" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPnlTitle"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="2dp"
        android:ellipsize="end"
        android:gravity="center_vertical|end"
        android:maxLines="1"
        android:text="@string/pnl"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvProdName"
        app:layout_constraintEnd_toStartOf="@id/ivShare"
        app:layout_constraintTop_toTopOf="@id/tvProdName"
        app:layout_goneMarginEnd="12dp" />

    <!-- 浮动盈亏 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPnl"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="@color/ce35728"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
        tool:text="-0.12345678" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="8dp"
        android:layout_marginTop="12dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvOpenPrice" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.33" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.66" />

</androidx.constraintlayout.widget.ConstraintLayout>
