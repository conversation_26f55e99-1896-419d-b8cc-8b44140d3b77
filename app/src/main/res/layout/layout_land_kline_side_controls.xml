<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_width="@dimen/kline_land_side_width"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDrawing"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/guidelineH50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewLine"
        android:layout_width="20dp"
        android:layout_height="1dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/guidelineH50"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineH50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivKeepDraw"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/select_kline_draw_tools_continue"
        app:layout_constraintBottom_toTopOf="@id/ivShowDrawView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/rvDrawing"
        app:layout_constraintTop_toBottomOf="@id/guidelineH50" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowDrawView"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="?attr/icon2KlineDrawToolsShowSelect"
        app:layout_constraintBottom_toTopOf="@id/ivClean"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/rvDrawing"
        app:layout_constraintTop_toBottomOf="@id/ivKeepDraw" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClean"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="?attr/icon2KlineDrawToolsClean"
        app:layout_constraintBottom_toTopOf="@id/ivShowMain"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/rvDrawing"
        app:layout_constraintTop_toBottomOf="@id/ivShowDrawView" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowMain"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="?attr/icon2KlineDrawToolsMain"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/rvDrawing"
        app:layout_constraintTop_toBottomOf="@id/ivClean" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpDrawing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="rvDrawing,ivKeepDraw,ivClean,ivShowDrawView,ivShowMain,viewLine"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vsMain"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/layout_land_kline_side_main"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</merge>