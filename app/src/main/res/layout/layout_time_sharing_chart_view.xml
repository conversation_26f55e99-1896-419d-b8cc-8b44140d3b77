<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingConstraints"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/splitView1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvSymbol"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="25dp"
        android:gravity="center_vertical|start"
        android:paddingStart="28dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/splitView2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/splitView1"
        tools:text="USDCAD+ Chart" />

    <View
        android:id="@+id/splitView2"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="25dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        android:visibility="invisible"
        app:layout_constraintTop_toBottomOf="@id/splitView1"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:src="@drawable/bitmap2_expand_up12x12_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/splitView2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/splitView1" />

    <ViewStub
        android:id="@+id/mVsChart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/layout_time_sharing_chart"
        app:layout_constraintTop_toBottomOf="@id/splitView2"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/mVsNoData"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout="@layout/vs_layout_no_data"
        app:layout_constraintTop_toBottomOf="@id/splitView2"
        app:ndv_icon="?attr/icNoConnection"
        tools:visibility="visible" />

</merge>