<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="HardcodedText">

    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/frequently_used"
        tools:textColor="?attr/color_c1e1e1e_cebffffff" />

    <TextView
        android:id="@+id/tvOpen"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:background="@drawable/shape_ce35728_r100"
        android:paddingHorizontal="4dp"
        android:paddingVertical="1dp"
        android:text="Open"
        android:textColor="@color/cffffff"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvTab"
        app:layout_constraintStart_toEndOf="@+id/tvTab"
        app:layout_constraintTop_toTopOf="@+id/tvTab"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>