<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/set_password" />

    <TextView
        android:id="@+id/tvTopProgress"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:drawablePadding="6dp"
        android:text="@string/set_your_password"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/mNestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:paddingBottom="30dp">

            <TextView
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/first_name"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputContentView
                android:id="@+id/firstNameView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                app:icv_hintText="@string/first_name" />

            <TextView
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                android:text="@string/last_name"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputContentView
                android:id="@+id/lastNameView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                app:icv_hintText="@string/last_name" />

            <TextView
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                android:text="@string/email"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputContentView
                android:id="@+id/emailView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                app:icv_hintText="@string/email"
                app:icv_isInputEmail="true" />

            <TextView
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                android:text="@string/password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/pwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="@dimen/margin_vertical_base_new"
                app:ipv_hintText="@string/_8_16_characters" />

            <include
                android:id="@+id/layoutPasswordCheck"
                layout="@layout/include_password_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base_new" />

            <TextView
                android:id="@+id/tvNext"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="@dimen/height_button_main"
                android:layout_marginTop="24dp"
                android:background="@drawable/selector_login_btn_bg"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/submit"
                android:textColor="@color/selector_login_btn_tv_color"
                android:textSize="16dp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>