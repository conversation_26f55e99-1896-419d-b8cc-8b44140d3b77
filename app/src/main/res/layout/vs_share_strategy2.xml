<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle1"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/return_ytd"
        android:textColor="@color/c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail1"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="4dp"
        android:textDirection="ltr"
        android:textSize="36dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle1"
        tools:text="+3405.98%"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/max_monthly_return"
        android:textColor="@color/c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@+id/tvDetail1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="4dp"
        android:textDirection="ltr"
        android:textSize="36dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitle2"
        app:layout_constraintTop_toBottomOf="@id/tvTitle2"
        tools:text="+3405.98%"
        tools:textColor="@color/c00c79c" />

</androidx.constraintlayout.widget.ConstraintLayout>