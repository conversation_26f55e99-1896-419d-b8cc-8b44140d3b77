<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/margin_horizontal_base">

    <androidx.cardview.widget.CardView
        android:id="@+id/mCardView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="369:114"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.youth.banner.Banner
            android:id="@+id/mBanner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="0.5dp"
            android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
            app:banner_indicator_height="0dp"
            app:banner_indicator_normal_width="0dp"
            app:banner_loop_time="3000"
            app:banner_radius="10dp" />

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContentTitle"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/cffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/mCardView"
        tools:text="Lorem ipsum dolor sit amet consectetur, adipiscing elit sed erat." />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="4dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/cffffff"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintBottom_toTopOf="@+id/tvContentTitle"
        tools:text="USDCAD" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/cffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        tools:text="22/10/2021 01:45" />

    <cn.com.vau.common.view.custom.BannerIndicatorView
        android:id="@+id/mIndicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="@+id/mCardView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>