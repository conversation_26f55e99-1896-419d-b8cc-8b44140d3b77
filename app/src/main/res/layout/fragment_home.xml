<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_title_bar"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:contentDescription="@string/app_name"
            android:src="?attr/imgLogoMark"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAccountStatus"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="@id/ivLogo"
            app:layout_constraintStart_toEndOf="@id/ivLogo"
            app:layout_constraintTop_toTopOf="@id/ivLogo"
            tools:text="Demo" />

        <TextView
            android:id="@+id/tvAccountId"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tvAccountStatus"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvAccountStatus"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="*********" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="4dp"
            android:layout_marginTop="3dp"
            android:src="@drawable/bitmap2_expand_down12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvAccountStatus"
            app:layout_constraintStart_toEndOf="@id/tvAccountId"
            app:layout_constraintTop_toTopOf="@id/tvAccountStatus" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clCollapsingAccountInfo"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:background="?attr/mainLayoutBg"
            android:clickable="true"
            android:focusable="true"
            android:visibility="visible"
            app:layout_constraintEnd_toStartOf="@id/ivProductSearch"
            app:layout_constraintStart_toEndOf="@id/ivLogo"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvCollapseAccountType"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:background="@drawable/shape_ce35728_r100"
                android:paddingHorizontal="6dp"
                android:paddingVertical="1.8dp"
                android:text="Demo"
                android:textColor="@color/cffffff"
                android:textSize="6.6dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvCollapseAccountId"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapseAccountType"
                app:layout_constraintStart_toEndOf="@id/tvCollapseAccountType"
                app:layout_constraintTop_toTopOf="@id/tvCollapseAccountType"
                tools:text="*********" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivCollapseArrow"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="4dp"
                android:src="@drawable/bitmap2_expand_down12x12_c731e1e1e_c61ffffff"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapseAccountId"
                app:layout_constraintStart_toEndOf="@id/tvCollapseAccountId"
                app:layout_constraintTop_toTopOf="@id/tvCollapseAccountId" />

            <TextView
                android:id="@+id/tvCollapsePnl"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/floating_pnl"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvCollapseAccountType" />

            <TextView
                android:id="@+id/tvCollapsePnlValue"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/tvCollapsePnl"
                app:layout_constraintStart_toEndOf="@id/tvCollapsePnl"
                app:layout_constraintTop_toTopOf="@id/tvCollapsePnl"
                tools:text="+25324.23" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvConnecting"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:background="?attr/mainLayoutBg"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivProductSearch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Connecting..." />

        <ImageView
            android:id="@+id/ivMessage"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            android:padding="12dp"
            android:src="?attr/icon1Msg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivRedDot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@color/ce35728"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/ivMessage"
            app:layout_constraintCircle="@id/ivMessage"
            app:layout_constraintCircleAngle="45"
            app:layout_constraintCircleRadius="11dp"
            app:layout_constraintEnd_toEndOf="@id/ivMessage"
            app:layout_constraintStart_toStartOf="@id/ivMessage"
            app:layout_constraintTop_toTopOf="@id/ivMessage"
            app:shapeAppearanceOverlay="@style/circleImageStyle"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivProductSearch"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:contentDescription="@string/app_name"
            android:padding="12dp"
            android:src="@drawable/draw_bitmap1_search_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivMessage"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:elevation="0dp">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsingToolbarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:contentScrim="@color/transparent"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/topInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_top_title"
                    android:layout_marginBottom="8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="parent"
                    tools:ignore="MissingConstraints">

                    <!-- 账户KYC验证 -->
                    <ViewStub
                        android:id="@+id/viewStubKycGuide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inflatedId="@+id/inflatedKycLayout"
                        android:layout="@layout/layout_kyc_verify_guide"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible" />

                    <cn.com.vau.common.view.TradeGuideView
                        android:id="@+id/mTradeGuideView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/inflatedKycLayout"
                        tools:ignore="UnknownId" />

                    <!-- 广告位 -->
                    <com.youth.banner.Banner
                        android:id="@+id/mBanner"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        app:banner_indicator_height="0dp"
                        app:banner_indicator_normal_width="0dp"
                        app:banner_loop_time="4000"
                        app:layout_constraintDimensionRatio="h,393:72"
                        app:layout_constraintTop_toBottomOf="@id/mTradeGuideView"
                        tools:visibility="visible" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivBannerClose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:padding="7dp"
                        android:src="?attr/icon2CloseCircle12x12"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/mBanner"
                        app:layout_constraintTop_toTopOf="@id/mBanner"
                        tools:visibility="visible" />

                    <cn.com.vau.common.view.custom.IndicatorNumView
                        android:id="@+id/indicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:layout_marginBottom="4dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/mBanner"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:visibility="visible" />

                    <View
                        style="@style/signal_profile_cut_off_line"
                        android:layout_width="match_parent"
                        app:layout_constraintTop_toBottomOf="@id/mBanner" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--设置<androidx.appcompat.widget.Toolbar>标签或<ImageView>标签的属性
                layout_collapseMode="pin"时，可以达到折叠时组件固定在屏幕边缘不会滑出屏幕的效果-->
            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <include
            android:id="@+id/fragment_deal"
            layout="@layout/include_fragment_deal_bottom"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        <!--        <include-->
        <!--            android:id="@+id/market_maintenance"-->
        <!--            layout="@layout/layout_market_maintenance"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_marginTop="18dp"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_behavior="@string/appbar_scrolling_view_behavior" />-->

        <!--系统维护提示页面-->
        <ViewStub
            android:id="@+id/mViewStubMarketMaintenance"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="18dp"
            android:layout="@layout/layout_market_maintenance_new"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>
