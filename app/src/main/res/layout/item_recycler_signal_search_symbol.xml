<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="USDCHF+"
        tools:textColor="?attr/color_c1e1e1e_cebffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAllName"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:text="US Dollar/Swiss Franc" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@id/tvAllName" />
</androidx.constraintlayout.widget.ConstraintLayout>