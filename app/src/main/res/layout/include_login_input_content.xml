<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    tools:ignore="LabelFor"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/bgView"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNewPasswordPrompt" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClear"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingStart="0dp"
        android:paddingEnd="12dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bgView"
        tools:visibility="visible" />

    <EditText
        android:id="@+id/etInput"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@null"
        android:gravity="center_vertical"
        android:importantForAutofill="no"
        android:inputType="text"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="0dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/bgView"
        app:layout_constraintEnd_toStartOf="@+id/ivClear"
        app:layout_constraintStart_toStartOf="@+id/bgView"
        app:layout_constraintTop_toTopOf="@+id/bgView"
        tools:hint="111111" />
</merge>