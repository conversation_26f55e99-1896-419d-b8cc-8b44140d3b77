<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:orientation="vertical">

    <View
        android:id="@+id/viewLine"
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:background="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r4"
        app:layout_constraintBottom_toTopOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            style="@style/bold_semi_font"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center_vertical|start"
            android:lineSpacingExtra="8dp"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintEnd_toStartOf="@+id/ivClose"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Select Account" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:paddingTop="5dp"
            android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/viewInterval"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="192dp"
        app:layout_constraintBottom_toTopOf="@id/viewBottom"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <View
        android:id="@+id/viewBottom"
        android:layout_width="0dp"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvList" />
</LinearLayout>