<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvData"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginEnd="32dp"
        android:gravity="start"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Miss" />

    <CheckBox
        android:id="@+id/mCheckBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@null"
        android:button="@drawable/select_checkbox_agreement"
        android:checked="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ifvArrowRight"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/cut_off_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>