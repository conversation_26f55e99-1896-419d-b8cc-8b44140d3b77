<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/authentication_center"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewTop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="-12dp"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r10"
        app:layout_constraintBottom_toBottomOf="@+id/ivIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="@+id/viewTop"
        app:layout_constraintTop_toTopOf="@+id/viewTop"
        app:shapeAppearanceOverlay="@style/circleImageStyle"
        tools:src="@mipmap/ic_launcher" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVerifiedState"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="2"
        android:text="@string/unverified"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@+id/tvState"
        app:layout_constraintEnd_toEndOf="@+id/viewTop"
        app:layout_constraintStart_toEndOf="@+id/ivIcon"
        app:layout_constraintTop_toTopOf="@+id/ivIcon"
        tools:text="Unverified" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvState"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivIcon"
        app:layout_constraintEnd_toEndOf="@+id/tvVerifiedState"
        app:layout_constraintStart_toStartOf="@+id/tvVerifiedState"
        app:layout_constraintTop_toBottomOf="@+id/tvVerifiedState"
        tools:text="0/3 standard verification completed" />

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/viewTop" />

    <View
        android:id="@+id/line"
        style="@style/TabLayoutBottomLineStyle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTabLayout" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />
</androidx.constraintlayout.widget.ConstraintLayout>
