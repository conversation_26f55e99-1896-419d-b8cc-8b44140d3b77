<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
    android:padding="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVerifyTitle"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:text="@string/verification_requirements"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVerifyDesc"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="4dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintTop_toBottomOf="@+id/tvVerifyTitle"
        tools:text="@string/financial_and_trading_proof_address_document" />
</androidx.constraintlayout.widget.ConstraintLayout>