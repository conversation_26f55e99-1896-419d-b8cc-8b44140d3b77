<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:orientation="vertical"
    android:paddingBottom="16dp">

    <View
        android:id="@+id/viewLine"
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:background="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="18dp"
        android:layout="@layout/dialog_bottom_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/viewLine"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <TextView
                android:id="@+id/tvLink"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/more"
                android:textColor="@color/ce35728"
                android:textSize="14dp"
                android:visibility="gone"
                tools:ignore="SpUsage"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
