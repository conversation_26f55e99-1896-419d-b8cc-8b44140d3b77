<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <View
        android:id="@+id/bgView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintBottom_toBottomOf="@id/etMobile"
        app:layout_constraintEnd_toEndOf="@id/etMobile"
        app:layout_constraintStart_toStartOf="@id/tvAreaCode"
        app:layout_constraintTop_toTopOf="@id/etMobile" />

    <TextView
        android:id="@+id/tvAreaCode"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:drawablePadding="10dp"
        android:minWidth="38dp"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="+86 "
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />

    <cn.com.vau.common.view.ClearAndHideEditText
        android:id="@+id/etMobile"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:etFontFamily="medium"
        app:etTextSize="14"
        app:hint="@string/phone_number"
        app:inputType="number"
        app:is_show="false"
        app:layout_constraintBottom_toBottomOf="@+id/tvAreaCode"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvAreaCode"
        app:layout_constraintTop_toTopOf="@+id/tvAreaCode"
        app:rootBackground="@null"
        app:textColor="?attr/color_c1e1e1e_cebffffff"
        app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

    <View
        android:id="@+id/viewDivider"
        android:layout_width="1.5dp"
        android:layout_height="23dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="@id/etMobile"
        app:layout_constraintStart_toStartOf="@id/etMobile"
        app:layout_constraintTop_toTopOf="@id/etMobile" />

    <cn.com.vau.common.view.ClearAndHideEditText
        android:id="@+id/etEmail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:etFontFamily="medium"
        app:etTextSize="14"
        app:hint="@string/email"
        app:inputType="textEmailAddress"
        app:is_show="false"
        app:layout_constraintEnd_toEndOf="@+id/etMobile"
        app:layout_constraintStart_toStartOf="@+id/tvAreaCode"
        app:layout_constraintTop_toTopOf="@+id/tvAreaCode"
        app:textColor="?attr/color_c1e1e1e_cebffffff"
        app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

    <TextView
        android:id="@+id/tvEmailPrompt"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/live_account_holders_via_email"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etEmail" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupPhone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="etMobile,tvAreaCode,viewDivider" />

    <TextView
        android:id="@+id/tvSendEms"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="@string/send_otp_via_sms"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEmailPrompt"
        app:layout_goneMarginTop="24dp" />

    <View
        android:id="@+id/viewWhatsApp"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:background="@drawable/shape_c3325d366_r100"
        app:layout_constraintBottom_toBottomOf="@+id/tvSendWhatsApp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSendEms" />

    <TextView
        android:id="@+id/tvSendWhatsApp"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="@string/send_otp_via_whatsapp"
        android:textColor="@color/cffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/ivWhatsApp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/viewWhatsApp"
        app:layout_constraintTop_toTopOf="@id/viewWhatsApp" />

    <ImageView
        android:id="@+id/ivWhatsApp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:src="@drawable/img_whatsapp"
        app:layout_constraintBottom_toBottomOf="@+id/tvSendWhatsApp"
        app:layout_constraintEnd_toEndOf="@+id/viewWhatsApp"
        app:layout_constraintStart_toEndOf="@+id/tvSendWhatsApp"
        app:layout_constraintTop_toTopOf="@+id/tvSendWhatsApp" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupWhatsApp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="viewWhatsApp,tvSendWhatsApp,ivWhatsApp" />

</androidx.constraintlayout.widget.ConstraintLayout>