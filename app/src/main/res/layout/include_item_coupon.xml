<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/couponItemParent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="1dp"
    android:background="@drawable/draw_main_card"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <TextView
        android:id="@+id/tvCouponType"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:text="@string/deposit_coupon"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v65"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/flCouponAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v65"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCouponType">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCouponAmount"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/ce35728"
            android:textDirection="ltr"
            android:textSize="26dp"
            tools:text="$200" />
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCouponContent"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        android:gravity="center_vertical|start"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v65"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/flCouponAmount"
        tools:text="Deposit $3000 save 200" />

    <TextView
        android:id="@+id/tvAllUser"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toEndOf="@+id/guideline_v65"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="All Users" />

    <View
        android:id="@+id/mDottedLine"
        android:layout_width="100dp"
        android:layout_height="0dp"
        android:layout_marginVertical="12dp"
        android:background="@drawable/draw_shape_dotted_vline_c1f1e1e1e_c1fffffff"
        android:layerType="software"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline_v65"
        app:layout_constraintStart_toStartOf="@+id/guideline_v65"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvDetails"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        android:gravity="center_vertical|start"
        android:text="@string/details"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v65"
        app:layout_constraintStart_toStartOf="@+id/tvAllUser" />

    <TextView
        android:id="@+id/tvEndTime"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:paddingTop="4dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toTopOf="@+id/tvDetails"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvAllUser"
        app:layout_constraintTop_toBottomOf="@+id/tvAllUser"
        tools:text="@string/x_days_remaining" />

    <ImageView
        android:id="@+id/ivCouponDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:contentDescription="@string/app_name"
        android:minWidth="80dp"
        android:minHeight="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v65" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cdReleaseBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:cardBackgroundColor="?attr/color_c0a1e1e1e_c0affffff"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvRelease"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/shape_ce35728_r100"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/release"
        android:textColor="@color/cffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvAllUser"
        app:layout_constraintTop_toBottomOf="@+id/tvAllUser" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v65"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.65" />

</androidx.constraintlayout.widget.ConstraintLayout>