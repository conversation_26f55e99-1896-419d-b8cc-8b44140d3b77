<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llFilter"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="?attr/mainLayoutBg"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStrategy"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_weight="1"
        android:gravity="center_vertical|start"
        android:text="@string/strategies"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvQuickFilter"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginEnd="20dp"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:drawableStartCompat="@drawable/icon_source2_community_return_bottom"
        app:drawableTint="?attr/color_c1e1e1e_cebffffff"
        tools:text="@string/return_another" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilter"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:text="@string/filters"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:drawableStartCompat="@drawable/icon_source2_community_filters"
        app:drawableTint="?attr/color_c1e1e1e_cebffffff" />

</LinearLayout>