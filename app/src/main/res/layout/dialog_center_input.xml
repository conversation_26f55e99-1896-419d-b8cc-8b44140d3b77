<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    android:paddingVertical="20dp"
    tools:ignore="Autofill,LabelFor">

    <TextView
        android:id="@+id/tvContent"
        style="@style/DialogCenterContentStyle"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/account_cancellation_will_not_be_restored"
        tools:visibility="visible"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/inputContainer"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10"
        android:layout_marginTop="12dp"
        app:layout_goneMarginTop="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        tools:ignore="HardcodedText,RtlCompat">

        <EditText
            android:id="@+id/etContent"
            style="@style/gilroy_500"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:background="@null"
            android:gravity="center_vertical"
            android:importantForAutofill="no"
            android:singleLine="true"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivClear"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:hint="hshhsshshsh" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClear"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:padding="5dp"
            android:src="?attr/icon2CloseCircle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/tvStart"
        style="@style/DialogCenterLeftButtonStyle"
        android:layout_marginEnd="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvEnd"
        app:layout_constraintEnd_toStartOf="@id/tvEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvEnd"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvEnd"
        style="@style/DialogCenterRightButtonStyle"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvStart"
        app:layout_constraintTop_toBottomOf="@id/inputContainer" />
</androidx.constraintlayout.widget.ConstraintLayout>