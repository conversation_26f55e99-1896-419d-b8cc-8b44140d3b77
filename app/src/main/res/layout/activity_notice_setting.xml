<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/notification_settings"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPushNotice"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvPushNoticeTitle"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginEnd="5dp"
            android:ellipsize="end"
            android:gravity="start"
            android:text="@string/push_notifications"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvPushNoticeStatus"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:src="@drawable/draw_bitmap2_arrow_end10x10_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@+id/tvPushNoticeTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvPushNoticeTitle" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivWarn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvPushNoticeTitle"
            app:layout_constraintEnd_toStartOf="@id/ivArrow"
            app:layout_constraintTop_toTopOf="@+id/tvPushNoticeTitle"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvPushNoticeStatus"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvPushNoticeTitle"
            app:layout_constraintEnd_toStartOf="@id/ivWarn"
            app:layout_constraintTop_toTopOf="@+id/tvPushNoticeTitle"
            tools:text="Enabled"
            tools:textColor="?attr/color_ca61e1e1e_c99ffffff" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDoNotDisturb"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="5dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/do_not_disturb"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@id/sbDisturb"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clPushNotice" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNoticeTime"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="start"
        android:text="@string/notifications_are_silenced_from_23_to_7"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@id/sbDisturb"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDoNotDisturb" />

    <cn.com.vau.util.widget.SwitchButton
        android:id="@+id/sbDisturb"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/tvNoticeTime"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvDoNotDisturb"
        app:switch_can_loading="true"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="22dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@id/tvNoticeTime" />
</androidx.constraintlayout.widget.ConstraintLayout>