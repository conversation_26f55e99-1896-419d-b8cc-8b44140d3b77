<?xml version="1.0" encoding="utf-8"?>
<cn.com.vau.common.view.RefreshSlideLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="MissingConstraints">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <cn.com.vau.util.widget.NoDataScrollView
                android:id="@+id/mNoDataScroll"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</cn.com.vau.common.view.RefreshSlideLayout>