<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/tvNext"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clPassportPhoto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:paddingVertical="@dimen/padding_vertical_base"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvPassportPhoto"
                    style="@style/bold_semi_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="@string/passport_photo"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/tvTip1"
                    style="@style/gilroy_500"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/shows_full_name_date_and_photo"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableStartCompat="@drawable/bitmap_img_source_tick11x8_c00c79c"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvPassportPhoto" />

                <TextView
                    android:id="@+id/tvTip2"
                    style="@style/gilroy_500"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/shows_id_number"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableStartCompat="@drawable/bitmap_img_source_tick11x8_c00c79c"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip1" />

                <TextView
                    android:id="@+id/tvTip3"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/shows_passport_front_and_signature_page"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableStartCompat="@drawable/bitmap_img_source_tick11x8_c00c79c"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip2" />

                <TextView
                    android:id="@+id/tvTip4"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/no_black_and_white_images"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableStartCompat="@drawable/bitmap2_close12x12_ce35728"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip3"
                    app:layout_goneMarginTop="8dp" />

                <TextView
                    android:id="@+id/tvTip5"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="8dp"
                    android:text="@string/no_digital_or_expired_documents"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableStartCompat="@drawable/bitmap2_close12x12_ce35728"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip4"
                    app:layout_goneMarginTop="8dp" />

                <ImageView
                    android:id="@+id/ivPhoto"
                    android:contentDescription="@string/app_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:src="?attr/imgPassport"
                    android:visibility="invisible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvTip5"
                    tools:visibility="visible"/>

                <TextView
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="40dp"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/upload_a_clear_id_or_blur"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="11dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ivPhoto" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="6dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_grid_upload"
                app:spanCount="3"
                tools:itemCount="5"
                app:layout_constraintTop_toBottomOf="@+id/clPassportPhoto" />

            <LinearLayout
                android:id="@+id/llTip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/recyclerView"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/tvMaxUploadWarnTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/maximum_6_photo_to_upload"
                    android:textSize="10dp"
                    android:textColor="@color/ce35728"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvSupportTip"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@string/supported_file_types"
                    android:textSize="10dp"
                    android:textColor="?attr/color_c731e1e1e_c61ffffff" />

                <TextView
                    android:id="@+id/tvSupportTip2"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@string/maximum_upload_file_size"
                    android:textSize="10dp"
                    android:textColor="?attr/color_c731e1e1e_c61ffffff" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:clickable="false"
        android:text="@string/finish"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>