<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.profile.activity.changeLoginPWD.ChangeLoginPWDActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/margin_horizontal_base">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvOldPwdPrompt"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:text="@string/original_funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <cn.com.vau.common.view.ClearAndHideEditText
                android:id="@+id/etOldPwd"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:etFontFamily="medium"
                app:etTextSize="14"
                app:hint="@string/original_funds_password"
                app:inputType="textPassword"
                app:is_show="true"
                app:rootBackground="@drawable/select_login_et_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOldPwdPrompt"
                app:textColor="?attr/color_c1e1e1e_cebffffff"
                app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvNewPwdPrompt"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:text="@string/reset_new_funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/etOldPwd" />

            <cn.com.vau.common.view.ClearAndHideEditText
                android:id="@+id/etPwdNew"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:etFontFamily="medium"
                app:etTextSize="14"
                app:hint="@string/reset_new_funds_password"
                app:inputType="textPassword"
                app:is_show="true"
                app:rootBackground="@drawable/select_login_et_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvNewPwdPrompt"
                app:textColor="?attr/color_c1e1e1e_cebffffff"
                app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvRepeatPwdPrompt"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:text="@string/confirm_new_funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/etPwdNew" />

            <cn.com.vau.common.view.ClearAndHideEditText
                android:id="@+id/etPwdRepeat"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:etFontFamily="regular"
                app:etTextSize="14"
                app:hint="@string/confirm_new_funds_password"
                app:inputType="textPassword"
                app:is_show="true"
                app:rootBackground="@drawable/select_login_et_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvRepeatPwdPrompt"
                app:textColor="?attr/color_c1e1e1e_cebffffff"
                app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

            <TextView
                android:id="@+id/tvPrompt"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:text="@string/funds_password_is_and_please_in_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etPwdRepeat" />

            <include
                android:id="@+id/layoutPasswordCheck"
                layout="@layout/include_password_check"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPrompt" />

            <TextView
                android:id="@+id/tvOk"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
                android:gravity="center"
                android:paddingVertical="@dimen/padding_vertical_base"
                android:text="@string/ok"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutPasswordCheck" />

            <TextView
                android:id="@+id/tvForgotSecurityCode"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:layout_marginBottom="30dp"
                android:text="@string/forgot_funds_password"
                android:textColor="?color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOk" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>