<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:maxWidth="280dp"
    android:minHeight="172dp"
    android:paddingBottom="12dp">

    <View
        android:id="@+id/viewTop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:padding="6dp"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/ivIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewTop"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:maxWidth="60dp"
        android:maxHeight="60dp"
        android:minWidth="48dp"
        android:minHeight="48dp"
        android:src="?attr/imgAlertOk"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/llTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivClose"
        app:layout_goneMarginTop="20dp"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/llTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvDetail"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivIcon"
        app:layout_goneMarginBottom="16dp"
        app:layout_goneMarginTop="0dp"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:lineSpacingExtra="6dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            tools:text="快速平仓设置快速平仓设置快速平仓设置"
            tools:visibility="visible" />

    </LinearLayout>

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvDetail"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:lineSpacingExtra="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvSubTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llTitle"
        app:layout_goneMarginBottom="16dp"
        tools:text="hecking_positions"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSubTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:textColor="@color/ce35728"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvLeft"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDetail" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLeft"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="30dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="@string/cancel"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:visibility="gone"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toTopOf="@id/tvBottom"
        app:layout_constraintEnd_toStartOf="@+id/tvRight"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_goneMarginBottom="8dp"
        tools:text="Cancel"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRight"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:lineSpacingExtra="10dp"
        android:maxLines="1"
        android:padding="10dp"
        android:text="@string/confirm"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        android:visibility="gone"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvLeft"
        app:layout_constraintTop_toTopOf="@+id/tvLeft"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvButton"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/yes_confirm"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvBottom"
        app:layout_constraintEnd_toEndOf="@+id/tvRight"
        app:layout_constraintStart_toStartOf="@+id/tvLeft"
        app:layout_goneMarginBottom="8dp"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBottom"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:layout_marginTop="12dp"
        android:gravity="center"
        android:lineSpacingExtra="3dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="bottom" />
</androidx.constraintlayout.widget.ConstraintLayout>