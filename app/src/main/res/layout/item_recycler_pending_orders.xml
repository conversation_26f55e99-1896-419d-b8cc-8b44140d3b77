<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvProdName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:ignore="SpUsage"
        style="@style/gilroy_700"
        tool:text="VAU-TEST" />

    <ImageView
        android:id="@+id/ivKLine"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="2dp"
        android:contentDescription="@string/app_name"
        android:padding="2dp"
        android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toEndOf="@+id/tvProdName"
        app:layout_constraintTop_toTopOf="@+id/tvProdName" />

    <cn.com.vau.util.widget.DashedTextView
        android:id="@+id/tvOrderId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:dashedColor="?attr/color_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        tool:ignore="SpUsage"
        style="@style/gilroy_400"
        tool:text="#4535356565" />

    <TextView
        android:id="@+id/tvOrderDirection"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:background="@drawable/shape_c1f00c79c_r4"
        android:gravity="center_vertical"
        android:paddingHorizontal="4dp"
        android:textColor="@color/c00c79c"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:ignore="Smalldp,SpUsage"
        style="@style/gilroy_500"
        tool:text="Buy" />

    <TextView
        android:id="@+id/tvOrderType"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="4dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center_vertical"
        android:paddingHorizontal="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderDirection"
        app:layout_constraintStart_toEndOf="@+id/tvOrderDirection"
        tool:ignore="Smalldp,SpUsage"
        style="@style/gilroy_500"
        tool:text="Limit" />

    <TextView
        android:id="@+id/tvCreateTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderDirection"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/gilroy_400"
        tool:text="04/06/2024 09:59:24" />

    <TextView
        android:id="@+id/tvVolTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="4dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="2"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v33"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderDirection"
        tool:ignore="SpUsage"
        style="@style/gilroy_400"
        tool:text="@string/volume" />

    <!-- 浮动盈亏 -->
    <TextView
        android:id="@+id/tvVolume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/volumeTitleBarrier"
        tool:ignore="SpUsage"
        style="@style/gilroy_500"
        tool:text="0.20" />

    <TextView
        android:id="@+id/tvConditionsTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/conditions"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolTitle"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v66"
        app:layout_constraintStart_toEndOf="@+id/guideline_v33"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvConditions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolume"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v66"
        app:layout_constraintStart_toEndOf="@+id/guideline_v33"
        tool:ignore="SpUsage"
        style="@style/gilroy_500"
        tool:text="--" />

    <TextView
        android:id="@+id/tvPendingPriceTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="2"
        android:text="@string/price"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v66"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <!-- 开单价 -->
    <TextView
        android:id="@+id/tvPendingPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolume"
        app:layout_constraintEnd_toEndOf="parent"
        tool:ignore="SpUsage"
        style="@style/gilroy_500"
        tool:text="1.2345" />

    <TextView
        android:id="@+id/tvCurrentPriceTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/current_price"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v33"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/volumeBarrier"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvCurrentPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCurrentPriceTitleBarrier"
        tool:ignore="SpUsage"
        style="@style/gilroy_500"
        tool:text="0.12345678" />

    <TextView
        android:id="@+id/tvPriceGapTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="2"
        android:text="@string/price_gap"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCurrentPriceTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline_v66"
        tool:ignore="SpUsage"
        style="@style/gilroy_400" />

    <TextView
        android:id="@+id/tvPriceGap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCurrentPrice"
        app:layout_constraintEnd_toEndOf="parent"
        tool:ignore="SpUsage"
        style="@style/gilroy_600"
        tool:text="1.3456" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSetTpSl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r4"
        app:layout_constraintTop_toBottomOf="@+id/tvCurrentPrice">

        <TextView
            android:id="@+id/tvTpSlTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="6dp"
            android:layout_marginStart="8dp"
            android:text="@string/tp_sl"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/gilroy_400" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTpSlEdit"
            android:layout_width="28dp"
            android:layout_height="24dp"
            android:paddingHorizontal="8dp"
            android:paddingVertical="6dp"
            android:src="?attr/icon2EditTpSl"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvSl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivTpSlEdit"
            app:layout_constraintTop_toTopOf="parent"
            tool:ignore="HardcodedText"
            style="@style/gilroy_500" />

        <TextView
            android:id="@+id/tvTpSlOff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="6dp"
            android:layout_marginStart="8dp"
            android:text=" / "
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvSl"
            app:layout_constraintTop_toTopOf="parent"
            tool:ignore="HardcodedText"
            style="@style/gilroy_500" />

        <TextView
            android:id="@+id/tvTp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvTpSlOff"
            app:layout_constraintTop_toTopOf="parent"
            tool:ignore="HardcodedText"
            style="@style/gilroy_500" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvModify"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:minHeight="32dp"
        android:paddingVertical="8dp"
        android:text="@string/modify"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintEnd_toStartOf="@+id/tvDelete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clSetTpSl"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvDelete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:minHeight="32dp"
        android:paddingVertical="8dp"
        android:text="@string/cancel"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvModify"
        app:layout_constraintTop_toBottomOf="@+id/clSetTpSl"
        app:layout_goneMarginStart="@dimen/margin_horizontal_base"
        style="@style/gilroy_500" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/cut_off_line_height"
        app:layout_constraintBottom_toBottomOf="parent"
        tool:background="?attr/color_c1f1e1e1e_c1fffffff" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/tvCurrentPriceTitleBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvCurrentPriceTitle,tvPriceGapTitle" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/volumeBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvVolume,tvConditions,tvPendingPrice" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/volumeTitleBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvVolTitle,tvConditionsTitle,tvPendingPriceTitle" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.33" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.66" />

</androidx.constraintlayout.widget.ConstraintLayout>
