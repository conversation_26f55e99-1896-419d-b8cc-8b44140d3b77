<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
    android:orientation="vertical"
    android:paddingStart="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFeatureTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base_new"
        android:gravity="start"
        android:text="@string/features"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLiveTradingAccountTitle"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="10dp"
        android:gravity="start|center_vertical"
        android:lineSpacingExtra="4dp"
        android:text="@string/live_trading_account"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableStartCompat="?attr/imgFeatureLock"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvFeatureTitle" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvLiveTrading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCryptoWalletAccountTitle"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:drawablePadding="10dp"
        android:gravity="start|center_vertical"
        android:lineSpacingExtra="4dp"
        android:text="@string/crypto_wallet_account"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableStartCompat="?attr/imgFeatureWallet"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLiveTradingAccount" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCryptoWallet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

</LinearLayout>