<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".trade.kchart.tradingview.ChartCandleLandscapeActivity">

    <View
        android:id="@+id/viewStatusbar"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/llBoard"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r100"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintStart_toEndOf="@id/viewStatusbar"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvName"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            tools:text="USDCAD" />

        <TextView
            android:id="@+id/tv_board_sellprice"
            style="@style/bold_semi_font"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            tools:text="+1833.20397" />

        <TextView
            android:id="@+id/tv_board_diff"
            style="@style/bold_semi_font"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:paddingEnd="5dp"
            android:textAlignment="viewEnd"
            android:textColor="@color/c00c79c"
            android:textDirection="ltr"
            android:textSize="14dp"
            tools:text="+0.0073 (+97.29%)" />

        <TextView
            android:id="@+id/tv_board_time"
            style="@style/regular_font"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            tools:text="13:50:22" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOrders"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:text="@string/orders"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/llBoard"
        app:layout_constraintStart_toEndOf="@id/llBoard"
        app:layout_constraintTop_toTopOf="@id/llBoard" />

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="22dp"
        android:contentDescription="@string/app_name"
        android:padding="7dp"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/llBoard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/llBoard"
        tools:ignore="ImageContrastCheck" />

    <ImageView
        android:id="@+id/ivSetting"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="18dp"
        android:contentDescription="@string/app_name"
        android:padding="5dp"
        android:src="?attr/icon1Setting"
        app:layout_constraintBottom_toBottomOf="@+id/ivBack"
        app:layout_constraintEnd_toStartOf="@+id/ivBack"
        app:layout_constraintTop_toTopOf="@+id/ivBack"
        tools:ignore="ImageContrastCheck" />

    <ImageView
        android:id="@+id/ivDrawing"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="?attr/icon1Drawing"
        app:layout_constraintBottom_toBottomOf="@+id/ivSetting"
        app:layout_constraintEnd_toStartOf="@+id/ivSetting"
        app:layout_constraintTop_toTopOf="@+id/ivSetting" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivKNewGuide"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="18dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivDrawing"
        app:layout_constraintEnd_toStartOf="@+id/ivDrawing"
        app:layout_constraintTop_toTopOf="@+id/ivDrawing"
        app:srcCompat="?attr/icon1Guide"
        tools:ignore="ImageContrastCheck" />

    <cn.com.vau.common.view.VauBridgeWebView
        android:id="@+id/mWebView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/viewStatusbar"
        app:layout_constraintTop_toBottomOf="@+id/llBoard" />

</androidx.constraintlayout.widget.ConstraintLayout>