<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="?attr/imgNoticeTrade" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivRedDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@color/ce35728"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        app:shapeAppearanceOverlay="@style/circleImageStyle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivIcon"
        app:layout_constraintTop_toTopOf="@id/ivIcon"
        tools:text="[Deposit] Successful" />

    <TextView
        android:id="@+id/tvSubTitle"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="Buy     0.10Lots     AUDJPY" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
        app:layout_goneMarginTop="8dp"
        tools:text="Order number: 222343813" />

    <TextView
        android:id="@+id/tvDate"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvContent"
        tools:text="31/01/2024 10:39:20" />

    <TextView
        android:id="@+id/tvViewDetail"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/view_detail"
        android:textColor="@color/ce35728"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvDate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvDate"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>