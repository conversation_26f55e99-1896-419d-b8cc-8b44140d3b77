<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivHead"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:contentDescription="@string/app_name"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundImageStyle6" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLabel"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/pending_review"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivHead"
        tool:ignore="Smalldp"
        tool:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingHorizontal="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toTopOf="@+id/tvId"
        app:layout_constraintEnd_toStartOf="@+id/tvLabel"
        app:layout_constraintStart_toEndOf="@+id/ivHead"
        app:layout_constraintTop_toTopOf="@+id/ivHead"
        tool:text="Follow signal 1 Follow signal 1 Follow signal 1 Follow signal 1" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvIdKey"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHead"
        app:layout_constraintStart_toStartOf="@+id/tvName"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        tool:ignore="HardcodedText"
        tool:text="Strategy ID：" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvId"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvIdKey"
        app:layout_constraintStart_toEndOf="@+id/tvIdKey"
        tool:text="123456" />

    <View
        android:id="@+id/cutOffLineView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/cut_off_line_height"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="12dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toBottomOf="@+id/ivHead" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPnlTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/pnl"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvBalanceTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cutOffLineView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBalanceTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/balance"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnlTitle"
        app:layout_constraintEnd_toStartOf="@+id/tvEquityTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvPnlTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEquityTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:text="@string/equity"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnlTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvBalanceTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPnl"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="0.00"
        android:textColor="@color/c00c79c"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvBalance"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
        tool:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBalance"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="0.00"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnl"
        app:layout_constraintEnd_toStartOf="@+id/tvEquity"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvPnl"
        tool:ignore="HardcodedText,MissingConstraints" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEquity"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvPnl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvBalance"
        tool:ignore="MissingConstraints"
        tool:text="300.00" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCreditTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/credit"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvPlaceholderTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tvTotalSharedProfitTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvPnl" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTotalSharedProfitTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="20dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/total_shared_profit"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
        app:layout_constraintEnd_toStartOf="@+id/tvCreditTitle"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPlaceholderTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCreditTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCredit"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvPlaceholder"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/tvTotalSharedProfit"
        app:layout_constraintTop_toBottomOf="@+id/tvCreditTitle"
        tool:ignore="MissingConstraints"
        tool:text="50.00" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTotalSharedProfit"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="20dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
        app:layout_constraintEnd_toStartOf="@+id/tvCredit"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        tool:text="0.00" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPlaceholder"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCredit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNextStart"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:paddingVertical="8dp"
        android:text="@string/details"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintEnd_toStartOf="@+id/tvNextEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCredit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNextEnd"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:paddingVertical="8dp"
        android:text="@string/manage"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvNextStart"
        app:layout_constraintTop_toBottomOf="@+id/tvCredit" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        tool:background="?attr/color_c0a1e1e1e_c0affffff" />

</androidx.constraintlayout.widget.ConstraintLayout>
