import{m as t,d as e,h as o}from"./index-BVEDVdSS.js";import{F as s}from"./failedModal-DlAunQaU.js";import{w as a}from"./wrapperContainer-CL-3pf79.js";import{_ as i}from"./index-M11nEPjl.js";import{I as r,J as n,K as l,j as d,L as c,S as u}from"./vue-vendor-DjIN0JG5.js";import{s as p}from"./vant-vendor-D8PsFlrJ.js";import"./navbar-DfgFEjpa.js";import"./vendor-CwRwASPO.js";const m={class:"container relative"},h={class:"bottom_btn"};const v=i({components:{wrapperContainer:a,FailedModal:s},mixins:[t],data:()=>({to_quiz:!1,show:!1}),methods:{async toCheckViolation(){try{const{data:t}=await this.useRequest(o,{userId:this.$pinia.state.value.params.userId});this.to_quiz=t.obj.isNormal,this.proclientProcess()}catch(t){p(t.msgInfo)}},async proclientProcess(){try{await this.useRequest(e,{userId:this.$pinia.state.value.params.userId,step:"0-4",isAgreedDeclaration:!0}),this.to_quiz?this.$router.push({path:"0-5",query:this.$route.query}):this.show=!0}catch(t){p(t.msgInfo)}}}},[["render",function(t,e,o,s,a,i){const p=r("wrapper-container"),v=r("FailedModal");return l(),n("div",m,[d(p,{loading:t.loading},{default:c((()=>[e[1]||(e[1]=u("div",{class:"contents marginBottom0"},[u("div",{class:"title"}," Thank you for submitting your Wholesale Client Categorisation Application Form. "),u("div",{class:"text medium"}," Based on what you have declared on your application, you may be eligible to be categorised as Wholesale client and be offered a PRO account – limited to Wholesale clients only. ")],-1)),e[2]||(e[2]=u("div",{class:"contents marginBottom0"},[u("div",{class:"title"},"To finalise your application:"),u("div",{class:"text medium"}," 1.Complete the Sophisticated Investor Quiz to help us complete your application. "),u("div",{class:"text medium"}," 2.Provide us with a copy of your trading history to assess your trading experience. ")],-1)),u("div",h,[u("div",{class:"btn flex-center quiz",onClick:e[0]||(e[0]=(...t)=>i.toCheckViolation&&i.toCheckViolation(...t))}," Access Sophisticated Investor Quiz ")])])),_:1},8,["loading"]),d(v,{value:a.show},null,8,["value"])])}],["__scopeId","data-v-62f9db9e"]]);export{v as default};
