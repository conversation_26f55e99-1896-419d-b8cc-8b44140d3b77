import{n as e}from"./navbar-DfgFEjpa.js";import{_ as s,r as o}from"./index-M11nEPjl.js";import{I as n,J as i,K as a,j as r,S as l}from"./vue-vendor-DjIN0JG5.js";import"./vant-vendor-D8PsFlrJ.js";import"./vendor-CwRwASPO.js";const t={class:"container"};const c=s({components:{navbar:e},data:()=>({isEventPage:new URLSearchParams(window.location.search).get("isEventPage")??0}),methods:{back(){try{1===Number(this.isEventPage)?window.history.go(-1):o("501")}catch(e){o("501")}}}},[["render",function(e,s,o,c,u,d){const y=n("navbar");return a(),i("div",t,[r(y,{onClickLeft:d.back},null,8,["onClickLeft"]),s[0]||(s[0]=l("div",{class:"tc_box"},[l("h5",null,"Disclaimer"),l("ol",null,[l("li",null,' Before you proceed, please read this disclaimer carefully to ensure you fully understand its contents and the associated legal consequences. By clicking "Agree" and confirming your submission, you acknowledge that you accept this disclaimer. '),l("li",null," Your passkeys will derive from your device's underlying operating iCloud or Google operating system. Passkeys will include syncronised passwords, keychains, passkeys, and other credentials. "),l("li",null," If your iCloud or Google account is compromised, such as third parties bypassing your device's security (including but not limited to Face ID, Touch ID, gesture unlocking and password unlocking), which will affect your passkey verification with Vantage, you will bear full responsibility for any consequence including any losses and legal outcomes arising from such scenario. "),l("li",null," We are not liable for any direct, indirect, incidental, or consequential losses resulting from the unauthorised use of cross-device passkeys. "),l("li",null," Please take all necessary precautions to secure your iCloud or Google account and ensure that your device's unlocking methods are not compromised. ")])],-1))])}],["__scopeId","data-v-f931d40a"]]);export{c as default};
