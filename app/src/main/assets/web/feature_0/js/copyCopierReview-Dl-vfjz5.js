import{L as t,h as e,i as a,T as s,j as i,b as o,s as n,k as l}from"./vant-vendor-D8PsFlrJ.js";import{_ as r,s as d}from"./index-M11nEPjl.js";import{N as h,R as c}from"./notFound-DDNUrKhI.js";import{n as p}from"./navbar-DfgFEjpa.js";import{a as m,m as u,b as g}from"./shadowAccount-6NsRkLs7.js";import{z as f}from"./vendor-CwRwASPO.js";import{I as w,J as y,K as v,j as I,L as b,F as C,Y as L,M as E,W as N,$ as A,S as R,V as T,B as j}from"./vue-vendor-DjIN0JG5.js";import"./empty_light-BcIdMEkV.js";const $={components:{[i.name]:i,[s.name]:s,[a.name]:a,[e.name]:e,[t.name]:t,ReviewCard:c,NotFound:h,Navbar:p},emits:["change-swipeable"],data(){return{stUserId:this.$route.query.stUserId,currentTab:"PENDING",swipeable:!0,loading:!1,refreshLoading:!1,finished:!1,pageNum:1,list:[{strategyCopyListItem:[{}]},{strategyCopyListItem:[{}]}],tabList:[{name:"PENDING",title:this.$t("shadow.Pending")},{name:"APPROVED",title:this.$t("shadow.Approved")},{name:"REJECTED",title:this.$t("shadow.Rejected")}],applyStatus:{PENDING:"Applied on",APPROVED:"Approved on",AUTO_APPROVED:"Auto-approved on",REJECTED:"Rejected on",AUTO_REJECTED:"Auto-rejected on"}}},methods:{dayjs:f,back(){this.currentTab="PENDING",this.$router.back()},async changeSwipeable(){await this.init()},reject({followerUserId:t,followerAccountId:e}){l({message:this.$t("shadow.ConfirmToRejectThisCopier"),confirmButtonText:this.$t("shadow.Confirm"),cancelButtonText:this.$t("shadow.Cancel"),className:"custom-dialog"}).then((async()=>{this.loading=!0,await g({signalUserId:this.stUserId,followerUserId:t,followerAccountId:e}),this.loading=!1,n("Application Rejected"),this.currentTab="REJECTED",this.init()})).catch((()=>{}))},async approve({followerUserId:t,followerAccountId:e}){this.loading=!0,await u({signalUserId:this.stUserId,followerUserId:t,followerAccountId:e}),this.loading=!1,n("Application Approved"),this.currentTab="APPROVED",this.init()},async init(){this.list=[{strategyCopyListItem:[{}]},{strategyCopyListItem:[{}]}],this.loading=!0;try{let{data:t}=await m({stUserId:this.stUserId,applyStatus:this.currentTab,pageNum:this.pageNum,pageSize:999});this.loading=!1,this.list=t.content,t.content.length>=t.totalElements&&(this.finished=!0)}catch(t){this.loading=!1,this.finished=!0}},async onRefresh(){await this.init(),this.refreshLoading=!1}},mounted(){d({code:"250",title:this.$t("shadow.CopierReview"),iconList:["CLOSE"]}),this.pageNum=1,this.init()}},_={class:"copier-review"},U={class:"pull-refresh-loading"},D={class:"review-info"},k={class:"review-info"},P=["onClick"],S=["onClick"];const V=r($,[["render",function(n,l,r,d,h,c){const p=w("navbar"),m=t,u=o,g=w("review-card"),f=a,$=w("not-found"),V=i,x=s,O=e;return v(),y("div",_,[I(p,{onClickLeft:c.back,"left-text":n.$t("shadow.CopierReview")},null,8,["onClickLeft","left-text"]),I(O,{class:"copier-review",modelValue:h.refreshLoading,"onUpdate:modelValue":l[2]||(l[2]=t=>h.refreshLoading=t),onRefresh:c.onRefresh},{pulling:b((()=>[j(T(n.$t("pull_down_to_refresh")),1)])),loosing:b((()=>[j(T(n.$t("release_to_refresh")),1)])),loading:b((()=>[R("div",U,[I(m,{type:"spinner",size:"16px"},{default:b((()=>[j(T(n.$t("loading")),1)])),_:1})])])),default:b((()=>[I(x,{active:h.currentTab,"onUpdate:active":l[1]||(l[1]=t=>h.currentTab=t),type:"card",class:"tabs-card",swipeable:"",onChange:c.changeSwipeable},{default:b((()=>[(v(!0),y(C,null,L(h.tabList,((t,e)=>(v(),E(V,{title:t.title,name:t.name,key:e},{default:b((()=>[I(f,{modelValue:h.loading,"onUpdate:modelValue":l[0]||(l[0]=t=>h.loading=t),finished:h.finished,"finished-text":"",onLoad:c.init},{default:b((()=>[(v(!0),y(C,null,L(h.list,((t,e)=>(v(),E(g,{key:e,avatar:t.avatar,title:(null==t?void 0:t.name)?`${t.name}(${t.followerAccountNo})`:"","avatar-shape":"round",subtitle:h.applyStatus[t.strategyCopyListItem[0].applyStatus]+" "+c.dayjs(Number("PENDING"===t.strategyCopyListItem[0].applyStatus?t.strategyCopyListItem[0].applyTime:t.strategyCopyListItem[0].reviewTime)).format("DD/MM/YYYY HH:mm:ss")},A({main:b((()=>[I(u,{"title-width":"100%",row:"2",loading:h.loading},{default:b((()=>[R("div",D,[R("span",null,T(n.$t("shadow.Strategy")),1),R("span",null,T(t.strategyCopyListItem[0].strategyName),1)]),R("div",k,[R("span",null,T(n.$t("shadow.Investment")),1),R("span",null,T(t.strategyCopyListItem[0].followAmount)+"  "+T(t.strategyCopyListItem[0].currency),1)])])),_:2},1032,["loading"])])),_:2},["PENDING"===h.currentTab?{name:"footer",fn:b((()=>[I(u,{"row-width":"100",row:"1",loading:h.loading},{default:b((()=>[R("div",{class:"shadow-btn shadow-btn-edit",onClick:e=>c.reject(t)},T(n.$t("shadow.Reject")),9,P)])),_:2},1032,["loading"]),I(u,{"row-width":"100",row:"1",loading:h.loading},{default:b((()=>[R("div",{class:"shadow-btn shadow-btn-more",onClick:e=>c.approve(t)},T(n.$t("shadow.Approve")),9,S)])),_:2},1032,["loading"])])),key:"0"}:void 0]),1032,["avatar","title","subtitle"])))),128))])),_:1},8,["modelValue","finished","onLoad"]),h.loading||0!==h.list.length?N("",!0):(v(),E($,{key:0}))])),_:2},1032,["title","name"])))),128))])),_:1},8,["active","onChange"])])),_:1},8,["modelValue","onRefresh"])])}],["__scopeId","data-v-e6b9df76"]]);export{V as default};
