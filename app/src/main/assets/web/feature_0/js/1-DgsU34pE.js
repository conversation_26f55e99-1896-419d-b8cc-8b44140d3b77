import{w as e}from"./wrapperContainer-CL-3pf79.js";import{m as t,b as s,a,c as i,d as o,g as r}from"./index-BVEDVdSS.js";import{_ as n,r as l}from"./index-M11nEPjl.js";import{I as u,M as h,K as d,L as c,S as p,J as g,y}from"./vue-vendor-DjIN0JG5.js";import{s as f}from"./vant-vendor-D8PsFlrJ.js";import"./navbar-DfgFEjpa.js";import"./vendor-CwRwASPO.js";const C={class:"text flex my-button"},v={key:0},m={key:0,xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},w={key:1,xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},b={key:1},q={key:0,xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},x={key:1,xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"};const I=n({components:{wrapperContainer:e},mixins:[t],data(){return{agree:!1,userId:this.$pinia.state.value.params.userId,chosen_quiz:0}},methods:{toggleAgreeStatus(){this.agree=!this.agree},async next(){if(!this.agree)return;const e=this.$pinia.state.value.params.userId;if(!e)return void l("10");if(!this.agree)return void f("Please agree to the terms");this.showLoading();const{data:t}=await this.useRequest(i,{userId:e});if("1"===t.obj.regluationType)if(!0===t.obj.isProclient){await o({userId:e,step:"1",isAgreedTnc:!0});const{data:t}=await this.useRequest(r,{userId:e,type:1});t.obj.confirm?(this.chosen_quiz=t.obj.choose,2===t.obj.choose?this.applyTestType(2):this.checkCurrStep()):this.$router.push({path:"1-1",query:this.$route.query})}else f("You are unable to upgrade to Pro");else f("Only for Asic")},async checkCurrStep(){const{data:e}=await this.useRequest(a,{userId:this.userId});if(""!==e.obj.step)switch(e.obj.step){case"1-4":case"0-7":this.$router.push({path:"success",query:this.$route.query});break;case"2":case"0-3":this.handleChosenQuiz();break;default:this.$router.push({path:`${e.obj.step}`,query:this.$route.query})}},async applyTestType(e){await this.useRequest(s,{userId:this.userId,choose:e,type:1}),this.checkCurrStep()},handleChosenQuiz(){switch(this.chosen_quiz){case 1:this.$router.push({path:"2",query:this.$route.query});break;case 2:this.$router.push({path:"0-3",query:this.$route.query})}}}},[["render",function(e,t,s,a,i,o){const r=u("wrapperContainer");return d(),h(r,{loading:e.loading},{default:c((()=>[t[7]||(t[7]=p("div",{class:"contents"},[p("div",{class:"text"}," Wholesale Client Categorisation Application Form "),p("div",{class:"text"}," Thank you for being a client of Vantage Global Prime Ltd (“Vantage”). "),p("div",{class:"text"}," Please complete the following steps to submit your application. Upon receipt of your request, we will assess the information we have on file for you and notify you regarding our decision. We may request additional information, supporting evidence from you or ask you to complete a quiz as well as close any open position(s) you may currently hold under your existing account(s) before finalising your application. "),p("div",{class:"text"}," For each client category, varying levels of protection and treatment apply. Before submitting your request for re-categorisation, ensure that you read, understand and agree to the Client Categorisation Notice. ")],-1)),p("div",C,[p("div",{class:"read_field",onClick:t[0]||(t[0]=(...e)=>o.toggleAgreeStatus&&o.toggleAgreeStatus(...e))},[0==e.$route.query.theme?(d(),g("div",v,[i.agree?(d(),g("svg",m,t[2]||(t[2]=[p("path",{d:"M7 14C3.134 14 0 10.866 0 7C0 3.134 3.134 0 7 0C10.866 0 14 3.134 14 7C14 10.866 10.866 14 7 14Z",fill:"#00C79C"},null,-1),p("path",{d:"M6.10262 8.07361L4.5333 6.50429C4.29687 6.26786 3.91367 6.26786 3.67725 6.50429C3.44092 6.74061 3.44092 7.12391 3.67725 7.36024L5.6747 9.35769C5.91102 9.59401 6.29432 9.59401 6.53065 9.35769L9.95465 5.93369C10.191 5.69726 10.191 5.31406 9.95465 5.07764C9.71822 4.84131 9.33502 4.84131 9.0986 5.07764L6.10262 8.07361Z",fill:"white","fill-opacity":"0.92"},null,-1)]))):(d(),g("svg",w,t[3]||(t[3]=[p("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 13.25C3.554 13.25 0.75 10.446 0.75 7C0.75 3.554 3.554 0.75 7 0.75C10.446 0.75 13.25 3.554 13.25 7C13.25 10.446 10.446 13.25 7 13.25ZM7 0C3.134 0 0 3.134 0 7C0 10.866 3.134 14 7 14C10.866 14 14 10.866 14 7C14 3.134 10.866 0 7 0Z",fill:"#1E1E1E","fill-opacity":"0.45"},null,-1)])))])):(d(),g("div",b,[i.agree?(d(),g("svg",q,t[4]||(t[4]=[p("path",{d:"M7 14C3.134 14 0 10.866 0 7C0 3.134 3.134 0 7 0C10.866 0 14 3.134 14 7C14 10.866 10.866 14 7 14Z",fill:"#00C79C"},null,-1),p("path",{d:"M6.10262 8.07361L4.5333 6.50429C4.29687 6.26786 3.91367 6.26786 3.67725 6.50429C3.44092 6.74061 3.44092 7.12391 3.67725 7.36024L5.6747 9.35769C5.91102 9.59401 6.29432 9.59401 6.53065 9.35769L9.95465 5.93369C10.191 5.69726 10.191 5.31406 9.95465 5.07764C9.71822 4.84131 9.33502 4.84131 9.0986 5.07764L6.10262 8.07361Z",fill:"white","fill-opacity":"0.92"},null,-1)]))):(d(),g("svg",x,t[5]||(t[5]=[p("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 13.25C3.554 13.25 0.75 10.446 0.75 7C0.75 3.554 3.554 0.75 7 0.75C10.446 0.75 13.25 3.554 13.25 7C13.25 10.446 10.446 13.25 7 13.25ZM7 0C3.134 0 0 3.134 0 7C0 10.866 3.134 14 7 14C10.866 14 14 10.866 14 7C14 3.134 10.866 0 7 0Z",fill:"white","fill-opacity":"0.38"},null,-1)])))]))]),t[6]||(t[6]=p("div",null,[p("div",{class:"selectImageSpan"}," I hereby confirm that I have read and understood the above Client Categorisation Notice, and I understand the implications of my requested categorisation. I formally request that Vantage re-categorise me accordingly. ")],-1))]),p("div",{class:"bottom_btn",onClick:t[1]||(t[1]=(...e)=>o.next&&o.next(...e))},[p("div",{class:y(["btn flex-center btn_upgrade",i.agree&&"active"])}," Upgrade to Pro ",2)])])),_:1},8,["loading"])}],["__scopeId","data-v-f29c50d4"]]);export{I as default};
