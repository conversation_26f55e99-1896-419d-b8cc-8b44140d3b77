import{c as a,p as e,g as s}from"./index-M11nEPjl.js";const t={token:s("token")};let o="https://stapp.vttechfx.com:16443/stTradeApp";const p=e=>a(`${o}/shadow/list/commissionPaymentAccount`,e),c=()=>a(`${o}/shadow/account-list`,null,t),r=({stUserId:e,applyStatus:s,pageNum:p,pageSize:c})=>a(`${o}/shadow/copier-review/page?stUserId=${e}&applyStatus=${s}&pageNum=${p}&pageSize=${c}`,null,t),i=a=>e(`${o}/shadow/copier-review/reject`,a,t),d=a=>e(`${o}/shadow/copier-review/approve`,a,t),n=a=>e(`${o}/shadow/account/load`,a,t),l=a=>e(`${o}/shadow/account/update/v2`,a,{...t,apiVer:"v2"}),u=()=>a(`${o}/strategy/get-profit-share-cycle-type`);export{r as a,i as b,u as c,p as d,c as g,n as l,d as m,l as u};
