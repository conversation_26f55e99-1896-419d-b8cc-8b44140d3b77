import{s as t,E as e}from"./vant-vendor-D8PsFlrJ.js";import{s}from"./skeletonAnalysis-DF5fKjSL.js";import{n as o}from"./footer-BLTvWGoM.js";import{n as a}from"./navbar-DfgFEjpa.js";import{i as n,s as i,r,_ as m}from"./index-M11nEPjl.js";import{a as d}from"./news-Bq96hVLI.js";import l from"./empty_dark-BVM0wdOH.js";import c from"./empty_light-t-w1hjKQ.js";import{I as u,J as f,K as p,j as v,M as k,S as g,V as h}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const w={class:"analysis container"},y={key:1,class:"content"},j={key:1},I={class:"title"},D={class:"img"},b=["src"],N={class:"intro"};const T=m({components:{navBar:a,skeletonAnalysis:s,newFooter:o},mixins:[{data(){return{content:"",newsId:this.$route.params.id||"",loading:!0,idNoData:!1}},computed:{token(){return this.$pinia.state.value.params.token}},methods:{formatTime:n,goBack(){r("501")},getNewInfo(){const e={token:this.token,id:this.newsId,timeZone:this.$route.query.zone},s=this;d(e).then((e=>{s.loading=!1;const{resultCode:o,data:a,msgInfo:n}=e;if("00000000"===o){s.content=a?null==a?void 0:a.obj:"";const t=Object.keys(s.content);s.idNoData=!t.length}else t({message:n,wordBreak:"break-word"}),s.idNoData=!0})).catch((e=>{s.loading=!1;const{message:o}=e;t({message:o,wordBreak:"break-word"})}))}},mounted(){i({code:"250",title:this.$t("analysis")}),this.getNewInfo()}}],computed:{noDataImg(){return"dark"===this.$pinia.state.value.params.themeTxt?l:c}}},[["render",function(t,s,o,a,n,i){const r=u("nav-bar"),m=u("skeletonAnalysis"),d=e,l=u("new-footer");return p(),f("div",w,[v(r,{leftText:t.$t("analysis"),onClickLeft:t.goBack},null,8,["leftText","onClickLeft"]),t.loading?(p(),k(m,{key:0})):(p(),f("div",y,[t.idNoData?(p(),k(d,{key:0,class:"no_data",image:i.noDataImg,description:t.$t("no_records_found"),"image-size":"80"},null,8,["image","description"])):(p(),f("div",j,[g("div",I,h(t.content.title),1),g("div",null,[g("div",D,[g("img",{alt:"",src:t.content.thumb},null,8,b)]),g("div",N,h(t.content.intro),1)]),v(l,{views:t.content.views||0,time:t.formatTime(t.content.createTime,"DD/MM/YYYY HH:mm"),source:"TRADING CENTRAL"},null,8,["views","time"])]))]))])}],["__scopeId","data-v-e873e8b8"]]);export{T as default};
