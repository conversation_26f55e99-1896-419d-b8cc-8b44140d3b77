import{s as t,b as e,E as a,I as s,c as i}from"./vant-vendor-D8PsFlrJ.js";import{_ as l,a as r}from"./empty_light-BcIdMEkV.js";import{_ as n,a as o}from"./question_light-CQqO8HhY.js";import{n as c}from"./navbar-DfgFEjpa.js";import{c as d,u as h,s as m,r as v,_ as p}from"./index-M11nEPjl.js";import{s as u,C as g}from"./calendar-Cgct8rqI.js";import{t as w}from"./tab-DAk8rFSJ.js";import{I as y,J as f,K as S,j as _,S as k,L as b,V as C,M as $,F as x,Y as D,f as L}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const{paramsStore:j}=h();const R={class:"container"},V=["src"],I={class:"content"},N={class:"select-picker"},P={class:"select-picker-item-text"},q=["src"],M={key:1},O={class:"section-container"},T={class:"header-title"},U={class:"title"},B={class:"card"},E={class:"card-wrapper"},F={class:"text"},W={class:"text right text-value"},A={class:"card"},J={class:"card-wrapper"},Y={class:"text"},z={class:"text right"},K={class:"card-wrapper highlight"},G={class:"text"},H={class:"text right"},Q={class:"section-container"},X={class:"header-title"},Z={class:"title"},tt={class:"card-wrapper"},et={class:"profile"},at={class:"profile-name"},st=["onClick"],it={class:"card-wrapper"},lt={class:"text"},rt={class:"text right"},nt={class:"card-wrapper"},ot={class:"text"},ct={class:"text right"},dt={class:"card-wrapper highlight"},ht={class:"text"},mt={class:"text right"},vt={class:"action-sheet-content"},pt={class:"content-title"},ut={class:"content-row"},gt={class:"content-row-title"},wt={class:"content-row-txt"},yt={class:"content-row"},ft={class:"content-row-title"},St={class:"content-row-txt"},_t={class:"content-row"},kt={class:"content-row-title"},bt={class:"content-row-txt"},Ct={class:"content-row"};const $t=p({mixins:[{data(){return{loading:!1,calendarVisible:!1,date:this.formatDate((new Date).getTime()),modalShow:!1,active:0,viewSummary:{},tabs:[this.$t("daily"),this.$t("weekly"),this.$t("monthly")],skeletonRowWidth:["40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%"]}},components:{navBar:c,Calendar:g,Tab:w,slideActionSheet:u},methods:{handleNavBarClickLeft(){v("501")},handleNavBarClickRight(){v("220")},handleOpenDatePicker(){this.calendarVisible=!0},handleCalendarClose(){this.calendarVisible=!1},handleCalendarSelect(t){this.date=t,this.calendarVisible=!1,this.getSummary()},formatDate(t){const e=new Date(t);return`${e.getDate().toString().padStart(2,"0")}/${(e.getMonth()+1).toString().padStart(2,"0")}/${e.getFullYear()}`},getSummary(){this.loading=!0;const{accountId:e}=this.$pinia.state.value.params;var a;(a={accountId:e,date:this.date},d("https://stapp.vttechfx.com:16443/stTradeApp/profit-sharing/settlement/follower/v1",a,{apiVer:"v1",userId:j.userId})).then((e=>{var a,s,i;"200"===e.code?(this.dailySummary=null==(a=e.data)?void 0:a.dailyData,this.weeklySummary=null==(s=e.data)?void 0:s.weeklyData,this.monthlySummary=null==(i=e.data)?void 0:i.monthlyData):(this.dailySummary=null,this.weeklySummary=null,this.monthlySummary=null,t(e.msg)),this.hanldeViewSummary(this.active),this.loading=!1})).catch((()=>{this.loading=!1}))},handleTabChange(t){this.active=t,this.hanldeViewSummary(t)},hanldeViewSummary(t){switch(t){case 0:default:this.viewSummary=this.dailySummary;break;case 1:this.viewSummary=this.weeklySummary;break;case 2:this.viewSummary=this.monthlySummary}},handleSummaryModal(){this.modalShow=!0},handleModalClose(){this.modalShow=!1},handleLink(){this.modalShow=!1,this.$router.push({name:"profitSharingRules"}),m({code:"250",title:this.$t("profit_sharing_statement"),iconList:["CLOSE"]})},handleViewDetails(t,e){t.forEach((t=>{t.dateLabel=this.getSettlementDateLabel(t.settleReason)})),this.$router.push({path:"followerDetails",query:{details:JSON.stringify(t),nickName:e}})},getSettlementDateLabel(t){let e="";switch(t.toLowerCase()){case"scheduled":e=0===this.active?"Daily Settlement":1===this.active?"Weekly Settlement":"Monthly Settlement";break;case"stopfollow":e="Stop Copy";break;case"withdrawal":e="Remove Fund"}return e}},mounted(){this.getSummary(),m({code:"250",title:this.$t("profit_sharing_statement"),iconList:["CUSTOMER"]})}}],setup(){const{paramsStore:t}=h();return{emptyImgUrl:L((()=>new URL(Object.assign({"../../assets/images/common/empty_dark.webp":r,"../../assets/images/common/empty_light.webp":l})[`../../assets/images/common/empty_${t.themeTxt}.webp`],import.meta.url).href)),questionImgUrl:L((()=>new URL(Object.assign({"./assets/images/question_dark.webp":o,"./assets/images/question_light.webp":n})[`./assets/images/question_${t.themeTxt}.webp`],import.meta.url).href))}}},[["render",function(t,l,r,n,o,c){const d=y("nav-bar"),h=y("calendar"),m=a,v=s,p=i,u=e,g=y("tab"),w=y("slide-action-sheet");return S(),f("div",R,[_(d,{"left-text":t.$t("profit_sharing_statement"),onClickLeft:t.handleNavBarClickLeft},{default:b((()=>[k("div",{class:"nav-bar_right",onClick:l[0]||(l[0]=(...e)=>t.handleNavBarClickRight&&t.handleNavBarClickRight(...e))},[k("img",{src:n.questionImgUrl,alt:""},null,8,V)])])),_:1},8,["left-text","onClickLeft"]),k("div",I,[_(h,{visible:t.calendarVisible,onClose:t.handleCalendarClose,onSelected:t.handleCalendarSelect},null,8,["visible","onClose","onSelected"]),k("div",N,[k("div",{class:"select-picker-item",onClick:l[1]||(l[1]=(...e)=>t.handleOpenDatePicker&&t.handleOpenDatePicker(...e))},[k("span",P,C(t.date),1),l[4]||(l[4]=k("span",{class:"icon icon-arrow_down"},null,-1))])]),_(g,{tabs:t.tabs,"active-color":"var(--color)",onChange:t.handleTabChange,swipeable:"",animated:""},{default:b((()=>[_(u,{class:"stmt-skeleton",row:t.skeletonRowWidth.length,loading:t.loading,"row-width":t.skeletonRowWidth},{default:b((()=>[t.viewSummary?(S(),f("div",M,[k("div",O,[k("div",T,[k("div",U,C(t.$t("summary")),1),_(v,{class:"icon",name:"info-o",onClick:t.handleSummaryModal},null,8,["onClick"])]),k("div",B,[k("div",E,[k("div",F,C(t.$t("current_periods_payout")),1),k("div",W,C(t.viewSummary.settlementStart)+" 00:00:00 - "+C(t.viewSummary.settlementEnd)+" 23:59:59 ",1)])]),k("div",A,[k("div",J,[k("div",Y,C(t.$t("eligible_profits_for_sharing")),1),k("div",z,C(t.viewSummary.netProfit||0)+" "+C(t.viewSummary.currency),1)]),k("div",K,[k("div",G,C(t.$t("shareable_profits")),1),k("div",H,C(t.viewSummary.sharableProfit||0)+" "+C(t.viewSummary.currency),1)])])]),l[7]||(l[7]=k("div",{class:"section-line"},null,-1)),k("div",Q,[k("div",X,[k("div",Z,C(t.$t("breakdown")),1)]),(S(!0),f(x,null,D(t.viewSummary.breakdown,((e,a)=>(S(),f("div",{class:"card",key:a},[k("div",tt,[k("div",et,[_(p,{square:"",width:"32px",height:"32px",fit:"cover",src:e.avatar},null,8,["src"]),k("div",at,C(e.signalNickname),1)]),k("div",{class:"profile-detail",onClick:a=>t.handleViewDetails(e.followerProfitShareDetails,e.signalNickname)},l[5]||(l[5]=[k("span",{class:"icon icon-docs"},null,-1)]),8,st)]),l[6]||(l[6]=k("div",{class:"thematic-break"},null,-1)),k("div",it,[k("div",lt,C(t.$t("eligible_profits_for_sharing")),1),k("div",rt,C(e.netProfit||0),1)]),k("div",nt,[k("div",ot,C(t.$t("profit_sharing_ratio")),1),k("div",ct,C(100*parseFloat(e.profitSharePercentage)||0)+"% ",1)]),k("div",dt,[k("div",ht,C(t.$t("shareable_profits")),1),k("div",mt,C(e.sharableProfit||0),1)])])))),128))]),l[8]||(l[8]=k("div",{class:"section-line"},null,-1))])):(S(),$(m,{key:0,class:"empty",description:t.$t("no_records_found"),"image-size":"80"},{image:b((()=>[k("img",{src:n.emptyImgUrl,alt:""},null,8,q)])),_:1},8,["description"]))])),_:1},8,["row","loading","row-width"])])),_:1},8,["tabs","onChange"]),_(w,{show:t.modalShow,"onUpdate:show":l[3]||(l[3]=e=>t.modalShow=e),onClose:t.handleModalClose},{content:b((()=>[k("div",vt,[k("div",pt,C(t.$t("glossary")),1),k("div",ut,[k("div",gt,C(t.$t("eligible_profits_for_sharing")),1),k("div",wt,C(t.$t("glossary_eligible_profits_for_sharing")),1)]),k("div",yt,[k("div",ft,C(t.$t("profit_sharing_ratio")),1),k("div",St,C(t.$t("glossary_profit_sharing_ratio")),1)]),k("div",_t,[k("div",kt,C(t.$t("shareable_profits")),1),k("div",bt,C(t.$t("glossary_shareable_profits")),1)]),k("div",Ct,[k("div",{class:"link",onClick:l[2]||(l[2]=(...e)=>t.handleLink&&t.handleLink(...e))},C(t.$t("learn_more")),1)])])])),_:1},8,["show","onClose"])])])}],["__scopeId","data-v-d0a6d571"]]);export{$t as default};
