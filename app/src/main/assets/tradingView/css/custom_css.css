.theme-dark,
:root {
  --dropdown-color: rgb(198, 198, 198);
  --header--gray: rgba(61, 61, 61, 0.65);
  --background--light: #fff;
  --background: var(--background--light);
  --background-light: var(--background--light);
  --header--active: rgb(41, 40, 40);
  --indicator-color: rgb(61, 61, 61);
  --indicator-background: rgba(222, 223, 225, 0.6);
  --dropdown-background: rgb(222, 223, 225);
  --dropdown-active: rgb(3, 72, 84);
  --dropdown-active-color: rgb(222, 223, 225);
  --drawing-close-tooltips: rgb(3, 72, 84);
  --drawing-close-color: white;
}
@font-face {
  font-family: Gilroy-SemiBold;
  src: url("./font/Gilroy-SemiBold.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
  font-feature-settings: unset !important;
}
@font-face {
  font-family: <PERSON><PERSON>-<PERSON>;
  src: url("./font/<PERSON><PERSON>-Regular.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
  font-feature-settings: unset !important;
}
@font-face {
  font-family: <PERSON><PERSON>-Medium;
  src: url("./font/Gilroy-Medium.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
  font-feature-settings: unset !important;
}
.loading-indicator,
.wrapper-b8SxMnzX {
  display: none !important;
}
.theme-dark {
  --header--gray: rgba(255, 255, 255, 0.6);
  --background--dark: rgba(26, 29, 32, 1);
  --background: var(--background--dark);
  --background-light: rgb(243, 245, 247);
  --header--active: rgba(255, 255, 255);
  --indicator-color: rgba(255, 255, 255, 0.87);
  --indicator-background: rgba(38, 41, 48, 1);
  --dropdown-background: rgba(38, 41, 48, 1);
  --dropdown-active: rgba(222, 223, 225, 0.6);
  --dropdown-active-color: rgba(26, 29, 32, 1);
  --drawing-close-tooltips: rgb(243, 245, 247);
  --drawing-close-color: rgb(3, 72, 84);
}
body {
  margin: 0;
  font-family: Gilroy-Medium;
}
html body {
  background-color: var(--background);
  font-feature-settings: unset !important;
}
#topPriceLine,
html #bottomIndicator,
html #topIndicator {
  padding: 0.15rem 0.45rem 0.07rem;
  min-width: 4.5rem;
  display: flex;
  font-family: Gilroy-Medium;
  color: var(--indicator-color);
  width: 100%;
  line-height: normal;
}
#topPriceLine {
  background: var(--indicator-background);
}
.bottomAppend,
.topAppend {
  position: absolute;
  left: 10px;
}
.bottomAppend {
  margin-top: 0.4rem;
}
#topPriceLineAppend {
  position: absolute;
  left: 7.8rem;
  top: 0.64rem;
  display: none;
  animation: 0.2s linear forwards fadeOut;
}
.topAppendDrawing {
  position: absolute;
  top: 65px;
  right: 90px;
  width: 10rem;
}
.topAppendDrawing > .closeImg {
  width: 15px;
  background: var(--drawing-close-tooltips);
  border-radius: 3px;
  position: absolute;
  right: 0.24rem;
  top: 0.25rem;
  display: flex;
  justify-content: center;
  padding: 2px;
  height: 15px;
}
.topAppendDrawing > .closeImg > img {
  width: 100%;
  filter: brightness(0) invert(1);
}
.theme-dark .topAppendDrawing > .closeImg > img {
  filter: unset;
}
.topAppendDrawing > .closeTooltips {
  position: absolute;
  width: 15%;
  top: -0.22rem;
  right: 0;
  font-size: 0.12rem;
  background: var(--drawing-close-tooltips);
  border-radius: 4px;
  padding: 0.1rem;
  text-align: center;
  color: var(--drawing-close-color);
  font-family: Gilroy-Regular;
  animation: 2s infinite bounce2;
}
.topAppendDrawing > .closeTooltips::after {
  content: "";
  position: absolute;
  left: 77%;
  margin-left: -5px;
  top: 98%;
  transform: rotate(270deg);
  border: 10px solid var(--drawing-close-tooltips);
  border-color: transparent var(--drawing-close-tooltips) transparent
    transparent;
  z-index: 999;
}
.bounce2 {
  animation: 2s infinite bounce2;
}
@keyframes bounce2 {
  0%,
  100%,
  20%,
  50%,
  80% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-5px);
  }
}
:root:not(.theme-dark) {
  --tv-color-platform-background: var(--background);
  --tv-color-pane-background: var(--background);
}
:root.theme-dark {
  --tv-color-platform-background: var(--background);
  --tv-color-pane-background: var(--background);
}
html .dialog-UM6w7sFp {
  background-color: var(--background) !important;
}
.layout__area--top {
  width: 98% !important;
}
.group-MBOVGQRI,
html .group-MBOVGQRI:first-of-type {
  font-size: 12px;
  color: var(--header--gray);
}

@media (orientation: portrait) {
  .group-MBOVGQRI,
  html .group-MBOVGQRI:first-of-type {
    margin-left: 0.8rem;
  }
}
.separator-xVhBjD5m {
  width: unset;
}
.customButton-qqNP9X6e,
html.theme-dark .customButton-qqNP9X6e {
  color: var(--header--gray);
}
.fill-OhqNVIYA.group-MBOVGQRI,
.floating-toolbar-react-widgets__button[data-name="more"],
.floating-toolbar-react-widgets__button[data-name="settings"] {
  display: none;
}
.innerWrap-OhqNVIYA {
  justify-content: space-between;
  width: 97.5%;
}
.customButton-qqNP9X6e {
  padding: 0;
}
.group-MBOVGQRI:not(:first-of-type) {
  opacity: 0.8;
}
.topAppend {
  top: 10px;
}
html #bottomIndicator,
html #topIndicator {
  border: none;
  border-radius: 5px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
html #bottomDropdown,
html #topDropdown {
  animation: 0.2s linear forwards fadeOut;
  left: 8.5rem;
  display: block;
  position: absolute;
  background: var(--dropdown-background);
  border-radius: 6px;
  text-align: center;
  color: var(--dropdown-color);
  font-size: 15px;
  z-index: 2;
}
#currentBottomIndicator,
#currentIndicator {
  margin-right: 0.15rem;
  font-size: 0.8rem;
  font-weight: 500;
  font-family: Gilroy-Medium;
}
html #topDropdown {
  top: 0;
}
.bottomDropdownIndicator,
.topDropdownIndicator {
  padding: 15px 50px;
  color: var(--indicator-color);
}
#bottomIndicatorInfo,
#topIndicatorInfo {
  font-size: 0.6rem;
}
.bottomDropdownIndicator.active,
.topDropdownIndicator.active {
  background: var(--dropdown-active);
  color: var(--dropdown-active-color);
  font-weight: 700;
}
.topDropdownIndicator:first-of-type.active {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
html #bottomDropdown {
  top: -6rem;
}
.bottomDropdownIndicator:first-of-type.active {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.arrowContainer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 60%;
  padding-bottom: 0.15rem;
}
.leftRightArrowContainer {
  width: 10%;
}
#leftArrow {
  justify-content: flex-start;
  padding-left: 0.6rem;
}
#rightArrow {
  padding-right: 0.6rem;
}
.indicatorArrow,
.priceLineArrow {
  height: 0.5rem;
}
#topPriceLine {
  flex-direction: row;
  align-items: center;
  border: none;
  border-radius: 5px;
  min-width: 15rem;
  padding: 0.18rem 0 0.1rem;
}
#priceLineContent {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  font-size: 0.7rem;
  font-weight: 100;
  width: 100%;
}
html .apply-common-tooltip.customButton-qqNP9X6e.active,
html .apply-common-tooltip.customButton-qqNP9X6e.tick-active {
  font-family: Gilroy-SemiBold;
  font-size: 1rem;
  color: var(--header--active);
}
.pointer-none {
  pointer-events: none;
}
.button-1WIwNaDF {
  width: 20px;
}
.logo {
  position: absolute;
  bottom: 0.5rem;
  width: 5.5rem;
  left: 0.5rem;
}
.logo > img,
#bottomToast img {
  width: 100%;
}
#bottomToast {
  display: none;
  justify-content: center;
  cursor: default;
  position: absolute;
  bottom: 3rem;
  left: 0rem;
  height: 2rem;
  width: 100%;
}

#bottomToastContainer {
  flex-direction: row;
  align-items: center;
  background-color: #2962ff;
  border-radius: 6px;
  height: 100%;
  display: flex;
  transition: all 0.5s ease-in-out;
  font-family: Gilroy-Regular;
  color: white;
  line-height: normal;
  padding: 0rem 0rem 0rem 0.5rem;
}

#bottomToastContainer > #bottomToastClose {
  width: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-bottom: 0.1rem;
  padding-right: 0.5rem;
  height: 100%;
}

#bottomToastContainer > #content {
  width: 95%;
  font-size: 12px;
  margin-right: .6rem;
}


@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slide-out {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
