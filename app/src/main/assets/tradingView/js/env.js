let mode = getQueryString('mode') || 'prod'; // 环境：测试dev。生产prod
let apiURL= "";
let mtsApiURL= "";
let uuid = "f16ae79d-8964-48df-94e2-b8dj372300ae";
let api_ip = "*******";
let apiVer = "v1";

function getQueryString(key) {
    var result = window.location.search.match(new RegExp("[\?\&]" + key + "=([^\&]+)", "i"));
    if (result == null || result.length < 1) {
        return "";
    }
    return decodeURIComponent(result[1]);
}
let product = getQueryString('product');
if(product == 'vjp'){
  
    apiURL = "https://app.vttechfx.com:18008/trade/order/historyMarkets/tradingView";
    mtsApiURL= "https://stapp.vttechfx.com:16443/stTradeApp/history/getKLine/mts/h5Connector";

    if(mode === 'prod'){
        apiURL = "https://app.vjpappprotech.com:18008/trade/order/historyMarkets/tradingView";
        mtsApiURL= "https://stapp.vttechfx.com:16443/stTradeApp/history/getKLine/mts/h5Connector";
    }
   
}else{
  apiURL= "https://au-one.app-alpha.com:18008/trade/order/historyMarkets/tradingView";
  mtsApiURL= "https://st-app.app-alpha.com:8088/stTradeApp/history/getKLine/mts/h5Connector";
  
  if(mode === 'prod'){
      apiURL = "https://app.vttechfx.com:18008/trade/order/historyMarkets/tradingView";
      mtsApiURL= "https://stapp.vttechfx.com:16443/stTradeApp/history/getKLine/mts/h5Connector";
  }
} 



