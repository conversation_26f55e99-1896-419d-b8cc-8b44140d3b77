<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFE"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <LinearLayout
        android:id="@+id/ll_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#F5F5F5">

            <EditText
                android:id="@+id/et_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@null"
                android:gravity="center_vertical|left"
                android:hint="输入框"
                android:paddingLeft="10dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:textColor="#292929"
                android:textColorHint="#CCCCCC"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/rb_input"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/rb_input"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:paddingLeft="15dp"
                android:paddingTop="5dp"
                android:paddingRight="15dp"
                android:paddingBottom="5dp"
                android:text="点击"
                android:textSize="13dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/et_input"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="5dp"
            android:background="#FF0000" />

    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_top"
        tools:listitem="@layout/item_button" />

</androidx.constraintlayout.widget.ConstraintLayout>


