<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFE"
    android:focusable="true"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingHorizontal="12dp"
    android:focusableInTouchMode="true">

    <cn.com.vau.util.widget.LoadingButtonView
        android:id="@+id/loadingView1"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        app:lbv_button_text_color="#15B374"
        app:lbv_button_background_color="#1E1E1E"
        app:lbv_icon_start="@drawable/icon_quick_close_test" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:id="@+id/loadingView2"
        android:layout_marginTop="10dp"
        android:layout_width="200dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        app:lbv_loading_color="#1534B9"
        app:lbv_button_text_color="#15B374"
        app:lbv_button_text="111111" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:layout_marginTop="10dp"
        android:id="@+id/loadingView3"
        app:lbv_icon_start="@drawable/icon_quick_close_test"
        app:lbv_loading_color="#15B374"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:lbv_button_background_color="#1e1e1e"
        app:lbv_button_text="Sell" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:layout_marginTop="10dp"
        android:id="@+id/loadingView4"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:lbv_icon_end="@drawable/icon_quick_close_test"
        app:lbv_button_background_color="#1E1E1E"
        app:lbv_button_text="Sell" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:layout_marginTop="10dp"
        android:id="@+id/loadingView5"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:lbv_button_background_color="#1E1E1E"
        app:lbv_button_text="Sell111111Sell111111Sell111111Sell1111111111112" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:layout_marginTop="10dp"
        android:id="@+id/loadingView6"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:lbv_button_background_color="#1E1E1E"
        app:lbv_icon_start="@drawable/icon_quick_close_test"
        app:lbv_button_text="Sell111111111111111111111111111111111111111111111111111111111112" />

    <cn.com.vau.util.widget.LoadingButtonView
        android:layout_marginTop="10dp"
        android:id="@+id/loadingView7"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:lbv_button_background_color="#1E1E1E"
        app:lbv_icon_end="@drawable/icon_quick_close_test"
        app:lbv_loading_color="#15B374"
        app:lbv_button_text="Sell111111111111111111111111111111111111111111111111111111111112" />

</LinearLayout>


