package com.kit

import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import cn.com.vau.R
import cn.com.vau.common.adapter.MultiTypeBindingAdapter
import cn.com.vau.common.adapter.buildMultiTypeAdapterByType
import cn.com.vau.common.adapter.replaceData
import cn.com.vau.common.ext.launchActivity
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.ActivityZlBinding
import cn.com.vau.databinding.ViewItemBinding
import cn.com.vau.demo.DashedTextDemoActivity
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px

class ZLActivity : BaseMvvmActivity<ActivityZlBinding, ZLViewModel>() {

    var adapter: MultiTypeBindingAdapter<BaseItemBean, ViewBinding>? = null

    override fun initView() {

        adapter = buildAdapter()
        mBinding.rv.layoutManager = LinearLayoutManager(this)
        mBinding.rv.adapter = adapter
        adapter?.replaceData(getList())

        mBinding.rv.addItemDecoration(
            DividerItemDecoration(0.5.dp2px(), dividerColor = ContextCompat.getColor(this, R.color.cf44040))
        )

    }

    private fun buildAdapter() = buildMultiTypeAdapterByType<BaseItemBean> {

        layout(ViewItemBinding::inflate) { _, item: ItemBean ->
            binding.tvTitle.setTextOrHide(item.title)
            binding.tvContent.setTextOrHide(item.content)
            binding.tvContent.setOnLongClickListener {
                item.longClick?.invoke()
                return@setOnLongClickListener true
            }
            binding.btnAction.setTextOrHide(item.btnText)
            binding.btnAction.clickNoRepeat {
                item.click?.invoke()
            }

        }

    }

    private fun getList(): List<BaseItemBean> {
        val list = mutableListOf<BaseItemBean>()


        list.add(ItemBean(title = "虚线TextView", btnText = "查看") {
            launchActivity<DashedTextDemoActivity>()
        })

        list.add(ItemBean(title = "手机号", content = "1222222"))


        return list

    }

    private fun TextView.setTextOrHide(text: String?) {
        if (text.isNullOrBlank()) {
            this.visibility = TextView.GONE
        } else {
            this.text = text
            this.visibility = TextView.VISIBLE
        }
    }

}

class ZLViewModel : BaseViewModel() {

}

open class BaseItemBean()

class ItemBean(var title: String = "", var content: String = "", var btnText: String = "", var longClick: (() -> Unit)? = null, var click: (() -> Unit)? = null) : BaseItemBean()



