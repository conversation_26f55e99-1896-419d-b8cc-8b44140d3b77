package cn.com.vau.kit.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;



public class KitDateUtil {

    //获得年月日
    public static String getYearMonthDay() {
        Calendar instance = Calendar.getInstance();
        int year = instance.get(Calendar.YEAR);
        int month = instance.get(Calendar.MONTH) + 1;
        int day = instance.get(Calendar.DAY_OF_MONTH);
        return year + "-" + month + "-" + day;
    }

    //获得时分秒
    public static String getHourMinuteSecond() {
        Calendar instance = Calendar.getInstance();
        int hour = instance.get(Calendar.HOUR);
        int minute = instance.get(Calendar.MINUTE);
        int second = instance.get(Calendar.SECOND);
        return hour + ":" + minute + ":" + second;
    }

    //毫秒转化为 年月日标准格式
    public static String formatter(long millSecond) {
        String time = SimpleDateFormat.getTimeInstance().format(new Date(millSecond));
        String date = SimpleDateFormat.getDateInstance().format(new Date(millSecond));
        return date + time;
    }

}
