package cn.com.vau.kit.adapter.sendbox;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import cn.com.vau.kit.R;
import cn.com.vau.kit.adapter.ViewAndHolderItem;
import cn.com.vau.kit.adapter.base.BaseKitHolder;
import cn.com.vau.kit.bean.BaseSpInfo;
import cn.com.vau.kit.bean.StringSpInfo;

public class StringViewHolderFactory implements ViewAndHolderItem<BaseSpInfo, StringViewHolderFactory.StringViewHolder> {

    private Context context;

    private SharedPreferences.Editor editor;

    public StringViewHolderFactory(Context context, SharedPreferences.Editor editor) {
        this.context = context;
        this.editor = editor;
    }

    @Override
    public boolean isSupport(BaseSpInfo baseSpInfo) {
        return baseSpInfo instanceof StringSpInfo;
    }

    @Override
    public StringViewHolder createHolder(View convertView) {
        return new StringViewHolder(convertView, editor);
    }

    @Override
    public View createConvertView() {
        return LayoutInflater.from(context).inflate(R.layout.kit_send_box_sp_string_item, null);
    }

    public static class StringViewHolder extends BaseKitHolder<StringSpInfo> {


        private SharedPreferences.Editor editor;
        private final TextView mKeyName;
        private final TextView mValueClass;
        private final EditText mValue;

        public StringViewHolder(View convertView, SharedPreferences.Editor editor) {
            this.editor = editor;
            mKeyName = convertView.findViewById(R.id.mKeyName);
            mValueClass = convertView.findViewById(R.id.mValueClass);
            mValue = convertView.findViewById(R.id.mValue);
        }

        @Override
        protected void initValue(final StringSpInfo stringSpInfo) {
            mKeyName.setText(stringSpInfo.getKey());
            mValueClass.setText("String");
            mValue.setText(stringSpInfo.getValue());
            mValue.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    String result = editable.toString();
                    editor.putString(stringSpInfo.getKey(), result);
                    stringSpInfo.setValue(result);
                }
            });
        }
    }

}
