package cn.com.vau.kit.constant;



public interface SharedPrefsKey {
    String FRAME_INFO_FPS_OPEN = "frame_info_fps_open";
    String FRAME_INFO_CPU_OPEN = "frame_info_cpu_open";
    String FRAME_INFO_MEMORY_OPEN = "frame_info_memory_open";
    String FRAME_INFO_TRAFFIC_OPEN = "frame_info_traffic_open";
    String GPS_MOCK_OPEN = "gps_mock_open";
    String CRASH_OPEN = "crash_open";
    String FLOAT_ICON_POS_X = "float_icon_pos_x";
    String FLOAT_ICON_POS_Y = "float_icon_pos_y";
    String LOG_INFO_OPEN = "log_info_open";
    String COLOR_PICK_OPEN = "color_pick_open";
    String ALIGN_RULER_OPEN = "align_ruler_open";
    String VIEW_CHECK_OPEN = "view_check_open";
    String LAYOUT_BORDER_OPEN = "layout_border_open";
    String LAYOUT_LEVEL_OPEN = "layout_level_open";
    String TOP_ACTIVITY_OPEN = "top_activity_open";
}
