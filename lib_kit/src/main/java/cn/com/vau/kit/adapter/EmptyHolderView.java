package cn.com.vau.kit.adapter;

import android.view.View;
import android.widget.TextView;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.adapter.base.BaseKitHolder;


public class EmptyHolderView implements ViewAndHolderItem {

    @Override
    public boolean isSupport(Object o) {
        return true;
    }

    @Override
    public BaseKitHolder createHolder(View convertView) {
        return new EmptyHolder();
    }

    @Override
    public View createConvertView() {
        return new TextView(Kit.getInstance().getApplication());
    }

    public class EmptyHolder extends BaseKitHolder {

        @Override
        protected void initValue(Object o) {

        }
    }
}
