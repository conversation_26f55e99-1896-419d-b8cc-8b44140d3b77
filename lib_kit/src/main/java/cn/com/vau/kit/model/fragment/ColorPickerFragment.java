package cn.com.vau.kit.model.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.model.pickcolor.ColorPickerFloatPage;
import cn.com.vau.kit.model.pickcolor.ColorPickerInfoFloatPage;
import cn.com.vau.kit.model.pickcolor.FloatPageManager;
import cn.com.vau.kit.model.pickcolor.PageIntent;
import cn.com.vau.kit.model.pickcolor.PageTag;



public class ColorPickerFragment extends BaseKitFragment {

    private static final int REQUEST_CODE = 1000;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        requestCaptureScreen();
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    public boolean requestCaptureScreen() {
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
            return false;
        }
        MediaProjectionManager mediaProjectionManager = (MediaProjectionManager) Kit.getInstance().getApplication().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        if (mediaProjectionManager == null) {
            return false;
        }
        startActivityForResult(mediaProjectionManager.createScreenCaptureIntent(), REQUEST_CODE);
        return true;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            showColorPicker(data);
        }
    }

    private void showColorPicker(Intent data) {
        PageIntent pageIntent = new PageIntent(ColorPickerInfoFloatPage.class);
        pageIntent.tag = PageTag.PAGE_COLOR_PICKER_INFO;
        pageIntent.mode = PageIntent.MODE_SINGLE_INSTANCE;
        FloatPageManager.getInstance().add(pageIntent);

        pageIntent = new PageIntent(ColorPickerFloatPage.class);
        pageIntent.bundle = data.getExtras();
        pageIntent.mode = PageIntent.MODE_SINGLE_INSTANCE;
        FloatPageManager.getInstance().add(pageIntent);
    }

}
