<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:paddingLeft="10dp"
        android:textColor="#337CC4"
        android:textSize="15dp"
        tools:text="权限名称"
        android:id="@+id/mName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:paddingLeft="10dp"
        android:layout_marginTop="10dp"
        android:id="@+id/mContent"
        android:textSize="12dp"
        tools:text="权限含义"
        android:textColor="#337CC4"
        app:layout_constraintTop_toBottomOf="@+id/mName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:textStyle="bold"
        android:paddingLeft="10dp"
        android:layout_marginTop="10dp"
        android:textColor="#0D47A1"
        android:id="@+id/mPermissionStatus"
        android:textSize="12dp"
        tools:text="已有权限"
        app:layout_constraintTop_toBottomOf="@+id/mContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/mPermissionStatus"
        android:background="#FF9800"
        android:layout_width="match_parent"
        android:layout_height="1dp" />

</androidx.constraintlayout.widget.ConstraintLayout>