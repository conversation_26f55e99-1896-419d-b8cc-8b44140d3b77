package com.github.tifezh.kchartlib.utils.klineoption

import androidx.annotation.Keep

@Keep
data class KlineOptionOneLineValueBean(
    var name: String,
    var value: Int,
    val defaultValue: Int,
    val isValueInteger: Boolean,
    val minValue: Int,
    val maxValue: Int,
    val mainTitle: String,
    val title: String,
    var enable: Boolean = true,
    val defaultEnable: Boolean = true,
    val canChangeEnable: Boolean = false,//能否切换enable
)