package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

@Keep
public class BbiIndexEntity extends BaseIndexEntity {
    private float bbi;

    @Override
    public void reset() {
        super.reset();
        bbi = 0f;
    }

    public void setBbi(float bbi) {
        this.bbi = bbi;
        setComputed(true);
    }

    public float getBbi() {
        return bbi;
    }

    @NonNull
    @Override
    public String toString() {
        return "BBIIndexEntity{" + "bbi=" + bbi + '}';
    }
}