package com.github.tifezh.kchartlib.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RadialGradient;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.helper.chart.KChartView;
import com.github.tifezh.kchartlib.utils.BraetheInterpolator;

/**
 * author : wanghui
 * date : 2020/6/8 2:20 PM
 * description : 呼吸灯view
 */
public class KlineBreathingLampView extends View {

    private static final String TAG = "KlineBreathView";
    private ValueAnimator mBreathAnimator;
    private float mBreathAnimatorValue;

    private Paint mBreathCirclePaint;
    private int mBreathCircleRadius;
    private Paint mBreathRadiaPaint;
    private int mBreathRadiaCircleRadius;
    private float mX, mY;
    private boolean isDrawBreath;
    private long mLastDrawTime;
    private BaseKChartView.OnCurrentPricePositionListener mOnCurrentPricePostionListener;
    private int radarStartColor = Color.parseColor("#66FFFFFF");
    private int radarEndColor = Color.TRANSPARENT;

    public KlineBreathingLampView(Context context) {
        this(context, null);
    }

    public KlineBreathingLampView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public KlineBreathingLampView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mBreathCirclePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBreathCirclePaint.setColor(Color.WHITE);
        mBreathCirclePaint.setStyle(Paint.Style.FILL);
        mBreathCircleRadius = dp2px(3);
        mBreathRadiaPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBreathRadiaPaint.setStyle(Paint.Style.FILL);
        mBreathRadiaCircleRadius = dp2px(11);

    }


    private Animator getBreathAnimator() {
        if (mBreathAnimator == null) {
            mBreathAnimator = ValueAnimator.ofFloat(0, 1);
            mBreathAnimator.setDuration(2000);
            mBreathAnimator.setRepeatCount(ValueAnimator.INFINITE);
            mBreathAnimator.setInterpolator(new BraetheInterpolator());
            mBreathAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    float animatedValue = (Float) animation.getAnimatedValue();
//                    Log.e(TAG, "onAnimationUpdate     animatedValue=" + animatedValue);
                    mBreathAnimatorValue = animatedValue;
                    if (System.currentTimeMillis() - mLastDrawTime > 30) {
                        invalidate();
                    }
                }
            });
        }
        return mBreathAnimator;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!isDrawBreath) {
            return;
        }
        float radius = mBreathRadiaCircleRadius * mBreathAnimatorValue;
        if (radius == 0) {
            return;
        }
        if (mX == 0 && mY == 0) {
            return;
        }

        try {
            mLastDrawTime = System.currentTimeMillis();
            RadialGradient mBreathRadialGradient = new RadialGradient(mX, mY, radius, radarStartColor, radarEndColor, Shader.TileMode.CLAMP);
            mBreathRadiaPaint.setShader(mBreathRadialGradient);
            canvas.drawCircle(mX, mY, radius, mBreathRadiaPaint);
            canvas.drawCircle(mX, mY, mBreathCircleRadius, mBreathCirclePaint);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void startBreathAnimatorWrapper() {
        mX = 0;
        mY = 0;
        startBreathAnimator();
        isDrawBreath = true;
    }

    private void startBreathAnimator() {
        Animator breathAnimator = getBreathAnimator();
        breathAnimator.start();
    }

    public void stopBreathAnimator() {
        isDrawBreath = false;
        Animator breathAnimator = getBreathAnimator();
        breathAnimator.cancel();
        mBreathAnimator = null;
        invalidate();
    }

    public int dp2px(float dp) {
        final float scale = getContext().getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    public int sp2px(float spValue) {
        final float fontScale = getContext().getResources().getDisplayMetrics().scaledDensity;
        return (int) (spValue * fontScale + 0.5f);
    }

    public void setKchartView(KChartView kChartView) {
        mOnCurrentPricePostionListener = new BaseKChartView.OnCurrentPricePositionListener() {
            @Override
            public void OnCurrentPricePosition(float x, float y) {
                mX = x;
                mY = y;
//                invalidate(); 不要刷新
            }
        };
        kChartView.setOnCurrentPricePostionListener(mOnCurrentPricePostionListener);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mOnCurrentPricePostionListener = null;
    }

    public void setCircleColor(int circleColor) {
        mBreathCirclePaint.setColor(circleColor);
    }

    public void setRadarStartColor(int radarStartColor) {
        this.radarStartColor = radarStartColor;
    }

    public void setRadarEndColor(int radarEndColor) {
        this.radarEndColor = radarEndColor;
    }
}