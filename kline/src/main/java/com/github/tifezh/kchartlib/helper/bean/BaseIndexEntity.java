package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.CallSuper;
import androidx.annotation.Keep;

@Keep
public abstract class BaseIndexEntity {
    private boolean isComputed;
    
    public final boolean isComputed() {
        return this.isComputed;
    }
    @CallSuper
    public void reset() {
        this.isComputed = false;
    }

    public final void setComputed(boolean isComputed) {
        this.isComputed = isComputed;
    }
}