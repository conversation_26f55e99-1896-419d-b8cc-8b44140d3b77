package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
@Keep
public class SarIndexEntity extends BaseIndexEntity {
    public float sar;

    @Override
    public void reset() {
        super.reset();
        sar = 0f;
    }

    public float getSar() {
        return sar;
    }

    public void setSar(float sar) {
        this.sar = sar;
        setComputed(true);
    }

    @NonNull
    @Override
    public String toString() {
        return "RSIIndexEntity{" +
                "Sar=" + sar +
                '}';
    }
}