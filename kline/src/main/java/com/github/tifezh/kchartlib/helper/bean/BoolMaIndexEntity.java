package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;

@Keep
public class BoolMaIndexEntity extends BaseIndexEntity {
    private float mA20Price;

    @Override
    public void reset() {
        super.reset();
        mA20Price = 0f;

    }
    public void setMA20Price(float mA20Price) {
        this.mA20Price = mA20Price;
        setComputed(true);
    }

    public float getMA20Price() {
        return mA20Price;
    }
}