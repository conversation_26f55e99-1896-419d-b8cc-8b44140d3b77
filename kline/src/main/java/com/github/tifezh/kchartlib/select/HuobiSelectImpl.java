package com.github.tifezh.kchartlib.select;

import android.text.TextUtils;

import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

/**
 * author : wanghui
 * date : 2020/7/24 11:18 AM
 * description :
 */
public class HuobiSelectImpl implements ISelect, ISelectGestureListener {
    private static final String TAG = "HuobiSelectImpl";

    ISelectCallBack callBack;
    private float mDownX;
    private boolean mCheckShouldShowMove;

    public HuobiSelectImpl(ISelectCallBack callBack) {
        this.callBack = callBack;
    }

    //选中线没有状态
    protected final static String SHOW_SELECTED_TYPE_NO = "show_Selected_type_no";
    //选中线处于点击状态
    protected final static String SHOW_SELECTED_TYPE_CLICK = "show_Selected_type_click";
    //选中线处于长按状态
    protected final static String SHOW_SELECTED_TYPE_LONG_PRESS = "show_Selected_type_long_press";
    //选中线的状态
    protected String showSelectedType = SHOW_SELECTED_TYPE_NO;


    @Override
    public boolean onDown(MotionEvent e) {
        return false;
    }


    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_NO)) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_CLICK, e);
        } else if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_LONG_PRESS)) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, e);
        } else {
            onSelected(e);
        }
        return true;
    }

    //切换选中类型
    protected void changeShowSelectedType(String type, MotionEvent event) {
        showSelectedType = type;
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_NO)) {
            onUnSelected();
        } else {
            onSelected(event);
        }
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_LONG_PRESS)) {
            onSelected(e2);
            return true;
        } else if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_CLICK)) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
//            scrollBy(Math.round(distanceX), 0);
            return false;
        } else {
//            scrollBy(Math.round(distanceX), 0);
            return false;
        }
    }

    @Override
    public void onLongPress(MotionEvent e) {
        if (e.getPointerCount() == 1) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_LONG_PRESS, e);
        }
    }

    @Override
    public boolean onScale(ScaleGestureDetector detector) {
        if (isSelected()) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
        }
        return false;
    }


    @Override
    public boolean onTouchEvent(MotionEvent e) {
        if (isSelected() && e.getPointerCount() > 1) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
        }

        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = e.getX();
                break;
            case MotionEvent.ACTION_MOVE:
                if (SHOW_SELECTED_TYPE_LONG_PRESS.equals(showSelectedType)) {
                    //需要检查
                    if (mCheckShouldShowMove) {
                        //大于指定值
                        if (e.getX() - mDownX > 2) {
                            mCheckShouldShowMove = false;
                            onSelected(e);
                        }
                    } else {
                        onSelected(e);
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mCheckShouldShowMove = true;
                break;
        }

        return false;
    }

    @Override
    public void onSelected(MotionEvent event) {
        if (callBack != null) {
            callBack.showSelectedLine(event);
        }
    }

    @Override
    public void onUnSelected() {
        if (callBack != null) {
            callBack.showUnSelectedLine();
        }
    }

    @Override
    public void setUnSelected() {
        changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
    }

    @Override
    public boolean isSelected() {
        return !SHOW_SELECTED_TYPE_NO.equals(showSelectedType);
    }

    @Override
    public boolean isAllowOtherEvnent() {
        return SHOW_SELECTED_TYPE_LONG_PRESS.equals(showSelectedType);
    }

}
