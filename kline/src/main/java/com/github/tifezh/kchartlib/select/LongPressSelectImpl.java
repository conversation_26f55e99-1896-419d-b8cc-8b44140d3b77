package com.github.tifezh.kchartlib.select;

import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

/**
 * author : wanghui
 * date : 2020/7/25 9:52 AM
 * description :
 */
public class LongPressSelectImpl implements ISelect, ISelectGestureListener {
    boolean isLongPress;
    ISelectCallBack callBack;

    public LongPressSelectImpl(ISelectCallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onSelected(MotionEvent event) {
        if (callBack != null) {
            callBack.showSelectedLine(event);
        }
    }

    @Override
    public void onUnSelected() {
        if (callBack != null) {
            callBack.showUnSelectedLine();
        }
    }

    @Override
    public boolean onDown(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (isLongPress) {
//            onSelected(e2);
            return true;
        }
        return false;
    }

    @Override
    public void onLongPress(MotionEvent e) {
        if (e.getPointerCount() == 1) {
            isLongPress = true;
            onSelected(e);

            if (callBack != null)
                callBack.disallowIntercept();
        }
    }

    @Override
    public boolean onScale(ScaleGestureDetector detector) {
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        if (isLongPress && e.getPointerCount() > 1) {
            isLongPress = false;
            onUnSelected();
        }
        switch (e.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_MOVE:
                if (isLongPress) {
                    onSelected(e);
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                isLongPress = false;
                onUnSelected();
                break;
        }
        return false;
    }

    @Override
    public void setUnSelected() {
        isLongPress = false;
    }

    @Override
    public boolean isSelected() {
        return isLongPress;
    }

    @Override
    public boolean isAllowOtherEvnent() {
        return isLongPress;
    }
}
