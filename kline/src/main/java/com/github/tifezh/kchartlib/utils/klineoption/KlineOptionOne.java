package com.github.tifezh.kchartlib.utils.klineoption;

import java.util.ArrayList;
import java.util.List;

/**
 * author : wanghui
 * date : 2020/7/2 4:58 PM
 * description :
 */
public class KlineOptionOne implements Cloneable {
    private List<KlineOptionOneLineValueBean> klineOptionValues = new ArrayList<>();
    private List<KlineOptionOneLineColorBean> klineOptionColors;
    private KlineOptionOneLineWidthBean klineOptionWidth;

    public List<KlineOptionOneLineValueBean> getKlineOptionValues() {
        return klineOptionValues;
    }

    public void setKlineOptionValues(List<KlineOptionOneLineValueBean> klineOptionValues) {
        this.klineOptionValues = klineOptionValues;
    }

    public List<KlineOptionOneLineColorBean> getKlineOptionColors() {
        return klineOptionColors;
    }

    public void setKlineOptionColors(List<KlineOptionOneLineColorBean> klineOptionColors) {
        this.klineOptionColors = klineOptionColors;
    }

    public KlineOptionOneLineWidthBean getKlineOptionWidth() {
        return klineOptionWidth;
    }

    public void setKlineOptionWidth(KlineOptionOneLineWidthBean klineOptionWidth) {
        this.klineOptionWidth = klineOptionWidth;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        KlineOptionOne that = (KlineOptionOne) o;

        if (klineOptionValues != null ? !klineOptionValues.equals(that.klineOptionValues) : that.klineOptionValues != null)
            return false;
        if (klineOptionColors != null ? !klineOptionColors.equals(that.klineOptionColors) : that.klineOptionColors != null)
            return false;
        return klineOptionWidth != null ? klineOptionWidth.equals(that.klineOptionWidth) : that.klineOptionWidth == null;
    }

    @Override
    public int hashCode() {
        int result = klineOptionValues != null ? klineOptionValues.hashCode() : 0;
        result = 31 * result + (klineOptionColors != null ? klineOptionColors.hashCode() : 0);
        result = 31 * result + (klineOptionWidth != null ? klineOptionWidth.hashCode() : 0);
        return result;
    }

    @Override
    protected KlineOptionOne clone() throws CloneNotSupportedException {
        return (KlineOptionOne) super.clone();
    }
}
