package com.github.tifezh.kchartlib.chart.draw

import android.content.Context
import android.graphics.*
import android.util.LruCache
import android.util.Pair
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.core.content.ContextCompat
import com.example.myapplication.R
import com.github.tifezh.kchartlib.chart.EntityImpl.DMIImpl
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl
import com.github.tifezh.kchartlib.chart.impl.*
import com.github.tifezh.kchartlib.utils.DpConstant

class DMIDraw(private val context: Context) : SimpleChartDraw<DMIImpl>() {

    private val mDMIPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.kchartlib_ma1)
        strokeWidth = context.resources.getDimension(R.dimen.chart_line_width)
        strokeCap = Paint.Cap.ROUND
        textSize = context.resources.getDimension(R.dimen.chart_zhibiao_text_size)
    }

    private val mDMIMinusPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.kchartlib_ma2)
        strokeWidth = context.resources.getDimension(R.dimen.chart_line_width)
        strokeCap = Paint.Cap.ROUND
        textSize = context.resources.getDimension(R.dimen.chart_zhibiao_text_size)
    }

    private val mADXPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.kchartlib_ma3)
        strokeWidth = context.resources.getDimension(R.dimen.chart_line_width)
        strokeCap = Paint.Cap.ROUND
        textSize = context.resources.getDimension(R.dimen.chart_zhibiao_text_size)
    }

    private val mADXRPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.kchartlib_ma4)
        strokeWidth = context.resources.getDimension(R.dimen.chart_line_width)
        strokeCap = Paint.Cap.ROUND
        textSize = context.resources.getDimension(R.dimen.chart_zhibiao_text_size)
    }

    private val paintList = listOf(mDMIPaint, mDMIMinusPaint, mADXPaint, mADXRPaint)

    override fun drawTranslated(@Nullable lastPoint: DMIImpl?, @NonNull curPoint: DMIImpl, lastX: Float, curX: Float, @NonNull canvas: Canvas, @NonNull view: IKChartView, position: Int) {
    }

    private var mDMILines: FloatArray? = null
    private var mDMIMinusLines: FloatArray? = null
    private var mADXLines: FloatArray? = null
    private var mADXRLines: FloatArray? = null

    override fun drawOnScreen(canvas: Canvas, view: IKChartView) {
//        super.drawOnScreen(canvas, view);
        val mPointList = view.pointList
        if (mPointList == null || mPointList.isEmpty()) {
            return
        }
        val pointSize = mPointList.size

        val linesSize = pointSize * 4
        var idxDMI = 0
        var idxDMIMinus = 0
        var idxADX = 0
        var idxADXR = 0
        var isNeedDraw = false
        if (mDMILines == null || mDMILines!!.size != linesSize) {
            mDMILines = FloatArray(linesSize)
        }
        if (mDMIMinusLines == null || mDMIMinusLines!!.size != linesSize) {
            mDMIMinusLines = FloatArray(linesSize)
        }
        if (mADXLines == null || mADXLines!!.size != linesSize) {
            mADXLines = FloatArray(linesSize)
        }
        if (mADXRLines == null || mADXRLines!!.size != linesSize) {
            mADXRLines = FloatArray(linesSize)
        }
        for (i in 1..<pointSize) {
            val lastPointPair: Pair<Float, KLineImpl> = mPointList[i - 1]
            val cuxPointPair: Pair<Float, KLineImpl> = mPointList[i]
            val lastX = lastPointPair.first
            val lastPoint = lastPointPair.second
            val curX = cuxPointPair.first
            val curPoint = cuxPointPair.second

            if (lastPoint == null || curPoint == null) {
                continue
            }
            mDMILines?.let {
                it[idxDMI++] = lastX
                it[idxDMI++] = getChildY(lastPoint.getDMIPlus())
                it[idxDMI++] = curX
                it[idxDMI++] = getChildY(curPoint.getDMIPlus())
            }

            mDMIMinusLines?.let {
                it[idxDMIMinus++] = lastX
                it[idxDMIMinus++] = getChildY(lastPoint.getDMIMinus())
                it[idxDMIMinus++] = curX
                it[idxDMIMinus++] = getChildY(curPoint.getDMIMinus())
            }

            mADXLines?.let {
                it[idxADX++] = lastX
                it[idxADX++] = getChildY(lastPoint.getADX())
                it[idxADX++] = curX
                it[idxADX++] = getChildY(curPoint.getADX())
            }

            mADXRLines?.let {
                it[idxADXR++] = lastX
                it[idxADXR++] = getChildY(lastPoint.getADXR())
                it[idxADXR++] = curX
                it[idxADXR++] = getChildY(curPoint.getADXR())
            }
            isNeedDraw = true
        }

        if (isNeedDraw) {
            if (mDMILines!!.size > idxDMI) {
                canvas.drawLines(mDMILines!!, 0, idxDMI, mDMIPaint)
            } else {
                canvas.drawLines(mDMILines!!, mDMIPaint)
            }
            if (mDMIMinusLines!!.size > idxDMIMinus) {
                canvas.drawLines(mDMIMinusLines!!, 0, idxDMIMinus, mDMIMinusPaint)
            } else {
                canvas.drawLines(mDMIMinusLines!!, mDMIMinusPaint)
            }
            if (mADXLines!!.size > idxADX) {
                canvas.drawLines(mADXLines!!, 0, idxADX, mADXPaint)
            } else {
                canvas.drawLines(mADXLines!!, mADXPaint)
            }
            if (mADXRLines!!.size > idxADX) {
                canvas.drawLines(mADXRLines!!, 0, idxADXR, mADXRPaint)
            } else {
                canvas.drawLines(mADXRLines!!, mADXRPaint)
            }
        }
    }

    override fun onDrawScreenLine(@Nullable lastPoint: DMIImpl?, @NonNull curPoint: DMIImpl, lastX: Float, curX: Float, @NonNull canvas: Canvas, @NonNull view: IKChartView, position: Int) {
        if (lastPoint != null) {
            drawChildLine(canvas, mDMIPaint, lastX, lastPoint.getDMIPlus(), curX, curPoint.getDMIPlus())
            drawChildLine(canvas, mDMIMinusPaint, lastX, lastPoint.getDMIMinus(), curX, curPoint.getDMIMinus())
            drawChildLine(canvas, mADXPaint, lastX, lastPoint.getADX(), curX, curPoint.getADX())
            drawChildLine(canvas, mADXRPaint, lastX, lastPoint.getADXR(), curX, curPoint.getADXR())
        }
    }

    private val mDMITextCache = LruCache<String, Float>(16)
    private val mDMIMinusTextCache = LruCache<String, Float>(16)
    private val mADXTextCache = LruCache<String, Float>(16)

    override fun drawText(@NonNull canvas: Canvas, @NonNull view: IKChartView, position: Int, x: Float, y: Float) {
        var x = x + DpConstant.dp10()

        var text = "DMI"
        var width: Float?
        canvas.drawText(text, x, y, mDMIPaint)

        width = mDMITextCache.get(text)
        if (width == null || width <= 0f) {
            width = mDMIPaint.measureText(text)
            mDMITextCache.put(text, width)
        }
        x += width + MainDraw.mTextMargin

        val point = view.getItem(position) as DMIImpl
        text = "DMI+:" + view.formatValue(point.getDMIPlus())
        canvas.drawText(text, x, y, mDMIPaint)

        width = mDMITextCache.get(text)
        if (width == null || width <= 0f) {
            width = mDMIPaint.measureText(text)
            mDMITextCache.put(text, width)
        }
        x += width + MainDraw.mTextMargin

        text = "DMI-:" + view.formatValue(point.getDMIMinus())
        canvas.drawText(text, x, y, mDMIMinusPaint)

        width = mDMIMinusTextCache.get(text)
        if (width == null || width <= 0f) {
            width = mDMIMinusPaint.measureText(text)
            mDMIMinusTextCache.put(text, width)
        }
        x += width + MainDraw.mTextMargin

        text = "ADX:" + view.formatValue(point.getADX())
        canvas.drawText(text, x, y, mADXPaint)

        width = mADXTextCache.get(text)
        if (width == null || width <= 0f) {
            width = mADXPaint.measureText(text)
            mADXTextCache.put(text, width)
        }
        x += width + MainDraw.mTextMargin

        text = "ADXR:" + view.formatValue(point.getADXR())
        canvas.drawText(text, x, y, mADXRPaint)
        //     x += mADXRPaint.measureText(text) + MainDraw.mTextMargin

//        text = "DX:" + view.formatValue(point.getDX())
//        canvas.drawText(text, x, y, mADXPaint)
//        x += mADXPaint.measureText(text) + MainDraw.mTextMargin
    }

    override fun setValueFormatter(valueFormatter: IValueFormatter) {
    }

    override fun getValueFormatter(): IValueFormatter? {
        return null
    }

    override fun getMaxValue(point: DMIImpl, position: Int): Float {
        return maxOf(point.getDMIPlus(), point.getDMIMinus(), point.getADX(), point.getADXR())
    }

    override fun getMinValue(point: DMIImpl, position: Int): Float {
        return minOf(point.getDMIPlus(), point.getDMIMinus(), point.getADX(), point.getADXR())
    }

    override fun setTypeFace(typeFace: Typeface?) {
        typeFace ?: return
        paintList.forEach { it.typeface = typeFace }
    }
}