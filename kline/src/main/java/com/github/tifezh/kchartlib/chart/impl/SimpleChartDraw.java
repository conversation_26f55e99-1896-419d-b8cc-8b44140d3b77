package com.github.tifezh.kchartlib.chart.impl;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.utils.klineoption.KlineOptionOneLineValueBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 画图的基类 根据实体来画图形
 * Created by tifezh on 2016/6/14.
 */

public abstract class SimpleChartDraw<T> implements IChartDraw<T> {

    protected final List<Integer> indicatorValues = new ArrayList<>();
    public Rect rect = new Rect();
    public float mMinValue = Float.MAX_VALUE;
    public float mMaxValue = -Float.MAX_VALUE;
    public float mChildScaleY = 1;

    protected int mStartIndex = 0;

    public float getChildY(float value) {
        return rect.bottom - (value - mMinValue) * mChildScaleY;
        //        mChildRect.bottom - (value - mChildMinValue) * mChildScaleY
    }

    public float getChildYValue(float y) {
        return mMinValue + (rect.bottom - y) / mChildScaleY;

    }

    public void drawChildLine(Canvas canvas, Paint paint, float startX, float startValue, float stopX, float stopValue) {
        canvas.drawLine(startX, getChildY(startValue), stopX, getChildY(stopValue), paint);
    }

    public abstract void setTypeFace(Typeface typeFace);
    @Override
    public IValueFormatter getValueFormatter() {
        return null;
    }
    @Override
    public void setValueFormatter(IValueFormatter valueFormatter) {

    }
    @Override
    public void onDrawKStart(Canvas canvas, @NonNull IKChartView view) {
        mStartIndex = 0;
    }

    @Override
    public void onDrawKEnd(Canvas canvas, @NonNull IKChartView view) {

    }

    /**
     * @noinspection unchecked
     */
    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        for (int i = 1; i < mPointList.size(); i++) {
            int position = mStartIndex + i - 1;//因为前面多加了一个 last
            Pair<Float, T> lastPointPair = (Pair<Float, T>) mPointList.get(i - 1);
            Pair<Float, T> cuxPointPair = (Pair<Float, T>) mPointList.get(i);
            Float lastX = lastPointPair.first;
            T lastPoint = lastPointPair.second;
            Float curX = cuxPointPair.first;
            T curPoint = cuxPointPair.second;
            onDrawScreenLine(lastPoint, curPoint, lastX, curX, canvas, view, position);
        }
    }

    @Override
    public void drawTranslated(@Nullable T lastPoint, @NonNull T curPoint, float lastX, float screenLastX, float curX, float screenCurX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if(view instanceof BaseKChartView) mStartIndex = ((BaseKChartView) view).getStartIndex();
        drawTranslated(lastPoint, curPoint, lastX, curX, canvas, view, position);
    }

    public abstract void drawTranslated(@Nullable T lastPoint, @NonNull T curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position);

    public abstract void onDrawScreenLine(@Nullable T lastPoint, @NonNull T curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position);

    @Override
    public int getTextH() {
        return 0;
    }

    /**
     * 外部动态设置指标值
     *
     * @param list
     */
    @Override
    public void setIndicatorList(@Nullable List<Integer> list) {
        if(list == null)
            return;
        indicatorValues.clear();
        indicatorValues.addAll(list);
    }
    @Override
    public void setKlineOptionList(@Nullable List<KlineOptionOneLineValueBean> list) {
        if(list == null)
            return;
        indicatorValues.clear();
        for (KlineOptionOneLineValueBean klineOptionOneLineValueBean : list) {
            indicatorValues.add(klineOptionOneLineValueBean.getValue());
        }
    }
}