
package com.upex.common.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.example.myapplication.databinding.KlineViewLandscapeEditToorBarBinding

class KLineLandscapeToolbar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private  val mBinding by lazy {
        KlineViewLandscapeEditToorBarBinding.inflate(LayoutInflater.from(context), this)
    }

    init {

    }


}