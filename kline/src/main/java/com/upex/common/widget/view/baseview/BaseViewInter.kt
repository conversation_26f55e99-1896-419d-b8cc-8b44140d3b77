package com.upex.common.widget.view.baseview

import android.content.Context
import android.util.AttributeSet
import com.upex.common.widget.view.BaseDrawable

/**
 *  author : wanghui
 *  date : 2020/11/30 5:07 PM
 *  description :
 */
interface BaseViewInter {
    var baseDrawable: BaseDrawable
    fun analizeStyle(context: Context, attrs: AttributeSet?)
    fun analyzeStyle(attrs: AttributeSet?) {}
    fun updateBackDrawable()
}

interface BaseTextViewInter : BaseViewInter {
    var fontWeight: Float
    fun autoTextSize()
}