package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.RectF
import kotlin.math.max
import kotlin.math.min


class RectLine: BaseDraw() {
    private val rectF = RectF()

    override fun setTotalPointCount():Int {
        return 2
    }

    override fun onFinalDraw(canvas: Canvas,mRect: RectF) {
        canvas.drawRect(mPointList.first().x, mPointList.first().y,mPointList.last().x, mPointList.last().y,getFillPaint())
        canvas.drawRect(mPointList.first().x, mPointList.first().y,mPointList.last().x, mPointList.last().y,getPaint())
    }

    override fun isSamePre() = false

    override fun isHasFill() = true

    override fun isInSelectArea(x:Float,y:Float,mRect: RectF): Bo<PERSON>an {
        val left = min(mPointList.first().x,mPointList.last().x)
        val right = max(mPointList.first().x,mPointList.last().x)
        val top = min(mPointList.first().y,mPointList.last().y)
        val bottom = max(mPointList.first().y,mPointList.last().y)
        rectF.set(
            left,
            top,
            right,
            bottom
        )
        val contains = rectF.contains(x, y)
        if (contains){
            return true
        }
        return super.isInSelectArea(x,y,mRect)
    }
}