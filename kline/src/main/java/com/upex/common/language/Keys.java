package com.upex.common.language;

/*************************************************************************
 * 文件说明        :
 *
 * @version V 1.0: ${Version}
 * @FileName     : Keys
 * @PackageName  :com.upex.exchange.utils
 * <AUTHOR> icechao
 * @Date         : 2018/10/18
 * @Email        :<EMAIL>
 *************************************************************************/
public interface Keys {

    // ----- KYC --------
    String KYC_VIEW_CERIFICATION_TITLE = "Kyc_view_Cerification_Title"; // 需要身份认证
    String KYC_VIEW_CERIFICATION_TIP = "Kyc_view_Cerification_Tip"; // 需要使用Bitget完整服务，请完成身份认证，您需要：
    String KYC_VIEW_CERIFICATION_ID_TIP = "Kyc_view_Cerification_Id_Tip"; // 上传有效的身份证件
    String KYC_VIEW_CERIFICATION_FACE_TIP = "Kyc_view_Cerification_Face_Tip"; // 人脸识别验证
    String KYC_VIEW_CERIFICATION_BUTTON = "Kyc_view_Cerification_Button"; // 立即认证
    String KYC_VIEW_INCERIFICATION_TITLE = "Kyc_view_InCerification_Title"; // 身份认证中
    String KYC_VIEW_INCERIFICATION_TIP = "Kyc_view_InCerification_Tip"; // 您的身份认证还在审核中，请等待审核通过后再使用此功能。
    String KYC_VIEW_INCERIFICATION_BUTTON = "Kyc_view_InCerification_Button"; // 查看认证状态
    // ------------------

    String WINDOW_IS_OPEN = "window_is_open"; //请解锁屏幕 、、已改

    String Personal_Figerprint_TouchID = "Personal_Figerprint_TouchID"; //“bitget” 的触控 ID 、、 已改

    String ATTEMPT_MORE = "attempt_more"; //尝试次数过多,请稍后再试  、、已改

    String ONE_MORE_TIME = "one_more_time"; //请再试一次  、、已改

    String WELCOME_BACK = "welcome_back"; //欢迎回来  、、已改

    String PLEASE_USE_PINER = "please_use_piner"; //点击使用指纹解锁、、已改

    String PWD_LOGIN = "pwd_login"; //密码登录、、已改

    String PLEASE_VERIFICATION_PINER = "please_verification_piner"; //请验证指纹 、、已改

    String LODING_LOADING = "loding_loading"; //加载中  、、已改

    String TEXT_REGIST_ERROR_PASSWROD = "text_regist_error_passwrod"; //密码须含字母和数字，字符长度8～20位、、已改

    String ASSETS_ASSETS_FREEZE = "assets_assets_freeze"; //冻结、、已改

    String APP_SUBMMIT = "app_submmit"; //提交、、已改


    String ADDRESS_MANAGEMENT_FILL_REMARK = "address_management_fill_remark"; //请输入备注名 、、已改

    String ENTRUSET_SHAIXUAN = "entruset_shaixuan"; //筛选、、已改

    String APP_WITHDRAW_MONEY = "app_withdraw_money"; //是否撤销提币、、已改

    String APP_INDEX_BANNER_TITLE = "app_index_banner_title"; //轻松拥有数字资产、、已改

    String APP_REVOCATION_SUCCESS = "app_revocation_success"; //撤销成功、、已改


    String APP_VERSION_TOO_LOW = "app_version_too_low"; //没有安装或者版本太低、、已改

    String APP_TRANSAC_LIST_PRICE = "app_transac_list_price"; //价格、、已改

    String ADDRESS_MANAGEMENT_ADDRESS = "address_management_address"; //地址、、已改

    String ADDRESS_MANAGEMENT_REMARKS = "address_management_remarks"; //备注、、已改

    String INVITE_COPY = "invite_copy"; //复制、、已改

    String TRADE_OPERATION_INNER_CHARGE = "trade_operation_inner_charge"; //充币、、已改

    String TRADE_ENTRUST_TABLE_TYPE = "trade_entrust_table_type"; //方向、、已改

    String APP_LOGIN_OUT = "app_login_out"; //退出登录、、已改

    String TEXT_EMPTY_NO_COIN = "text_empty_no_coin"; //无搜索记录、、已改

    String TEXT_OPERATOR_DELETE = "text_operator_delete"; //删除、、已改

    String TEXT_RESET_ENSURE1 = "text_reset_ensure1"; //确定、、已改

    String TEXT_CHOOSE_LIMIT = "text_choose_limit"; //限价、、已改


    String TEXT_EMPTY_NO_DATA = "text_empty_no_data"; //暂无数据、、已改

    String TEXT_KLINE_FIGURE = "text_kline_figure"; //附图、、已改

    String TEXT_UPDATA_CURRENT_NEW = "text_updata_current_new"; //已更新至最新版本、、已改

    String RECHARGE_ADDRESS = "recharge_address"; //充币地址、、已改

    String WITHDRAW_ADDRESS = "withdraw_address"; //提币地址、、已改

    String FORGET_PWD = "forget_pwd"; //忘记密码、、已改

    String SAFE_LOGIN_PWD = "safe_login_pwd"; //登录密码

    String LOGIN_PINER_TEXT = "login_piner_text"; //指纹登录、、已改

    String TEXT_SUOCANG = "text_suocang"; //锁仓、、已改

    String TEXT_PERSONAL_JOIN = "text_personal_join"; //加入社群、、已改

    String ENTUST_DATA_DONE = "entust_data_done"; //已成交、、已改

    String FINAL_REGIST = "final_regist"; //注册、、已改

    String TRADE_MARKET_SEARCH = "trade_market_search"; //搜索币种、、已改

    String BACK_PINER_NOBODY = "back_piner_nobody"; //暂无、、已改

    String NET_TIME_OUT = "net_time_out"; //请求超时、、已改

    String NET_FAIL = "net_fail"; //连接失败、、已改

    String PLEASE_FAIL = "please_fail"; //请求失败、、已改

    String RESET_PWD_SUCCESS = "reset_pwd_success"; //重置密码成功、、已改

    String SCAN_FAIL = "scan_fail"; //扫码失败、、已改

    String DOWN_FAIL = "down_fail"; //下载失败、、已改

    String ADD_SUCCESS = "add_success"; //添加成功、、已改

    String PLEASE_OPEN_PINER_TEXT = "please_open_piner_text"; //请开启指纹解锁功能、、已改

    String NOW_OPEN_PINER = "now_open_piner"; //现在开启、、已改

    String AFTER_OPEN_PINER = "after_open_piner"; //稍后再说、、已改

    String APP_IS_BACKGROUND = "app_is_background"; //bitget应用已切入后台勿输入账号信息、、已改

    String FILTER_ALL = "filter_all"; //全部、、已改
    String X221023_TRACK_FILTER_ALL_HISTORY = "x221023_track_filter_all_history"; //合约>全部

    String FILTER_DATE = "filter_date"; //日期、、已改

    String HOME_TEXT_ZX = "home_text_zx"; //自选、、已改

    String SURE_OUT_USER = "sure_out_user"; //确定退出当前账号?、、已改

    String TEXT_SHARE = "text_share"; //分享

    String REWARD_DETAIL = "reward_detail"; //明细、、已改

    String User_Setting_Update_Find_NewCode = "User_Setting_Update_Find_NewCode"; //发现新版本、、没用到

    String COPY_LINK = "copy_link"; //复制链接

    String TEXT_BIND_SEND = "text_bind_send"; //发送、、已改

    String ADD_SELF_SUCESS = "add_self_sucess"; //添加自选成功、、已改

    String ADD_SELF_FAIL = "add_self_fail"; //添加自选失败、、已改

    String REMOVE_SELF_SUCESS = "remove_self_sucess"; //删除自选成功、、已改

    String REMOVE_SELF_FAIL = "remove_self_fail"; //删除自选失败、、已改

    String CHOOSE_COUNTRY_REGION = "choose_country_region";//国家语言标题
    String SEARCH_TEXT = "search_text";//搜索

    String LOGIN_IN = "login_title";//登录<
    /*--------------------------------------------------------------------*/
    String LOGIN_PLACEHOLDER_NAME = "login_placeholder_name";//请输入邮箱/手机号码
    String LOGIN_PLACEHOLDER_PWD = "login_placeholder_pwd";//请输入登录密码
    String APP_TRADE_DEAL = "app_trade_deal";//成交
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String TRADE_ENTRUST_TABLE_TOTAL = "trade_entrust_table_total";///交易额
    String LINKS_USER_SAFE = "links_user_safe";//安全中心
    String APP_MANAGER_ADDRESS = "app_manager_address";//地址管理
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String FOOTER_HELP_CENTER = "footer_help_center";//帮助中心
    String FOOTER_RATE = "footer_rate";//费率标准
    String APP_ONLINE_SERVER = "app_online_server";//在线客服
    String CUSTOMERSERVICE_PERSONALCENTER_EXCLUSIVE_CUSTOMER_SERVICE = "CustomerService_PersonalCenter_exclusive_customer_service"; // 专属客服
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String USER_SETTING_SETTING = "user_setting_setting";//设置
    String TRADE_ENTRUST_TAB_CURRENT = "trade_entrust_tab_current";//当前委托
    String Futures_Main_OpenOrders = "Futures_Main_OpenOrders";//交易页的 当前委托Tab
    String Futures_History_OpenOrders = "Futures_History_OpenOrders";// 全部页面的 当前委托Tab

    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String COMMON_BUY = "common_buy";//买入
    String COMMON_SELL = "common_sell";//卖出
    String TRANSAC_CANCLE = "transac_cancal";//撤销
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String APP_COMMON_CANCLE = "app_common_cancle";//取消
    String TEXT_SEARCH_HISTORY = "text_search_history";//历史搜索
    String TEXT_SEARCH_HISTORY_CLEAR = "text_search_history_clear";//清除
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_INPUT_ACCOUNT = "app_input_account";//请输入账号
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String REGISTER_REG_READ = "app_register_reg_read";//我已阅读并同意
    String REGISTER_REG_PROTOCOL = "register_reg_protocol";//《用户协议》
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_COMMON_GOON = "app_common_goon";//继续
    String APP_COMMON_ENSURE = "app_common_ensure";//确认
    String APP_SECURITY_EMAIL_DESC = "app_security_email_desc";//若未收到邮件，请检查邮箱垃圾箱
    String APP_GOOGLE_CODE = "app_google_code";//谷歌验证码
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String FORGET_PASSWORD_RESET_PASSWORD = "forget_password_reset_password";//重置密码
    String TRADE_OPERATION_INNER_CANUSE = "trade_operation_inner_canuse";//可用
    String APP_COMMON_AMOUNT = "app_common_amount";//app_common_amount
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String APP_COMMON_FEE = "app_common_fee";//手续费
    String APP_PWS_NOT_SAME = "app_pws_not_same";//密码输入不一致
    String Safe_Center_EnterPassword = "Safe_Center_EnterPassword";//请输入密码
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String ASSETS_SUCCESSFUL_COPY = "assets_successful_copy";//复制成功
    String TEXT_COPY_FAIL = "text_copy_fail";//发送验证码
    String TEXT_PASTE_FAIL = "text_paste_fail";//请重新输入密码
    String APP_LANGUAGE = "app_language";//隐藏零额币
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String ADDRESS_MANAGEMENT_ADD_ADDRESS = "address_management_add_address";//添加地址
    String APP_GO_TRADE = "app_go_trade";//去交易
    String APP_COIN_RECORD = "app_coin_record";//财务记录
    /*--------------------------------------------------------------------*/



    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_PHONE = "app_phone";//手机
    String APP_EMAIL = "app_email";//邮箱
    String APP_GOOGLE = "app_google";//谷歌验证
    String APP_NOTICE = "app_notice";//公告
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String APP_SAFE_VERIFY = "app_safe_verify";//安全验证
    String Assets_view_Withdraw = "Assets_view_Withdraw";//提币
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_GET_AMOUNT = "app_get_amount";//到账数量
    String APP_COMMON_NEXT_STEP = "app_common_next_step";//下一步
    String USER_BIND_EMAIL_LABEL_CODE = "user_bind_email_label_code";//邮箱验证码
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String USER_BIND_MOBILE_LABEL_CODE = "user_bind_mobile_label_code";//手机验证码
    String REGISTER_PLACEHOLDER_EMAIL_CODE = "register_placeholder_email_code";//请输入邮箱验证码
    String APP_INPUT_GOOGLE_CODE = "app_input_google_code";//请输入谷歌验证码
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String REGISTER_PLACEHOLDER_SMS = "register_placeholder_sms"; //请输入短信验证码
    String APP_EMAIL_REGIST = "app_email_regist";//邮箱注册
    String APP_SEND_CODE_REPEAT = "app_send_code_repeat";//重新发送
    String APP_INVETED_CODE = "app_inveted_code";//请输入邀请码(选填)
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String App_Phone_Number = "app_phone_number";//手机号
    String APP_INPUT_PHONE_NUMBER = "app_input_phone_number";//请输入手机号
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_COMMON_TIME = "app_common_time";//时间
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String APP_DEAL_AMOUNT = "app_deal_amount";//成交量
    String APP_ENTRUST_PRICE = "app_entrust_price";//委托价
    String APP_ENTRUST_AMOUNT = "app_entrust_amount";//委托量

    String Futures_History_TradeDetails = "Futures_History_TradeDetails";// 成交明细
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/

    String APP_RESET = "app_reset";//重置
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_VERSION = "app_version";//版本
    String User_Setting_Currency = "User_Setting_Currency";//计价方式
    /*--------------------------------------------------------------------*/

    /*--------------------------------------------------------------------*/
    String APP_USER_PROTECOL = "app_user_protecol";//用户协议
    String APP_LOGIN_REGIST = "app_login_regist";//登录/注册
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String TRADE_OPERATION_TIP_NO_ENOUGH_COIN = "trade_operation_tip_no_enough_coin";//可用余币不足
    String FOOTER_ABOUT = "footer_about";//关于我们
    /*--------------------------------------------------------------------*/


    /*--------------------------------------------------------------------*/
    String APP_UPDATE_NOW = "app_update_now";  //立即更新
    String APP_UPDATA_LATER = "app_updata_later";//暂不更新
    /*--------------------------------------------------------------------*/

    String APP_INPUT_PRICE = "app_input_price";//请输入价格
    String APP_INPUT_TRIGGER_PRICE = "app_input_trigger_price";//请输入触发价格

    String APP_INPUT_AMOUNT = "app_input_amount";//请输入数量
    String ASSETS_FILL_COIN_ADDRESS = "assets_fill_coin_address";//请输入提币地址


    String LINKS_HOME = "links_home";
    String ASSETS_TITLE = "assets_title";
    /**
     * "下拉可以刷新"
     */
    String PULL_TO_FRESH = "pull_to_fresh";
    /**
     * 正在刷新...
     */
    String PULL_RESHING = "pull_freshing";
    /**
     * 释放立即刷新
     */
    String RELEASE_TO_FRESH = "release_to_fresh";
    /**
     * 刷新完成
     */
    String REFRESH_COMPLETE = "refresh_complete";
    /**
     * 刷新失败
     */
    String REFRESH_FAIL = "refresh_fail";
    /**
     * 上次更新
     */
    String LAST_REFRESH_TIME = "last_refresh_time";

    /**
     * 折合
     */
    String TO_BASE_COIN = "to_base_coin";

    /**
     * 已绑定
     */
    String TEXT_SAFE_HAS_BIND = "text_safe_has_bind";
    /**
     * 换绑TGM
     */
    String TEXT_SAFE_CHANEG_BIND = "x221104_change_bind_tgm";

    /**
     * 新 去绑定
     */
    String USER_OVERVIEW_GO_BIND = "user_overview_go_bind";
    /**
     * 委托成功
     */
    String TOAST_TRADE_SHOW_ENTRUST_SUCCESS = "toast_trade_show_entrust_success";


    /**
     * market
     * <string name="market_text_timeline">分时</string>
     * <string name="market_text_15">15分</string>
     * <string name="market_text_1hour">1时</string>
     * <string name="market_text_4hour">4时</string>
     * <string name="market_text_1day">日线</string>
     */


    String MARKET_TEXT_TIMELINE = "market_text_timeline";
    String MARKET_TEXT_15 = "market_text_15";
    String MARKET_TEXT_1HOUR = "market_text_1hour";
    String MARKET_TEXT_4HOUR = "market_text_4hour";
    String MARKET_TEXT_1DAY = "market_text_1day";
    String MARKET_TEXT_2DAY = "market_text_1day";
    String Market_Search_Hot_Spotgoods = "Market_Search_Hot_Spotgoods";
    String Market_Search_Hot_Contract = "Market_Search_Hot_Contract";
    String Market_Search_Hot_Swap = "Market_Search_Hot_Swap";
    String Market_Change_Bottom_des = "Market_Change_Bottom_des";
    String Market_Change_Trend = "Market_Change_Trend";
    String Market_Search_Placeholder = "Market_Search_Placeholder";
    String Market_Search_Cancel = "Market_Search_Cancel";
    String Market_Search_All = "Market_Search_All";
    String Market_Search_Spotgoods = "Market_Search_Spotgoods";
    String Market_Search_Contract = "Market_Search_Contract";
    String Market_Search_Swap = "Market_Search_Swap";
    String Market_Search_History = "Market_Search_History";
    String Market_Change_Ttile = "Market_Change_Ttile";
    String Market_Change_Token_Time = "Market_Change_Token_Time";
    String Market_Change_State = "Market_Change_State";
    String Market_Search_Contract_Perpetual = "Market_Search_Contract_Perpetual";
    String Market_Search_Contract_Delivery = "Market_Search_Contract_Delivery";
    String Market_Change_Data = "Market_Change_Data";


    /**
     * <string name="market_text_more"><![CDATA[更多<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_zhibiao"><![CDATA[指标<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_1time"><![CDATA[1分<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_5time"><![CDATA[5分<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_30time"><![CDATA[30分<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_1week"><![CDATA[周线<small><small><small>◢</small></small></small>]]></string>
     * <string name="market_text_1month"><![CDATA[1月<small><small><small>◢</small></small></small>]]></string>
     */

    String MARKET_TEXT_MORE = "market_text_more";
    String MARKET_TEXT_ZHIBIAO = "market_text_zhibiao";
    String MARKET_TEXT_1TIME = "market_text_1time";
    String MARKET_TEXT_5TIME = "market_text_5time";
    String MARKET_TEXT_30TIME = "market_text_30time";
    String MARKET_TEXT_1WEEK = "market_text_1week";
    String MARKET_TEXT_1MONTH = "market_text_1month";


    /**
     * <string name="k_open">开</string>
     * <string name="k_close">收</string>
     * <string name="k_max">高</string>
     * <string name="k_min">低</string>
     * <string name="k_num">量</string>
     */

    String K_OPEN = "k_open";
    String K_NUM = "k_num";
    String K_MAX = "k_max";
    String K_MIN = "k_min";
    String K_CLOSE = "k_close";
    String APP_DEAL_CHANGE_AMOUNT = "app_deal_change_amount"; //涨跌额
    String APP_DEAL_CHANGE_RASE = "app_deal_change_rase";
    String Markets_view_CoinListSort_ChangeTitle = "Markets_view_CoinListSort_ChangeTitle";
    String MARKET_VIEW_TURNOVER = "Market_view_Turnover";


    /**
     * <string name="trade_depth_table_sell">买</string>
     * <string name="trade_depth_table_buy">卖</string>
     */
    String TEXT_KLINE_MASTER = "text_kline_master";


    /**
     * invite_activity_rule=活动规则
     * no_more_data = 没有更多数据
     */
    String INVITE_ACTIVITY_RULE = "invite_activity_rule";
    String NO_MORE_DATA = "no_more_data";


    /**
     * //android_withdraw_tip_1	提币需完成实名认证，每一个身份信息只可完成一次实名认证，且对应一个帐号；
     * //android_withdraw_tip_2	为了用户资金安全，平台客服可能会以电话形式确认您的提币操作，请您注意接听；
     * //text_withdraw_tip_3	最小提币数量为：bg_param_1，最大提币金额不得超过您账户的可用金额
     * //android_tip_4	请务必确认移动设备安全，防止信息被篡改或泄露。
     */
    String ANDROID_WITHDRAW_TIP_2 = "android_withdraw_tip_2";
    String Kyc_Withdraw_Tip3 = "Kyc_Withdraw_Tip3";
    String ASSETS_RECHARGE_TIP_4 = "Assets_Recharge_Tip4";

    /**
     * android_recharge_tip_2
     */
    String ASSETS_RECHARGE_TIP1 = "Assets_Recharge_Tip1";
    String ASSETS_RECHARGE_TIP2 = "Assets_Recharge_Tip2";
    String ASSETS_RECHARGE_TIP3 = "Assets_Recharge_Tip3";

    String APP_BACK_AGAIN = "app_back_again";
    String ADDRESS_MANAGEMENT_SUCCESSFULLY_DELETED = "address_management_successfully_deleted";

    String APP_HELP_CENTER = "app_help_center";
    String APP_NOTICE_CENTER = "app_notice_center";
    String APP_REWARD_URL = "app_reward_url";
    String APP_ABOUT_US = "app_about_us";
    String APP_USER_PROTECAL = "app_user_protecal";

    String X220709_PRIVATE_PROTECAL = "x220709_private_protecal";
    String APP_TEXT_AUDITED_FAIL = "app_text_audited_fail";  //本次身份认证审核未通过

    String VERIFIED_NAME = "verified_name"; //姓名

    String VERIFIED_IDENTITY_NUMBER = "verified_identity_number"; //证件号码

    String Safe_Kyc_Identity_Number = "Safe_Kyc_Identity_Number";

    String VERIFIED_LAST_NAME = "verified_last_name";  //姓氏

    String Safe_Kyc_Full_Person_Info_First_Name = "Safe_Kyc_Full_Person_Info_First_Name";//名字

    String CHECK_FAIL = "check_fail";//审核失败

    String VERIFIED_UNDER_REVIEW = "verified_under_review"; //审核中

    String APP_TEXT_CERTIFIED = "app_text_certified"; //已认证


    String VERIFIED_FILL_FIRST_NAME = "verified_fill_first_name";//请输入名字

    String VERIFIED_FILL_LAST_NAME = "verified_fill_last_name";//请输入姓氏

    String APP_ID_AUTHEN = "app_id_authen";//身份认证

    /**
     * 上传照片
     */
    String UPLOAD_PHOTOS = "upload_photos";


    /**
     * 上传中...
     */
    String UPPLOADING = "verified_uploading";

    /**
     * 手持身份证及声明
     * 身份证有效期面
     * 身份证人像面
     * <p>
     * 护照封面照片
     * 护照带有身份信息页照片
     * 手持护照和声明
     */
    String APP_CORD_AND_PERSON = "verified_card_handle_both";
    String APP_CORD_BACK = "verified_id_card";
    String APP_CORD_FRONT = "verified_id_card_face";


    String PASSPORT_BACK = "verified_passport_with_photo";


    /**
     * 请将文件上传完整
     */
    String VERIFIED_UPLOAD_FILE_COMPLETELY = "verified_upload_file_completely";
    /**
     * 上传要求
     * 拍摄示例
     * 上传成功
     */
    String UPLOAD_RULE = "upload_rule";
    String PHONE_EXAMPLE = "phone_example";
    String UPLOAD_DONE = "upload_done";


    /**
     * 图片仅支持jpg、jpeg、bmp、png格式，建议大小在2M以内。
     * 证件图片，手持证件及手写声明的文字信息需清晰可见，且无错别字。
     * 照片需免冠，建议未化妆，手持证件人的五官清晰可见，完整露出双手双臂。
     * 需同时手持证件照与手写声明：“我在bitget.com平台的所有行为均为本人操作，并已知悉相关风险，愿意承担本人在此平台所做行为导致的一切法律后果。”，并附姓名与当前日期。
     * 照片内容需真实有效，不得做任何修改。
     * 每一个身份信息只可完成一次实名认证，且对应一个帐号。认证信息一经验证不能修改，请务必如实填写。
     */

    String UPLOAD_RULE_5 = "upload_rule_5";
    String UPLOAD_RULE_6 = "upload_rule_6";

    /**
     * 标准
     * 边框缺失
     * 照片模糊
     * 闪光强烈
     */

    String APP_PHOTO_STANDARD = "app_photo_standard";
    String APP_PHOTO_BORDER = "app_photo_border";
    String APP_PHOTO_VAGUE = "app_photo_vague";
    String APP_PHOTO_SHINE = "app_photo_shine";

    //护照
    /**
     * 上传失败，请点击重拍
     */
    String APP_TAKE_PHOTO_REPEAT = "app_take_photo_repeat";


    /**
     * 去认证
     * 交易时间
     * 选择币种
     * 使用GT抵扣交易手续费
     * gt rate zendesk url
     */
    String USER_OVERVIEW_GO_VERIFIED = "user_overview_go_verified";


    //拍照增加的key

    String APP_REALNAME_PHOTOGRAPH = "app_realname_photograph"; //拍照


    String APP_COINNAME_SEARCH = "app_coinname_search";
    String APP_DEDUCTION_TRADEFEE = "app_deduction_tradefee";

    /**
     * 操作失败
     * 币币
     * 杠杆
     */
    String TRANSACTION_COIN = "transaction_coin";
    String TRANSACTION_LEVERAGE = "transaction_leverage";

    String CAPITAL_TRANSFER = "capital_transfer";//资金划转

    String TEXT_FROM = "text_from";//从
    String TEXT_TO = "text_to";//到

    String TEXT_TRANS_COUNT = "text_trans_count";//划转数量
    String CONFIRM_TRANSFER = "confirm_transfer";//确认划转
    String TEXT_COUNT_LIMIT = "text_count_limit";//最多可转
    String TEXT_CLOSE_POSITION_TIME = "text_close_position_time";//平仓时间
    String TEXT_EVENING_UP_PRICE = "text_evening_up_price";//平仓价格
    String view_ShareView_Contract_ExitPrice = "view_ShareView_Contract_ExitPrice";//我的跟单-合约-仓位分享-平仓价格

    // 新加Key

    String TEXT_LEVER_TYPE = "text_lever_type";//类型
    //    OTC
    String APP_TV_TRANSACTION_GO = "app_tv_transaction_go"; //我知道了
    String APP_TV_TRANSACTION_COINCOLA_ZENDESK = "app_tv_transaction_coincola_zendesk"; //
    String APP_TV_TRANSACTION_COINCOLA_URL = "app_tv_transaction_coincola_url"; //     下载
    String APP_TV_TRANSACTION_EPAY_URL = "app_tv_transaction_epay_url"; //     下载

    /**
     * 爆仓说明
     * 当安全率≤110%时，账户将触发爆仓以归还借入资金
     * 知道了
     * 杠杆管理
     * 本次交易需借
     * otc提示弹框按钮
     */
    String LEVERAGE_EXIT_BUTTON = "leverage_exit_button";
    /**
     * 不再提示
     */
    String LEVERAGE_SHOW_NO_MORE = "leverage_show_no_more";


    String TEXT_ALL_SELECT = "text_all_select";//全选


    String TRADE_TYPE = "trade_type";//交易类型


    String TEXT_TRADE_TOAST = "text_trade_toast";//交易买入量不可小于最小买入量

    String TEXT_TRADE_TOAST_SELL = "text_trade_toast_sell";//交易卖出量不可小于最小卖出量

    String TEXT_NOTICE_CHECK = "text_notice_check";//查看公告

    String TEXT_NOTICE_UPDATE_TIME = "text_notice_update_time";//预计升级时间

    String TEXT_NOTICE_UPDATE_RELOAD = "text_notice_update_reload";//服务器正在升级


    String TEXT_TIP_IS_BIND_PHONE = "text_tip_is_bind_phone";//v0.4.1   是否绑定手机号

    String TEXT_BIND_NOW = "text_bind_now";//立即绑定

    String TEXT_HINT_INPUT_ADDRESS_TAG = "text_hint_input_address_tag";//地址标签(填写错误可能导致资产丢失)
    String TEXT_HINT_INPUT_ADDRESS_TAG_NEW = "text_hint_input_address_tag_new";//请输入地址标签

    String TEXT_PASTE = "text_paste";//粘贴

    String Kyc_Withdraw_Hint4 = "Kyc_Withdraw_Hint4";//建议您完成KYC认证以提高账户安全性；未认证每日提币总额bg_param_1 BTC；认证后每日提币总额bg_param_2 BTC；
    String ASSETS_WITHDRAW_KYC_QUOTA_ADJUST_HINT = "Assets_Withdraw_kycQuotaAdjust_hint";//建议您完成KYC认证以提高账户安全性；未认证每日提币总额 {0} USD；每月提币总额 {1} USD；认证后每日提币总额 {2} USD

    String ANDROID_UPLOAD_RULE_7 = "android_upload_rule_7";//需同时手持证件与手写声明：“bitget.com”，并附手写及您的 UID（%s），姓名及当前日期。

    String ASSETS_RECHARGE_TIP_5 = "Assets_Recharge_Tip5";//请务必填写并仔细核对地址标签，这是您账户的唯一标识，否则资产将不可找回。

    String APP_COPY_ADDRESS_TAG = "app_copy_address_tag";//复制tag

    String ASSETS_RECHARGE_EOS_MEMO_TIP = "Assets_Recharge_EosMemoTip";//通过MEMO方式充值 EOS时，需填写我们提供的数字形式的MEMO标签，非法MEMO充值会导致交易失败，请您谅解。

    String ASSETS_RECHARGE_EOS_INLINE_TIP = "Assets_Recharge_EosInlineTip";//EOS暂不支持Inline方式的转账充值，通过Inline方式的转账充值将不会上账，请您谅解。

    String TEXT_ADDRESS_TAG_TIP = "text_address_tag_tip";//标签

    String CopyTrade_TraderFilter_ConditionTag = "CopyTrade_TraderFilter_ConditionTag";//交易员筛选界面 Tags

    String TEXT_TIP_IS_BIND_PHONE_SUB = "text_tip_is_bind_phone_sub";//以便及时接受交易的风险提示等消息提醒


    String TEXT_HOME_CONTRACT_TITLE = "text_new_contract_title";//合约
    String TEXT_TAB_HOME_CONTRACT_TITLE = "text_home_contract_title";//首页-viewPager 合约 title
    String Markets_Favourite_MarginTAB = "Markets_Favourite_MarginTAB";//首页-viewPager 杠杆 title
    String TEXT_SELECT_COIN_LIST = "text_select_coin_list";//币币列表
    String TEXT_SELECT_CONTRACT_LIST = "text_select_contract_list";//永续合约列表

    String TEXT_ACCOUNT_RIGHTS = "text_account_rights";//账户权益


    String TEXT_CONTRACT_OPEN_LONG = "text_contract_open_long";//开多
    String TEXT_CONTRACT_OPEN_SHORT = "text_contract_open_short";//开空
    String TEXT_CONTRACT_CLOSE_LONG = "text_contract_close_long";//平多
    String TEXT_CONTRACT_CLOSE_SHORT = "text_contract_close_short";//平空
    String TEXT_CONTRACT_POSITIONS_OPEN = "text_contract_positions_open";//开仓
    String TEXT_CONTRACT_POSITIONS_CLOSE = "text_contract_positions_close";//平仓
    String TEXT_CONTRACT_FAIR_PRICE = "text_contract_fair_price";//标记价格
    String Futures_FuturesSetting_MarkPrice = "Futures_FuturesSetting_MarkPrice"; //标记价格
    String TEXT_CONTRACT_BUY_OPEN_LONG = "text_contract_buy_open_long";//买入开多
    String Futures_BuyLong = "Futures_Main_BuyLong";
    String Futures_SellShort = "Futures_Main_SellShort";
    String TEXT_CONTRACT_SELL_OPEN_SHORT = "text_contract_sell_open_short";//卖出开空
    String TEXT_CONTRACT_SELL_CLOSE_LONG = "text_contract_sell_close_long";//卖出平多
    String TEXT_CONTRACT_BUY_CLOSE_SHORT = "text_contract_buy_close_short";//买入平空
    String TEXT_CONTRACT_POSITIONS_TIP = "text_contract_positions_tip";//仓位
    String TEXT_CONTRACT_LEVERAGE = "text_contract_leverage";//仓位档位
    String TEXT_CHOICE_CONTRACT = "text_choice_contract";//选择合约
    String TEXT_CONTRACT_CAN_OPEN_AMOUNT = "text_contract_can_open_amount";//可开
    String TEXT_CONTRACT_CAN_CLOSE_AMOUNT = "text_contract_can_close_amount";//可平
    String Markets_Kline_high_levelrate_tip = "Markets_Kline_high_levelrate_tip";//最高倍数
    String Futures_AdjustLeverageAlert_MaxLeverage = "Futures_AdjustLeverageAlert_MaxLeverage"; // 最高倍数
    String TEXT_LEVELRATE_HIGH_OPEN = "text_levelrate_high_open";//当前杠杆倍数最高可开
    String TEXT_LEVELRATE_POSITION_NEED_MARGIN = "text_levelrate_position_need_margin";//当前仓位所需保证金
    String TEXT_COIN_INFO_INTRODUCE = "text_coin_info_introduce";//币种介绍

    String TEXT_CONTRACT_ORDER_ACHIEVED_EARNING = "text_contract_order_achieved_earning";//已实现盈亏
    String TEXT_TRANSFER_STATUS = "text_transfer_status";//状态
    String TEXT_CONTRACT_INFO_TO_EXPENSES_TIME = "text_contract_info_to_expenses_time";//下一结算时间
    String TEXT_CONTRACT_ORDER_UNACHIEVE_EARNING = "text_contract_order_unachieve_earning";//未实现盈亏
    String TEXT_CONTRACT_HOLD_CANG = "text_contract_hold_cang";//持仓
    String TEXT_CONTRACT_OPEN_CANG_AVERAGE_PRICE = "text_contract_open_cang_average_price";//开仓均价
    String TEXT_CONTRACT_FORECAST_FORCE_CLOSE_PRICE = "text_contract_forecast_force_close_price";//预估强评价
    String TEXT_CONTRACT_ADD_DEPOSIT = "text_contract_add_deposit";//增加保证金
    String TEXT_CONTRACT_REDUCE_DEPOSIT = "text_contract_reduce_deposit";//减少保证金
    String TEXT_CONTRACT_DEPOSIT_ADD_MAX = "text_contract_deposit_add_max";//最多可增加
    String TEXT_CONTRACT_DEPOSIT_ADDED_FORCE_CLOSE_PRICE = "text_contract_deposit_added_force_close_price";//增加后的预估强平价
    String TEXT_CONTRACT_DEPOSIT_REDUCE_MAX = "text_contract_deposit_reduce_max";//最多可减少
    String TEXT_CONTRACT_DEPOSIT_REDUCED_FORCE_CLOSE_PRICE = "text_contract_deposit_reduced_force_close_price";//减少后的预估强平价
    String TEXT_CONTRACT_CANG_NO_RISK = "text_contract_cang_no_risk";//永不爆仓
    String TEXT_LEVELRATE_EDIT_SHORT = "text_levelrate_edit_short";//调整空头杠杆
    String TEXT_CONTRACT_CLOSE_SHORT_POSITION = "text_contract_close_short_position";//空头仓位平仓
    String ONLY_TEXT_RATE_OF_RETURN_TIP = "only_text_rate_of_return_tip";//回报率

    String TEXT_CONTRACT_CANG_DO_MORE = "text_contract_cang_do_more";//做多
    String TEXT_CONTRACT_CANG_DO_LESS = "text_contract_cang_do_less";//做空
    String TEXT_CONTRACT_OPEN_POSITIONS_PRICE = "text_contract_open_positions_price";//开仓价格
    String view_ShareView_Contract_EntryPrice = "view_ShareView_Contract_EntryPrice";//合约-仓位分享-开仓价格
    String TEXT_CONTRACT_CURRENT_PRICE = "text_contract_current_price";//当前价格
    String TEXT_SHARE_SAVE_IMG = "text_share_save_img";//保存图片
    String TEXT_SHARE_WX_FRIEND = "text_share_wx_friend";//微信好友
    String TEXT_CONTRACT_LESS_POSITIONS = "text_contract_less_positions";//空仓

    String API_MANAGEMENT_BIND_IP_SECRET_KEY_TIPS = "api_management_bind_ip_secret_key_tips";//提示
    String TEXT_WEB_SAVE_IMG = "text_web_save_img";//保存图片到本地
    String TEXT_WEB_DOWNLOAD_IMG = "text_web_download_img";//下载图片
    String TEXT_TOUCH_TO_FOCUS = "text_touch_to_focus";//触摸对焦
    String TEXT_CONTRACT_CASHGIFT_TIPS = "text_contract_cashgift_tips";//①体验金包含在可用中；②体验金可用于开仓、交易手续费、保证金、平仓盈亏结算；③体验金不可直接转出，超出体验金部分转出后体验金清零；④盈亏结算时优先扣除体验金。
    String TEXT_CONTRACT_CASHGIFT = "text_contract_cashgift";//合约体验金
    String CONTRACT_PLAN_DELEGATE_TIP = "contract_plan_delegate_tip";// 计划委托
    String Futures_DelegateTypeAlert_TriggerOrder = "Futures_DelegateTypeAlert_TriggerOrder";//计划委托
    String CONTRACT_PLAN_TRIGGER_PRICE_TIP = "contract_plan_trigger_price_tip";//触发价格
    String CONTRACT_PLAN_EXECUTE_PRICE_TIP = "contract_plan_execute_price_tip";//执行价格
    String CONTRACT_SHARE_POSTER_TIP = "contract_share_poster_tip";//
    String CONTRACT_PRODUCT_NAME_SL = "contract_product_name_sl";//模拟
    String CONTRACT_PRODUCT_NAME_SWAP = "contract_product_name_swap";//永续
    String TRADE_CONTRACT_SEARCH = "trade_contract_search";//搜索合约
    String TEXT_CONTRACT_SIMULATE_FUNDS_SUCCESS = "text_contract_simulate_funds_success";//领取成功
    String HOME_CONTRACT_TOP_FORWARD = "home_contract_top_forward";//USDT
    String TEXT_IS_GET_CONTRACT_GRANTAMOUNT = "android_text_is_get_contract_grantamount";//是否开通bitget永续合约模拟账户,领取 %1$s 体验金


    String CONTRACT_PLAN_TRIGGER_PRICE_TEXT = "contract_plan_trigger_price_text";//触发价
    String CONTRACT_PLAN_EXECUTE_PRICE_TEXT = "contract_plan_execute_price_text";//执行价
    String CONTRACT_PLAN_EXECUTE_COUNT_TIP = "contract_plan_execute_count_tip";//执行量
    String CONTRACT_PLAN_NORMAL_ORDER_TIP = "contract_plan_normal_order_tip";//普通委托
    String CONTRACT_PLAN_TRIGGER_ORDER_TIP = "contract_plan_trigger_order_tip";//止盈止损
    String TEXT_LEVELRATE_HIGH_WARNING = "text_levelrate_high_warning";//杠杆风险

    String TEXT_PLAN_TAKE_PROFIT_TIP = "text_plan_take_profit_tip";//止盈
    String TEXT_PLAN_STOP_LESS_TIP = "text_plan_stop_less_tip";//止损
    String TEXT_PLAN_TAKE_PROFIT_PRICE = "text_plan_take_profit_price";//请输入止盈价格
    String TEXT_PLAN_STOP_PROFIT_PRICE = "text_plan_stop_profit_price";//请输入止损价格
    String TEXT_PLAN_STOP_PROFILT_TIP2 = "text_plan_long_less_current_price";//  多头仓位的止损价格请≤当前价格
    String TEXT_PLAN_STOP_PROFILT_TIP4 = "text_plan_long_profit_current_price";// 多头仓位的止盈价格请≥当前价格
    String TEXT_PLAN_STOP_PROFILT_TIP6 = "text_plan_short_less_current_price";  //空头仓位的止损价格请≥当前价格
    String TEXT_PLAN_STOP_PROFILT_TIP8 = "text_plan_short_profit_current_price"; //空头仓位的止盈价格请≤当前价格
    String TEXT_PLAN_CAN_CLOSE_AMOUNT = "text_plan_can_close_amount";//可平数量
    String view_Reminders = "view_Reminders";//温馨提示

    String TEXT_CONTRACT_CONTRACT_INFO = "text_contract_contract_info";//合约信息
    String TEXT_SPOT_INFO = "x220617_text_spot_info";//合约信息

    String TEXT_CONTRACT_FUND_RATE_SETTLEMENT_TIME = "text_contract_fund_rate_settlement_time";//资金费率结算时间
    String TEXT_CONTRACT_TYPE_USDT = "android_text_contract_type_usdt";//USDT永续%s合约

    String TEXT_CONTRACT_RATIONAL_FAIR_PRICE = "text_contract_rational_fair_price";//合理标记价格
    String TEXT_CONTRACT_AUTO_ADD_BONF_DIALOG_CANCEL = "text_contract_auto_add_bond_dialog_cancel";//再想想
    String TEXT_WITHDRAW_CHAIN_NAME = "text_withdraw_chain_name";//链名称
    String ASSETS_RECHARGE_CHAIN_NAME = "Assets_Recharge_ChainName";//链名称

    String TEXT_CONTRACT_PROFIT_LOSS_DIALOG_HINT_CONTENT_TIP = "text_contract_profit_loss_dialog_hint_content_tip";//若市场波动剧烈，触发止盈/止损时，实际成交价可能与该时刻的价格存在差异。止盈/止损订单也存在无法成交或无法完全成交的可能。
    String TEXT_CONTRACT_PROFIT_LOSS_DIALOG_HINT_CONTENT = "text_contract_profit_loss_dialog_hint_content";//当市场最新成交价格到达您设置的止盈/止损价格时，将会以最优成交价的形式将您此仓位设置的张数进行委托挂单。
    String TEXT_CONTRACT_PROFIT_LOSS_DIALOG_HINT_CONTENT_MIX = "text_contract_profit_loss_dialog_hint_content_mix";//当市场最新成交价格到达您设置的止盈/止损价格时，将会以最优成交价的形式将您此仓位设置的数量进行委托挂单。
    String TEXT_AUTO_ADD_BOND_HINT_CONTENT_TIP = "text_auto_add_bond_hint_content_tip";//这将降低您被强平的概率，但在极端情况下可能会导致您合约账户中的可用全部损失。

    String text_stop_loss_introduct_content_tip = "text_stop_loss_introduct_content_tip";//若市场波动剧烈，止损订单存在无法成交或无法完全成交的可能。

    String Safe_Center_FundCode = "Safe_Center_FundCode";//资金密码
    String TEXT_BUND_PWD_DIALOG_CONTENT = "text_bund_pwd_dialog_content";//您本次登录的设备为新的设备，为保证账户安全，本次交易请输入资金密码
    String TEXT_BUND_PWD_DIALOG_INPUT_HINT = "text_bund_pwd_dialog_input_hint";//请输入资金密码
    String TEXT_BUND_PWD_DIALOG_FORGET_BOND_PWD = "text_bund_pwd_dialog_forget_bond_pwd";//忘记资金密码
    String TEXT_RESET_BUND_PWD_TITLE = "text_reset_bund_pwd_title";//新资金密码
    String TEXT_RESET_BUND_PWD_SURE_NEW_BUND_PWD = "text_reset_bund_pwd_sure_new_bund_pwd";//确认新资金密码
    String TEXT_RESET_BUND_PWD_SURE_BUND_PWD = "text_reset_bund_pwd_sure_bund_pwd";//确认资金密码
    String TEXT_NEVER_ENTER_WHEN_TRADING = "text_never_enter_when_trading";//交易时永不输入
    String TEXT_EVERY_TIME_ENTER_WHEN_TRADING = "text_every_time_enter_when_trading";//交易时每次输入
    String TEXT_ONEDAY_ENTER_WHEN_TRADING = "text_oneday_enter_when_trading";//交易时24小时输入
    String ADDRESS_TRADE_PWD_RESET = "address_trade_pwd_reset";//重置资金密码
    String TEXT_BUND_PWD_GO_TO_SET = "text_bund_pwd_go_to_set";//去设置
    String TEXT_BUND_PWD_GO_TO_MODIFY = "text_bund_pwd_go_to_modify";//修改
    String ADDRESS_TRADE_PWD_UPDATE = "address_trade_pwd_update";//修改资金密码
    String TEXT_BUND_PWD_CURRENT_PWD = "text_bund_pwd_current_pwd";//当前资金密码
    String TEXT_BUND_PWD_REINPUT_PWD = "text_bund_pwd_reinput_pwd";//重复输入密码
    String API_MANAGEMENT_EDIT_API_CHANGE_SUCCESS = "api_management_edit_api_change_success";//修改成功
    String TEXT_BUND_PWD_ACCOUNT = "text_bund_pwd_account";//账号
    String TEXT_BUND_PWD_NEW_PWD = "text_bund_pwd_new_pwd";//	请输入新资金密码
    String TEXT_BUND_PWD_TIP = "text_bund_pwd_tip";//为了您的资金安全，修改资金密码后24小时内不允许提币及法币交易
    String TEXT_WITHDRAW_SETTING_TIP = "text_withdraw_setting_tip";//立即设置
    String TEXT_PWD_ERROR_COUNT_MORE_THAN = "text_pwd_error_count_more_than";//资金密码连续输入错误5次，请重新登录。并在24小时内禁止提币及法币交易
    String ANDROID_TEXT_BUND_PWD_ERROR_TIMES = "android_text_bund_pwd_error_times";//资金密码错误，您还有%s次机会
    String TEXT_WITHDRAW_SETTING_CONTENT = "text_withdraw_setting_content";//你还没有设置资金密码，为了您的账户安全，请前往设置
    String TEXT_SEND_VER_CODE = "text_send_ver_code";//发送验证码

    String TEXT_CONTRACT_OPTIMAL_TRANSACTION_PRICE = "text_contract_optimal_transaction_price";//最优成交价
    String TEXT_CONTRACT_MOST_APPROPRIATE_PRICE_OPEN_POSITION = "text_contract_most_appropriate_price_open_position";//最优追价开仓
    String TEXT_CONTRACT_LIGHTING_OPEN_POSITION_DETAIL = "text_contract_lighting_open_position_detail";//系统选择最容易成交的价格进行挂单，若该委托没有成交或完全成交，会以最新的容易成交的价格继续挂单
    String TEXT_CONTRACT_CLOSE_POSITION_FROZEN_TIP = "text_contract_close_position_frozen_tip";//您已经提交，但是还没有成交的平仓委托
    String TEXT_CONTRACT_LIMIT_CLOSE_POSITION = "text_contract_limit_close_position";//限价平仓
    String TEXT_CONTRACT_FORCE_CLOSE_TIP_TITLE = "text_contract_force_close_tip_title";//预估强平价说明

    String TEXT_CONTRACT_PLAN_EXPLAN = "text_contract_plan_explan";//计划委托说明
    String TEXT_PWD_UPDATE_SAME = "text_pwd_update_same";//新资金密码不能和旧交易密码一致。

    String TEXT_OTC_TRADE_LIMIT = "text_otc_trade_limit";//法币交易额度
    String TEXT_IDENTY_LEVEL_PERMISSION = "text_identy_level_permission";//身份认证等级权限
    String TEXT_IDENTY_LEVEL = "text_identy_level";//等级
    String ANDROID_TEXT_IDENTY_LEVEL_ALONE_LIMIT = "android_text_identy_level_alone_limit";//单笔(%s)
    String ANDROID_TEXT_IDENTY_LEVEL_ALL = "android_text_identy_level_all";//累计(%s)
    String TEXT_IDENTY_LEVEL_NO_VERF = "text_identy_level_no_verf";//未认证

    String TEXT_IDENTY_LEVEL_PERI = "text_identy_level_peri";//初级
    String TEXT_IDENTY_LEVEL_CURRENT = "text_identy_level_current";//=当前等级
    String TEXT_IDENTY_LEVEL_NO_LIMIT = "text_identy_level_no_limit";//不限
    String TEXT_IDENTY_LEVEL_NO_TRADE = "text_identy_level_no_trade";//不可交易
    String Safe_Kyc_Completed_Verification = "Safe_Kyc_Completed_Verification";//您已完成身份认证
    String TEXT_IDENTY_HIGH_LEVEL_FACEID = "text_identy_high_level_faceid";//Face ID人脸核身服务
    String TEXT_IDENTY_HIGH_LEVEL_FACEID_IDCARD = "text_identy_high_level_faceid_idcard";//=身份证识别
    String TEXT_IDENTY_HIGH_LEVEL_FACEID_IDCARD_BEGIN_CHECK = "text_identy_high_level_faceid_idcard_begin_check";//=开始拍摄身份证
    String TEXT_IDENTY_PRI_LEVEL_SUCCESS = "text_identy_pri_level_success";//已通过
    String TEXT_IDENTY_CHECK_WARN = "text_identy_check_warn";//注意事项
    String TEXT_PRI_IDENTY_VER = "text_pri_identy_ver";//初级身份认证
    String TEXT_LEVEL_PERMISSION = "text_level_permission";//等级权限
    String TEXT_START = "text_start";//	开始拍摄

    String IDCARD_CN_TIPS_EMBLEM = "idcard_cn_tips_emblem";//国徽面拍摄
    String IDCARD_CN_TIPS_FACE = "idcard_cn_tips_face";//人像面拍摄
    String REMIND_IDCARD_QUALITY_FAILED_1 = "remind_idcard_quality_failed_1";//未检测到身份证
    String REMIND_IDCARD_QUALITY_FAILED_2 = "remind_idcard_quality_failed_2";//请对齐线框
    String REMIND_IDCARD_QUALITY_FAILED_3 = "remind_idcard_quality_failed_3";//图像不清晰，请调整拍摄
    String REMIND_IDCARD_QUALITY_FAILED_4 = "remind_idcard_quality_failed_4";//=存在反光，请调整拍摄
    String REMIND_IDCARD_QUALITY_FAILED_5 = "remind_idcard_quality_failed_5";//=存在阴影，请调整拍摄
    String REMIND_IDCARD_QUALITY_FAILED_6 = "remind_idcard_quality_failed_6";//=请拍摄人像面
    String REMIND_IDCARD_QUALITY_FAILED_7 = "remind_idcard_quality_failed_7";//=请拍摄国徽面
    String REMIND_IDCARD_QUALITY_FAILED_8 = "remind_idcard_quality_failed_8";//=身份证倒置,请放正
    String TIP_RECT_FIRST_TIP = "tip_rect_first_tip";//=请保证身份证边缘与线框对齐
    String TEXT_HIGH_VER_BACK_DIALOG_TIP = "text_high_ver_back_dialog_tip";//=当前页面返回，您将重新开始身份认证，是否确定返回？

    String USER_MODIFY_PWD_SUFFIX_LEVEL_HIGHT = "user_modify_pwd_suffix_level_hight";//=高级

    String KYC_FACE_PHONE = "kyc_face_phone";//=正对手机
    String KYC_LIGHT_ENUGH = "kyc_light_enugh";//=光线充足
    String KYC_SLOW = "kyc_slow";//=放慢动作

    String TEXT_HIGH_VER_FAILED = "text_high_ver_failed";//=验证超时

    String TEXT_HIGH_VER_FAILED_REASION = "text_high_ver_failed_reasion";//=由于网络原因，未获取到验证结果
    String TEXT_HIGH_VER_FAILED_REFRESH = "text_high_ver_failed_refresh";//=重新验证
    String TEXT_HIGH_VER_FAILED_REFRESH_SUBMIT = "text_high_ver_failed_refresh_submit";//=刷新提交
    String TEXT_HIGH_VER_FAILED_TIMES3 = "text_high_ver_failed_times3";//身份认证已校验3次失败，请24小时后再试
    String TEXT_HIGH_VER_UNIT = "text_high_ver_unit";//万

    String LEGAL_MANAGER = "legal_manager";//P2P管理
    String MY_ASSET = "my_asset";//我的资产


    String PAYMENT_METHOD_ADD = "payment_method_add";//	添加
    String PAYMENT_METHOD_GO_TO_ADD = "payment_method_go_to_add";//去添加

    String MIN_AMOUNT = "min_amount";//	最小数量


    String KYC_CLICK_IDCARD_FONT = "kyc_click_idcard_font";//=点击拍摄身份证人像面
    String KYC_CLICK_IDCARD_BACK = "kyc_click_idcard_back";//=点击拍摄身份证有效期面
    String KYC_IMAGE_VAGUE = "kyc_image_vague";//	模糊不清


    String TEXT_PAYMENT_ADD_BANK_CARD = "text_payment_add_bank_card";//=添加银行卡
    String TEXT_PAYMENT_ADD_SAVE = "text_payment_add_save";//=保存
    String TEXT_PAYMENT_ADD_TIP = "text_payment_add_tip";//=为保证账户资金安全，只能绑定认证用户本人的银行卡
    String TO_INCREASE_LIMIT = "to_increase_limit";//	去提额

    String SELECT_COLLECTION_PAYMENT_METHOD_BUY = "select_collection_payment_method_buy";//	选择付款方式
    String TEXT_OTC_TRADE_INFO_PERFECT = "text_otc_trade_info_perfect";//完善交易信息

    String TEXT_PAYMENT_DELETE_THIS_PAYMENT = "text_payment_delete_this_payment";//确定要删除该收款方式？


    String TEXT_OTC_COIN_TYPE = "text_otc_coin_type";//=币种

    String TEXT_OTC_FLO_PRICE_PER = "text_otc_flo_price_per";//=浮动比例
    String TEXT_OTC_INPUT_COUNT = "text_otc_input_count";//=请输入交易数量

    String TEX_OTC_I_ALREADY_READ = "tex_otc_i_already_read";//=我已阅读并同意遵守


    String TEXT_OTC_CHOOSE_ONE_LESS_DIALOG_TIP = "text_otc_choose_one_less_dialog_tip";//=至少选择一种付款方式
    String TEXT_SINGLE_MAX_TRADE_LIMIT = "text_single_max_trade_limit";//单笔最大交易限额

    String TEXT_MIN_TRADE_AMOUNT = "text_min_trade_amount";//最小交易数量
    String TEXT_OTC_SEL_GET_PAYMENT = "text_otc_sel_get_payment";//=请选择收款方式
    String TEXT_OTC_SEL_PAYMENT = "text_otc_sel_payment";//=请选择付款方式


    String TEXT_OTC_MAX_PRICE_HINT = "text_otc_max_price_hint";//请输入最高购买价格


    String TEXT_OTC_MY_AD_CREATE_TIME = "text_otc_my_ad_create_time";//=创建时间

    String TEXT_OTC_MY_AD_LEAVE_COUNT = "text_otc_my_ad_leave_count";//剩余数量
    String TEXT_OTC_MY_AD_DOWN = "text_otc_my_ad_down"; //已下架
    String TEXT_OTC_MY_AD_DOWN_TIP = "text_otc_my_ad_down_tip";//您确定要下架该广告吗?


    String TEXT_APPEAL_TITLE_HINT = "text_appeal_title_hint";//提起申诉后资产将会冻结，申诉专员将介入本次交易，申诉结束后，冻结解除
    String TEXT_APPEAL_TYPE_SELECT = "text_appeal_type_select";//选择申述类型
    String TEXT_APPEAL_SUBMIT = "text_appeal_submit";//提交申诉
    String TEXT_APPEAL_NO_FREED = "text_appeal_no_freed";//对方未放币
    String TEXT_APPEAL_NO_ANSWER = "text_appeal_no_answer";//对方无应答
    String TEXT_APPEAL_OTHER = "text_appeal_other";//其他

    String TEXT_OTC_ORDER_PAY_TIP = "text_otc_order_pay_tip";//请使用本人（bg_param_1）名下的bg_param_2向此账号进行转账汇款，如不一致，卖方可要求退款或取消订单

    String TEXT_ORDER_ID = "text_order_id";//订单号
    String TEXT_OTC_DETAIL_TIP_COMPLETED_BUYER = "text_otc_detail_tip_completed_buyer";//卖家已放币，成功购买了bg_param_1
    String TEXT_OTC_DETAIL_TIP_COMPLETED_SELLER = "text_otc_detail_tip_completed_seller";//成功卖出了bg_param_1

    String TEXT_STATE_OTC_ORDER_COMPLETED = "text_state_otc_order_completed";//已完成
    String TEXT_STATE_OTC_ORDER_CANCELED = "text_state_otc_order_canceled";//已取消

    String TEXT_BANK_CARD_NUMBER = "text_bank_card_number";//银行卡号
    String TEXT_I_THINK_THINK_AGAIN = "text_i_think_think_again";//我再想想

    String TEXT_OTHER_SIDE_BANK = "text_other_side_bank";//对方银行卡

    String TEXT_RELEASE_COIN_ENSURE = "text_release_coin_ensure";//放币确认
    String TEXT_REALESE_COIN_WARNING = "text_realese_coin_warning";//请务必登录网上银行或第三方支付账号，确认收到该笔款项后，再进行放币
    String TEXT_ENSURE_RELEASE_COIN_ENSURE = "text_ensure_release_coin_ensure";//我确认已登录收款账户，并核对收款无误
    String TEXT_TO_IDENTIFICATE_NOW = "text_to_identificate_now";//立即认证
    String TEXT_CREATE_ORDER_AGAIN = "text_create_order_again";//重新下单


    String TRADE_ENTRUST_TAB_HISTORY = "trade_entrust_tab_history";//历史委托
    String Futures_History_OrderHistory = "Futures_History_OrderHistory";//历史委托

    String TEXT_KYC_IDENTITY_TIP = "text_kyc_identity_tip";//	实名认证功能暂时只支持中国大陆用户

    String TEXT_OTC_OPEN_RULES_URL = "text_otc_open_rules_url";//https://bitget.zendesk.com/hc/zh-cn/articles/************
    String TEXT_OTC_OPEN_RULES_TITLE = "text_otc_open_rules_title";//bitget法币交易用户使用协议
    String TEXT_IDENTITY_IDCARD_TITLE = "text_identity_idcard_title";//身份信息认证
    String TEXT_IDENTITY_FACE_TITLE = "text_identity_face_title";//视频认证
    String TEXT_IDENTITY_FACE_TITLE_TIP = "text_identity_face_title_tip";//	视频认证通过后，身份认证成功

    String TEXT_OTC_ORDER_NUMBER = "text_otc_order_number";//订单编号
    String TEXT_OTC_TRADE_PERSON = "text_otc_trade_person";//	交易对象


    String TEXT_OTC_PUBLISH_TIP_PAYTIME_CONTENT1 = "text_otc_publish_tip_paytime_content1";//=您需在付款期限内完成线下付款并点击“已付款”按钮，超时未点击，系统将自动取消该笔交易。
    String TEXT_OTC_PUBLISH_TIP_PAYTIME_CONTENT2 = "text_otc_publish_tip_paytime_content2";//=对方需在付款期限内完成线下付款并点击“已付款”按钮，超时未点击，系统将自动取消该笔交易。


    String TEXT_VERIFICATION_FAILED_PLEASE_RETRY = "text_verification_failed_please_retry";//验证失败，请重新验证


    String MARKET_TEXT_2HOUR = "market_text_2hour";//2时
    String MARKET_TEXT_6HOUR = "market_text_6hour";//6时

    String TEXT_FILTER_DATE_7_DAY = "text_filter_date_7_day";//最近7天
    String TEXT_FILTER_DATE_7_DAY_TO_1_MONTH = "text_filter_date_7_day_to_1_month";//7天至1个月
    String TEXT_FILTER_DATE_7_DAY_TO_3_MONTH = "text_filter_date_7_day_to_3_month";//7天至3个月

    String TEXT_COIN_INTRODUCE_URL_ = "text_coin_introduce_url_";//币种介绍连接

    String WITHDRAW_TRANSFER_ACCOUNT_TEXT = "withdraw_transfer_account_text";//账户名
    String WITHDRAW_TRANSFER_TEXT = "withdraw_transfer_text";//内部转账


    String WITHDRAW_TRANSFER_FREE_TEXT = "withdraw_transfer_free_text";//免费
    String WITHDRAW_NORMAL_TITLE_TEXT = "withdraw_normal_title_text";//普通提币
    String TEXT_INPUT_COUNT_LOT_FEE_HINT = "text_input_count_lot_fee_hint";//输入的提币数量请大于手续费

    String TEXT_CALCULAR_INPUT_PINGCANG_PRICE_HINT = "text_calcular_input_pingcang_price_hint";//请输入平仓价格
    String TEXT_CALCULAR_INCOME = "text_calcular_income";//收益
    String CopyTrade_view_Income = "CopyTrade_view_Income";//收益
    String TEXT_CALCULAR_CALCUTOR = "text_calcular_calcutor";//计算
    String TEXT_CALCULAR_SELECT_GANGGAN_MUL = "text_calcular_select_ganggan_mul";//请输入杠杆倍数git

    String P2P_Manage_nickname_setting_title = "P2P_Manage_nickname_setting_title";//昵称设置
    String P2P_Manage_nickname_setting_input_hint = "P2P_Manage_nickname_setting_input_hint";//请输入昵称
    String TEXT_OTC_NICKNAME_SETTING_TIP1 = "text_otc_nickname_setting_tip1";//=只允许设置汉字、字母和数字
    String P2P_Manage_nickname_setting_tip3 = "P2P_Manage_nickname_setting_tip3";//昵称设置后将不可更改，请谨慎操作
    String TEXT_OTC_NICKNAME_SETTING_TIP4 = "text_otc_nickname_setting_tip4";//昵称将用于您在法币交易过程中及广告发布后的显示，以避免您的真实信息泄露


    String TEXT_SINGLE_LIMIT_MIN = "text_single_limit_min";//	单笔最小限额
    String TEXT_SINGLE_LIMIT_MAX = "text_single_limit_max";//	单笔最大限额

    String TEXT_SINGLE_LIMIT_MIN_HINT = "text_single_limit_min_hint";//	请输入单笔最小限额
    String TEXT_SINGLE_LIMIT_MAX_HINT = "text_single_limit_max_hint";//	请输入单笔最大限额

    String TEXT_CALCULAR_HINT = "text_calcular_hint";//	*计算器计算的数值仅作为您交易时的参考，计算时并未计算实际交易时所需的手续费等其他实际费用。

    String TEXT_KYC_IDENTITY_CHECK_TITLE = "text_kyc_identity_check_title";//	身份核验
    String TEXT_KYC_IDENTITY_CHECK_HINT = "text_kyc_identity_check_hint";//	达到限额要求，需进行身份核验后再操作
    String TEXT_KYC_IDENTITY_CHECK_FAILED = "text_kyc_identity_check_failed";//	核验失败
    String TEXT_KYC_IDENTITY_CHECK_FAILED_HINT = "text_kyc_identity_check_failed_hint";//	本次身份核验未通过
    String TEXT_KYC_IDENTITY_CHECK_SUCCESS = "text_kyc_identity_check_success";//	核验成功，请于bg_param_1分钟内完成下单
    String TEXT_KYC_IDENTITY_CHECK_GO_TO_HINT = "text_kyc_identity_check_go_to_hint";//	您的累计交易额达到档位上限，请完成活体认证后再操作

    String Safe_Center_AntiphishingCodeEmailToast = "Safe_Center_AntiphishingCodeEmailToast";//防钓鱼码可以防范假冒Bitget的网站和邮件。设置后Bitget给您发送的邮件中包含此码，没有包含的为诈骗邮件。
    String Safe_Center_AntiphishingPlace = "Safe_Center_AntiphishingPlace";//请输入8-32位英文或数字

    String view_NotificationPermissionHint = "view_NotificationPermissionHint"; //无法获取通知权限，请在手机权限管理中打开Bitget的通知权限
    String Safe_Center_Set_AntiphishingCode = "Safe_Center_Set_AntiphishingCode";//设置防钓鱼码
    String TEXT_CHANGE_ANTIPHISHING_CODE = "text_change_antiphishing_code";//修改防钓鱼码

    String TEXT_STATUS_MAINTENANCE = "text_status_maintenance";//维护中


    String TEXT_MONEY = "text_money";//金额


    String TEXT_SALE_BY_COUNT = "text_sale_by_count";//	按数量出售

    String TEXT_CANCLE_BEFORE_MIN = "text_cancle_before_min";//bg_param_1后自动取消

    String TEXT_C2C_TRANSFER_ALERT = "text_c2c_transfer_alert";//出售前需要将币划转至OTC账户，是否划转？

    String TEXT_HAS_BEEN_SET = "text_has_been_set";


    String TEXT_PUBLISH_MAX_LIMIT = "text_publish_max_limit";//该广告单的交易金额大于广告单的单笔最大交易金额bg_param_1


    String LINK_JOIN_COMMUNITY = "link_join_community";//http://dev1h5.k8s.itssofast.com/html/pages/app/join_community.html

    String DIALOG_OPEN_THIRD_APP_HINT = "dialog_open_third_app_hint";//即将打开第三方应用bg_param_1
    String DIALOG_OPEN_BROWSER_HINT = "dialog_open_browser_hint";//即将在浏览器中访问

    String HIGH_PERMISSION_HINT = "high_permission_hint";//	无法获取摄像头数据或存储权限，请在手机权限管理中打开Bitget的摄像头及存储权限
    String HIGH_PERMISSION_EXTERNAL_HINT = "high_permission_external_hint";//	存储

    String APP_NETWORK_ERROR = "app_network_error";//网络异常，请稍后重试
    String TEXT_SOUCE_FROM_CONTENT = "text_souce_from_content";//	"*"表示,该市场暂无此交易对的指数来源，采用btc交易作为换算替代

    String TEXT_CODE_SEND_TO = "text_code_send_to";//验证码已发送至

    String TEXT_ACCOUNT_REGISTED = "text_account_registed";//该账号已注册
    String TEXT_AUTH_LOGIN_TITLE = "text_auth_login_title";//授权登录
    String TEXT_AUTH_LOGIN_CONTENT_HINT = "text_auth_login_content_hint";//申请授权使用您的UID及从属关系
    String TEXT_AUTH_LOGIN_USED_ACCOUNT = "text_auth_login_used_account";//使用账号
    String TEXT_AUTH_LOGIN_REFUSED = "text_auth_login_refused";//拒绝
    String TEXT_AUTH_LOGIN_CONTENT_TIP = "text_auth_login_content_tip";//如果继续，Bitget将与bg_param_1共享您账户的部分可公开信息
    String TEXT_WELFARE_CENTER = "text_welfare_center";//福利中心
    String TEXT_API_MANAGER = "x220225_api_manager";//Api 管理
    String TEXT_BFT_DEDUCTION_DESCRIPTION = "text_bft_deduction_description";//使用BFT支付交易手续费优惠30%。当BFT余额不足以支付本次交易手续费时，将按原有方式全额支付交易手续费。

    String TEXT_WEBVIEW_DOWNLOD_IMG_SUCCESS_HINT = "text_webview_downlod_img_success_hint";//图片下载成功
    String TEXT_WEBVIEW_DOWNLOD_IMG_FAILED_HINT = "text_webview_downlod_img_failed_hint";//图片下载失败

    String TEXT_MAREKT_COLOR_TITLE = "text_marekt_color_title";//涨跌颜色
    String TEXT_MARKET_COLOR_GREEN_RISE = "text_market_color_green_rise";//绿涨红跌
    String TEXT_MAREKT_COLOR_RED_RISE = "text_marekt_color_red_rise";//红涨绿跌
    String TEXT_SELECT_YOUR_COUNTRY = "text_select_your_country";//请选择您的国籍
    String TEXT_MAINLAND_USER = "text_mainland_user";//中国大陆用户
    String TEXT_OTHER_AREA_COUNTRY_USER = "text_other_area_country_user";//其他国家或地区用户
    String TEXT_ID_CARD_TYPE = "text_id_card_type";//证件类型
    String TEXT_IDENTITY_TYPE_MAINLAND_CARD = "text_identity_type_mainland_card";//身份证
    String TEXT_IDENTITY_TYPE_PASSPORT = "text_identity_type_passport";//护照
    String TEXT_IDENTITY_TYPE_OTHER = "text_identity_type_other";//其他证件
    String TEXT_INPUT_IDENTITY_NUMBER_PLEASE = "text_input_identity_number_please";//请输入证件号码
    String TEXT_COUNTRY_AND_AREA = "text_country_and_area";//国家/地区

    String TEXT_SELECT_CARD_TYPE = "text_select_card_type";//请选择证件类型
    String TEXT_ID_CARD_POSITIVE = "text_id_card_positive";//身份证正面照
    String TEXT_ID_CARD_REVERSE = "text_id_card_reverse";//身份证反面照
    String TEXT_HAND_ID_CARD_AND_STATEMENT = "text_hand_id_card_and_statement";//手持身份证及声明
    String TEXT_UPDATE_ID_PHOTO_HINT1 = "text_update_id_photo_hint1";//*请上传清晰的证件照片，必须能看清证件号和姓名
    String TEXT_UPDATE_ID_PHOTO_HINT2 = "text_update_id_photo_hint2";//*仅支持PNG、JPG、JPEG格式，每张大小限制在2M以内
    String TEXT_UPDATE_ID_PHOTO_HINT3 = "text_update_id_photo_hint3";//*手持证件照需要包含“Bitget”和当日日期
    String TEXT_HAND_PASSPORT_AND_STATEMENT = "text_hand_passport_and_statement";//手持护照及声明
    String TEXT_HAND_CARD_AND_STATEMENT = "text_hand_card_and_statement";//手持证件及声明
    String TEXT_FOREIGN_KYC_COUNTRY_DEFAULT = "text_foreign_kyc_country_default";//韩国
    String TEXT_FOREIGN_KYC_COUNTRY_DEFAULT_KEY = "text_foreign_kyc_country_default_key";//94
    String TEXT_FOREIGN_KYC_COUNTRY_DEFAULT_SHORT = "text_foreign_kyc_country_default_short";//KR
    String TEXT_FOREIGN_KYC_TRADE_LIMIT_TIP = "text_foreign_kyc_trade_limit_tip";//交易额已达到档位限制，由于非中国大陆用户不支持重启活体认证，暂无法继续交易。

    String TEXT_CURRENT_FOLLOW_ORDER = "text_current_follow_order";//当前跟单
    String TEXT_CURRENT_CARRAY_ORDER = "text_current_carray_order";//当前带单
    String TEXT_ENSURE_CLOSE_POSITION = "text_ensure_close_position";//确定平仓

    String TEXT_TOTAL_INCOME = "text_total_income";//累计分润(USDT)
    String TEXT_INITIATE_SINGLE = "text_initiate_single";//发起带单
    String TEXT_MY_SINGLE = "text_my_single";//我的带单
    String TEXT_MY_FOLLOWER = "text_my_follower";//我的跟随者
    String TEXT_SINGLE_INCOME = "text_single_income";//=带单分润
    String TEXT_SEE_COPY_INCOME = "text_see_copy_income";//=查看跟单收益


    String TEXT_COPY = "text_copy";//=跟单
    String TEXT_COPIED = "text_copied";//=已跟单
    String TEXT_FOLLOWER_COUNT = "text_follower_count";//	累计跟随人数


    String TEXT_SELECT_TRADER = "text_select_trader";//	搜索交易员
    String TEXT_MARKET_PRICE_TO_LIMIT_TO_END_LOSS = "text_market_price_to_limit_to_end_loss";//市场价格bg_param_1 将触发止损单
    String TEXT_MY_TRACE_TEACHER = "text_my_trace_teacher";//我的交易员
    String TEXT_HISTORY_FOLLOW = "text_history_follow";//历史跟单
    String TEXT_REMOVE = "text_remove";//移除
    String TEXT_YESTADAY_INCOME = "text_yestaday_income";//=昨日分润
    String TEXT_INCOME_DETAIL = "text_income_detail";//=收益明细
    String TEXT_COPY_TOTAL_VOL = "text_copy_total_vol";//=跟随总额

    String TEXT_JOINED = "text_joined";//=已入驻 bg_param_1 天
    String TEXT_HIS_COPY = "text_his_copy";//=历史带单
    String TEXT_FOLLOWER = "text_follower";//=跟随者
    String TEXT_BEGIN_COPY_TRADE = "text_begin_copy_trade";//=发起带单交易
    String TEXT_PING_PRICE = "text_ping_price";//=平仓价
    String TEXT_FOLLOW_INCOME = "text_follow_income";//=跟随收益
    String TEXT_EDIT_COPY = "text_edit_copy";//=编辑跟单
    String TEXT_OPEN_POSITION_TIME = "text_open_position_time";//开仓时间

    String TEXT_PRICE_TO_LIMIT_TO_END_PROFIT = "text_price_to_limit_to_end_profit";//止盈价
    String TEXT_PRICE_TO_LIMIT_TO_END_LOSS = "text_price_to_limit_to_end_loss";//止损价

    String TEXT_CURRENT_PRICE_CLEAR = "text_current_price_clear";//当前价
    String TEXT_END_TRACE_PROFIT_TIP = "text_end_trace_profit_tip";//止盈止损价为预估值，未考虑资金费用等影响因素；在止盈止损触发一次后会自动清空上次设置的价格，如有需要请再次设置；在极端行情下止盈止损可能会触发失败。


    String TEXT_FOLLOW_ORDER_CONTRACT = "text_follow_order_contract";//=跟单合约
    String TEXT_FOLLOW_ORDER_SUCCESS = "text_follow_order_success";//=跟单成功

    String TEXT_ALL_CONTRACT = "text_all_contract";//全部合约
    String TEXT_TIMES = "text_times";//	倍
    String TEXT_CLICK_VIEW_POSITION_DETAIL = "text_click_view_position_detail";//点击查看仓位详情
    String CopyTrade_OrderMessage_Title = "CopyTrade_OrderMessage_Title";//	跟单提醒
    String TEXT_DEPOSIT = "text_deposit";//保证金
    String Futures_History_Margin = "Futures_History_Margin";//保证金
    String TEXT_CLOSE_POSITION_AVE_PRICE = "text_close_position_ave_price";//平仓均价
    String API_MANAGEMENT_API_EDIT = "api_management_api_edit";//编辑
    String TEXT_FOLLOW_MARGIN = "text_follow_margin";//跟随本金
    String TEXT_SETTING_SUCCESS_HINT = "text_setting_success_hint";//	设置成功
    String TEXT_MAKE_SURE_REMOVE = "text_make_sure_remove";//=确认移除
    String TEXT_REMOVE_FOLLOWER_HINT = "text_remove_follower_hint";//=移除该用户后仅表示用户不再跟随您开新的订单，而已跟随开仓的订单不受影响
    String TEXT_STOP_COPY_ORDER_HINT = "text_stop_copy_order_hint";//您当前的交易员身份已被撤销，如有疑问请联系客服

    String TEXT_ONLY_SHOW_FOLLOW_TRACE_POSITIONS = "text_only_show_follow_trace_positions";//仅显示跟单持仓,不包含自主下单的仓位
    String TEXT_FOLLOW_LAST_TIME_I_SAWTHIS = "text_follow_last_time_i_sawthis";//  以下为历史消息
    String TEXT_FOLLOW_GO_COPY = "text_follow_go_copy";// 去跟单

    String TEXT_FOLLOW_ORDER_RULE_URL = "text_follow_order_rule_url";//=https://bitget.zendesk.com/hc/zh-cn/articles/360041904552
    String TEXT_FOLLOW_ORDER_RULE_TITLE = "text_follow_order_rule_title";//bitget跟单协议
    String TEXT_TODAY_INCOME_TIP_URL_MIX = "text_today_income_tip_url_mix";//=https://bitget.zendesk.com/hc/zh-cn/articles/360042661391
    String TEXT_COMMUNITY_TRADER_HINT = "text_community_trader_hint";//=数据每小时更新一次
    String TEXT_PERSONAL_INTRO_INPUT_PLEASE = "text_personal_intro_input_please";//请输入个人简介
    String TEXT_PERSONAL_INTRO = "text_personal_intro";//个人简介
    String TEXT_ALBUM = "text_album";//相册
    String X221026_TRACER_TEXT_NICKNAME = "x221026_tracer_text_nickname";//带单个人设置昵称
    String TEXT_PERSONAL_SETTING = "text_personal_setting";//个人设置
    String TEXT_ALEARDY_SAVE_TO_PICTURES = "text_aleardy_save_to_pictures";//已保存到相册
    String TEXT_COMMUNITY_TRACER_SELF_DES = "text_community_tracer_self_des";//=忙着赚钱，什么也不想说
    String TEXT_OPERATE_FAILE_PLEASE_RETRY = "text_operate_faile_please_retry";//操作失败，请重试
    String TEXT_COMMUNITY_INCOME_RATE = "text_community_income_rate";//  收益率

    String USER_SETTING_CURMB_PWD = "user_setting_curmb_pwd";//修改登录密码
    String USER_MODIFY_PWD_LABEL_OLDPWD = "user_modify_pwd_label_oldpwd";//原登录密码
    String USER_MODIFY_PWD_LABEL_PWD = "user_modify_pwd_label_pwd";//新登录密码
    String USER_MODIFY_PWD_LABEL_DPWD = "user_modify_pwd_label_dpwd";//确认新密码
    String TEXT_MODIFY_LOGIN_PWD_HINT = "text_modify_login_pwd_hint";//为了您的资金安全，修改登录密码后24小时内不允许提币及OTC卖出交易
    String TEXT_MODIFY_LOGIN_PWD_NO_SAME_ERROR = "text_modify_login_pwd_no_same_error";//	原登录密码不能和新登录密码一样

    String TEXT_MODIFY_LOGIN_PWD_SUCCESS_HINT = "text_modify_login_pwd_success_hint";//登录密码已修改成功，请重新登录

    String TEXT_SET_TRADE_PWD_SUCCESS_HINT = "text_set_trade_pwd_success_hint";//设置资金密码成功
    String TEXT_MODIFY_TRADE_PWD_SUCCESS_HINT = "text_modify_trade_pwd_success_hint";//修改资金密码成功
    String TEXT_RESET_TRADE_PWD_SUCCESS_HINT = "text_reset_trade_pwd_success_hint";//	重置资金密码成功
    String TEXT_SET_ANTIPHISHING_CODE_SUCCESS_HINT = "text_set_antiphishing_code_success_hint";//设置防钓鱼码成功
    String TEXT_MODIFY_ANTIPHISHING_CODE_SUCCESS_HINT = "text_modify_antiphishing_code_success_hint";//修改防钓鱼码成功
    String TEXT_POSITION_TYPE_ALL = "text_position_type_all";//=全仓
    String TEXT_POSITION_TYPE_CHASE = "text_position_type_chase";//=逐仓
    String TEXT_POSITION_CHASE_LEVER = "text_position_chase_lever";//=全仓杠杆
    String TEXT_POSITIONS_CANG_TYPE = "text_positions_cang_type";//	仓位类型
    String Text_Max_Buy_Price = "text_max_buy_price";//	最高买价
    String Text_Min_Sell_Price = "text_min_sell_price";//	最低卖价
    String Markets_Kline_change_leverage = "Markets_Kline_change_leverage";// 调整杠杆
    String Futures_AdjustLeverageAlert_Title = "Futures_AdjustLeverageAlert_Title";

    String Text_Can_Not_Change_Lever_Because_Entrusting = "text_can_not_change_lever_because_entrusting";// 调整杠杆

    String Text_Max_Single_Market_Price_Open_Position_Amout_Limit = "text_max_single_market_price_open_position_amout_limit";//单笔闪电开仓张数最多bg_param_1张
    String Text_Max_Single_Market_Price_Close_Position_Amout_Limit = "text_max_single_market_price_close_position_amout_limit";//单笔闪电平仓张数最多bg_param_1张

    String TEXT_TAKE_PROFIT_PRICE = "text_take_profit_price";//=止盈价格
    String TEXT_END_LOSS_PRICE = "text_end_loss_price";//=止损价格
    String TEXT_EXPECTED_PROFIT = "text_expected_profit";//=预计收益
    String TEXT_INPUT_COUNT_HINT = "text_input_count_hint";//=可止盈 bg_param_1 (单笔最大bg_param_2)
    String TEXT_END_LOSS_COUNT = "text_end_loss_count";//=止损数量
    String TEXT_TAKE_PROFIT_COUNT = "text_take_profit_count";//=止盈数量
    String PLAN_MANUAL_CANCEL = "plan_manual_cancel";//手动撤销
    String T_CANCLE_REMARK = "t_cancle_remark";// 其他原因

    String TEXT_AUTH_FAILED_DIALOG_TITLE = "text_auth_failed_dialog_title";//=授权失败
    String TEXT_AUTH_FAILED_DIALOG_HINT = "text_auth_failed_dialog_hint";//=请查看您的手机设置：
    String TEXT_AUTH_FAILED_DIALOG_REASION_CONTENT1 = "text_auth_failed_dialog_reasion_content1";//=1.是否网络连接正常
    String TEXT_AUTH_FAILED_DIALOG_REASION_CONTENT2 = "text_auth_failed_dialog_reasion_content2";//=2.是否有使用安全软件或VPN等网络加速软件
    String TEXT_AUTH_FAILED_DIALOG_REASION_CONTENT3 = "text_auth_failed_dialog_reasion_content3";//=3.是否更改手机本地时间
    String Text_Begin_To_Speed_Otc = "text_begin_to_speed_otc";//开始极速交易
    String Text_Otc_Service_Time = "text_otc_service_time";//0手续费，7x24小时服务

    String TEXT_KLINE_HEIGHT_SET_SCREEN_SET = "text_kline_height_set_screen_set";//界面设置
    String TEXT_KLINE_HEIGHT_SET_HEIGHT_TITLE = "text_kline_height_set_height_title";//K线图高度
    String TEXT_KLINE_HEIGHT_SET_KLINE_ATTACH_CURRENT_PRICE = "text_kline_height_set_kline_attach_current_price";//现价线

    String TEXT_CONTRACT_MARKET_WR_INDEX_SET = "text_contract_market_wr_index_set";//指标设置

    String TEXT_KLINE_INDEX_LIST_INDEX = "text_kline_index_list_index";//指标
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX = "text_kline_index_list_main_index";//主图指标
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_MA_TITLE = "text_kline_index_list_main_index_ma_title";//移动平均线
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_MA_CONTENT = "text_kline_index_list_main_index_ma_content";//MA是对收盘价进行平均之后的生成的一条曲线，对收盘价平均的目的是为了消除汇价短期波动对趋势判断的影响。MA是连续若干天的价格的算数平均，时间周期就是MA的参数。如果参数选择为20天，20日移动平均线的值用符号MA(20)表示。
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_EMA_TITLE = "text_kline_index_list_main_index_ema_title";//指数平均数指数
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_EMA_CONTENT = "text_kline_index_list_main_index_ema_content";//EMA 指数平均数指标，是一种趋向类指标，其原理是对价格收盘价进行算术平均，并根据计算结果来进行分析，用于判断价格未来走势的变动趋势。与 MACD 指标、DMA 指标相比，EMA 指标由于其计算公式中着重考虑了价格当天（当期）行情的权重，因此指标自身的计算公式决定了作为一类趋势分析指标。
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_BOLL_TITLE = "text_kline_index_list_main_index_boll_title";//布林线
    String TEXT_KLINE_INDEX_LIST_MAIN_INDEX_BOLL_CONTENT = "text_kline_index_list_main_index_boll_content";//BOLL 指标是根据统计学中的标准差原理设计出来的一种非常简单实用的技术分析指标。一般而言，股价的运动总是围绕某一价值中枢（如均线、成本线等）在一定的范围内变动，布林线指标指标正是在上述条件的基础上，引进了“股价通道”的概念，其认为股价通道的宽窄随着股价波动幅度的大小而变化，而且股价通道又具有变异性，它会随着股价的变化而自动调整。正是由于它具有灵活性、直观性和趋势性的特点，BOLL 指标渐渐成为投资者广为应用的市场上热门指标。一般认为，BOLL 指标有三大功能：1、指示支撑和阻力作用；2、显示超买和超卖；3、显示整体趋势。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX = "text_kline_index_list_other_index";//副图指标
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_VOL_CONTENT = "text_kline_index_list_other_index_vol_content";//VOL 成交量是指在某一时段内具体的交易数，反映了资金进出市场的情况，成交量是判断市场走势的重要指标。一般情况下，成交量大且价格上涨的股票，趋势向好。成交量持续低迷时，一般出现在熊市或股票整理阶段，市场交易不活跃。成交量是判断股票走势的重要依据，对分析主力行为提供了重要的依据。投资者对成交量异常波动的股票应当密切关注。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_MACD_TITLE = "text_kline_index_list_other_index_macd_title";//指数平滑移动均线
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_MACD_CONTENT = "text_kline_index_list_other_index_macd_content";//MACD 指数平滑移动均线 MACD 指标是根据均线的构造原理，对股票价格的收盘价进行平滑处理，求出算术平均值以后再进行计算，是一种趋向类指标。MACD 指标是运用快速（短期）和慢速（长期）移动平均线及其聚合与分离的征兆，加以双重平滑运算。而根据移动平均线原理发展出来的 MACD ，一则去除了移动平均线频繁发出假信号的缺陷，二则保留了移动平均线的效果，因此，MACD 指标具有均线趋势性、稳重性、安定性等特点，是用来研判买卖股票的时机，预测股票价格涨跌的技术分析指标。MACD 指标主要是通过 EMA、DIFF 和 DEA（或叫 MACD、DEM ）这三值之间关系的研判，DIFF 和 DEA 连接起来的移动平均线的的研判以及 DIFF 减去 DEM 值而绘制成的柱状图（ BAR ）的研判等来分析判断行情，预测股价中短期趋势的主要的股市技术分析指标。其中， DIFF 是核心， DEA 是辅助。 DIFF 是快速平滑移动平均线（ EMA 1）和慢速平滑移动平均线（ EMA 2）的差。 BAR 柱状图在股市技术软件上是用红柱和绿柱的收缩来研判行情。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_KDJ_TITLE = "text_kline_index_list_other_index_kdj_title";//随机指标
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_KDJ_CONTENT = "text_kline_index_list_other_index_kdj_content";//KDJ 随机指标是根据统计学的原理，通过一个特定的周期（常为9日、9周等）内出现过的最高价、最低价及最后一个计算周期的收盘价及这三者之间的比例关系，来计算最后一个计算周期的未成熟随机值 RSV ，然后根据平滑移动平均线的方法来计算 K 值、 D 值与 J 值，并绘成曲线图来研判股票走势。随机指标( KDJ )是以最高价、最低价及收盘价为基本数据进行计算，得出的 K 值、 D 值和 J 值分别在指标的坐标上形成的一个点，连接无数个这样的点位，就形成一个完整的、能反映价格波动趋势的 KDJ 指标。它主要是利用价格波动的真实波幅来反映价格走势的强弱和超买超卖现象，在价格尚未上升或下降之前发出买卖信号的一种技术工具。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_RSI_TITLE = "text_kline_index_list_other_index_rsi_title";//相对强弱指数
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_RSI_CONTENT = "text_kline_index_list_other_index_rsi_content";//RSI 相对强弱指数是通过比较一段时期内的平均收盘涨数和平均收盘跌数来分析市场买沽盘的意向和实力，从而作出未来市场的走势。 RSI 通过特定时期内股价的变动情况计算市场买卖力量对比，来判断股票价格内部本质强弱、推测价格未来的变动方向的技术指标。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_ROC_TITLE = "text_kline_index_list_other_index_roc_title";//变动率指标
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_ROC_CONTENT = "text_kline_index_list_other_index_roc_content";//ROC 变动率指标是由当天的股价与一定的天数之前的某一天股价比较，其变动速度的大小,来反映股票市场变动的快慢程度。当 ROC 向上则表示强势,以100为中心线,由中心线下上穿大于100时为买入信号。当 ROC 向下则表示弱势,以100为中心线,由中心线上下穿小于100时为卖出信号。当股价创新高时, ROC 未能创新高,出现背离,表示头部形成。当股价创新低时, ROC 未能创新低,出现背离,表示底部形成。
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_CCI_TITLE = "text_kline_index_list_other_index_cci_title";//顺势指标
    String TEXT_KLINE_INDEX_LIST_OTHER_INDEX_CCI_CONTENT = "text_kline_index_list_other_index_cci_content";//种比较新颖的技术指标。它最早是用于期货市场的判断，后运用于股票市场的研判，并被广泛使用。与大多数单一利用股票的收盘价、开盘价、最高价或最低价而发明出的各种技术分析指标不同， CCI 指标是根据统计学原理，引进价格与固定期间的股价平均区间的偏离程度的概念，强调股价平均绝对偏差在股市技术分析中的重要性，是一种比较独特的技术分析指标。 CCI 指标是专门衡量股价是否超出常态分布范围，属于超买超卖类指标的一种,但它与其他超买超卖型指标又有自己比较独特之处。象 KDJ 、 WR %、 CCI 等大多数超买超卖型指标都有“0——100”上下界限，因此，它们对待一般常态行情的研判比较适用，而对于那些短期内暴涨暴跌的股票的价格走势时，就可能会发生指标钝化的现象。而 CCI 指标却是波动于正无穷大到负无穷大之间，因此不会出现指标钝化现象，这样就有利于投资者更好地研判行情，特别是那些短期内暴涨暴跌的非常态行情。

    String TEXT_KLINE_INDEX_SET_VALUE_DAY = "text_kline_index_set_value_day";//日
    String TEXT_KLINE_INDEX_SET_VALUE_WIDTH = "text_kline_index_set_value_width";//宽度
    String TEXT_KLINE_INDEX_SET_VALUE_PERIOD = "text_kline_index_set_value_period";//周期

    String TEXT_KLINE_INDEX_SET_VALUE_MACD_DIFF_TITLE = "text_kline_index_set_value_macd_diff_title";//DIFF：收盘价短期与长期平滑移动平均值的差
    String TEXT_KLINE_INDEX_SET_VALUE_MACD_SHORT_TITLE = "text_kline_index_set_value_macd_short_title";//短期(日)
    String TEXT_KLINE_INDEX_SET_VALUE_MACD_LONG_TITLE = "text_kline_index_set_value_macd_long_title";//长期(日)
    String TEXT_KLINE_INDEX_SET_VALUE_MACD_DEF_TITLE = "text_kline_index_set_value_macd_def_title";//DEF：DIFF的M日平滑移动平均值
    String TEXT_KLINE_INDEX_SET_VALUE_MACD_M_TITLE = "text_kline_index_set_value_macd_m_title";//M(日)
    String TEXT_KLINE_INDEX_SET_VALUE_CCI_PERIOD = "text_kline_index_set_value_cci_period";//周期(日)


    String TEXT_CANG_STATUS_LONG = "text_cang_status_long";//=多
    String TEXT_CANG_STATUS_SHORT = "text_cang_status_short";//=空
    String Text_Unable_To_Change_Positon_Type = "text_unable_to_change_positon_type";//
    String TEXT_ASSET_TRANS_WARN = "text_asset_trans_warn";//在全仓时，资金从合约账户转出，会将您仓位的预估强平价向不利方向变动，请您合理控制仓位的风险
    String TEXT_SELECT_ACCOUNT = "text_select_account";//选择账户
    String TEXT_TRANSFER_BUND_RECORD = "text_transfer_bund_record";//=划转记录
    String TEXT_TRANSFER_BUND_FILTER_ACCOUNT = "text_transfer_bund_filter_account";//=	账户
    String TEXT_TRANSFER_BUND_HINT_TYJ = "text_transfer_bund_hint_tyj";//合约体验金bg_param_1不可转出
    String TEXT_ENTRUST_TYPE = "text_entrust_type";//委托类型
    String ASSETS_RECHARGE_ETH_TIP = "Assets_Recharge_EthTip";//目前不支持区块奖励（Coinbase）和智能合约的转账充值，请您谅解。
    String ASSETS_RECHARGE_ERC20_TIP1 = "Assets_Recharge_Erc20Tip1";//ERC20-USDT不支持区块奖励（Coinbase）的转账充值，请您谅解。
    String ASSETS_RECHARGE_ERC20_TIP2 = "Assets_Recharge_Erc20Tip2";//ERC20-USDT 仅支持 transfer 的方法，使用其他智能合约转账方法的充值暂时无法上账，请您谅解。
    String ASSETS_RECHARGE_OMINI_TIP = "Assets_Recharge_OminiTip";//USDT充值仅支持simple send的方法，使用其他方法（send all）的充币暂时无法上账，请您谅解
    String ASSETS_RECHARGE_XRP_MEMO_TPI = "Assets_Recharge_XrpMemoTip";//通过MEMO方式充值 XRP时，需填写我们提供的数字形式的MEMO标签，非法MEMO充值会导致交易失败，请您谅解
    String ASSETS_RECHARGE_TRC20_TIP = "Assets_Recharge_Trc20Tip";// = TRC20-USDT 仅支持 transfer 的方法，使用其他智能合约转账方法的充值暂时无法上账，请您谅解。


    String TEXT_APPLY_TO_BE_TRACER = "text_apply_to_be_tracer";//申请交易员
    String TEXT_APPLY_TRACER_INTRO = "text_apply_tracer_intro";//开启带单模式，赚10%分润

    String TEXT_SELECT_TO_INPUT = "text_select_to_input";//选填
    String CopyTrade_ApplyTrader_Agreement = "CopyTrade_ApplyTrader_Agreement";//我已仔细阅读并同意
    String TEXT_SUMIT_APPLY = "text_sumit_apply";//提交申请
    String TEXT_APPLY_TRACER_PROTOCAL_NAME = "text_apply_tracer_protocal_name";//《交易员协议》
    String TEXT_APPLY_TRACER_CHECKING = "text_apply_tracer_checking";//信息已提交成功，预计1个工作日内完成审核，请耐心等待我们的工作人员与您联系
    String Assets_AddressAdd_EmailPlaceHolder = "Assets_AddressAdd_EmailPlaceHolder";//请输入邮箱
    String URL_APOLY_TRACER_PROTOCAL = "url_apoly_tracer_protocal";//https://bitget.zendesk.com/hc/zh-cn/articles/360042706211

    String X220622_EXPAND_STR = "x220622_expand_str";//	展开	Expand
    String X220622_PACKUP_STR = "x220622_packup_str";//	收起	Shrink
    String TEXT_TRADE_DATA_TITLE = "text_trade_data_title";//=交易数据
    String TEXT_TRANCER_INFO_CURRENT_FOLLOW = "text_trancer_info_current_follow";//	当前跟随

    String TEXT_TRACE_CONTRACT = "text_trace_contract";//带单合约
    String TEXT_MUST_GOOD_AT_CONTRACTS = "text_must_good_at_contracts";// 最擅长或频繁交易的合约
    String CopyTrade_TraderFilter_SelectTradingPairs = "CopyTrade_TraderFilter_SelectTradingPairs";//选择带单合约，选择带单币对 新词条
    String TEXT_FOLLWER_ONLY_TRACER_SELECT_CONTRACTS = "text_follwer_only_tracer_select_contracts";// 跟随者只能跟随您的带单合约
    String TEXT_NICKNAME_MAX_LEN_CHAR = "text_nickname_max_len_char";// 最大长度为bg_param_1个字符
    String TEXT_FOLLOW_PERSON_TIP_TITLE = "text_follow_person_tip_title";//=跟随人数/最大跟随人数
    String TEXT_FOLLOW_PERSON_TIP_CONTENT = "text_follow_person_tip_content";//=最大可跟随人数受交易员的当前资金情况的影响，已跟随成功的人数不会受到影响


    String TEXT_EMPTY_REMIND = "text_empty_remind";//=空位提醒
    String TEXT_EMPTY_REMIND_CONTENT = "text_empty_remind_content";//=该交易员的带单人数已满员，当交易员非满员状态时发送一次提醒？
    String TEXT_EMPTY_NO_NEED_REMIND = "text_empty_no_need_remind";//=不用了
    String TEXT_EMPTY_NEED_REMIND = "text_empty_need_remind";//=提醒我

    String TEXT_MY_COPY_COUNT = "Follow_Mycopy_Count";//=跟随交易员 bg_param_1人

    String TEXT_MY_COPY_CAPTAL = "text_my_copy_captal";//=跟单本金
    String TEXT_MY_COPY_CAPTAL_REPLACE = "text_my_copy_captal_replace";//=跟单本金
    String TEXT_MY_COPY_PROFIT = "text_my_copy_profit";//=跟单净利润
    String TEXT_MY_COPY_PROFIT_REPLACE = "text_my_copy_profit_replace";//=跟单净利润
    String TEXT_LEVER_SETTING_WARN = "text_lever_setting_warn";//【说明】杠杆倍数为全局设置，与交易员下单杠杠无关。您在此处修改杠杆后，仓位杠杆也会同时修改。
    String TEXT_MORE_POSITION_LEVER = "text_more_position_lever";//
    String TEXT_LESS_POSITION_LEVER = "text_less_position_lever";//
    String TEXT_FOLLOW_ORDER_HIGH_SETTING = "text_follow_order_high_setting";//=高级设置
    String TEXT_FOLLOW_ORDER_SETTING_STOP_LOSS_DESC = "text_follow_order_setting_stop_loss_desc";//=设定的止损比例将应用到您跟随该交易员的每笔订单，如您设置10%，那么当跟随订单亏损超过10%时，系统将以市价平仓该订单。
    String TEXT_FOLLOW_ORDER_SETTING_TAKE_PROFIT_DESC = "text_follow_order_setting_take_profit_desc";//=设定的止盈比例将应用到您跟随该交易员的每笔订单，如您设置50%，那么当跟随订单盈利超过50%时，系统将以市价平仓该订单。
    String TEXT_TRACER_FOLLOW_ORDER_CONTRACT_ING_HINT = "text_tracer_follow_order_contract_ing_hint";//==交易员目前正在进行带单交易的合约
    String TEXT_EMPTY_REMIND_SETTING_SUCCESS_HINT = "text_empty_remind_setting_success_hint";//设置提醒成功

    String CopyTrade_view_TotalIncome = "CopyTrade_view_TotalIncome";// 筛选交易员界面 总收益

    String TEXT_APPLY_TRACE_TIP = "text_apply_trace_tip";//交申请后我们的工作人员会尽快与您取得联系，请按照要求提供相应的申请材料

    String VIEW_NETWORK_ERROR_WITH_CODE = "view_Network_ErrorWithCode";//网络异常，请稍后重试 (bg_param_1)
    String TEXT_ORDER_FINISHED_CONTACT_SERVICE = "text_order_finished_contact_service";//=订单已结束，如有疑问请联系客服
    String Assets_Transfer_BundAccountNotOpenContent = "Assets_Transfer_BundAccountNotOpenContent";//=bg_param_1尚未开通
    String TEXT_TRANSFER_BUND_ACCOUNT_NOT_OPEN_BT = "text_transfer_bund_account_not_open_bt";//=去开通
    String TEXT_TRANSFER_BUND_SUCCESS_HINT = "text_transfer_bund_success_hint";//=资金划转成功
    String TEXT_CHECKED_TYPE = "text_checked_type";//	验证方式
    String TEXT_CHECKED_BY_WANGYI = "text_checked_by_wangyi";//	网易验证
    String X220510_GEETEST_TITLE = "x220510_geetest_title";//极验验证
    String TEXT_CONTRACT_MARKET_PRICE = "text_contract_market_price";// 市场价格
    String Futures_FuturesSetting_MarketPrice = "Futures_FuturesSetting_MarketPrice"; //合约设置-市场价格
    String Future_FuturesSetting_MarkPrice = "Future_FuturesSetting_MarkPrice";// 合约设置-标记价格
    String TEXT_CONTRACT_CAL_PRICE_TYPE_TIP = "text_contract_cal_price_type_tip";//注意：仅用于显示，并不影响保证金率的计算方式，真实盈亏已实际成交为准。

    String TEXT_PRESET_PROFILE_LOSS = "text_preset_profile_loss";//=预设止盈/预设止损


    String TEXT_MODIFY_PLAN_ENTRUST_TITLE = "text_modify_plan_entrust_title";//=修改计划委托
    String TEXT_MODIFY_LOSS_END_TITLE = "text_modify_loss_end_title";//=修改止损
    String TEXT_MODIFY_PROFILE_TAKE_TITLE = "text_modify_profile_take_title";//=修改止盈
    String TEXT_INPUT_TIGGER_PRICE_HINT = "text_input_tigger_price_hint";//=请输入触发价
    String TEXT_ENTRUST_PRICCE_TITLE = "text_entrust_pricce_title";//==委托价格
    String TRADE_OPERATION_INNER_MARKETORDERTIP = "trade_operation_inner_marketordertip";//=市场最优价
    String Futures_TP_PriceNeed = "Futures_TP_PriceNeed";//=止盈价格需要bg_param_1委托价格bg_param_2
    String Futures_SL_PriceNeed = "Futures_SL_PriceNeed";//=止损价格需要bg_param_1委托价格bg_param_2
    String TEXT_INPUT_EXECUTE_PRICE_HINT = "text_input_execute_price_hint";//=请输入执行价
    String TEXT_SIGN = "text_sign";//=标记
    String TEXT_PRESET_PROFILE_LOSS_DIALOG_HINT1 = "text_preset_profile_loss_dialog_hint1";//=预设止盈止损并非实际存在的止盈止损单，需委托成交后才可发起。下单委托成交后，默认将按照您的实际成交数量全部挂止盈止损单。如果您撤销挂单委托，预设止盈止损单将同时失效。
    String TEXT_PRESET_PROFILE_LOSS_DIALOG_HINT2 = "text_preset_profile_loss_dialog_hint2";//=若市场波动剧烈，触发止盈/止损时，实际成交价可能与该时刻的价格存在差异。止盈/止损订单也存在无法成交或无法完全成交的可能。
    String User_Setting_Update_Title = "User_Setting_Update_Title";//=【更新内容】
    String TEXT_APK_UPDATE_BROWSER_DOWN = "text_apk_update_browser_down";//=浏览器下载
    String TEXT_APK_UPDATE_LOAD_LINES_TITLE = "text_apk_update_load_lines_title";//=【推荐浏览器下载线路】
    String TEXT_APK_UPDATE_LOAD_LINES = "text_apk_update_load_lines";//=线路
    String TEXT_APK_UPDATE_LOAD_LINES_BACK_TITLE = "text_apk_update_load_lines_back_title";//=【备用浏览器下载线路】
    String TEXT_APK_UPDATE_LOAD_LINES_BACK = "text_apk_update_load_lines_back";//备用线路
    String TEXT_APK_UPDATE_LOAD_LINES_BACK_HINT = "text_apk_update_load_lines_back_hint";//=备用线路：当推荐线路出现下载速度缓慢或失败，请切换备用线路尝试下载。
    String TEXT_OTC_DETAIL_HINT = "text_otc_detail_hint";//text_otc_detail_hint=为保护资产安全，将对买入资产实行T+1提币和内部转账限制，到期自动解锁；期间不影响交易。

    String TEXT_QRCODE_LOGIN_TITLE = "text_qrcode_login_title";//设备登录确认
    String TEXT_QRCODE_LOGIN_TITLE_INVALID = "text_qrcode_login_title_invalid";//二维码已失效
    String TEXT_QRCODE_LOGIN_HINT = "text_qrcode_login_hint";//您将登录Bitget官网
    String TEXT_QRCODE_LOGIN_HINT_INVALID = "text_qrcode_login_hint_invalid";//请重新扫描登录
    String TEXT_QRCODE_LOGIN_SCAN = "text_qrcode_login_scan";//扫码
    String TEXT_QRCODE_LOGIN_SCAN_NO_KEY = "text_qrcode_login_scan_no_key";//此二维码无效，请扫描bitget官网二维码。

    String STR_OTC_SPEED_ORDER = "str_otc_speed_order";//极速订单

    String STR_OTC_CAN_SALE_VALUE_TIP = "str_otc_can_sale_value_tip";//  为了用户账户资产的安全，24小时内购买的额度限制出售、提币，24小时后系统自动解除限制。
    String STR_ACCOUNT_BALANCE = "str_account_balance";//账户余额

    String Futures_Preset_Profit_Tigger_Price = "Futures_Preset_Profit_Tigger_Price";//=止盈价格需要bg_param_1触发价格bg_param_2
    String Futures_Preset_Loss_Tigger_Price = "Futures_Preset_Loss_Tigger_Price";//=止损价格需要bg_param_1触发价格bg_param_2
    String Futures_Preset_Profit_Excute_Price = "Futures_Preset_Profit_Excute_Price";//=止盈价格需要bg_param_1执行价格bg_param_2
    String Futures_Preset_Loss_Excute_Price = "Futures_Preset_Loss_Excute_Price";//=止损价格需要bg_param_1执行价格bg_param_2
    String TEXT_STOP_PROFILE_LOSS_PART_ENTRUST_HINT = "text_stop_profile_loss_part_entrust_hint";//=	部分成交委托暂时不支持修改预设止盈止损

    String TEXT_DIALOG_ALL_CANCEL_TITLE = "text_dialog_all_cancel_title";//全部撤销

    String Futures_SL_CurrentPriceNeed = "Futures_SL_CurrentPriceNeed";//止损价格需要bg_param_1当前价格bg_param_2
    String Futures_TP_CurrentPriceNeed = "Futures_TP_CurrentPriceNeed";//止盈价格需要bg_param_1当前价格bg_param_2
    String TEXT_PRICE_MUST_ABOVE_ZERO = "text_price_must_above_zero";//价格必须大于0
    String TRADE_MARKET_TITLE = "trade_market_title";//市场


    String TEXT_PWD_CONTAINS_ERRLR_CHAR_STRONG = "text_pwd_contains_errlr_char_strong";//=8-32位，两种以上字母/数字/符号组合，仅支持特殊字符：~`!@#$%^&*()_-+={}[]|;:,<>.?/
    String REGISTER_PLACEHOLDER_PWD_STRONG = "register_placeholder_pwd_strong";//=8-32位，两种以上字母/数字/符号组合
    String TEXT_PWD_CONTAINS_ERRLR_CHAR_STRONG_ONLY = "text_pwd_contains_errlr_char_strong_only";//=特殊字符仅支持：~`!@#$%^&*()_-+={}[]|;:,<>.?/
    String TEXT_MATCH_REG_RULE = "text_match_reg_rule";//=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ~`!@#$%^&*()_-+={}[]|;:,<>.?/


    String TEXT_KLINE_SET_LINE_PLAN = "text_kline_set_line_plan";//计划委托线
    String TEXT_KLINE_SET_LINE_HELP = "text_kline_set_line_help";//辅助线

    String TEXT_KLINE_SET_CHANGE_SYMBOL = "text_kline_set_change_symbol";//手势切换币种
    String TEXT_KLINE_SET_CHANGE_SYMBOL_CONTENT = "text_kline_set_change_symbol_content";//上滑下滑可切换其他合约
    String TEXT_KLINE_SET_CHANGE_INDEX = "text_kline_set_change_index";//点击切换指标
    String TEXT_KLINE_SET_CHANGE_INDEX_CONTENT = "text_kline_set_change_index_content";//点击k线可切换其他指标
    String TEXT_KLINE_SET_SELECTED_SHAKE = "text_kline_set_selected_shake";//震动反馈

    String TEXT_KLINE_SET_CHANGE_INDEX_DIALOG_CONTENT = "text_kline_set_change_index_dialog_content";//因手势冲突，点击k线显示十字线功能暂时关闭

    String TEXT_KLINE_ORDER_EDIT_GUIDE_STEP1_TITLE = "text_kline_order_edit_guide_step1_title";//画线下单(1/4)
    String Markets_Platform_kline_order_edit_guide_step1_content = "text_kline_order_edit_guide_step1_content";//点击“+”添加1条计划委托线
    String TEXT_KLINE_ORDER_EDIT_GUIDE_STEP2_TITLE = "text_kline_order_edit_guide_step2_title";//画线下单(2/4)
    String Markets_Kline_text_kline_order_edit_guide_step2_content = "text_kline_order_edit_guide_step2_content";//上下拖动此线确定触发价
    String TEXT_KLINE_ORDER_EDIT_GUIDE_STEP3_TITLE = "text_kline_order_edit_guide_step3_title";//画线下单(3/4)
    String TEXT_KLINE_ORDER_EDIT_GUIDE_STEP3_CONTENT = "text_kline_order_edit_guide_step3_content";//选择交易方式、数量下单
    String TEXT_KLINE_ORDER_EDIT_GUIDE_STEP4_TITLE = "text_kline_order_edit_guide_step4_title";//画线下单(4/4)
    String Markets_Kline_order_edit_guide_step4_content = "text_kline_order_edit_guide_step4_content";//选中委托单可以在这里快速撤销

    String COIN_COMPLETION_COMPLETE = "coin_completion_complete";//完成
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH = "text_kline_flash_take_order_flash";//闪电
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH_HINT = "text_kline_flash_take_order_flash_hint";//最优追价
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH_BUY_LONG_AVAILABLE = "text_kline_flash_take_order_flash_buy_long_available"; //可开多
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH_BUY_SHORT_AVAILABLE = "text_kline_flash_take_order_flash_buy_short_available"; //可开空
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH_SELL_LONG_AVAILABLE = "text_kline_flash_take_order_flash_sell_long_available"; //可平多
    String TEXT_KLINE_FLASH_TAKE_ORDER_FLASH_SELL_SHORT_AVAILABLE = "text_kline_flash_take_order_flash_sell_short_available"; //可平空
    String TEXT_TAKE_ORDER_CONFIRM_CANG_MULTIPLE = "text_take_order_confirm_cang_multiple"; //仓位杠杆
    String TEXT_TAKE_ORDER_CONFIRM_CONFIRM = "text_take_order_confirm_confirm"; //确定下单

    String TEXT_TAKE_ORDER_CONFIRM_COST = "text_take_order_confirm_cost"; //委托成本

    String TEXT_TAKE_ORDER_CONFIRM_DELETE = "text_take_order_confirm_delete"; //确定删除？
    String TEXT_KLINE_HIDE_DEAL = "text_kline_hide_deal"; //隐藏成交列表
    String TEXT_KLINE_HIDE_LIST = "text_kline_hide_list"; //隐藏列表


    String VERIFIED_TITLE = "verified_title"; //实名认证

    String User_Leverage_Content = "User_Leverage_Content";//ip限制弹框内容

    String TEXT_SELECT_PIC_BY_GALLERY_ERROR = "text_select_pic_by_gallery_error"; //图片路径不可用，请尝试从文件中选择该图片

    String TEXT_NET_LINE_AUTO = "text_net_line_auto"; //自动
    String TEXT_NET_CHAGE_LINE = "text_net_chage_line"; //切换线路

    String TEXT_TUTORIALS_CENTER_TITLE = "text_tutorials_center_title"; // 教程中心
    String TEXT_TUTORIALS_CENTER_TITLE2 = "text_tutorials_center_title2"; // 给您提供最完整的教程服务
    String TEXT_TUTORIALS_CENTER_ACCOUNT = "text_tutorials_center_account"; // 合约教程
    String TEXT_TUTORIALS_CENTER_CONTRACT_DEAL = "text_tutorials_center_contract_deal"; // 合约交易
    String TEXT_TUTORIALS_CENTER_ONEKEY_FOLLOW = "text_tutorials_center_onekey_follow"; // 一键跟单


    String TEXT_OTC_SENDAWAY_COIN = "text_otc_sendaway_coin";//平均放币
    String TEXT_OTC_REGISTER_BEGIN_TIME = "text_otc_register_begin_time";//注册时间

    String TEXT_HOME_PAGE_ALL_PING = "text_home_page_all_ping"; //=全平
    String TEXT_TRANSFER_ASSETS = "text_transfer_assets"; //	划转


    String TEXT_FOLLOW_STEP_TITLE1 = "text_follow_step_title1";//=跟单步骤(1/4)
    String TEXT_FOLLOW_STEP_TITLE2 = "text_follow_step_title2";//=跟单步骤(2/4)
    String TEXT_FOLLOW_STEP_TITLE3 = "text_follow_step_title3";//=跟单步骤(3/4)
    String TEXT_FOLLOW_STEP_TITLE4 = "text_follow_step_title4";//=跟单步骤(4/4)
    String TEXT_FOLLOW_STEP_CONTENT1 = "text_follow_step_content1";//=选择要跟随该交易员的合约
    String TEXT_FOLLOW_STEP_CONTENT2 = "text_follow_step_content2";//=选择杠杆类型；张数或比例
    String TEXT_FOLLOW_STEP_CONTENT3 = "text_follow_step_content3";//=设置止盈止损比例、最大持仓张数
    String TEXT_FOLLOW_STEP_CONTENT4 = "text_follow_step_content4";//=点击”立即跟单”确认信息后，即跟单成功
    String TEXT_FOLLOW_TOTAL_TITLE = "text_follow_total_title";//=汇总
    String TEXT_FOLLOW_SETTING_SURE_CLOSE = "text_follow_setting_sure_close";//	关闭
    String TEXT_CANG_LEVEL_VALUES = "text_cang_level_values";//=指定杠杆倍数

    String TEXT_LEVEL_TYPE_HINT_TITLE = "text_level_type_hint_title";//=杠杆形式说明
    String TEXT_LEVEL_TYPE_HINT_TITLE1 = "text_level_type_hint_title1";//=跟随仓位杠杆：
    String TEXT_LEVEL_TYPE_HINT_CONTENT1 = "text_level_type_hint_content1";//=按照您本人的合约杠杆进行开仓，与交易员的杠杆无关。\n例：您的BTC/USDT合约杠杆为10X，交易员合约杠杆20X，当交易员开仓时您将按照10X跟随；即使您跟随了多个交易员，也是按照10X跟随。
    String TEXT_LEVEL_TYPE_HINT_TITLE2 = "text_level_type_hint_title2";//=按指定杠杆：
    String TEXT_LEVEL_TYPE_HINT_CONTENT2 = "text_level_type_hint_content2";//=当交易员xxx开仓时，您将会按照下面设置的杠杆跟随开仓，当余额不足时将跟随失败。\n例：您跟随交易员A时，设置了BTC/USDT合约以5X跟随，那么交易员A无论以多少倍开仓，您均会以5X跟随。
    String TEXT_LEVEL_TYPE_HINT_TITLE3 = "text_level_type_hint_title3";//=按交易员杠杆：
    String TEXT_LEVEL_TYPE_HINT_CONTENT3 = "text_level_type_hint_content3";//=当跟随多个交易员开仓时，您将会按照交易员当时的杠杆跟随开仓，当余额不足时将跟随失败。\n例：交易员A和B的杠杆分别为10X和20X，当A开仓时您以10X跟随，当B开仓时您以20X跟随。

    String TEXT_FOLLOW_ALERT_TITLE_ALL = "text_follow_alert_title_all";//=汇总说明
    String TEXT_FOLLOW_ALERT_CONTENT1 = "text_follow_alert_content1";//=汇总是将相同交易对的多条带单记录进行了合并，其中的杠杆、开仓均价、收益率均为汇总后的数据。
    String TEXT_FOLLOW_ALERT_CONTENT2 = "text_follow_alert_content2";//=全平代表将指定交易对的多条带单一键平仓
    String TEXT_CONTRACT_ALERT_WAREHOUSE = "text_contract_alert_warehouse";//=	您当前有正在跟随的交易员，切换仓位模式会同步修改跟单的设置项，确定修改吗？

    String FOLLOW_INCOME_ALREADY_DETIAL = "follow_income_already_detial";//累计已分润明细
    String FOLLOW_INCOME_HISTORY = "follow_income_history";//历史分润
    String FOLLOW_INCOME_WILL = "follow_income_will";//待分润
    String FOLLOW_INCOME_RECENTLY_MONTH = "follow_income_recently_month";//以上数据仅展示最近3个月
    String FOLLOW_INCOME_UPDATE_ONEDAY = "follow_income_update_oneday";//数据每日更新一次
    String FOLLOW_INCOME_DETIAL = "follow_income_detial";//分润明细
    String FOLLOW_INCOME_AUTO_FOLLOW = "follow_income_auto_follow";//自动取消跟随
    String COPYTRADE_PERSONALSET_TEXT_AUTO_CANCLE_TIPS = "CopyTrade_PersonalSet_AutoCancleTips";//当交易员连续n日无开仓时自动取消跟随
    String FOLLOW_INCOME_WILL_DETIAL = "follow_income_will_detial";//待分润明细
    String FOLLOW_INCOME_ALREADY = "follow_income_already";//累计已分润
    String FOLLOW_INCOME_HISTORY_DETIAL = "follow_income_history_detial";//历史分润明细

    String FOLLOW_INCOME_TITLE = "follow_income_title";//分润说明
    String FOLLOW_INCOME_CONTENT = "follow_income_content";//分润说明
    String FOLLOW_INCOME_CONTENT_MIX = "follow_income_content_mix";//累计已分润是将带单获得的分润统一折合为USDT进行展示，实际发放分润时是用户跟随时使用的保证金币种。
    String FOLLOW_INCOME_COIN = "follow_income_coin";//分润币种
    String FOLLOW_INCOME_NUMBER = "follow_income_number";//分润数量

    String FOLLOW_INCOMING_CONTENT = "follow_incoming_content";//待分润为动态值，当用户平仓一笔亏损的跟随订单后，待分润可能会减少。
    String FOLLOW_INCOMING_CONTENT_MIX = "follow_incoming_content_mix";//将带单获得的分润统一折合为USDT进行展示；预计待分润为动态值，当用户平仓一笔亏损的跟随订单后，预计待分润可能会减少

    String TEXT_CONTRACT_LEVERAGE_TIP = "text_contract_leverage_tip";//仓位实际杠杠说明
    String TEXT_CONTRACT_LEVERAGE_SHOW_TIP = "text_contract_leverage_show_tip";//仓位实际杠杠=仓位价值/保证金
    String TEXT_CONTRACT_LEVERAGE_SHOW_TIP1 = "text_contract_leverage_show_tip1";//仓位实际杠杠:
    String TEXT_CONTRACT_MARGIN_TIP = "text_contract_margin_tip";//保证金说明
    String TEXT_CONTRACT_MARGIN_SHOW_TIP = "text_contract_margin_show_tip";//保证金=持仓仓位价值/杠杆

    String TEXT_CANG_LEVEL_MAX_HINT = "text_cang_level_max_hint";//=所选合约组合最大杠杆：bg_param_1
    String TEXT_APPLY_TRANCER_SUBMIT_HINT = "text_apply_trancer_submit_hint";//=提交后若出现下列情况将导致审核失败：持有仓位、持有当前委托、持有当前计划、有跟随交易员

    String TEXT_TUTORIALS_CENTER_COIN_DEAL = "text_tutorials_center_coin_deal";//=提交后若出现下列情况将导致审核失败：持有仓位、持有当前委托、持有当前计划、有跟随交易员


    String TEXT_FOLLOW_ORDER_USER_SETTING_NICKNAME_LANGUAGES = "text_follow_order_user_setting_nickname_languages";//=多语言昵称
    String TEXT_FOLLOW_ORDER_USER_SETTING_NICKNAME_LANGUAGES_HINT = "text_follow_order_user_setting_nickname_languages_hint";//=设置不同语言的昵称，以便海外用户更好的记忆、查找
    String TEXT_CHANGE_QR_TITLE = "text_change_qr_title";//	切换二维码
    String TEXT_TRACER_SHARE_MY_ATTENTION = "text_tracer_share_my_attention";//	我正在关注：
    String TEXT_CANG_SHARE_INCOME = "text_cang_share_income";//收益额


    String TEXT_LIMIT_PRICE_ORDER = "text_limit_price_order";//限价委托


    String TEXT_CONTRACT_SET_HINT = "text_contract_set_hint";//编辑后，你可以在交易页看到对应的顺序
    String TEXT_CONTRACT_SET_EDIT = "text_contract_set_edit";//编辑顺序
    String TEXT_CONTRACT_SET_EDIT_TIP = "text_contract_set_edit_tip";//长按拖动可调整排序

    String TEXT_ACCOUNT_AVAILABLE = "text_account_available";//账户可用
    String Futures_FuturesSetting_AvailableInAccount = "Futures_FuturesSetting_AvailableInAccount";// 账户可用
    String TEXT_ACCOUNT_FUNDING_RATE = "text_account_funding_rate";//资金费率
    String Futures_FuturesSetting_FundingRate = "Futures_FuturesSetting_FundingRate";//资金费率

    String TEXT_CONTRACT_DIALOG_UNREALIZED_PROFIT_AND_LOSS_CALCULATION_METHOD = "text_contract_dialog_unrealized_profit_and_loss_calculation_method";//资金费率
    String Futures_FuturesSetting_UnrealizedPnLCalculationMethod = "Futures_FuturesSetting_UnrealizedPnLCalculationMethod";//资金费率
    String TEXT_CONTRACT_MARGIN_TYPE = "text_contract_margin_type";//保证金模式
    String TEXT_CONTRACT_MARGIN_CHANGE_TIP = "text_contract_margin_change_tip";//调整保证金模式仅对当前合约生效
    String BasicServices_Common_dialog_margin_type_is_what = "BasicServices_Common_dialog_margin_type_is_what";//什么是全仓和逐仓模式？
    String Futures_MarginModeAlert_WhatIsMarginMode = "Futures_MarginModeAlert_WhatIsMarginMode"; //什么是全仓和逐仓模式？
    String TEXT_CONTRACT_MARGIN_TYPE_ALL_TITLE = "text_contract_margin_type_all_title";//全仓模式：
    String TEXT_CONTRACT_MARGIN_TYPE_ALL_CONTENT = "text_contract_margin_type_all_content";//所有仓位共用合约账户中的保证金来避免仓位被强平。在强平事件中，交易者可能会损失所有的保证金和仓位
    String TEXT_CONTRACT_MARGIN_TYPE_CHASE_TITLE = "text_contract_margin_type_chase_title";//逐仓模式：
    String TEXT_CONTRACT_MARGIN_TYPE_CHASE_CONTENT = "text_contract_margin_type_chase_content";//一定数量保证金被分配到仓位上。如果仓位保证金亏损到低于维持保证金的水平，仓位将被强平。在逐仓模式下，您可以为这个仓位添加和减少保证金。
    String TEXT_CONTRACT_ADVANCED_SETTINGS_DESCRIPTION = "text_contract_advanced_settings_description";//高级设置说明：
    String TEXT_CONTRACT_ADVANCED_SETTINGS_MARKER_TITLE = "text_contract_advanced_settings_marker_title";//高级设置说明：
    String Futures_EffectiveTimeAlert_PostOnly = "Futures_EffectiveTimeAlert_PostOnly";//高级设置说明：
    String TEXT_CONTRACT_ADVANCED_SETTINGS_MARKER_CONTENT = "text_contract_advanced_settings_marker_content";//高级设置说明：
    String TEXT_CONTRACT_ADVANCED_SETTINGS_IOC_TITLE = "text_contract_advanced_settings_ioc_title";//高级设置说明：
    String Futures_EffectiveTimeAlert_IOC = "Futures_EffectiveTimeAlert_IOC";//高级设置说明 IOC 标题
    String TEXT_CONTRACT_ADVANCED_SETTINGS_IOC_CONTENT = "text_contract_advanced_settings_ioc_content";//高级设置说明：
    String TEXT_CONTRACT_ADVANCED_SETTINGS_FOK_TITLE = "text_contract_advanced_settings_fok_title";//高级设置说明：
    String Futures_EffectiveTimeAlert_FOK = "Futures_EffectiveTimeAlert_FOK";//高级设置说明：FOK 标题
    String TEXT_CONTRACT_ADVANCED_SETTINGS_FOK_CONTENT = "text_contract_advanced_settings_fok_content";//高级设置说明：

    String TEXT_CONTRACT_ORDER_LAYOUT_SET = "text_contract_order_layout_set";//订单布局设置

    String TEXT_CONTRACT_DATA_SET = "text_contract_data_set";//数据设置
    String Futures_FuturesSetting_DataSetting = "Futures_FuturesSetting_DataSetting";
    String TEXT_CONTRACT_SETTING = "text_contract_setting";//合约设置
    String Futures_FuturesSetting_Title = "Futures_FuturesSetting_Title";// 合约设置
    String TEXT_CONTRACT_UNIT_SET = "text_contract_unit_set";//合约单位设置
    String Futures_FuturesSetting_UnitSetting = "Futures_FuturesSetting_UnitSetting";//合约单位设置
    String TEXT_CONTRACT_PERCENT_SET = "text_contract_percent_set";//百分比设置
    String Futures_FuturesSetting_PercentSetting = "Futures_FuturesSetting_PercentSetting";
    String CONTRACT_DEAL_SETTING = "contract_deal_setting";//交易面板设置
    String Futures_FuturesSetting_TradePanelSetting = "Futures_FuturesSetting_TradePanelSetting";

    String CONTRACT_DOWN_ORDER_SET = "contract_down_order_set";//下侧下单
    String Futures_FuturesTradePanelSetting_OrderPanel_OrderDown = "Futures_FuturesTradePanelSetting_OrderPanel_OrderDown";
    String CONTRACT_MORE_SET = "contract_more_set";//多空设置
    String Futures_FuturesSetting_LongShortSetting = "Futures_FuturesSetting_LongShortSetting";
    String CONTRACT_LEFT_ORDER_SET = "contract_left_order_set";//左侧下单
    String Futures_FuturesTradePanelSetting_OrderPanel_OrderLeft = "Futures_FuturesTradePanelSetting_OrderPanel_OrderLeft";//左侧下单
    String CONTRACT_MORE_SPLIT_SCREEN = "contract_more_split_screen";//多空分屏
    String Futures_FuturesTradePanelSetting_OrderPanel_LongShortSplit = "Futures_FuturesTradePanelSetting_OrderPanel_LongShortSplit";//多空分屏
    String CONTRACT_MORE_TOGETHER_SCREEN = "contract_more_together_screen";//多空同屏
    String Futures_FuturesTradePanelSetting_OrderPanel_LongShortTogether = "Futures_FuturesTradePanelSetting_OrderPanel_LongShortTogether"; //多空同屏
    String CONTRACT_RIGHT_ORDER_SET = "contract_right_order_set";//右侧下单
    String Futures_FuturesTradePanelSetting_OrderPanel_OrderRight = "Futures_FuturesTradePanelSetting_OrderPanel_OrderRight";
    String CONTRACT_UP_ORDER_SET = "contract_up_order_set";//上侧下单
    String Futures_FuturesTradePanelSetting_OrderPanel_OrderUp = "Futures_FuturesTradePanelSetting_OrderPanel_OrderUp";//上侧下单
    String CONTRACT_SET_ORDER_CREAT_SET = "contract_set_order_creat_set";//下单操作设置
    String Futures_TradePanelSetting_OrderPanel = "Futures_TradePanelSetting_OrderPanel";//下单操作设置

    String TEXT_CONTRACT_CALCULATOR = "text_contract_calculator";//计算器
    String TEXT_CONTRACT_FEE_HISTORY = "text_contract_fee_history";//费率历史
    String TEXT_CONTRACT_ADVANCED_SETTINGS_MARKER = "text_contract_advanced_settings_marker";//只做maker

    String TRANSAC_ENTRUST_COMMISSION_DISABLE = "transac_entrust_commission_disable";//当前委托不支持隐藏
    String TRANSAC_POSITION_COMMISSION_DISABLE = "transac_position_commission_disable";//持仓不支持隐藏

    String TEXT_DOWNLOAD_APK_TITLE = "text_download_apk_title";//正在下载Bitget新版客户端
    String TEXT_DOWNLOAD_APK_PROGRESS = "text_download_apk_progress";//下载中
    String TEXT_DOWNLOAD_APK_SUCCESS = "text_download_apk_success";//下载完成
    String TEXT_INSTALL_NOW = "text_install_now";//立即安装
    String TEXT_INSTALL_CANCEL = "text_install_cancel";//取消安装
    String TEXT_DOWNLOAD_IN_BACKGROUND = "text_download_in_background";//开始后台下载
    String TEXT_DOWNLOADING_IN_BACKGROUND = "text_downloading_in_background";//开始后台下载

    String TEXT_STICKY_DISPLAY = "text_sticky_display";//置顶显示
    String TEXT_SEND_TIME = "text_send_time";//发送时间
    String APP_COPY_ADDRESS_TAG_HINT = "app_copy_address_tag_hint";// 充值bg_param_1需要地址标签和充币地址同时正确填写，遗漏地址标签将导致资金丢失
    String TEXT_REVERSE_ORDER = "text_reverse_order";//=反手
    String TEXT_REVERSE_ORDER_DIALOG_TITLE = "text_reverse_order_dialog_title";//=反手说明
    String TEXT_REVERSE_ORDER_DIALOG_CANG_AMOUNT = "text_reverse_order_dialog_cang_amount";//=仓位数量
    String TEXT_REVERSE_ORDER_SETTING_DIALOG_TIP = "text_reverse_order_setting_dialog_tip";//=是否包含已委托
    String TEXT_REVERSE_ORDER_SETTING_DIALOG_HINT = "text_reverse_order_setting_dialog_hint";//=数量不包含已委托不会撤销您的已有挂单。
    String TEXT_REVERSE_ORDER_SETTING_DIALOG_HINT1 = "text_reverse_order_setting_dialog_hint1";//=数量包含已委托(不含开仓委托)，优先可用的部分，可用不足会撤销您的已有挂单(先挂先撤)。
    String TEXT_REVERSE_ORDER_SETTING_SUCCESS = "text_reverse_order_setting_success";//=反手设置成功


    String TEXT_CONTRACT_TRADE_SIMPLE = "text_contract_trade_simple";//简洁版
    String TEXT_CONTRACT_TRADE_NORMAL = "text_contract_trade_normal";//专业版
    String TEXT_CONTRACT_SIMP_STOP_PROFILE = "text_contract_simp_stop_profile";//预期收益


    String TEXT_FLOATING_WINDOW_ENTER = "text_floating_window_enter";///	实时盯盘
    String TEXT_FLOATING_WINDOW_SETTING = "text_floating_window_setting";//	悬浮窗设置
    String TEXT_FLOATING_WINDOW_SETTING_SHOW = "text_floating_window_setting_show";//	悬浮窗显示
    String TEXT_FLOATING_WINDOW_SETTING_CLOSE = "text_floating_window_setting_close";//	悬浮窗锁定
    String TEXT_FLOATING_WINDOW_SETTING_ALPHA = "text_floating_window_setting_alpha";//	透明度
    String TEXT_FLOATING_WINDOW_SETTING_COUNT_PAGE = "text_floating_window_setting_count_page";//	每页显示数量
    String TEXT_FLOATING_WINDOW_SETTING_PRICE = "text_floating_window_setting_price";//	显示价格
    String TEXT_FLOATING_WINDOW_SETTING_TEXT_SIZE = "text_floating_window_setting_text_size";//	字体大小
    String TEXT_FLOATING_WINDOW_SETTING_CONTRACT = "text_floating_window_setting_contract";//	行情选择
    String TEXT_FLOATING_WINDOW_SETTING_ALPHA_SETTING = "text_floating_window_setting_alpha_setting";//	设置透明度
    String TEXT_FLOATING_WINDOW_SETTING_BIG = "text_floating_window_setting_big";//	大
    String TEXT_FLOATING_WINDOW_SETTING_MODDLE = "text_floating_window_setting_moddle";//	中
    String TEXT_FLOATING_WINDOW_SETTING_SMALL = "text_floating_window_setting_small";//	小
    String TEXT_FLOATING_WINDOW_SETTING_SHOW_AUTO = "text_floating_window_setting_show_auto";//	自动轮播
    String TEXT_FLOATING_WINDOW_SETTING_CONTRACTED = "text_floating_window_setting_contracted";//已选行情
    String TEXT_FLOATING_WINDOW_SETTING_CONTRACTED_SIZE_UNIT = "text_floating_window_setting_contracted_size_unit";//=个

    String TEXT_HAS_NEW_HOT_FIX_PAC_TO_DOWNLOAD = "text_has_new_hot_fix_pac_to_download";//有新的补丁包，是否下载
    String URL_EXPERIENCE_MONEY_TIP = "url_experience_money_tip";//合约体验金介绍文档
    String TEXT_WILL_INSTALL_NEW_APK = "text_will_install_new_apk";//本次下载将会为您安装一个新的APP，你可以手动卸载旧APP


    String ASSETS_RECHARGE_RECORDS_RECHARGE = "Assets_Recharge_RecordsRecharge";//=充币记录
    String TEXT_RECORDS_WITHDRAW = "text_records_withdraw";//=提币记录
    String TEXT_ASSETS_TOTAL_VALUE = "text_assets_total_value";//=总资产估值(bg_param_1)
    String TEXT_ASSETS_BILLS = "text_assets_bills";//=账单
    String TEXT_ASSETS_OTC_MANAGER = "text_assets_otc_manager";//	OTC
    String TEXT_ASSETS_COIN_BFT_HINT = "text_assets_coin_bft_hint";//=交易手续费BFT支付优惠30%
    String TEXT_COIN_ASSETS_FINACIAL_RECORDS = "text_coin_assets_finacial_records";//=币币财务账单
    String TEXT_OTC_ASSETS_FINACIAL_RECORDS = "text_otc_assets_finacial_records";//=OTC财务账单
    String TEXT_FINACIAL_SELECTED_DATE_TITLE = "text_finacial_selected_date_title";//	选择时间
    String TEXT_FINACIAL_SELECTED_DATE_NEAR_MOUTH = "text_finacial_selected_date_near_mouth";//=最近30天
    String TEXT_FINACIAL_SELECTED_DATE_NEAR_3MOUTH = "text_finacial_selected_date_near_3mouth";//=最近3个月
    String TEXT_SELECT_HOT_COIN_TITLE = "text_select_hot_coin_title";//=热门币种
    String TEXT_ASSETS_WITHDRAW_COUNT = "text_assets_withdraw_count";//提币数量
    String TEXT_ADDRESS_ORDERNARY_TITLE = "text_address_ordernary_title";//=普通地址
    String TEXT_ADDRESS_INNER_TITLE = "text_address_inner_title";//=内部地址
    String TEXT_RECHARGE_ADDRESS_TITLE = "text_recharge_address_title";//=bg_param_1充值地址
    String TEXT_RECHARGE_ADDRESS_TAG_TITLE = "text_recharge_address_tag_title";//=Tag
    String TEXT_CHAIN_TRADE_ID = "text_chain_trade_id";//=区块链交易ID
    String TEXT_RECHARGE_ADDRESS_TYPE = "text_recharge_address_type";//=充币类型
    String TEXT_RECHARGE_ADDRESS_NET = "text_recharge_address_net";//=充币网络
    String TEXT_WITHDRAW_ADDRESS_TYPE = "text_withdraw_address_type";//=提币类型
    String TEXT_WITHDRAW_ADDRESS_NET = "text_withdraw_address_net";//=提币网络
    String TEXT_RECHARGE_RECORD_DETAIL_TITLE = "text_recharge_record_detail_title";//=充币详情
    String TEXT_WITHDRAW_RECORD_DETAIL_TITLE = "text_withdraw_record_detail_title";//=提币详情
    String Assets_Withdraw_CanUsed = "Assets_Withdraw_CanUsed";//=可用:bg_param_1
    String TEXT_WITHDRAW_LIMIT_INFO = "text_withdraw_limit_info";//=限额说明
    String Kyc_Withdraw_LimitTime = "Kyc_Withdraw_LimitTime";//=单次限额(bg_param_1)
    String Kyc_Withdraw_LimitCanUse = "Kyc_Withdraw_LimitCanUse";//=单次可用(bg_param_1)
    String Kyc_Withdraw_LimitDay = "Kyc_Withdraw_LimitDay";//=当日限额(bg_param_1)
    String Kyc_Withdraw_LimitDayCanUse = "Kyc_Withdraw_LimitDayCanUse";//=当日可用(bg_param_1)
    String TEXT_WITHDRAW_ADDRESS_TAG = "text_withdraw_address_tag";//=地址标签
    String TEXT_WITHDRAW_ADDRESS_TAG_HINT = "text_withdraw_address_tag_hint";//=(填写错误可能导致资产丢失)
    String TEXT_ADD_ADDRESS_TITLE = "text_add_address_title";//=新增bg_param_1地址
    String TEXT_CAN_NOT_WITHDRAW_HINT = "text_can_not_withdraw_hint";//	该币种暂不可提币
    String TEXT_ADDRESS_DELETE_TITLE = "text_address_delete_title";//删除地址
    String TEXT_WITHDRAW_DELETE_ADDRESS_HINT = "text_withdraw_delete_address_hint";//确定要删除这个地址吗
    String TEXT_WITHDRAW_ADDRESS_ADD_HINT = "text_withdraw_address_add_hint";//	是否将此地址添加至地址簿
    String SYS_OPERATE_SUCCESS = "sys_operate_success";//操作成功
    String TEXT_FINACIAL_DETAIL_TITLE = "text_finacial_detail_title";//text_finacial_detail_title账单详情
    String TEXT_WITHDRAW_INNER_ACCOUNT_TYPE = "text_withdraw_inner_account_type";//	账号类型
    String TEXT_WITHDRAW_INNER_ACCOUNT_INPUT_UID_HINT = "text_withdraw_inner_account_input_uid_hint";//请输入UID
    String TEXT_UID = "text_uid";//	UID
    String ASSETS_RECHARGE_AUTO_TRANSFER_DESC = "Assets_Recharge_AutoTransferDesc";// 充币后资产自动划转至
    String ASSETS_RECHARGE_AUTO_TRANSFER_TITLE = "Assets_Recharge_AutoTransferTitle";//	资产划转帐户
    String X220902_RECHARGE_AUTO_TRANSFER_INTRODUCTION = "x220902_recharge_auto_transfer_introduction";//	划转说明
    String ASSETS_RECHARGE_ACCOUNT_SETTING_HISTORY = "Assets_Recharge_AccountSettingHistory";//	账户设置历史
    String ASSETS_RECHARGE_AUTO_TRANSFER_TO_ACCOUNT_DESC = "Assets_Recharge_AutoTransferToAccountDesc";//	您的充值到账后，将自动转入选择的账户中。
    String X220902_RECHARGE_ACCOUNT_HISTORYLIST_MAX_TIP = "x220902_recharge_account_historyList_max_tip";//	只展示近20条记录

    String TEXT_SAFE_ITEM_CAN_NOT_USE = "text_safe_item_can_not_use";//=安全项不可用？
    String TEXT_SAFE_ITEM_SETTING_CHOOSE = "text_safe_item_setting_choose";//=选取安全项
    String TEXT_SAFE_ITEM_SETTING_SUBMIT = "text_safe_item_setting_submit";//=提交资料审核
    String TEXT_SAFE_ITEM_SETTING_COMPARE = "text_safe_item_setting_compare";//=申请完成
    String TEXT_SAFE_ITEM_SETTING_CHOOSE_CANT_USE = "text_safe_item_setting_choose_cant_use";//=选取不可用的安全项
    String TEXT_SAFE_ITEM_SETTING_EMAIL_CANT_USE = "text_safe_item_setting_email_cant_use";//=邮箱bg_param_1不可用，申请重置
    String TEXT_SAFE_ITEM_SETTING_PHONE_CANT_USE = "text_safe_item_setting_phone_cant_use";//=手机号bg_param_1不可用，申请重置
    String TEXT_SAFE_ITEM_SETTING_GOOGLE_CANT_USE = "text_safe_item_setting_google_cant_use";//=谷歌验证码不可用，申请解绑
    String TEXT_SAFE_ITEM_SETTING_HINT1 = "text_safe_item_setting_hint1";//=请选择您的丢失的安全验证方式，提交资料后将会在24小时内通过客服审核后，将完成重置操作
    String TEXT_SAFE_ITEM_SETTING_HINT2 = "text_safe_item_setting_hint2";//=勾选两项以上选项时，客服将会与您联系并视频确认，该流程会花费时间较长，请谨慎勾选
    String TEXT_SAFE_ITEM_SETTING_HINT3 = "text_safe_item_setting_hint3";//=重置谷歌验证码
    String TEXT_SAFE_ITEM_SETTING_RESET = "text_safe_item_setting_reset";//=重置安全验证
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBTITLE = "text_safe_item_setting_step_subtitle";//=您正在进行以下操作
    String TEXT_SAFE_ITEM_SETTING_STEP_RESET_PHONE = "text_safe_item_setting_step_reset_phone";//=重置手机号
    String TEXT_SAFE_ITEM_SETTING_STEP_RESET_EMAIL = "text_safe_item_setting_step_reset_email";//=重置邮箱
    String TEXT_SAFE_ITEM_SETTING_STEP_NEW_EMAIL = "text_safe_item_setting_step_new_email";//=新邮箱
    String TEXT_SAFE_ITEM_SETTING_STEP_INPUT_NEW_EMAIL_HINT = "text_safe_item_setting_step_input_new_email_hint";//=请输入新邮箱
    String TEXT_SAFE_ITEM_SETTING_STEP_NEW_EMIAL_CODE = "text_safe_item_setting_step_new_emial_code";//=新邮箱验证码
    String TEXT_SAFE_ITEM_SETTING_STEP_NEW_EMAIL_HINT = "text_safe_item_setting_step_new_email_hint";//=请输入新邮箱验证码
    String TEXT_SAFE_ITEM_SETTING_STEP_PHONE = "text_safe_item_setting_step_phone";//=新手机
    String TEXT_SAFE_ITEM_SETTING_STEP_INPUT_NEW_PHONE_SMS_HINT = "text_safe_item_setting_step_input_new_phone_sms_hint";//=请输入新手机验证码
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_FRONT_IMG = "text_safe_item_setting_step_submit_front_img";//=上传身份证/护照正面
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_BACK_IMG = "text_safe_item_setting_step_submit_back_img";//=上传身份证/护照反面
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_HANDS_IMG = "text_safe_item_setting_step_submit_hands_img";//=上传手持身份证/护照信息页面
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT1 = "text_safe_item_setting_step_submit_img_hint1";//=请上传身份/证护照正面/反面和一张手持身份证护照及手持白纸（bg_param_1）
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT2 = "text_safe_item_setting_step_submit_img_hint2";//=证件上的信息不可进行修改和遮挡，必须看清证件号和姓名
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT3 = "text_safe_item_setting_step_submit_img_hint3";//=图片支持jpg、jpeg、png格式，文件最大支持小于等于2M
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT1_VALUE = "text_safe_item_setting_step_submit_img_hint1_value";//=需写当天申请日期和申请办理业务
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT4 = "text_safe_item_setting_step_submit_img_hint4";//=安全项更改后24小时内禁止提币，充值交易不受影响
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT5 = "text_safe_item_setting_step_submit_img_hint5";//=申请将于1个工作日内处理完毕，请保持手机畅通便于客服及时与您取得联系
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_IMG_HINT6 = "text_safe_item_setting_step_submit_img_hint6";//=申请结果会以短信或邮件的形式发送通知
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_SURE = "text_safe_item_setting_step_submit_sure";//=确定重置
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_COMPARE = "text_safe_item_setting_step_submit_compare";//=提交申请完成
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_COM_SUCCESS_HINT = "text_safe_item_setting_step_submit_com_success_hint";//=申请将于1个工作日处理完毕，申请结果会以短信或邮件形式发送通知
    String TEXT_SAFE_ITEM_SETTING_STEP_INPUT_NEW_PHONE_HINT = "text_safe_item_setting_step_input_new_phone_hint";//=请输入新手机号
    String EXT_SAFE_ITEM_SETTING_STEP_INPUT_NEW_PHONE_CODE_TITLE = "text_safe_item_setting_step_input_new_phone_code_title";//=	新手机验证码
    String TEXT_SAFE_ITEM_SETTING_STEP_EXAMPLE_HINT = "text_safe_item_setting_step_example_hint";//示例
    String TEXT_SAFE_ITEM_SETTING_STEP_ERROR_HINT = "text_safe_item_setting_step_error_hint";//	请至少选择一项
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_FRONT_IMG_HINT = "text_safe_item_setting_step_submit_front_img_hint";//=请上传身份证/护照正面
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_BACK_IMG_HINT = "text_safe_item_setting_step_submit_back_img_hint";//=请上传身份证/护照反面
    String TEXT_SAFE_ITEM_SETTING_STEP_SUBMIT_HANDS_IMG_HINT = "text_safe_item_setting_step_submit_hands_img_hint";//=请上传手持身份证/护照信息页面
    String TEXT_ASSETS_ADDRESS_TAG_HINT = "text_assets_address_tag_hint";//收币地址可能需要填写地址标签（MEMO/TAG）或数字ID或备注。不填或填错将导致资产丢失。（如果是向个人钱包提币，标签可以随意填写）
    String URL_TRADE_FEE_RATE = "url_trade_fee_rate";
    String URL_SHARE_DOWNLOAD_DEFAULT = "url_share_download_default";

    String TEXT_HOME_CHANGE_THEME_GUIDE = "text_home_change_theme_guide";//暗黑模式全新上线啦
    String TEXT_PERSONAL_CHANGE_THEME_GUIDE = "text_personal_change_theme_guide";//点击切换暗黑模式
    String TEXT_CONTRACT_ACCOUNT_HINT = "text_contract_account_hint";//	账户权益包含体验金额度
    String TEXT_RECHARGE_HINT_SURE_BT = "text_recharge_hint_sure_bt";//	我已知晓
    String TEXT_RECHARGE_DIALOG_HINT = "text_recharge_dialog_hint";//	请先勾选“我已知晓”
    String TEXT_PERSONAL_SHARE_APP = "text_personal_share_app";//	分享应用

    String TEXT_DIALOG_AMOUNT_TYPE_USDT_CONTENT_MIX = "text_dialog_amount_type_usdt_content_mix";//	按照金额下单时，将根据额度自动换算成下单比例，最终成交数量以实际显示的数量为准。因行情变动，可能会导致下单失败。
    String TEXT_NO_MORE_ALERT = "text_no_more_alert";//	不再提醒

    String TEXT_QUICK_BUY_COIN_TITLE = "text_quick_buy_coin_title";//	=快捷买币
    String TEXT_QUICK_BUY_COIN_TIP = "text_quick_buy_coin_tip";//=买币从未如此便捷
    String TEXT_QUICK_BUY_COIN_SERVICE_PROVIDER = "text_quick_buy_coin_service_provider";//=服务商
    String TEXT_QUICK_BUY_COIN_BUY_BT = "text_quick_buy_coin_buy_bt";//=购买bg_param_1

    String TEXT_QUICK_BUY_COIN_REFER_PRICE = "text_quick_buy_coin_refer_price";//=参考单价
    String TEXT_QUICK_BUY_COIN_REFER_GET = "text_quick_buy_coin_refer_get";//=预估获得
    String TEXT_QUICK_BUY_COIN_SELECT_SER_DIALOG_TITLE = "text_quick_buy_coin_select_ser_dialog_title";//选择服务商
    String TEXT_QUICK_BUY_COIN_SELECT_SER_DIALOG_HINT = "text_quick_buy_coin_select_ser_dialog_hint";//请选择您交易的服务商
    String TEXT_QUICK_BUY_COIN_JUMP_TO_THIRD_TITLE = "text_quick_buy_coin_jump_to_third_title";//=即将跳转至第三方平台
    String TEXT_QUICK_BUY_COIN_CONNECT_CUSROMER = "text_quick_buy_coin_connect_cusromer";//=联系客服
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_COUNT = "text_quick_buy_coin_sure_dialog_count";//=订单金额(含手续费)
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_ACOUNT = "text_quick_buy_coin_sure_dialog_acount";//=购买账户
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_BUY_COUNT = "text_quick_buy_coin_sure_dialog_buy_count";//=预计买入数量
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_ARRIVE_TIME = "text_quick_buy_coin_sure_dialog_arrive_time";//=预计到账时间
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_TITLE = "text_quick_buy_coin_sure_dialog_title";//	信息确认
    String TEXT_QUICK_OTC_BUY_COIN_SELECTED_AGREE_DEAL = "text_quick_otc_buy_coin_selected_agree_deal";//	请先勾选“我已阅读并同意上述条款”
    String TEXT_TAKE_INFO = "text_take_info";
    String TEXT_FOLLOW_INFO = "text_follow_info";


    String TEXT_MIX_CONTRACT_TITLE_USDT = "text_mix_contract_title_usdt";//U本位合约
    String TEXT_MIX_CONTRACT_TITLE_MIX = "text_mix_new_contract_title_mix";//混合合约
    String TEXT_MIX_CONTRACT_TITLE_USDT_SIMULATE = "text_mix_contract_title_usdt_simulate";//模拟U本位合约
    String TEXT_MIX_CONTRACT_TITLE_MIX_SIMULATE = "text_mix_new_contract_title_mix_sim";//模拟混合合约
    String TEXT_MIX_CONTRACT_TITLE = "text_mix_contract_title";

    String TEXT_FOLLOW_CURRENT_PROFILE_RATE = "text_follow_current_profile_rate";// 当前分润比例
    String TEXT_FOLLOW_PROFILE_RATE = "text_follow_profile_rate";//	分润比例
    String TEXT_FOLLOW_PROFILE_RATE_CONTENT = "text_follow_profile_rate_content";//	*您的分润比例受BFT锁仓数量的影响，详情请查看“了解更多”
    String URL_FOLLOW_SHARE_RATE_MIX = "url_follow_share_rate_mix";//https://bitget.zendesk.com/hc/zh-cn/articles/360060872592 交易员分润新政策公告

    String TEXT_MIX_CANCLE_ALL_ORDERNARY_ENTRUST = "text_mix_cancle_all_ordernary_entrust";//"撤销全部普通委托",
    String TEXT_MIX_CANCLE_ALL_PROFIL_ENTRUST = "text_mix_cancle_all_profil_entrust";//"撤销全部止盈止损",
    String TEXT_MIX_CANCLE_ALL_PLAN_ENTRUST = "text_mix_cancle_all_plan_entrust";//"撤销全部计划委托",
    String Futures_Entrust_ModifyPlanDealHint = "Futures_Entrust_ModifyPlanDealHint";//	当市场价bg_param_1时，将以bg_param_2执行

    String S_MIX_MARGIN_RATE = "s_mix_margin_rate";//	保证金比率
    String S_MIX_FORECAST_REDUCE = "s_mix_forecast_reduce";//	预估减仓价
    String S_ENTRUST_PROFILE_INPUT_RATE_HINT = "s_entrust_profile_input_rate_hint";//请输入收益率
    String Futures_TPSL_PresetProfileLossCalculateResultHint = "Futures_TPSL_PresetProfileLossCalculateResultHint";//预期收益率bg_param_1，当价格bg_param_2时，将以bg_param_3平仓，数量bg_param_4，预计收益bg_param_5
    String S_MIX_CANCEL_ALL_POSITION = "s_mix_cancel_all_position";//全部平仓
    String S_MIX_CANCEL_ALL_POSITION_TIP_UNCONTAINS = "s_mix_cancel_all_position_tip_uncontains";//将以最优价平掉全部可用仓位，不含已委托的平仓单
    String S_MIX_UNACHIEVE_TIP = "s_mix_unachieve_tip";//根据开仓均价计算的未实现盈亏，可在设置处选择标记价格或最新价展示
    String S_MIX_MARGIN_RATIO = "s_mix_margin_ratio";//用来衡量用户仓位风险，当等于100%的时候发生减仓或者爆仓，保证金比率=当前仓位的维持保证金额/(账户权益-逐仓冻结-逐仓未实现盈亏-逐仓仓位保证金)
    String Futures_Operation_MinMargin = "Futures_Operation_MinMargin";//本交易对不支持使用xx作为保证金
    String S_MIX_POS_REST_END_PROFIT_AMOUNT = "s_mix_pos_rest_end_profit_amount";//剩余可止盈数量
    String S_MIX_POS_REST_END_LOSS_AMOUNT = "s_mix_pos_rest_end_loss_amount";//剩余可止损数量
    String S_MIX_ONLY_CURRENT_SYMBOL = "s_mix_only_current_symbol";//只看当前合约
    String Futures_OnlyCurrentContract = "Futures_Main_OnlyCurrentContract";// 只看当前合约
    String S_MIX_CANCLE_ALL_ENTRUST_MARGIN = "s_mix_cancle_all_entrust_margin";//	bg_param_1保证金
    String S_MIX_CANCLE_ALL_ENTRUST_PROFILE = "s_mix_cancle_all_entrust_profile";//	撤销全部止盈
    String S_MIX_CANCLE_ALL_ENTRUST_LOSS = "s_mix_cancle_all_entrust_loss";//	撤销全部止损

    String S_NO_ENTRUST_HINT = "s_no_entrust_hint";//暂无委托可撤销"
    String S_HIS_ENTRUST_CANCLE_SYSTEM = "s_his_entrust_cancle_system";//	系统撤销
    String S_FOLLOW_TYPE_HINT = "s_follow_type_hint";// "可带单/跟单币本位合约、USDT合约",
    String S_SPECIAL_FOLLOW_TYPE_HINT = "s_special_follow_type_hint";// "可带单/跟单混合合约、专业USDT合约",
    String TEXT_MIX_FOLLOW_STEP_CONTENT2 = "text_mix_follow_step_content2";// "选择跟单额度或比例",
    String TEXT_MIX_FOLLOW_STEP_CONTENT3 = "text_mix_follow_step_content3";// "设置止盈止损、最大跟随"
    String TEXT_MY_COPY_FOLLOW = "text_my_copy_follow";//跟随交易员
    String S_CAN_OPEN_AMOUT_RESOURCE = "s_can_open_amout_resource";//可开数量同时受杠杆，可用资金， 仓位档位最大开仓量的限制
    String S_HOLD_POS_AVE_PRICE = "s_hold_pos_ave_price";//持仓均价

    String TEXT_CONTRACT_AMOUNT_TRANS_TIP_CONTENT = "text_contract_amount_trans_tip_content";//按照价格进行计算，仅用于参考，实际数量已成交结果为准
    String TEXT_CONTRACT_CAN_OPEN_TIP_CONTENT = "text_contract_can_open_tip_content";//可开数量同时受杠杠，价格，可用资金，仓位档位最大开仓量的限制
    String TEXT_HOME_COIN_INFO_COUNT = "text_home_coin_info_count";//24h
    String TEXT_CONTRACT_TOKENID_GUIDE_CONTENT = "text_contract_tokenid_guide_content";//点击切换保证金

    String TEXT_ADD_FAVORITE = "text_add_favorite";//添加自选
    String TEXT_DELETE_FAVORITE = "text_delete_favorite";//删除自选
    String T_ENSURE_PLACE_ACTION = "t_ensure_place_action";//确认下单
    String T_PERSONAL_PREFERENCES = "t_personal_preferences";//偏好设置

    String TEXT_MIX_POSITION_BLAST_PRICE_HINT = "text_mix_position_blast_price_hint";//预估减仓（爆仓）价：预计进入减仓/爆仓流程的价格，此价格根据仓位保证金比率计算得出，不代表真实的爆仓价格，仅供参考
    String TEXT_MIX_POSITION_HOLD_NUMBER_HINT = "text_mix_position_hold_number_hint";//持仓量：当前持有的数量
    String TEXT_MIX_POSITION_HOLD_PRICE_HINT = "text_mix_position_hold_price_hint";//持仓均价：持仓的平均价格，可能由多次交易产生

    String CONTRACT_PLAN_TRIGGER_TYPE = "contract_plan_trigger_type";//触发方式
    String T_SECOND_CONFIRM_UNTIP = "t_second_confirm_untip";//不再提示，可在“偏好设置”中再次打开
    String CONTRACT_END_EXPECTED_RATE = "contract_end_expected_rate";//预期收益率
    String T_ACCOUNT_SECURITY = "t_account_security";//提升账户安全
    String T_NOW_CHECK = "t_now_check";//立即查看
    String T_NOW_DEAL = "t_now_deal";//充值即刻开启交易
    String T_NOW_RECHARGE = "t_now_recharge";//立即充值


    String T_EXPERIENCE_GOLD = "t_experience_gold";//体验金
    String T_REST_EXPERIENCE_GOLD = "t_rest_experience_gold";//剩余体验金
    String T_EXPERIENCE_GOLD_TIP = "t_experience_gold_tip";//	（1）体验金可以用于抵扣手续费，资金费率与亏损，可作为开仓保证金使用。/n（2）体验金在抵扣亏损时需满足条件，平仓时的当前权益＜（期初+累计入金+体验金剩余额度）/n（3）任何的转出行为均将导致体验金失效/n（4）如转出导致的体验金失效会引起减仓（爆仓）或开仓委托无法维持，请先处理后再进行操作
    String T_EXPERIENCE_GOLD_TRANS_TIP = "t_experience_gold_trans_tip";//	转出后，体验金将失效，当前账户权益会相应减少。如有持仓，请注意您的风险变化；如体验金失效将导致减仓（爆仓）或开仓委托单无法维持，请处理后再进行操作

    String TEXT_TRANSFER_TO = "text_transfer_to";//至
    String T_FINGER_CHECK_TITLE = "t_finger_check_title";//指纹验证
    String T_FINGER_UNABLE_VER = "t_finger_unable_ver";// 无法识别
    String T_FINGER_VER_SUCCESS = "t_finger_ver_success";// 验证成功


    String THEME_FOLLOW_SYSTEM = "theme_follow_system";//主题跟随系统
    String THEME_FOLLOW_SYSTEM_CHANGE_CONTENT = "theme_follow_system_change_content";//主动切换后，将暂时关闭主题跟随系统功能。可在个人中心页面重新开启主题跟随系统。
    String S_FOREIGN_KYC_IDENTITY_AUTO = "s_foreign_kyc_identity_auto";//活体认证
    String S_FOREIGN_KYC_IDENTITY_MANUAL = "s_foreign_kyc_identity_manual";//人工审核
    String S_FOREIGN_KYC_IDENTITY_PROFILE_KEY = "s_foreign_kyc_identity_profile_key";//继续认证代表您同意bg_param_1
    String S_FOREIGN_KYC_IDENTITY_PROFILE_VALUE = "s_foreign_kyc_identity_profile_value";//<隐私协议>
    String S_FOREIGN_KYC_IDENTITY_DRIVER = "s_foreign_kyc_identity_driver";//	驾驶证
    String TEXT_FOREIGN_KYC_CHECKING_HINT = "text_foreign_kyc_checking_hint";//	您的认证信息提交成功，请等待审核（预计5分钟内完成）

    String TEXT_KCHART_STYLE_TYPE = "text_kchart_style_type";//	风格设置
    String T_CURRENT_FOLLOW_SHOW_MAX = "Follow_TraderDetail_ShowMax";//以上数据仅显示bg_param_1条
    String T_CURRENT_FOLLOW_TOTAL_SHOW_MAX = "Follow_Currentcopy_TotalShowMax";//仅汇总最新的bg_param_1条数据
    String T_GUIDE_JUMP_THIS = "t_guide_jump_this";//	跳过
    String T_GUIDE_EXPERIENCE = "t_guide_experience";//	立即体验
    String T_GUIDE_PAGE1_DES = "t_guide_page1_des";//KCGI\n全球交易盛事\n100BTC 总奖池
    String T_GUIDE_PAGE2_DES = "t_guide_page2_des";//尤文图斯官方伙伴
    String T_GUIDE_PAGE3_DES = "t_guide_page3_des";//PGL Major Official Partner

    String T_ID_CARD_FRONT_PIC = "t_id_card_front_pic";//正面照
    String T_ID_CARD_BACK_PIC = "t_id_card_back_pic";//反面照
    String T_ID_CARD_SELF_PIC = "t_id_card_self_pic";//自拍照
    String T_NEED_SELF_PIC = "t_need_self_pic";//需要拍摄一张清晰的自拍照
    String T_PERSONAL_PREFERENCES_QUANTITY_UNIT = "t_personal_preferences_quantity_unit"; //数量单位
    String T_PERSONAL_PREFERENCES_COST_VALUE = "t_personal_preferences_cost_value"; //成本价值
    String Futures_FuturesUnitSetting_CostValue = "Futures_FuturesUnitSetting_CostValue";
    String T_PERSONAL_PREFERENCES_NOMINAL_VALUE = "t_personal_preferences_nominal_value"; //名义价值
    String Futures_FuturesUnitSetting_NominalValue = "Futures_FuturesUnitSetting_NominalValue";

    String T_TOTAL_ACCOUNT_QUE = "t_total_account_que";//总权益
    String T_TOTAL_ACCOUNT_QUE_DES = "t_total_account_que_des";//展示数据为交易员在专业合约账户的总权益，已将所有币种统一折合为USDT
    String T_TOTAL_ACCOUNT_QUE_OPEN_HINT = "t_total_account_que_open_hint";//开启后您的专业合约总权益将公开显示
    String T_HOLD_POS_AVE_PRICE_TIP = "t_hold_pos_ave_price_tip";//反映跟单或带单的实际盈亏。例如多头仓位，当平仓价低于该持仓均价时，该笔实际情况为亏损。
    String TEXT_MIX_RISK_CONTROL_CLAUSE = "text_mix_risk_control_clause";//风控条款

    String CopyTrade_MixTracerPosition_Tracer_Pl = "CopyTrade_MixTracerPosition_Tracer_Pl";//止盈止损
    String CopyTrade_MixTracerPosition_Tracer_Pl_Tip = "CopyTrade_MixTracerPosition_Tracer_Pl_Tip";//交易员的止盈止损价
    String TEXT_SPOT_SETTING = "text_spot_setting";//现货设置
    String PLEASE_INPUT_TURNOVER = "please_input_turnover";//请输入交易额
    String TEXT_SPOT_OPENING_SOON = "text_spot_opening_soon";//即将开启
    String TEXT_SPOT_OPEN_TRADE = "text_spot_open_trade";//开放交易
    String TEXT_SPOT_OPENING_DESC = "text_spot_opening_desc";//具体信息留意官方公告
    String TEXT_SPOT_HOUR_UNIT = "text_spot_hour_unit";//时
    String TEXT_SPOT_MINUTE_UNIT = "text_spot_minute_unit";//分
    String TEXT_SPOT_SECOND_UNIT = "text_spot_second_unit";//秒
    String TEXT_TRANCER_INFO_TOTAL_DEAL_COUNT = "text_trancer_info_total_deal_count";//总笔数
    String TEXT_TRADERLIST_MODE_SWITCH_GUIDE_DESC = "text_follow_traderlist_mode_switch_guide_desc";//分类视图上线啦，点击这里可以切换其他视图～
    String TEXT_FOLLOW_TRADERLIST_FRESH_SELECT_PROMPT = "text_follow_traderlist_fresh_select_prompt";//	交易员数据每小时更新一次。交易员满足一定条件后才可在列表中显示或被搜索到。
    String URL_FOLLOW_TRADERLIST_FRESH_SELECT_KNOWN_MORE_TIP = "url_follow_traderlist_fresh_select_known_more_tip";//https://bitget.zendesk.com/hc/zh-cn/articles/************

    String TEXT_FOLLOW_VARIETY_PREFERENCE = "text_follow_variety_preference";//品种偏好
    String TEXT_TRADE_VOLUME = "text_trade_volume";//交易量
    String TEXT_TRADE_HOLD_TIME = "text_trade_hold_time";//持仓时间
    String TEXT_TRADE_PROFIT = "text_trade_profit";//盈利
    String TEXT_TRADE_LOSS = "text_trade_loss";//亏损


    String TEXT_TRANCER_HOME_PAGE_INFO_CHART = "text_trancer_home_page_info_chart";//图表
    String Futures_Main_Chart = "Futures_Main_Chart"; //图表

    String TEXT_TRANCER_HOME_PAGE_INFO_DATA = "text_trancer_home_page_info_data";//	数据
    String TEXT_TRACER_HOME_PAGE_ORDER_CHANGE_HINT = "text_tracer_home_page_order_change_hint";//	点击这里查看汇总订单

    //kyc新增----------------------------------
    String T_Manual_Review = "text_manual_review";//切换为人工审核
    String T_Living_Certification = "text_living_certification";//切换为活体认证
    String T_Dont_Have_My_Id_Type = "text_dont_have_my_id_type";//没有我的证件类型？
    String T_Go_To_Manual_Audit = "text_go_to_manual_audit";//去人工审核
    String T_Driver_License_Front_View = "text_driver_license_front_view";//驾驶证正面照
    String T_Reverse_Driver_License = "text_driver_license_reverse_view";//驾驶证反面照
    String T_Portrait_Selfie = "text_portrait_selfie";//人像自拍照
    String T_Please_Upload_Passport_Photo = "text_please_upload_passport_photo";//请上传护照带有身份信息页照片
    String T_Please_Upload_Portrait_Selfie = "text_please_upload_portrait_selfie";//请上传人像自拍照
    String T_Please_Upload_Front_Photo = "text_please_upload_front_photo";//请上传身份证正面照
    String T_Please_Upload_Reverse_Photo = "text_please_upload_reverse_photo";//请上传身份证反面照
    String T_Please_Upload_Front_Driver_License = "text_please_upload_front_driver_license";//请上传驾驶证正面照
    String T_Authentication_Info_Submitted_Successfully = "text_authentication_info_submitted_successfully";//您的认证信息提交成功
    String T_Please_Wait_For_Review = "text_please_wait_for_review";//请等待审核(预计5分钟内完成)
    String T_Hand_Statement_Desc = "text_hand_statement_desc";//包含“Bitget”和当日日期
    String T_Please_Upload_Hold_Passport_And_Statement = "text_please_upload_hold_passport_and_statement";//请上传手持护照及声明
    String T_Please_Upload_Hold_Drive_And_Statement = "text_please_upload_hold_drive_and_statement";//请上传手持证件及声明
    String T_Please_Upload_Hold_Idcard_And_Statement = "text_please_upload_hold_idcard_and_statement";//请上传手持身份证及声明
    String TEXT_NEW_MIX_CONTRACT_TITLE_SIM = "text_new_mix_contract_title_sim";//模拟盘
    String TEXT_MYTRACER_REMOVE_TRADER_WARNING = "text_mytracer_remove_trader_warning";//当前用户不可被移除",

    String TEXT_TRACER_PERSON_SET_LABEL_SETING = "text_tracer_person_set_label_seting";// 标签设置
    String TEXT_BRUST_PRICE = "text_brust_price";// 爆仓价格
    String TEXT_BRUST_PRICE_INFO = "text_brust_price_info";// 此价格为爆仓时的标记价格
    String TEXT_SUPPORT_CHAT_URL = "text_support_chat_url";// https://h5.appwebbg.com/html/pages/app/chat.html
    String TEXT_MIX_HAND_BACK_DESC = "text_mix_hand_back_desc";//	说明：您的仓位将进行市价平仓，并反向开仓相同数量。如资金不足以开仓相同数量，则按照最大可开数量进行开仓。受保证金、行情等因素的影响，您的操作未必100%成功

    String TEXT_TRACER_FILTER_NEARLY_24H = "text_tracer_filter_nearly_24h";// 近24小时
    String TEXT_TRACER_FILTER_NEARLY_7D = "text_tracer_filter_nearly_7d";// 近7天
    String T_ATTENTION_THIS_PLEASE = "t_attention_this_please";//  请注意
    String TEXT_TRACE_PROFILE_RATE_HINT_TITLE = "text_trace_profile_rate_hint_title";// 收益率(净值法)
    String TEXT_TRACE_PROFILE_RATE_HINT_CONTENT = "text_trace_profile_rate_hint_content";// 采用基金行业的净值法计算收益率。
    String TEXT_TRACE_PROFILE_HINT_CONTENT = "text_trace_profile_hint_content";// 历史带单的盈亏总和。
    String TEXT_TRACE_HOLD_TIME_HINT_CONTENT = "text_trace_hold_time_hint_content";// 每笔历史带单持有的时长及盈亏。
    String TEXT_TRACE_TRADE_PREFERENCE_HINT_CONTENT = "text_trace_trade_preference_hint_content";// 历史带单各合约笔数占总笔数的百分比。
    String TEXT_TRACE_TRADE_VOLUME_HINT_CONTENT = "text_trace_trade_volume_hint_content";// 历史带单所有订单的交易量总和(全部折合为USDT)。


    //计划委托------------------------------
    String Text_Buying_Rate = "text_buying_rate";//买入价
    String Text_Selling_Rate = "text_selling_rate";//卖出价
    String Text_Buy_At_Market_Price = "text_buy_at_market_price";//以市价买入
    String Text_Sell_At_Market_Price = "text_sell_at_market_price";//以市价卖出
    String Text_Project_Commission_Statement = "text_project_commission_statement";//当市场最新价达到触发价格时，将按照预先设置的委托价格和数量自动帮您下限价或市价单。计划委托支持限价，市价2种方式，并且触发前，不会冻结资产。
    String Text_Purchases = "text_purchases";//买入量
    String Tetxt_Sell_Quantity = "text_sell_quantity";//卖出量
    String Text_ST_Explain = "text_st_explain";//ST说明
    String view_Explain_Hint = "view_Explain_Hint";//bg_param_1交易对，风险较高，请谨慎投资
    String Url_Spot_Plan_Learn_More = "url_spot_plan_learn_more";//现货计划委托了解更多链接

    String TEXT_KLINE_TRADE_DEPTH = "text_kline_trade_depth";// 交易深度
    String TEXT_CHECK_GOOGLE_FAILED = "text_check_google_failed";//
    String TEXT_HOME_P2P_THIRD_HINT = "text_home_p2p_third_hint";// "Support USDT ，BTC，ETH，ETC",
    String TEXT_HOME_P2P_HINT = "text_home_p2p_hint";// "Trade directly with peers"

    String Text_Please_Select_Chain = "text_please_select_chain";//请选择链
    String ASSETS_RECHARGE_SELECT_CHAIN = "Assets_Recharge_SelectChain";//请选择链
    String App_Copy_Address = "app_copy_address";//复制地址
    String ASSETS_RECHARGE_COPY_ADDRESS = "Assets_Recharge_CopyAddress";//复制地址

    String Text_Address_Verification_Failed = "text_address_verification_failed";//地址校验失败
    String Text_Mismatching = "text_mismatching";//不匹配
    String Text_Suspend_Withdrawal = "text_suspend_withdrawal";//暂停提现
    String Kyc_Withdraw_MinHint = "Kyc_Withdraw_MinHint";//=最小bg_param_1
    String Text_Hints_During_Maintenance = "text_hints_during_maintenance";//详情信息留意官方公告或客服
    String Text_Suspend_Recharge = "text_suspend_recharge";//暂停充币
    String Text_To_Service_Charge = "text_to_bitget_service_charge";//提币至Bitget账户,免手续费
    String Text_Address_Not_Match_The_Selected_Chain = "text_address_not_match_the_selected_chain";//该地址与当前选中的链不匹配
    String Text_Mention_Money_Success = "text_mention_money_success";//提币成功

    String CONTRACT_PLAN_MARKET_PRICE_TIP = "contract_plan_market_price_tip";//市价
    String T_HOME_PAGE_SEE_MORE = "t_home_page_see_more";//查看更多
    String T_HOME_PAGE_FOLLOW_HINT = "t_home_page_follow_hint";//抛开行情、策略、点位，轻松赚钱
    String view_RankingsTitle_TopGainers = "view_RankingsTitle_TopGainers";//涨幅榜
    String T_HOME_PAGE_DEAL_LIST = "t_home_page_deal_list";//成交榜
    String view_RankingsTitle_TopSearches = "view_RankingsTitle_TopSearches";//热搜榜
    String view_RankingsTitle_NewListings = "view_RankingsTitle_NewListings";//新币榜
    String Markets_Home_LiveRankings = "Markets_Home_LiveRankings";//实时榜单
    String Markets_Home_Data = "Markets_Home_Data";//数据
    String T_QUOTES_LAST_PRICE = "t_quotes_last_price";//最新价
    String Markets_view_CoinListSort_LastPrice = "Markets_view_CoinListSort_LastPrice";//最新价
    String T_QUOTES_COIN_VOL = "t_quotes_coin_vol";//币种/交易量
    String Markets_view_CoinListSort_NameVolume = "Markets_view_CoinListSort_NameVolume";//币种/成交额
    String T_MARKET_CHANGES_TITLE = "t_market_changes_title";//行情
    String Markets_view_Recommend_AddFavorites = "Markets_view_Recommend_AddFavorites";//添加到自选
    String TEXT_MARKET_CHANGE_SELECTED_FAVOURITES = "text_market_change_selected_favourites";//请至少选择一个
    String INVITE_FRIENDS_UNLOGIN_REFERRAL = "invite_friends_unlogin_referral";//推荐计划
    String User_PersonCenter_InviteFriends = "User_PersonCenter_InviteFriends";//韩语区下使用这个词条展示推荐计划
    String Market_Change_Trend_5mins_Up = "Market_Change_Trend_5mins_Up";//5分钟涨幅
    String Market_Change_Trend_5mins_Down = "Market_Change_Trend_5mins_Down";//5分钟跌幅
    String Market_Change_Trend_24Hour_Up = "Market_Change_Trend_24Hour_Up";//24 小时新高
    String Market_Change_Trend_24Hour_Down = "Market_Change_Trend_24Hour_Down";//24 小时新低

    String Market_CoinSelect_CoinName = "Market_CoinSelect_CoinName";//名称
    String Market_CoinSelect_MarginList = "Market_CoinSelect_MarginList";//杠杆列表

    String T_ALREADY_ENTRUSTED = "t_already_entrusted";// 已委托

    String T_CAN_CLOSE_TIP = "t_can_close_tip";//可平：可用于平仓的数量，可在设置处选择“平仓是否包含已委托”数量\n选择包含，则可平仓数量=持仓数量，已委托订单可能会被撤销。
    String T_TRADE_CAN_CLOSE_TIP = "t_trade_can_close_tip";//    可平：可用于平仓的数量，可在设置处选择“平仓是否包含已委托"
    String T_ALREADY_ENTRUSTED_CLOSE_AMOUNT = "t_already_entrusted_close_amount";//   已委托平仓的数量
    String view_RankingsTitle_PopularFutures = "view_RankingsTitle_PopularFutures";//"热门合约"
    String T_PWD_PASSWORD = "pwd_password";//密码

    String T_App_Email_Login = "app_email_login";//邮箱登录
    String T_App_Verify_Failed = "app_verify_failed";//人机身份验证失败?
    String App_Verify_Method = "app_verify_method";//切换验证方式
    String App_Phone_Login = "app_phone_login";//手机号登录
    String User_AccountInformation_CountryResidence = "User_AccountInformation_CountryResidence";//居住国家/地区
    String App_Pwd_Find_Email = "app_pwd_find_email";//通过邮箱找回
    String App_Pwd_Find_Phone = "app_pwd_find_phone";//通过手机号找回
    String Text_Reset_Pwd_Tip = "text_reset_pwd_tip";// 重置登录密码后24小时内不允许提币及P2P卖出交易
    String Text_Set_Password = "text_set_password";//设置密码
    String T_Personal_Mobile_Verification = "t_personal_mobile_verification";//手机验证

    String APP_CURRENT_ACCOUNT = "app_current_account";//确认退出当前账号吗？
    String User_PersonalCenter_Welcome = "User_PersonalCenter_Welcome";

    String User_PersonalCenter_Login = "User_PersonalCenter_Login";

    String User_PersonalCenter_Sign_Up = "User_PersonalCenter_Sign_Up";

    String User_PersonalCenter_Welcome_Content = "User_PersonalCenter_Welcome_Content";

    String User_PersonalCenter_My_profile = "User_PersonalCenter_My_profile";

    String User_PersonalCenter_Verify_Your_Identity = "User_PersonalCenter_Verify_Your_Identity";

    String User_PersonalCenter_Verify_Your_Identity_Top = "User_PersonalCenter_Verify_Your_Identity_Top";

    String User_PersonalCenter_Verify_Now = "User_PersonalCenter_Verify_Now";

    String User_PersonalCenter_Rewards = "User_PersonalCenter_Rewards";

    String User_PersonalCenter_Coupons = "User_PersonalCenter_Coupons";

    String User_PersonalCenter_Campaigns = "User_PersonalCenter_Campaigns";

    String User_PersonalCenter_Web3_Wallet = "User_PersonalCenter_Web3_Wallet";

    String User_PersonalCenter_Vip_Service = "User_PersonalCenter_Vip_Service";

    String User_PersonalCenter_P2P_Manager = "User_PersonalCenter_P2P_Manager";

    String User_PersonalCenter_Api_Key = "User_PersonalCenter_Api_Key";

    String User_PersonalCenter_Trading = "User_PersonalCenter_Trading";

    String User_PersonalCenter_Trading_Tip = "User_PersonalCenter_Trading_Tip";

    String User_PersonalCenter_Notifications = "User_PersonalCenter_Notifications";

    String User_PersonalCenter_Get_Up_to = "User_PersonalCenter_Get_Up_to";

    String User_PersonalCenter_When_Invite_Friends = "User_PersonalCenter_When_Invite_Friends";
    String User_PersonalCenter_Invite_Friends_Title = "User_PersonalCenter_Invite_Friends_Title";
    String User_PersonalCenter_Realtime_Tracking = "User_PersonalCenter_Realtime_Tracking";

    String User_PersonalCenter_Invite_Button = "User_PersonalCenter_Invite_Button";

    String User_PersonalCenter_Invite_Reward = "User_PersonalCenter_Invite_Reward";

    String User_PersonalCenter_Invite_Reward_Amount = "User_PersonalCenter_Invite_Reward_Amount";

    String User_PersonalCenter_Your_Referral_Code = "User_PersonalCenter_Your_Referral_Code";

    String User_PersonalCenter_Share = "User_PersonalCenter_Share";

    String User_PersonalCenter_Rate_App = "User_PersonalCenter_Rate_App";

    String User_PersonalCenter_About = "User_PersonalCenter_About";

    String User_PersonalCenter_About_Check_For_Update = "User_PersonalCenter_About_Check_For_Update";

    String User_PersonalCenter_About_Join_Our_Community = "User_PersonalCenter_About_Join_Our_Community";

    String User_PersonalCenter_About_Version = "User_PersonalCenter_About_Version";

    String User_PersonalCenter_About_Terms = "User_PersonalCenter_About_Terms";

    String User_PersonalCenter_About_Policy = "User_PersonalCenter_About_Policy";
    String User_PersonalCenter_Update_NewCode = "User_PersonalCenter_Update_NewCode";
    String User_PersonalCenter_Update_Version = "User_PersonalCenter_Update_Version";

    String User_PersonalCenter_Setting_Display = "User_PersonalCenter_Setting_Display";

    String User_PersonalCenter_Setting_Currency = "User_PersonalCenter_Setting_Currency";

    String User_PersonalCenter_Setting_Quote_Color = "User_PersonalCenter_Setting_Quote_Color";

    String User_PersonalCenter_Setting_Change_Basis = "User_PersonalCenter_Setting_Change_Basis";

    String User_PersonalCenter_Setting_Theme_Light = "User_PersonalCenter_Setting_Theme_Light";

    String User_PersonalCenter_Setting_Theme_Dart = "User_PersonalCenter_Setting_Theme_Dart";

    String User_PersonalCenter_Setting_Theme_Use_System_Tip = "User_PersonalCenter_Setting_Theme_Use_System_Tip";

    String User_PersonalCenter_Edit_Background = "User_PersonalCenter_Edit_Background";

    String User_PersonalCenter_Background_Add = "User_PersonalCenter_Background_Add";

    String User_PersonalCenter_Background_Current = "User_PersonalCenter_Background_Current";

    String User_PersonalCenter_Background_Set_Current = "User_PersonalCenter_Background_Set_Current";

    String User_PersonalCenter_Background_Add_Tip_Title = "User_PersonalCenter_Background_Add_Tip_Title";

    String User_PersonalCenter_Background_Add_Tip_Content = "User_PersonalCenter_Background_Add_Tip_Content";

    String User_PersonalCenter_Background_Delete = "User_PersonalCenter_Background_Delete";

    String User_PersonalCenter_Background_Deleted = "User_PersonalCenter_Background_Deleted";

    String User_PersonalCenter_Background_Delete_Tip_Title = "User_PersonalCenter_Background_Delete_Tip_Title";

    String User_PersonalCenter_Background_Delete_Tip_Content = "User_PersonalCenter_Background_Delete_Tip_Content";

    String User_PersonalCenter_Background_Change = "User_PersonalCenter_Background_Change";

    String User_PersonalCenter_Background_Take_Photo = "User_PersonalCenter_Background_Take_Photo";

    String User_PersonalCenter_Background_Choose_From_Album = "User_PersonalCenter_Background_Choose_From_Album";

    String User_PersonalCenter_Background_Set_Success = "User_PersonalCenter_Background_Set_Success";

    String User_PersonalCenter_Role_Core_User = "User_PersonalCenter_Role_Core_User";

    String User_PersonalCenter_Role_Socialize_Expert = "User_PersonalCenter_Role_Socialize_Expert";

    String User_PersonalCenter_Role_Trade_Expert = "User_PersonalCenter_Role_Trade_Expert";

    String User_PersonalCenter_Campaigns_Launchpad = "User_PersonalCenter_Campaigns_Launchpad";

    String User_PersonalCenter_Campaigns_Launchpool = "User_PersonalCenter_Campaigns_Launchpool";

    String User_PersonalCenter_Campaigns_BGB_Zone = "User_PersonalCenter_Campaigns_BGB_Zone";

    String User_PersonalCenter_Campaigns_BGB_Lotto = "User_PersonalCenter_Campaigns_BGB_Lotto";

    String User_PersonalCenter_Setting_System_theme = "User_PersonalCenter_Setting_System_theme";

    String User_PersonalCenter_Background_Under_Review = "User_PersonalCenter_Background_Under_Review";

    String User_PersonalCenter_Setting_Light_Mode = "User_PersonalCenter_Setting_Light_Mode";

    String User_PersonalCenter_Setting_Dark_Mode = "User_PersonalCenter_Setting_Dark_Mode";

    String User_PersonalCenter_Background_Review_Failed = "User_PersonalCenter_Background_Review_Failed";

    String User_PersonalCenter_Unverified = "User_PersonalCenter_Unverified";

    String User_PersonalCenter_Agent_Affiliate_Project = "User_PersonalCenter_Agent_Affiliate_Project";

    String User_PersonalCenter_Agent_Dashboard = "User_PersonalCenter_Agent_Dashboard";

    String User_PersonalCenter_Agent_Im_Know = "User_PersonalCenter_Agent_Im_Know";

    String User_PersonalCenter_Agent_Look_Data_Change = "User_PersonalCenter_Agent_Look_Data_Change";

    String User_PersonalCenter_Price_Alert = "User_PersonalCenter_Price_Alert";

    String User_PersonalCenter_CandyBomb = "User_PersonalCenter_CandyBomb";

    String User_PersonalCenter_Super_Airdrop = "User_PersonalCenter_Super_Airdrop";

    String PrefSetting_ChangeBasis_Last_24h = "PrefSetting_ChangeBasis_Last_24h";
    String User_Modify_Pwd_Label_Email = "user_modify_pwd_label_email";//邮箱验证
    String App_Pwd_Tip1 = "app_pwd_tip1";//8-32位
    String App_Pwd_Tip2 = "app_pwd_tip2";//两种以上字母/数字/符号组合
    String REWARD_INVITATION_CODE = "reward_invitation_code";//邀请码
    String APP_SYMBOL_TIP = "app_symbol_tip1"; //特殊符号仅支持：~`!@#$%^&*()_-+={}[]|;:,&lt;&gt;.?/
    String U220218_REGISTER_PHONE = "u220218_register_phone";//手机号注册
    String U220222_Account_Msg = "u220222_account_msg";//账号信息
    String U220225_Email_Account_Format_Error = "u220225_email_account_format_error";//邮箱账号格式错误
    String U220225_Regist_Reans_Ronsent = "u220225_regist_means_consent";//注册即代表同意
    String App_Email_Error = "app_email_error";//邮箱账号格式错误
    String USER_BIND_GOOGLE_ERR_DIALOG_MOBILE = "user_bind_google_err_dialog_mobile";//验证码格式不正确
    String U220225_Email_Verification_Code_Err = "u220225_email_verification_code_err";//邮箱验证码错误
    String U220225_Phone_Verification_Code_Err = "u220225_phone_verification_code_err";//手机验证码错误
    String U220225_Google_Verification_Code_Err = "u220225_google_verification_code_err";//google验证码错误
    String User_Modify_Pwd_Label_Mobile = "user_modify_pwd_label_mobile";//短信验证
    String U220215_FOLLOW_REPORT_DONE = "u220215_follow_report_done";//举报
    String U220215_FOLLOW_REPORT_TITLE = "u220215_follow_report_title";//跟单举报
    String U220215_FOLLOW_REPORT_REASION = "u220215_follow_report_reasion";//请选择举报理由
    String U220215_FOLLOW_REPORT_DESCRIPITION = "u220215_follow_report_descripition";//详细描述(选填）
    String U220215_FOLLOW_REPORT_DESCRIPITION_HINT = "u220215_follow_report_descripition_hint";//请不要填写手机号等可能泄漏个人身份的信息
    String U220215_FOLLOW_REPORT_DESCRIPITION_INPUT_ADDRESS = "u220215_follow_report_descripition_input_address";//请输入详细描述
    String U220215_FOLLOW_REPORT_UPLOAD_IMG = "u220215_follow_report_upload_img";//上传凭证(选填）
    String U220215_FOLLOW_REPORT_UPLOAD_IMG_HINT1 = "u220215_follow_report_upload_img_hint1";//请提供能证明违规的截图信息
    String U220215_FOLLOW_REPORT_UPLOAD_IMG_HINT2 = "u220215_follow_report_upload_img_hint2";//仅支持JPEG/PNG,每张大小不超过2M
    String U220215_FOLLOW_REPORT_SUBMIT_HINT = "u220215_follow_report_submit_hint";//提交成功后不可修改，确认提交吗？
    String U220215_FOLLOW_CHANGE_FILTER = "u220215_follow_change_filter";//筛选条件
    String U220215_FOLLOW_CHANGE_FILTER_CLEAN = "u220215_follow_change_filter_clean";//清除全部
    String U220215_FOLLOW_CHANGE_HIDDEN_FULL = "u220215_follow_change_hidden_full";//隐藏满员
    String U220215_FOLLOW_REPORT_1 = "u220215_follow_report_1";//抗单不平
    String U220215_FOLLOW_REPORT_2 = "u220215_follow_report_2";//刷收益率
    String U220215_FOLLOW_REPORT_3 = "u220215_follow_report_3";//小额刷单
    String U220215_FOLLOW_REPORT_4 = "u220215_follow_report_4";//其他
    String U220215_FOLLOW_REPORT_SUBMIT_SUCCESS = "u220215_follow_report_submit_success";//提交成功
    String TEXT_WIN_RATE = "text_win_rate";//近三周胜率
    String U20220226_FOLLOW_DELETE_SEARCH_RECORD_HINT = "u20220226_follow_delete_search_record_hint";//	确定清空搜索历史吗？

    String X220309_COIN_CURRENCY_INFO = "x220309_coin_currency_info";// "币种信息",
    String X220309_COIN_CURRENCY_INFO_CMC_RANKING = "x220309_coin_currency_info_CMC_ranking";// "CMC排行",
    String X220309_COIN_CURRENCY_INFO_MARKET_VALUE = "x220309_coin_currency_info_market_value";// "市值",
    String X220309_COIN_CURRENCY_INFO_CIRCULATE_SUPPLY = "x220309_coin_currency_info_circulate_supply";// "流通供应量",
    String X220309_COIN_CURRENCY_INFO_TOTAL_SUPPLY = "x220309_coin_currency_info_total_supply";// "总供应量",
    String X220318_COIN_INFO_MAXSUPPLY = "x220318_coin_info_maxsupply";// 最大供给量
    String X220309_COIN_CURRENCY_INFO_PUBLIC_DATE = "x220309_coin_currency_info_public_date"; //"发行日期",
    String X220309_COIN_CURRENCY_INFO_BLOCK_CHAIN_BROWSER = "x220309_coin_currency_info_block_chain_browser";// "区块链浏览器",
    String X220624_COMMUNITY_PERSONAL_DESC = "x220624_community_personal_desc";// "简介"
    String X220310_K_LINE_24H_VOL = "x220310_k_line_24h_vol";//	"24h量"

    String X220324_PARENT_CHILD_ACCOUT_PERMISSION_TOAST = "x220324_parent_child_account_permission_toast";

    String X220316_Draw_Exchange_Alert_Tip = "x220316_draw_exchange_alert_tip";//提示：按照相关法律规定，如您向以上交易所提币，需要满足双方均进行kyc否则提币可能会不到账
    String X220316_Draw_Kyc_Tip = "x220316_draw_kyc_tip";//您填写的姓名需要与提币交易所KYC姓名相符合，否则验证可能不会通过
    String X220316_Draw_Exchange_Title = "x220316_draw_exchange_title";//地址类型
    String X220316_Draw_Receive_Name = "x220316_draw_receive_name";//输入收款方姓名
    String X220316_Draw_Exchange_Kyc_Tip = "x220316_draw_exchange_kyc_tip";//向该交易所提币需要进行KYC，请先进行KYC。
    String X220903_HG_CODE_SERVICE_PROVIDER = "x220903_hg_code_service_provider";//Service Provider
    String X220903_HG_CODE_TYPE_OF_ACCOUNT = "x220903_hg_code_type_of_account";//Type of account
    String X220903_HG_CODE_PERSONAL = "x220903_hg_code_personal";//Personal
    String X220903_HG_CODE_CORPORATE = "x220903_hg_code_corporate";//Corporate
    String X220903_HG_CODE_CORPORATE_NAME = "x220903_hg_code_corporate_name";//Corporate Name
    String X220903_HG_CODE_NAME = "x220903_hg_code_name";//Name
    String X220903_HG_CODE_LAST_NAME = "x220903_hg_code_last_name";//Last name
    String X220903_HG_CODE_FIRST_NAME = "x220903_hg_code_first_name";//First name
    String X220903_HG_CODE_SERVICE_PROVIDER_TIP = "x220903_hg_code_service_provider_tip";//按照相关法律规定，如您像以下交易所提币，需要满足双方均进行kyc否则提币可能会不到账
    String X220903_HG_CODE_PERSONAL_NAME_TIP = "x220903_hg_code_personal_name_tip";//您填写的姓名需要与提币交易所KYC通过的姓名相符合，否则验证可能不会通过
    String X220903_HG_CODE_CORPORATE_LEGAL_NAME_TIP = "x220903_hg_code_corporate_legal_name_tip";//您填写的公司法人姓名需要与提币交易所KYC通过的法人姓名相符合，否则验证可能不会通过
    String X220903_HG_CODE_CORPORATE_NAME_TIP = "x220903_hg_code_corporate_name_tip";//您填写的公司名需与提币交易所KYC通过的公司名一致，否则验证可能不会通过
    String X220903_HG_CODE_PLS_SELECT_PERSONAL_OR_CORPORATE = "x220903_hg_code_pls_select_personal_or_corporate";//请选择个人还是公司
    String X220903_HG_CODE_PLS_INPUT_PERSONAL_FIRST_NAME_TOS = "x220903_hg_code_pls_input_personal_first_name_tos";//请填写姓
    String X220903_HG_CODE_PLS_INPUT_PERSONAL_LAST_NAME_TOS = "x220903_hg_code_pls_input_personal_last_name_tos";//请填写名
    String X220903_HG_CODE_PLS_INPUT_CORPORATE_FIRST_NAME_TOS = "x220903_hg_code_pls_input_corporate_first_name_tos"; //请填写法人姓
    String X220903_HG_CODE_PLS_INPUT_CORPORATE_LAST_NAME_TOS = "x220903_hg_code_pls_input_corporate_last_name_tos"; //请填写法人姓
    String X220903_HG_CODE_PLS_INPUT_CORPORATE_NAME_TOS = "x220903_hg_code_pls_input_corporate_name_tos";//请填写公司名称

    String X220304_WITHDRAW_SUCCESS_HINT = "x220304_withdraw_success_hint";//	"提币订单已提交",
    String X220304_WITHDRAW_SUCCESS_DATE = "x220304_withdraw_success_date";//	"申请时间",
    String X220304_WITHDRAW_SUCCESS_BACK_ASSETS = "x220304_withdraw_success_back_assets";//	"返回资产"
    String Futures_Follow_RevertCountHint = "Futures_Follow_RevertCountHint";//	"平仓后，您将以市价反向开仓，数量bg_param_1"
    String X220310_FOLLOW_PING_DIALOG_CONTENT = "x220310_follow_ping_dialog_content";//"该合约存在部分成交的\"当前委托\"订单，需要撤销后才能平仓，确定撤销并平仓吗"
    String X220312_FOLLOW_REVERT_DIALOG_HINT = "x220312_follow_revert_dialog_hint";//  "该合约存在部分成交的\"当前委托\"订单，需要撤销后才能进行反手，确认继续操作吗？"
    String X220315_HUAWEI_MARKET_UPDATE = "x220315_huawei_market_update";//  请打开华为应用市场更新bitget
    String X220317_FOLLOW_REVERT_DIALOG_TITLE = "x220317_follow_revert_dialog_title";//	"带单反手说明"
    String X220317_FOLLOW_REVERT_DIALOG_CONTENT = "x220317_follow_revert_dialog_content";//	"您的仓位将进行市价平仓，并反向开仓相同数量。如资金不足以开仓相同数量，则按照最大可开数量进行开仓。受保证金、行情、单笔最小下单量等因素的影响，您的操作不一定成功。"
    String X220329_T_EFFECTIVE_TIME = "t_effective_time";
    String Futures_EffectiveTime = "Futures_Main_EffectiveTime";
    String X220414_CONTRACT_SELECT_DEPOSIT = "x220414_contract_select_deposit";

    String X220414_TEXT_TAKE_ADD_PROFIT = "x220414_text_take_add_profit";
    String X220414_TEXT_TAKE_ADD_LESS = "x220414_text_take_add_less";
    String X220416_CONTRACT_SETTING_ALL_UNWIND_BUTTON_POSITION_DESCRIPTION = "x220416_contract_setting_all_unwind_button_position_description";
    String Future_FuturesSetting_DisplayedAbovePositions = "Future_FuturesSetting_DisplayedAbovePositions";

    String X220416_NEW_PROFILE_DESC = "x220416_new_profile_desc";//新增止盈说明
    String X220416_NEW_LOSS_DESC = "x220416_new_loss_desc";//新增止损说明
    String X220416_STOP_PROFIT_LOSS_DESC = "x220416_stop_profit_loss_desc";//止盈/止损说明


    String x220225_API_TRADE = "x220225_api_trade";//交易


    String X220412_ASSET_PAGE_DETAIL_OVERVIEW = "x220412_asset_page_detail_overview"; //总览

    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_SPOT_TIP_DISCOUNT = "x220412_asset_page_detail_total_asset_spot_tip_discount";//优惠30%
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_SPOT_TIP = "x220412_asset_page_detail_total_asset_spot_tip"; // 现货手续费交易BGB支付
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_QUE = "x220412_asset_page_detail_total_asset_que";//权益
    String Assets_TotalAssetsPage_Hide_small_price = "Assets_TotalAssetsPage_Hide_small_price";//隐藏{0}余额
    String Assets_Overview_account_hide = "Assets_Overview_account_hide";
    String Assets_Overview_account_account = "Assets_Overview_account_account";
    String ASSETS_OVERVIEW_ACCOUNT_MINE = "Assets_Overview_account_mine";
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_HIDE_SMALL_ASSET = "x220412_asset_page_detail_total_asset_hide_small_asset";//隐藏小额资产
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_HIDE_COUNT = "x220412_asset_page_detail_total_asset_hide_count";//隐藏数量
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_HIDE_INPUT_TIP = "x220412_asset_page_detail_total_asset_hide_input_tip";//请输入小于1BTC隐藏余额
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_USED = "x220412_asset_page_detail_total_asset_used";//已用
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_TOTAL_PRICE = "x220412_asset_page_detail_total_asset_total_price";//总额
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_QUE_TIP = "x220412_asset_page_detail_total_asset_que_tip";//账户权益说明内容

    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_TOTAL_VALUATION = "x220412_asset_page_detail_total_asset_total_valuation";//总资产估值
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_SPOT_VALUATION = "x220412_asset_page_detail_total_asset_spot_valuation";//现货估值
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_FUTURES_VALUATION = "x220412_asset_page_detail_total_asset_futures_valuation";//合约估值
    String X220412_ASSET_PAGE_DETAIL_TOTAL_ASSET_P2P_VALUATION = "x220412_asset_page_detail_total_asset_p2p_valuation";//P2p估值


    String X220317_PLAN_STOP_LINE = "x220317_plan_stop_line";//止盈止损线
    String X220325_ADD_LIMITED_LINE = "x220325_add_limited_line";//新增止盈
    String X220325_ADD_STOP_LINE = "x220325_add_stop_line";//新增止损
    String X220325_POSITION_COUNT_NOTENOUGH = "x220325_position_count_notenough";//已全部平仓,可平数量不足
    String X220325_KLINE_CURRENT_PROFIT = "x220325_kline_current_profit";//当前收益
    String X220406_LIMITEDSTOP_LEAD_TIPS_ONE = "x220406_limitedstop_lead_tips_one";//选中仓位线，上下拖动设置止盈止损
    String X220406_LIMITEDSTOP_LEAD_TIPS_TWO = "x220406_limitedstop_lead_tips_two";//选中止盈止损单，可以在这里快速撤销
    String X220326_KLINE_POSITION_DES = "x220326_kline_position_des";//仓位信息

    String view_RankingsTitle_PopularSpot = "view_RankingsTitle_PopularSpot";//	热门现货
    String X220408_IDENTITY_AUTHENTICATION_BIRTHDAY = "x220408_identity_authentication_birthday";// 出生日期
    String X220408_IDENTITY_AUTHENTICATION_BIRTHDAY_INPUT_PROMPT = "x220408_identity_authentication_birthday_input_prompt";// 请选择出生日期
    String X220513_PWD_ENCRY_ERR_TIP = "x220513_pwd_encry_err_tip";// 请求异常，请联系客服

    String X220429_DIALOG_SOURCE = "x220429_dialog_source";//来源

    String X220505_NOTIFICATION_PUSH_TITLE = "x220505_notification_push_title";//成交通知
    String X220505_NOTIFICATION_PUSH_PERMISSION_TIP = "x220505_notification_push_permission_tip";//请到“设置-Bitget”选项中，允许打开悬浮窗。

    String X220526_CALL_BACK_RATE = "x220526_call_back_rate"; //回调幅度
    String X220526_MODIFY_TRACE_DELEGATE = "x220526_modify_trace_delegate";//修改追踪委托
    String X220526_ALL_CAN_FLAT = "x220526_all_can_flat";//"全部可平"
    String X220526_STOP_PROFIT_LOSS_POSITIONS = "x220526_stop_profit_loss_positions";//""仓位止盈止损""
    String X220526_MOVE_STOP_PROFIT_LOSS = "x220526_move_stop_profit_loss";//"移动止盈止损"
    String X220526_MODIFY_STOP_PROFIT_LOSS_MOVE = "x220526_modify_stop_profit_loss_move";//"修改移动止盈止损"
    String X220526_CANCEL_ALL_TRACKING_ENTRUST = "x220526_cancel_all_tracking_entrust";//"撤销全部追踪委托"
    String X220527_MODIFY_POSITIONS_STOP_PROFIT = "x220527_modify_positions_stop_profit";//"修改仓位止盈// "
    String X220527_MODIFY_POSITIONS_STOP_LOSS = "x220527_modify_positions_stop_loss";//"修改仓位止损失// "
    String X220513_MIX_CONTRACT_RANGE_RATE_TIP = "x220513_mix_contract_range_rate_tip"; //回调幅度说明
    String X220513_MIX_CONTRACT_RANGE_RATE_TIP_CONTENT = "x220513_mix_contract_range_rate_tip_content"; //回调幅度说明内容
    String X220513_MIX_CONTRACT_RANGE_RATE_PERCENT = "x220513_mix_contract_range_rate_percent"; //回调率
    String X220512_PLAN_TRACK = "x220512_plan_track";//追踪委托
    String X220513_TRIGGER_TYPE = "x220513_trigger_type";//"触发类型"
    String X220513_TEXT_MIX_CANCEL_ALL_POSITION_PROFIT = "x220513_text_mix_cancel_all_position_profit";//"撤销全部仓位止盈"
    String X220513_TEXT_MIX_CANCEL_ALL_POSITION_LOSS = "x220513_text_mix_cancel_all_position_loss";//"撤销全部仓位止损"
    String X220513_TEXT_MIX_CANCEL_ALL_MOVE_PROFIT_LOSS = "x220513_text_mix_cancel_all_move_profit_loss";//"撤销全部移动止盈止损"
    String X220513_HAS_SET_POSITION_PROFIT = "x220513_has_set_position_profit";//您当前已存在仓位止盈委托，请不要重复设置。
    String X220513_HAS_SET_POSITION_LOSS = "x220513_has_set_position_loss";//您当前已存在仓位止损委托，请不要重复设置
    String Futures_ProfileLoss_CalculateResultHint = "Futures_ProfileLoss_CalculateResultHint";//当价格bg_param_1时，将以bg_param_2平仓，预计收益bg_param_3bg_param_4
    String X220518_TRACK_RATE_PRICE_HINT1 = "x220518_track_rate_price_hint1"; //修改追踪触发价格  和回调幅度 说明1
    String X220518_TRACK_RATE_PRICE_COMMON_HINT = "x220518_track_rate_price_common_hint";//触发价格  和回调幅度 说明
    String X220518_TRACK_PROFIT_LOSS_HINT1 = "x220518_track_profit_loss_hint1";////修改追踪委托 止盈止损说明
    String X220518_MOVE_RATE_PRICE_HINT1 = "x220518_move_rate_price_hint1";//修改移动止盈止损 触发价格 和回调幅度 说明1
    String X220518_POSITION_PROFIT_LOSS_HINT1 = "x220518_position_profit_loss_hint1"; //移动止盈止损  止盈止损说明
    String X220518_PROFIT_LOSS_COMMON_HINT = "x220518_profit_loss_common_hint"; //止盈止损通用说明
    String X220518_RATE_PRICE_COMMON_HINT = "x220518_rate_price_common_hint"; //触发价格 回调幅度 通用说明
    String X220521_PULLBACK_RANGE_EMPTY_TOAST = "x220521_pullback_range_empty_toast";// 请输入回调幅度
    String Futures_Move_Profit_Loss_SurplusCount = "Futures_Move_Profit_Loss_SurplusCount";// 剩余可设置数量
    String X220521_TAB_PROFIT_LOSS_INTRODUCE_1 = "x220521_tab_profit_loss_introduce_1";// 止盈止损 tab页说明
    String X220521_TAB_PROFIT_LOSS_INTRODUCE_2 = "x220521_tab_profit_loss_introduce_2";// 止盈止损 tab页说明
    String X220521_TAB_PROFIT_LOSS_INTRODUCE_3 = "x220521_tab_profit_loss_introduce_3";// 止盈止损 tab页说明
    String X220521_TAB_PROFIT_LOSS_INTRODUCE_4 = "x220521_tab_profit_loss_introduce_4";// 止盈止损 tab页说明
    String X220521_PROFIT_COUNT_HINT = "x220521_profit_count_hint";// 请输入止盈数量
    String X220521_LOSS_COUNT_HINT = "x220521_loss_count_hint";// 请输入止损数量
    String X220521_PROFIT_LOSS_HINT = "x220521_profit_loss_hint";// 请选择止盈或止损
    String Futures_Profit_SurplusCount = "Futures_Profit_SurplusCount";// 剩余可止盈数量
    String Futures_Loss_SurplusCount = "Futures_Loss_SurplusCount";// 剩余可止损数量
    String APP_TRADE_PRICE = "app_trade_price";// 成交均价
    String TEXT_FOLLOW_ORDER_SETTING_TAKE_PROFIT_HINT = "text_follow_order_setting_take_profit_hint";// 请输入止盈比例
    String TEXT_FOLLOW_ORDER_SETTING_STOP_LOSS_HINT = "text_follow_order_setting_stop_loss_hint";// 请输入止损比例

    String X220530_INPUT_STOP_PROFIT_RATE = "x220530_input_stop_profit_rate";// 请输入止盈幅度
    String X220530_INPUT_STOP_LOSS_RATE = "x220530_input_stop_loss_rate";// 请输入止损幅度

    String X220530_INPUT_TRACK_LOSS_RATE_ZERO_HINT = "x220530_input_track_loss_rate_zero_hint";// 止损输入的收益率必须<0
    String X220530_INPUT_TRACK_PROFIT_RATE_ZERO_HINT = "x220530_input_track_profit_rate_zero_hint";// 止盈输入的收益率必须>0


    String X220425_GTC_DESCRIPTION = "x220425_gtc_description";
    String X220523_LISTING_COIN_NAME = "x220523_listing_coin_name";//待上币种
    String X220523_LISTING_COIN_TIME = "x220523_listing_coin_time";//上线时间
    String X220523_LISTING_COIN_COUNTDOWN = "x220523_listing_coin_countdown";//	开盘倒计时
    String x220521_color_black = "x220521_color_black";// 黑色
    String x220521_color_white = "x220521_color_white";// 白色
    String x220521_theme_follow_app = "x220521_theme_follow_app";// 跟随应用
    String x220521_theme_kline = "x220521_theme_kline";// k线主题

    String X220314_Run_Time = "x220314_run_time";//运行时间
    String X220314_Annual_Yield = "x220314_annual_yield";//年化收益率
    String X220314_Current_Balance = "x220314_current_balance";//当前余额
    String U220226_VIP_FEE_LEVER = "u220226_vip_fee_lever";//费率等级
    String U220226_VIP_FEE_LEVER_VIP = "u220226_vip_fee_lever_vip";//VIP
    String U220224_KLINE_STEP_SET_TITLE = "u220224_kline_step_set_title"; //周期设置
    String U220224_ALREADY_ADD = "u220224_already_add"; //已添加
    String U220224_KLINE_HOUR_TITLE = "u220224_kline_hour_title"; //小时

    String U220224_KLINE_DAY_TITLE = "u220224_kline_day_title"; //天

    String U220225_KLINE_TOAST_5MORE = "u220225_kline_toast_5more"; //天
    String U220225_KLINE_TOAST_5LESS = "u220225_kline_toast_5less"; //至少选择5个
    String U220225_KLINE_STEP_SETDEFAULT = "u220225_kline_step_setdefault"; //恢复默认
    String U220225_MARKET_TEXT_3MIN = "u220225_market_text_3min"; //3分
    String U220225_MARKET_TEXT_8HOUR = "u220225_market_text_8hour"; //8时
    String U220225_MARKET_TEXT_12HOUR = "u220225_market_text_12hour"; //12时
    String U220225_MARKET_TEXT_3DAY = "u220225_market_text_3day"; //3天
    String X220818_BIND_TELEGRAM_HELPER = "x220818_bind_telegram_helper";// telegram助手
    String X220818_BIND_STATUS_BINDING = "x220818_bind_status_binding";// 绑定中
    String Text_Company_Name = "u220215_kyc_company_name";//公司名称
    String Text_Company_Number = "u220215_kyc_company_number";//公司编号
    String Text_Company_Address = "u220215_kyc_company_address";//公司地址
    String TEXT_COMPANY_AUDIT_TIP = "u220215_kyc_company_audit_tip";//机构认证请前往官网
    String U220215_Kyc_Wait_For_Review_Company = "u220215_kyc_wait_for_review_company";//您已成功提交申请资料，我们会在3-5个工作日完成审核。如果通过审核，我们将通过邮件通知您，请耐心等待。
    String X220315_Grid_Cancel_Confirm = "x220315_grid_cancel_confirm";//网格终止确认
    String X220315_Grid_Cancel_Confirm_Tip1 = "x220315_grid_cancel_confirm_tip1";//终止网格后，策略的{0}将以市价单卖出，因市场价格波动可能会产生价差。
    String Text_Transfer_Status = "text_transfer_status";//状态
    String X2205016_Strategy_End_Operate_Yes = "x220516_strategy_end_operate_yes";//是
    String X2205016_Strategy_End_Operate_No = "x220516_strategy_end_operate_no";//否
    String X220401_Strategy_Order_Cancel_Tip = "x220401_strategy_order_cancel_tip";//策略委托单撤销后，策略将会终止
    String X220408_Grid_Cancel_Order_Tip = "x220408_grid_cancel_order_tip";//撤单确认
    String X220520_Current_Position = "x220520_current_position";//当前持仓
    String X220526_Strategy_Grid_Close_Count = "x220526_strategy_grid_close_count";//终止时平仓数量
    String X220526_Strategy_Grid_Close_Price = "x220526_strategy_grid_close_price";//终止时平仓价格
    String X220526_Contract_Grid_Cancel_Confirm_Tip1 = "x220526_contract_grid_cancel_confirm_tip1";//终止网格后，策略的{0}将以市价单平仓，因市场价格波动可能会产生价差。

    String X220610_Strategy_Spot_Grid_Tutorial_Url = "x220610_strategy_spot_grid_tutorial_url";
    String X220610_Strategy_Contract_Grid_Tutorial_Url = "x220610_strategy_contract_grid_tutorial_url";

    String StrategicTrading_Strategy_trade_contract_what_is_init_margin = "StrategicTrading_Strategy_trade_contract_what_is_init_margin";//策略将会使用您合约的资产。创建策略时实际使用的资金取决于市场，可能不等于您输入的数量

    String X220531_MIX_CONTRACT_TITLE_USDC = "x220531_mix_contract_title_usdc";// USDC合约
    String X220531_MIX_CONTRACT_TITLE_USDC_SIMULATE = "x220531_mix_contract_title_usdc_simulate";//	模拟USDC合约

    String StrategicTrading_Strategy_trade_create_strategy = "StrategicTrading_Strategy_trade_create_strategy";//创建策略

    String X220609_TRANCER_FILTER_LOCAL = "x220609_trancer_filter_local";// 本土化
    String X220609_TRANCER_FILTER_LOCAL_HINT = "x220609_trancer_filter_local_hint";// 开启后，与您地区相同的交易员会优先展示
    String X220628_Trans_Fund_Tip = "x220628_trans_fund_tip";//您拥有{0}体验金，划转会使体验金失效

    String X220609_SAFE_LAST_LOGIN_DATE = "x220609_safe_last_login_date";//最后登录日期
    String X220609_SAFE_SELECT_DATE = "x220609_safe_select_date";//选择日期
    String X220615_SAFE_SELECT_COUNTRY = "x220615_safe_select_country";//请选择
    String X220609_SAFE_NO_TRANSACTION = "x220609_safe_no_transaction";//
    String X220611_SAFE_NO_ASSET = "x220611_safe_no_asset";
    String X220614_SAFE_SELECT_DATE_TIP = "x220614_safe_select_date_tip";
    String X220615_SAFE_COIN_LIMIT_MAX = "x220615_safe_coin_limit_max";//最多可以选择{0}个
    String X220615_SAFE_SELECT_LOGIN_DATE = "x220615_safe_select_login_date";//请选择登陆日期
    String X220615_SAFE_SELECT_LOGIN_COUNTRY = "x220615_safe_select_login_country";//请选择登陆国家
    String X220615_SAFE_SELECT_LOGIN_EQUIP = "x220615_safe_select_login_equip";//请选择最后登陆设备
    String X220615_SAFE_SELECT_COIN_TYPE = "x220615_safe_select_coin_type";//请选择币种
    String X220615_SAFE_SELECT_TRANSACTION = "x220615_safe_select_transaction";//请选择是否有交易

    String APP_LEARN_MORE = "app_learn_more";
    String BIND_PHONE = "bind_phone";
    String TEXT_PLEASE_WAIT_FOR_REVIEW_MANUAL = "text_please_wait_for_review_manual";

    String X220523_SMALL_EXCHANGE_BGB = "x220523_small_exchange_bgb";
    String U220630_Upload_Image_Size_Hint = "u220630_upload_image_size_hint";//仅支持PNG、JPG、JPEG格式，每张大小限制在{0}M以内
    String X220705_DEEP_LINK_TIP = "x220705_deep_link_tip"; //该行为将要跳转到其他应用，bitget不能保证其安全性。确认要跳转吗？
    String X220620_Grid_Order_Not_Buy = "x220620_grid_order_not_buy";//未买入
    String X220620_Grid_Order_Not_Sell = "x220620_grid_order_not_sell";//未卖出

    String X220620_Grid_Termination_Buy_Price = "x220620_grid_termination_buy_price";//终止时买入价格
    String X220620_Grid_Termination_Sell_Price = "x220620_grid_termination_sell_price";//终止时卖出价格
    String X220620_Grid_Termination_Buy_Count = "x220620_grid_termination_buy_count";//终止时买入数量
    String X220620_Grid_Termination_Sell_Count = "x220620_grid_termination_sell_count";//终止时卖出数量

    String X220708_Grid_Cancel_Confirm_Tip3 = "x220708_grid_cancel_confirm_tip3";//终止网格后，将以市价买入名义价值{0}的{1}，因市场价格波动可能会产生价差
    String X2207011_TELEGRAM_HINT = "x2207011_telegram_hint"; //请输入Telegram
    String X220718_VERIFY_FAIL_CONTENT = "x220718_verify_fail_content"; //验证失败，是否切换验证方式？
    String X220709_PWD_TIP1 = "x220709_pwd_tip1";//8到32位，至少包含1个数字、1个大写字母和1个{0}
    String X220709_Pwd_Tip2 = "x220709_pwd_tip2";//至少包含1个数字、1个大写字母和1个{0}
    String X220711_Special_Characters = "x220711_special_characters";//特殊字符
    String X220712_App_Symbol_Tip = "x220712_app_symbol_tip";//特殊符号仅支持：\n~`!@#$%^&*()_-+={}[]|;:,<>.?/

    String X220714_COPYTRADING = "x220714_copytrading";//跟单
    String X220718_TRADER_OVERVIEW = "x220718_trader_overview";//图表
    String X220727_Common_Fee_Ws = "x220727_common_fee_ws";//手续费：
    String X220429_DIALOG_SOURCE_WS = "x220429_dialog_source_ws";//来源：
    String X220707_COMMON_AMOUNT_WS = "x220707_common_amount_ws";//数量：
    String X220727_Register_Guide_Experience_Title = "x220727_register_guide_experience_title";//Unlock the Path to a Better Life
    String X220727_Register_Guide_Experience_Desc = "x220727_register_guide_experience_desc";//Begin your seamless crypto trading journey here.
    String X220727_Register_Guide_Experience_Tip1 = "x220727_register_guide_experience_tip1";//I'm New
    String X220727_Register_Guide_Experience_Tip2 = "x220727_register_guide_experience_tip2";//I'm Experienced
    String X220727_Register_Guide_Experience_Hint1 = "x220727_register_guide_experience_hint1";//I have little to no experience trading crypto.
    String X220727_Register_Guide_Experience_Hint2 = "x220727_register_guide_experience_hint2";//I have experience trading stocks and crypto.
    String X220727_Register_Guide_Coin_Title = "x220727_register_guide_coin_title";//Welcome to Bitget
    String X220727_Register_Guide_Coin_Desc = "x220727_register_guide_coin_desc";//Get {0} in rewards for completing tasks on Bitget ——It's easy
    String X220727_Register_Guide_Coin_Tip1 = "x220727_register_guide_coin_tip1";//I don't own crypto
    String X220727_Register_Guide_Coin_Tip2 = "x220727_register_guide_coin_tip2";//I own crypto
    String X220727_Register_Guide_Coin_Hint1 = "x220727_register_guide_coin_hint1";//That's ok! You can use any our fiat gateway partners to buy your first crypto with your credit/debit card!
    String X220727_Register_Guide_Coin_Hint2 = "x220727_register_guide_coin_hint2";//That's great! All you have to do is deposit any crypto asset you own into your bitget account to start trading!
    String X220727_Register_Guide_Start_Title = "x220727_register_guide_start_title";//Start Now
    String X220727_Register_Guide_Coin_Desc_Default = "x220727_register_guide_coin_desc_default";//


    /**************************************红包*****************************/
    String X220611_SEND_DIGITAL_CURRENCY = "x220611_send_digital_currency";//发送数字货币给你的好友
    String X220611_SEND_RED_ENVELOPE = "x220611_send_red_envelope";//我要发红包
    String X220611_AGREE_PRIVACY = "x220611_agree_privacy";//同意Bitget的{0}和{1}
    String X220623_PRIVATE_AGREE = "x220623_private_agree";//隐私协议
    String X220623_RED_PACKET_NUMBER = "x220623_red_packet_number";//个
    String X220611_GET_LUCK = "x220611_get_luck";//拼手气
    String X220611_GENERAL_GIVEAWAYS = "x220611_general_giveaways";//普通红包
    String X220611_GIVEAWAYS_NUMBER = "x220611_giveaways_number";//红包个数
    String X220611_CURRENT_LUCKY = "x220611_current_lucky";//当前为{0}，改为{1}
    String X220611_OPEN_AND_FINISH = "x220611_open_and_finish";//开启红包功能需完成
    String X220611_ACHIEVE_KYC = "x220611_achieve_kyc";
    String X220611_TOTAL_NUMBER = "x220611_total_number";//总金额
    String X220611_GIVE_ENVELOPE = "x220611_give_envelope";//Bitget邀您抢红包
    String X220611_PASSWORD = "x220611_password";//口令

    String X220611_FAKE_NUMBER = "x220611_fake_number";//虚拟抢红包人数
    String X220611_HOW_MANY_PEOPLE = "x220611_how_many_people";//人
    String X220611_ONLY_NEW_USER = "x220611_only_new_user";//仅限新用户领取
    String X220611_PUT_IN_MONEY = "x220611_put_in_money";//塞币进红包
    String X220611_AMOUNT_AVAILBLE = "x220611_amount_availble";
    String X220704_INPUT_AMOUNT = "x220704_input_amount";//请输入金额
    String X220611_USE_IT = "x220611_use_it";//使用
    String X220707_GIVEAWAYS_LEAST_AMOUNT = "x220707_giveaways_least_amount";//红包总金额最低不能为{0}U
    String X220707_USER_LEAST_AMOUNT = "x220707_user_least_amount";//每个用户最低获得要大于{0}U
    String X220611_GIVEAWAYS_WRAPPED = "x220611_giveaways_wrapped";//红包已包好
    String X220611_SHARE_LINKS = "x220611_share_links";//分享红包链接
    String X220611_SHARE_POSTER = "x220611_share_poster";//分享红包海报
    String X220611_LINE_SELECTION = "x220611_line_selection";//线路选择
    String X220709_LINK_NUMBER = "x220709_link_number";//线路{0}
    String X220611_SEND_YOU_GIVEAWAYS = "x220611_send_you_giveaways";//送你一个红包
    String X220611_OPEN_RIGHT_NOW = "x220611_open_right_now";//立即领取
    String X220611_RECEIVE_MULTI_RED_ENVELOPE = "x220611_receive_multi_red_envelope";//您领取了一个多币种红包
    String X220611_CHECK_DETAILS = "x220611_check_details";//查看领取详情
    String X220712_REFRESH_CONTENT = "x220712_refresh_content";//即刻刷新抢红包
    String X220708_REGISTER_GET_BONUS = "x220708_register_get_bonus";//注册Bitget有机会获得 ${0} 奖励
    String X220611_CHANGE_LINE = "x220611_change_line";//切换线路进行分享
    String X220611_SIX_CODE = "x220611_six_code";
    String X220611_INTERNET_SPEED = "x220611_internet_speed";//该功能可能会受到部分网速影响
    String X220713_RED_AGREE_PROTOCOL = "x220713_red_agree_protocol";//请同意相关协议
    String X220715_INPUT_CODE_PLEASE = "x220715_input_code_please";
    String X220611_EXPIRATION_DATE = "x220611_expiration_date";//失效日期
    String X220715_WRONG_FAKE_NUMBER = "x220715_wrong_fake_number";//虚拟人数设置错误
    String X220704_SINGLE_AMOUNT = "x220704_single_amount";
    String TEXT_REWARD_POSTER = "text_reward_poster";
    String X220705_RED_PACKET_COVER = "x220705_red_packet_cover";//红包封面
    String X220816_CONTRACT_MARGIN_TYPE = "x220816_contract_margin_type";//{0}保证金模式
    String Futures_MarginModeAlert_Title = "Futures_MarginModeAlert_Title";// {0}保证金模式
    String X220818_Grid_Mast_Above_Maximum_Ticker_Price_Reverse = "x220818_grid_mast_above_maximum_ticker_price_reverse";//止损价需要大于当前价和网格最高价
    String X220818_Grid_Less_Minimum_Ticker_Price_Reverse = "x220818_grid_less_minimum_ticker_price_reverse";//止盈价需要小于当前价和网格最低价
    String Trade_Detail_Fail_Reason = "trade_detail_fail_reason";//失败原因

    String X220811_FOLLOW_ASSETS_DESC = "x220811_follow_assets_desc";//①跟随所有交易员的已平仓订单的跟随本金",
    String X220811_FOLLOW_REAL_INCOME_DESC = "x220811_follow_real_income_desc";//  "②跟随所有交易员已平仓订单的净利润(已减去开仓、平仓手续费)"


    String X220721_ASSETS_REWARD = "x220721_assets_reward";//reward

    String X220721_ASSETS_REWARD_EXPERIENCE = "x220721_assets_reward_experience";//Experience
    String X220721_ASSETS_REWARD_DEDUCT = "x220721_assets_reward_deduct";//Deduct
    String X220721_ASSETS_REWARD_V_F_REWARDS = "x220721_assets_reward_v_f_rewards";//View Failure Rewards

    String X220721_ASSETS_REWARD_REGULATION = "x220721_assets_reward_regulation";//regulation
    String X220721_ASSETS_REWARD_REGULATION_URL = "x220721_assets_reward_regulation_url";//regulation的url
    String X220721_ASSETS_REWARD_FAILURE_REASON = "x220721_assets_reward_failure_reason";//Failure reason
    String X220721_ASSETS_REWARD_REMAINING = "x220721_assets_reward_remaining";//Remaining

    String X220611_MY_RECEIVED = "x220611_my_received";//已领取
    String X220721_ASSETS_REWARD_RECEIVE = "x220721_assets_reward_receive";//Receive
    String X220721_ASSETS_REWARD_INVALID = "x220721_assets_reward_invalid";//Invalid
    String X220721_ASSETS_REWARD_VALID_UNTIL = "x220721_assets_reward_valid_until";//Valid Until
    String X220721_ASSETS_REWARD_RECEIVE_CONTENT = "x220721_assets_reward_receive_content";//Congratulations on getting {0} {1}
    String X220721_ASSETS_REWARD_PERMANENTLY_VALID = "x220721_assets_reward_permanently_valid";//Permanently valid
    String X220721_ASSETS_REWARD_ASSET_VALUE = "x220721_assets_reward_asset_value";//Reward assetvalue
    String X220721_ASSETS_REWARD_RECEIVED = "x220721_assets_reward_received";//received

    String X220822_P2P_DEPOSIT = "x220822_p2p_deposit";

    String Assets_Page_Strategy_name = "Assets_Page_Strategy_name";//策略
    String X220622_ASSET_PAGE_SWAP_NAME = "x220622_asset_page_swap_name";//swap
    String X220622_ASSET_PAGE_SWAP_ASSET_NAME = "x220622_asset_page_swap_asset_name";//swap
    String X220624_SWAP_WITHDRAW = "x220624_swap_withdraw";//swap提币
    String X220523_TEXT_EXCHANGE = "x220523_text_exchange";//兑换
    String CURRENCY_RECORD_FINANCE_STATUS = "text_transfer_status";//状态
    String X220329_P2P_MAX = "x220329_p2p_max";//最大
    String X220624_SWAP_SLIDING_POINT = "x220624_swap_sliding_point";//滑点
    String X220624_SWAP_BEST_PRICE = "x220624_swap_best_price";//最优价格
    String X220624_SWAP_PRICE_EFFECT = "x220624_swap_price_effect";//价格影响
    String X220624_SWAP_ACCOUNT_TIME = "x220624_swap_account_time";//到账时间
    String X220624_SWAP_INSTANT_ACCOUNT = "x220624_swap_instant_account";//即时到账
    String X220624_SWAP_WHAT_IS_SLIDING_POINT = "x220624_swap_what_is_sliding_point";//什么是滑点
    String X220624_SWAP_SLIDING_POINT_DESC = "x220624_swap_sliding_point_desc";//滑点描述
    String X220624_SWAP_WHAT_IS_PRICE_EFFECT = "x220624_swap_what_is_price_effect";//什么是价格影响
    String X220624_SWAP_PRICE_EFFECT_DESC = "x220624_swap_price_effect_desc";//价格影响描述
    String X220624_SWAP_WHAT_IS_ACCOUNT_TIME = "x220624_swap_what_is_account_time";//什么是到账时间
    String X220624_SWAP_ACCOUNT_TIME_DESC = "x220624_swap_account_time_desc";//到账时间描述
    String X220624_SWAP_WHAT_IS_FEE = "x220624_swap_what_is_fee";//什么是手续费
    String X220624_SWAP_FEE_DESC = "x220624_swap_fee_desc";//手续费描述
    String X220624_SWAP_ORDER_ALREADY_SUBMIT = "x220624_swap_order_already_submit";//订单已提交
    String X220624_SWAP_ORDER_SUBMIT_DESC = "x220624_swap_order_submit_desc";//正在等待区块确认，您可以到订单页面查看详情
    String T_NOTICE_VIEW_DETAIL = "t_notice_view_detail";//查看详情
    String X220624_SWAP_TITLE_HISTORY = "x220624_swap_title_history";//兑换历史
    String X220624_SWAP_HISTORY_PRICE = "x220624_swap_history_price";//兑换价格
    String TEXT_OTC_UPDATE_TIME = "text_otc_update_time";//更新时间
    String X220624_SWAP_CUSTOM_SLIPPAGE = "x220624_swap_custom_slippage";//自定义滑点
    String X220624_SWAP_AUTO_SLIPPAGE = "x220624_swap_auto_slippage";//自动滑点
    String X220624_SWAP_AUTO_SLIPPAGE_DESC = "x220624_swap_auto_slippage_desc";//自动滑点
    String X220624_SWAP_SELF_SLIPPAGE = "x220624_swap_self_slippage";//手动滑点
    String X220624_SWAP_SELF_SLIPPAGE_VALUE_DESC = "x220624_swap_self_slippage_value_desc";//手动输入滑点值，最小2%，最大100%
    String X220624_SWAP_SELF_SLIPPAGE_VALUE_WARNING = "x220624_swap_self_slippage_value_warning";//当前滑点过低，容易导致兑换失败
    String X220624_SWAP_SELECT_BUY_COIN = "x220624_swap_select_buy_coin";//选择买入币种
    String X220624_SWAP_SELECT_SELL_COIN = "x220624_swap_select_sell_coin";//选择卖出币种
    String X220624_SWAP_SELECT_COIN_SEARCH_HINT = "x220624_swap_select_coin_search_hint";//搜索名称或者输入合约地址
    String X220624_SWAP_SUBMIT_SURE = "x220624_swap_submit_sure";//兑换确定
    String X220624_SWAP_SUBMIT_MARKET_DESC = "x220624_swap_submit_market_desc";//即将通过 bg_param_1 交易市场进行兑换，到账时间取决于当前区块的网络速度
    String X220723_SWAP_PROTOCOL = "x220723_swap_protocol";//swap使用协议
    String SUCESS = "sucess";//成功
    String X220723_COMMON_FAIL = "x220723_common_fail";//失败
    String X220523_EXCHANGING = "x220523_exchanging";//兑换中
    String X220723_SWAP_SLIDING_POINT_HINT = "x220723_swap_sliding_point_hint";//最小值不能小于{0}
    String X220723_SWAP_FINDING_BEST_CHAIN = "x220723_swap_finding_best_chain";//正在寻找最佳通道
    String X220523_EXCHANGE_SUCCESS = "x220523_exchange_success";//兑换成功
    String X220723_SWAP_EXCHANGE_FAIL = "x220723_swap_exchange_fail";//兑换失败
    String X220723_SWAP_WITHDRAW_NORMAL = "x220723_swap_withdraw_normal";//普通&内部
    String X220723_SWAP_SLIDING_INPUT_HINT = "x220723_swap_sliding_input_hint";//请输入滑点
    String X220723_SWAP_EXCHANGE_SEARCHFAIL = "x220723_swap_exchange_searchfail";//寻找通道失败
    String X220723_SWAP_NOT_SUPORT_CROSS_CHAIN = "x220723_swap_not_suport_cross_chain";//暂不支持跨链兑换
    String LOGIN_TITLE = "login_title";//登录
    String X220723_SWAP_YOU_GET = "x220723_swap_you_get";//您将收到
    String X220723_SWAP_CONFIRM_ORDER = "x220723_swap_confirm_order";//确认订单
    String X220723_SWAP_TRANSFER_NET = "x220723_swap_transfer_net";//转账网络
    String X220723_SWAP_WITHDRAW_ACCOUNT = "x220723_swap_withdraw_account";//提现账户
    String X220723_SWAP_WITHDRAW_MONEY = "x220723_swap_withdraw_money";//提现总额
    String X220723_SWAP_WITHDRAW_FEE = "x220723_swap_withdraw_fee";//网络手续费
    String X220723_SWAP_WITHDRAW_DESC = "x220723_swap_withdraw_desc";//请确保您输入了正确的提币地址并且您选择的转账网络与地址相匹配。
    String X220723_SWAP_ADDRESS_ERROR = "select_chain.address_error";//地址校驗失敗
    String X220723_SWAP_ACCOUNT = "x220723_swap_account"; //swap账户
    String BALANCE_AVAILABLE_NOT_ENOUGH = "balance_available_not_enough"; //资产可用不足

    String X220623_FUND_POOL = "x220623_fund_pool"; // 资金池
    String X220623_FUND_POOL_CHANGE = "x220623_fund_pool_change"; //资金池变化
    String X220623_NUMBER_COIN = "x220623_number_coin";
    String X220623_USER = "x220623_user";
    String X220623_REDUCE = "x220623_reduce";
    String X220623_INCREASE = "x220623_increase";


    String X220624_24H_HIGH = "x220624_24h_high"; // 24H高
    String X220624_24H_LOW = "x220624_24h_low";//"24H低"


    String X220628_GREAT_WHALE = "x220628_great_whale";//巨鲸
    String X220628_GREAT_WHALE_BUY = "x220628_great_whale_buy";//巨鲸买入
    String X220628_GREAT_WHALE_SELL = "x220628_great_whale_sell";//巨鲸卖出
    String X220628_INCREAS_NUMBER = "x220628_increas_number";//增加量
    String X220628_MID_WHALE = "x220628_mid_whale";//中鲸
    String X220628_MID_WHALE_BUY = "x220628_mid_whale_buy";//中鲸买入
    String X220628_MID_WHALE_SELL = "x220628_mid_whale_sell";//中鲸卖出
    String X220628_REDUCE_NUMBER = "x220628_reduce_number";//减少量:
    String X220628_SMALL_ORDER = "x220628_small_order";//小单
    String X220628_SMALL_ORDER_BUY = "x220628_small_order_buy";//小单买入
    String X220628_SMALL_ORDER_SELL = "x220628_small_order_sell";//小单卖出
    String X220623_COIN_DES = "x220623_coin_des";//币信息
    String X220623_RESOURCE = "x220623_resource";//资源
    String X220623_INTRODUCE = "x220623_introduce";//介绍
    String X220623_ABOUNT_SOME = "x220623_abount_some";//关于
    String X220623_OPEN_ALLDES = "x220623_open_alldes";//查看全部
    String X220628_24H_TRADE_ADDRESS = "x220628_24h_trade_address";//24h 交易地址数"
    String X220628_BUY_ADDRESS_NUMBER = "x220628_buy_address_number";//买入地址数
    String X220628_SELL_ADDRESS_NUMBER = "x220628_sell_address_number";//卖出地址数
    String X220623_VOLUE_PRICE = "x220623_volue_price";//卖出地址数
    String X220225_API_TRADE = "x220225_api_trade";//交易

    String X220628_24H_FUNDS_DATA = "x220628_24h_funds_data";//24h 资金数据分析

    String X220628_24H_FLOW_OF_FUNDS = "x220628_24h_flow_of_funds";//24h 资金流向分析
    String X220623_OFFICIAL_WEBSITE = "x220623_official_website";//官网
    String X220723_SWAP_WITHDRAW_PROCESSING = "x220723_swap_withdraw_processing";//提币中
    String X220723_SWAP_WITHDRAW_REVOKE = "x220723_swap_withdraw_revoke";//已撤销
    String X220723_SWAP_DETAILS = "x220723_swap_details";//查看详情
    String X220723_SWAP_FEE = "x220723_swap_fee";//Gas Fee

    String X220917_ASSETS_FINANCIAL = "x220917_assets_financial";//理财
    String X220917_ASSETS_FINANCIAL_TOTAL = "x220917_assets_financial_total";//理财总资产估值
    String X220917_ASSETS_FINANCIAL_EARN = "x220917_assets_financial_earn";//理财总资产估值
    String X220920_ASSETS_ABOUT = "x220920_assets_about";//资产大约总额
    String X220920_ASSETS_INCOME_YESTERDAY = "x220920_assets_income_yesterday";//昨日收益
    String X220920_ASSETS_PROFIT_TOTAL = "x220920_assets_profit_total";//累计利润(含估算)
    String X220920_ASSETS_INCOME_TOTAL = "x220920_assets_income_total";//累计收益
    String X220920_ASSETS_EARN_COIN = "x220920_assets_earn_coin";//立即赚币
    String X220920_ASSETS_FINANCIAL_CURRENT = "x220920_assets_financial_current";//活期
    String X220920_ASSETS_FINANCIAL_REFERENCE = "x220920_assets_financial_reference";//参考年华
    String X220920_ASSETS_CURRENT_HOLD = "x220920_assets_current_hold";//当前持有
    String X220920_ASSETS_RATE_COIN = "x220920_assets_rate_coin";//利息币种
    String X220920_ASSETS_JACKPOT = "x220920_assets_jackpot";//奖池
    String X220920_ASSETS_PLEDGE_COIN = "x220920_assets_pledge_coin";//质押币种
    String X220920_ASSETS_PLEDGE_TOTAL = "x220920_assets_pledge_total";//质押总额
    String X220920_ASSETS_INCOME_HOUR = "x220920_assets_income_hour";//前小时收益
    String X220920_ASSETS_FINANCIAL_COMMON = "x220920_assets_financial_common";//优选理财
    String X220920_ASSETS_FINANCIAL_TIME = "x220920_assets_financial_time";//期限
    String X220920_ASSETS_FINANCIAL_REGULAR = "x220920_assets_financial_regular";//定期
    String X220920_FINANCIAL_KEY_SAVINGS = "x220920_financial_key_savings";//理财宝
    String X220920_FINANCIAL_KEY_LAUNCHPOOL = "x220920_financial_key_launchpool";//LaunchPool
    String X220920_FINANCIAL_KEY_BGBEARN = "x220920_financial_key_bgbearn";//BgbEarn
    String X220923_FINANCIAL_APY_TITLE = "x220923_financial_apy_title";//收益等级说明
    String X220923_FINANCIAL_APY_SUBTITLE = "x220923_financial_apy_subtitle";//产品收益根据以下阶梯式规则计算:

    String X220831_Please_Send_Ver_Code = "x220831_please_send_ver_code";//请先点击按钮，获取验证码

    //三方登录
    String X20220811_LINK_ACCOUNT = "x20220811_link_account";//Link account
    String X20220811_LINK_RULE = "x20220811_link_rule";//One email address can only create one Bitget account, We found that email already exists in the Bitget system. Please enter your password and link it with your
    String X20220816_ACCOUNT_LINKED = "x20220816_account_linked";//Account linked !
    String X20220816_ACCOUNT_LINKED_FAIL = "x20220816_account_linked_fail";//Account link failed !
    String X20220816_LOGIN_QUICKLY = "x20220816_login_quickly";//You can now use your {0} to log in quickly
    String X20220816_UNLINK_ORIGINAL_ACCOUNT = "x20220816_unlink_original_account";//This Bitget account is already linked with another Bitget account, please unlink with the original account first
    String X20220816_OK = "x20220816_ok";//OK
    String X20220812_CREATE_ACCOUNT = "x20220812_create_account";
    String X20220817_OR = "x20220817_or";//Ok
    String X20220816_LINK_BITGET_ACCOUNT = "x20220816_link_bitget_account";//Link your Bitget account
    String X20220816_LINK_EXIST_ACCOUNT_TIP = "x20220816_link_exist_account_tip";//If you already have a bitget account , you can link it to your {0}
    String X20220817_CHOOSE_GOOGLE_ACCOUNT = "x20220817_choose_google_account";//Google account
    String X20220817_CHOOSE_APPLE_ID = "x20220817_choose_apple_id";
    String X20220816_LINK_EXIST_ACCOUNT = "x20220816_link_exist_account";//Link existing Bitget account
    String X20220816_SIGN_UP_ACCOUNT = "x20220816_sign_up_account";//Sign up a new Bitget account
    String X20220817_LINK_ORIGINAL_ACCOUNT_TIP = "x20220817_link_original_account_tip";//Please enter email or phone number of your Bitget account
    String X20220817_CHOOSE_ACCOUNT_LOGIN = "x20220817_choose_account_login";//This bitget account has been link to another {0} , Please select a account to link and log in
    String X20220816_LINK_NEW_ACCOUNT = "x20220816_link_new_account";//Are you sure to link with the new account ?
    String X220516_STRATEGY_END_OPERATE_YES = "x220516_strategy_end_operate_yes";//Yes
    String X220516_STRATEGY_END_OPERATE_NO = "x220516_strategy_end_operate_no";//No
    String X20220816_WITHOUT_ACCOUNT = "x20220816_without_account";//Don't have an account ? {0}
    String X20220826_SIGN_UP_FAIL_UNLINK = "x20220826_sign_up_fail_unlink";//One email address can only create one Bitget account, this email is already linked to Bitget through a third-party account. Please unlink or log in to that account.
    String X20220826_SIGN_UP_FAIL = "x20220826_sign_up_fail";//Sign up failed!
    String X20220816_SIGN_UP_FOR_FREE = "x20220816_sign_up_for_free";
    String X20220817_CHOOSE_ACCOUNT = "x20220817_choose_account";
    String X220831_PLEASE_SEND_VER_CODE = "x220831_please_send_ver_code";
    String X220916_LIMIT_ORDER = "x220916_limit_order";//限价单

    String X220915_KYC_BAN_COUNTRY_TIPS = "x220915_kyc_ban_country_tips";
    String X220916_MARKET_ORDER = "x220916_market_order";//市价单

    String EUM_RECHARGE_ACCOUNT_NAME = "eum_recharge_account_name_";

    String X220916_LOGOUT_TOAST_TITLE = "x220916_logout_toast_title";
    String X220916_LOGOUT_TOAST_SUBTITLE = "x220916_logout_toast_subtitle";
    String X220916_LOGOUT_TOAST_SUBTITLE1 = "x220916_logout_toast_subtitle1";
    String X220916_LOGOUT = "x220916_logout";
    String X220916_LIMIT_MARKET_ORDER = "x220916_limit_market_order";//限价|市价
    String X220930_ALREADY_LOGIN = "x220930_already_login";

    String X220919_TRACER_FILTER_TITLE = "x220919_tracer_filter_title";//认证交易员
    String X220919_TRACER_FILTER_HINT = "x220919_tracer_filter_hint";//已通过官方审核验证为币圈内知名社群媒体主、公共人物

    String X220616_COMMUNITY_VIEWPOINT = "x220616_community_viewpoint";//	观点

    String X220713_COMMUNITY_ALL_LOAD_MORE = "x220713_community_all_load_more";  //已加载全部
    String X220713_COMMUNITY_LOADING = "x220713_community_loading";//正在加载...
    String X220720_SAVE_IMG_FAILED = "x220720_save_img_failed";//保存图片失败
    String X220720_SAVE_IMG_SUCCESS = "X220720_SAVE_IMG_SUCCESS";//保存图片成功
    String X220720_STORAGE_PERMISSON_FAILED = "x220720_storage_permisson_failed";//读取内存卡全县被拒绝

    String X220802_COMMUNITY_AUTO_TRANSLATE_PROTOCOL = "x220802_community_auto_translate_protocol";//自动翻译协议
    String X220802_COMMUNITY_AUTO_TRANSLATE_URL = "x220802_community_auto_translate_url";//
    String X220805_TOPPING = "x220805_topping"; //置顶
    String X220805_BOUTIQUE = "x220805_boutique"; //精选
    String X220805_OFFICIAL = "x220805_official"; //官方
    String X220921_HOME_PULLING_REFRESH_TIP = "x220921_home_pulling_refresh_tip";//松开刷新
    String X220921_HOME_PULLING_COMMUNITY_TIP = "x220921_home_pulling_community_tip";//下拉查看观点
    String x221013_utc_change_time_set = "x221013_utc_change_time_set";//涨跌幅周期和K线时间
    String x221013_utc_change_time_alert = "x221013_utc_change_time_alert";//"UTC+8 0:00\\n涨跌幅和K线开盘价以香港时间0点开始计算\\nUTC+0 0:00\\n涨跌幅和K线开盘价以国际时间0点开始计算"
    String X221028_Withdraw_Count_Step = "x221028_withdraw_count_step";//该币种只能输入{0}的倍数

    String X220801_Copy_Number = "x220801_copy_number";//复制次数
    String X220801_Subscribe_Title = "x220801_subscribe_title";//订阅
    String StrategicTrading_My_strategy_strategic_income = "StrategicTrading_My_strategy_strategic_income";//策略收益
    String X220804_Gallery = "x220804_gallery";//已上架
    String Text_Otc_My_Ad_Down = "text_otc_my_ad_down";//已下架
    String Text_Otc_My_Ad_Down_State = "text_otc_my_ad_down_state";//下架

    String X220802_Strategy_Trader = "x220802_strategy_trader";//策略交易员

    String X20805_Subscribed = "x20805_subscribed";//已订阅

    String X220225_Api_Chek_Up = "x220225_api_chek_up";//查看

    String X220816_Search_Coin = "x220816_search_coin";//搜索币对
    String X220816_Follow_Trader_Termination_Strategy_Hint = "x220816_follow_trader_termination_strategy_hint";//关闭“跟随交易员终止策略”后，需要你手动终止策略，这可能会导致“自动复制”失效，确认关闭？

    String X220811_Subscribe_Expire = "x220811_subscribe_expire";//{0} 到期

    String X220812_On_The_Shelf = "x220812_on_the_shelf";//上架

    String X220802_Apply_Strategy_Trader_Desc = "x220802_apply_strategy_trader_desc";//Bitget三大独家优势资源

    String X220822_Apply_Trader_Review = "x220822_apply_trader_review";//审核中，请等待

    String StrategicTrading_Strategy_trade_strategy_plaza = "StrategicTrading_Strategy_trade_strategy_plaza";//策略广场

    String MegaSwap_Board_Hot = "MegaSwap_Board_Hot"; // 热门榜
    String MegaSwap_Board_Gainers = "MegaSwap_Board_Gainers"; // 涨幅榜
    String MegaSwap_Board_Losers = "MegaSwap_Board_Losers"; // 跌幅榜
    String MegaSwap_Board_List = "MegaSwap_Board_List"; // 新币榜
    String MegaSwap_Board_Soccer = "MegaSwap_Board_Soccer"; // Soccer
    String MegaSwap_SelfEdit_Title = "MegaSwap_SelfEdit_Title"; // 编辑自选
    String MegaSwap_SelfEdit_Complete = "MegaSwap_SelfEdit_Complete";// 完成
    String MegaSwap_SelfEdit_ChooseAll = "MegaSwap_SelfEdit_ChooseAll";// 全选
    String MegaSwap_SelfEdit_Delete = "MegaSwap_SelfEdit_Delete";// 删除
    String MegaSwap_SelfEdit_Coin = "MegaSwap_SelfEdit_Coin";// 币种
    String MegaSwap_SelfEdit_ToTop = "MegaSwap_SelfEdit_ToTop";// 置顶
    String MegaSwap_SelfEdit_Drag = "MegaSwap_SelfEdit_Drag";// 拖动
    String MegaSwap_KLineDetail_DialogRiskWarning = "MegaSwap_KLineDetail_DialogRiskWarning";// 风险提示
    String MegaSwap_KLineDetail_DialogContent = "MegaSwap_KLineDetail_DialogContent";// 此币未被收录审核交易请注意风险！任何人都可以创建代币，包括创建著名项目现有代币的伪造版本，如果您购买此代币，您可能无法将其卖出
    String MegaSwap_Market_SwapSearchTitle = "MegaSwap_Market_SwapSearchTitle";// 搜索
    String MegaSwap_Market_SwapSearchButton = "MegaSwap_Market_SwapSearchButton";// 搜索
    String MegaSwap_Market_SwapSearchEditHint = "MegaSwap_Market_SwapSearchEditHint";// 搜索名称或者输入合约地址
    String MegaSwap_SymolSearch_PlaceHolder = "MegaSwap_SymolSearch_PlaceHolder";// 名称/合约地址
    String MegaSwap_Market_SwapSearchHistory = "MegaSwap_Market_SwapSearchHistory";// 搜索历史
    String MegaSwap_Market_SwapSearchListAll = "MegaSwap_Market_SwapSearchListAll";// 全部
    String MegaSwap_Exchange_Confirm = "MegaSwap_Exchange_Confirm";// 兑换
    String MegaSwap_Exchange_RechargeAlert_Title = "MegaSwap_Exchange_RechargeAlert_Title";// 充值
    String MegaSwap_Exchange_RechargeAlert_CarryFromSpot = "MegaSwap_Exchange_RechargeAlert_CarryFromSpot";// 从现货提币
    String MegaSwap_Exchange_RechargeAlert_RechargeFromWallet = "MegaSwap_Exchange_RechargeAlert_RechargeFromWallet";// 从外部钱包充值
    String MegaSwap_Exchange_SwapInsufficient = "MegaSwap_Exchange_SwapInsufficient";// 您的MegaSwap帐户余额不足，请
    String MegaSwap_Exchange_SwapInsufficientRecharge = "MegaSwap_Exchange_SwapInsufficientRecharge";// 充值
    String MegaSwap_Exchange_PriceExplainAlert_Title = "MegaSwap_Exchange_PriceExplainAlert_Title";// 价值偏差
    String MegaSwap_Exchange_PriceExplainAlert_Content = "MegaSwap_Exchange_PriceExplainAlert_Content";// 价值偏差是支付币种的法币价值和接收币种的法币价值之间的差值，价值偏差越大则表示市场交易深度越差，交易损失也越大。
    String MegaSwap_Exchange_BestPassage = "MegaSwap_Exchange_BestPassage";// 最佳通道
    String MegaSwap_Exchange_ArrivalTime = "MegaSwap_Exchange_ArrivalTime";// <{0}分钟
    String MegaSwap_ImportCoin_SearchPlaceholder = "MegaSwap_ImportCoin_SearchPlaceholder";// 搜索名称或者输入合约地址
    String MegaSwap_ImportCoin_ThirdWarn = "MegaSwap_ImportCoin_ThirdWarn";// 以下代币为第三方搜索结果，交易请注意风险！
    String MegaSwap_ImportCoin_Title = "MegaSwap_ImportCoin_Title";// 导入代币
    String MegaSwap_ImportCoin_Warning = "MegaSwap_ImportCoin_Warning";// 此币未被收录审核交易请注意风险!
    String MegaSwap_ImportCoin_Source = "MegaSwap_ImportCoin_Source";// 来源
    String MegaSwap_ImportCoin_WarningContent = "MegaSwap_ImportCoin_WarningContent";// 任何人都可以创建代币，包括创建著名项目现有代币的伪造版本，如果您购买此代币，您可能无法将其卖出
    String MegaSwap_ImportCoin_ImportCoinConfirm = "MegaSwap_ImportCoin_ImportCoinConfirm";// 导入此代币
    String MegaSwap_ImportCoin_ChainAll = "MegaSwap_ImportCoin_ChainAll";// 全部
    String MegaSwap_ImportCoin_UserAdd = "MegaSwap_ImportCoin_UserAdd";// 用户添加
    String MegaSwap_AddCoin_Title = "MegaSwap_AddCoin_Title";// 添加币种
    String MegaSwap_AddCoin_Custom = "MegaSwap_AddCoin_Custom";// 自定义
    String MegaSwap_AddCoin_SearchEditHint = "MegaSwap_AddCoin_SearchEditHint";// 搜索名称或者输入合约地址
    String MegaSwap_AddCoin_ListAll = "MegaSwap_AddCoin_ListAll";// 全部
    String MegaSwap_AddCoin_Added = "MegaSwap_AddCoin_Added";// 已添加币种
    String MegaSwap_AddCoin_UnAdd = "MegaSwap_AddCoin_UnAdd";// 未添加币种
    String MegaSwap_AddCoin_NoDataTitle = "MegaSwap_AddCoin_NoDataTitle";//无匹配结果
    String MegaSwap_AddCoin_NoDataContent = "MegaSwap_AddCoin_NoDataContent";//请尝试使用合约地址搜索\n点击下方按钮添加
    String MegaSwap_AddCoin_NoDataButton = "MegaSwap_AddCoin_NoDataButton";//自定义添加代币
    String MegaSwap_AddCoin_DeleteAlertTitle = "MegaSwap_AddCoin_DeleteAlertTitle";//确定删除展示吗？
    String MegaSwap_AddCoin_DeleteAlertContent = "MegaSwap_AddCoin_DeleteAlertContent";//您可以在首页重新添加
    String MegaSwap_AddCoin_DeleteAlertConfirm = "MegaSwap_AddCoin_DeleteAlertConfirm";//确定
    String MegaSwap_AddCoin_DeleteAlertCancel = "MegaSwap_AddCoin_DeleteAlertCancel";//取消
    String MegaSwap_AddCoin_ToastFail = "MegaSwap_AddCoin_ToastFail";//没有合约地址
    String MegaSwap_AddCoin_ToastSuccess = "MegaSwap_AddCoin_ToastSuccess";//添加成功
    String MegaSwap_CustomAddCoin_Title = "MegaSwap_CustomAddCoin_Title";// 自定义添加代币
    String MegaSwap_CustomAddCoin_ChooseMainChain = "MegaSwap_CustomAddCoin_ChooseMainChain";// 选择主链
    String MegaSwap_CustomAddCoin_ChooseMainChainHint = "MegaSwap_CustomAddCoin_ChooseMainChainHint";// 请选择链
    String MegaSwap_CustomAddCoin_ContractAddress = "MegaSwap_CustomAddCoin_ContractAddress";// 请输入需要添加的代币合约
    String MegaSwap_CustomAddCoin_ChooseMainChainNoChain = "MegaSwap_CustomAddCoin_ChooseMainChainNoChain";// 请选择链
    String MegaSwap_CustomAddCoin_NeedRightAddress = "MegaSwap_CustomAddCoin_NeedRightAddress";// 请输入正确的代币合约
    String MegaSwap_CustomAddCoin_ContractAddressHint = "MegaSwap_CustomAddCoin_ContractAddressHint";// 合约地址
    String MegaSwap_CustomAddCoin_Confirm = "MegaSwap_CustomAddCoin_Confirm";// 确定
    String MegaSwap_SwitchNet_Explain = "MegaSwap_SwitchNet_Explain";// 当您切换为特定区块链网络时总资产估值为该特定区块链网络的总资产估
    String MegaSwap_SwitchNet_Title = "MegaSwap_SwitchNet_Title";// 切换网络
    String MegaSwap_Deposit_Spot = "MegaSwap_Deposit_Spot";// 现货充币
    String MegaSwap_Deposit_Swap = "MegaSwap_Deposit_Swap";// Swap充币
    String MegaSwap_Deposit_Address = "MegaSwap_Deposit_Address";// 充币地址
    String MegaSwap_Deposit_SelectChain = "MegaSwap_Deposit_SelectChain";// 选择链
    String MegaSwap_Assets_AllNetworks = "MegaSwap_Assets_AllNetworks";// 全部网络
    String MegaSwap_Assets_AddCurrency = "MegaSwap_Assets_AddCurrency";// 添加币种
    String MegaSwap_Assets_SwitchNetwork_Tip = "MegaSwap_Assets_SwitchNetwork_Tip";// 当您切换为特定区块链网络时总资产估值为该特定区块链网络的总资产估值
    String MegaSwap_Kline_Alert_Title = "MegaSwap_Kline_Alert_Title";// 风险提示
    String MegaSwap_Kline_Alert_Content = "MegaSwap_Kline_Alert_Content";// 此币未被收录审核交易请注意风险!任何人都可以创建代币，包括创建著名项目现有代币的伪造版本，如果您购买此代币，您可能无法将其卖出
    String MegaSwap_Assets_Send = "MegaSwap_Assets_Send";// 发送
    String MegaSwap_Assets_TakeOver = "MegaSwap_Assets_TakeOver";// 接收
    String MegaSwap_Select_SearchHint = "MegaSwap_Select_SearchHint";// 搜索币名或者合约
    String MegaSwap_Send_Submit = "MegaSwap_Send_Submit";// 提交
    String MegaSwap_Send_Address = "MegaSwap_Send_Address";// 发送地址
    String MegaSwap_Accept_Address = "MegaSwap_Accept_Address";// 接收地址
    String MegaSwap_Send_InputAddress = "MegaSwap_Send_InputAddress";// 请输入发送地址
    String MegaSwap_Send_Count = "MegaSwap_Send_Count";// 发送数量
    String MegaSwap_Send_InputCount = "MegaSwap_Send_InputCount";// 请输入数量
    String MegaSwap_Send_SpotAddress = "MegaSwap_Send_SpotAddress";// 发送到现货地址
    String MegaSwap_Send_Tips = "MegaSwap_Send_Tips";// 可用  1.0btc
    String MegaSwap_RiskWarning = "MegaSwap_RiskWarning";// 警告：所选币种不在您的安全白名单上，该币种或为知名币种的仿品。
    String MegeSwap_Exchange_SlippageHighTips = "MegeSwap_Exchange_SlippageHighTips";// 当前输入滑点过高，会帮你交易成功，但会造成损失，请注意使用！
    String MegeSwap_Exchange_SlippageLowTips = "MegeSwap_Exchange_SlippageLowTips";// 当前输入滑点过低，成交可能会失败且会损失交易手续费，请注意使用！
    String MegaSwap_Withdraw_Pop_Content = "MegaSwap_Withdraw_Pop_Content";//  {0}手续费余额不足，余额最少补充 {1} {0}，请到MegaSwap兑换。
    String MegaSwap_Withdraw_Pop_ContentWithoutExchange = "MegaSwap_Withdraw_Pop_ContentWithoutExchange";//  {0}手续费余额不足，余额最少补充 {1} {0}。
    String MegaSwap_Withdraw_Pop_Hint = "MegaSwap_Withdraw_Pop_Hint";//  复制错误信息
    String MegaSwap_Withdraw_Pop_Title = "MegaSwap_Withdraw_Pop_Title";//  转账失败
    String MegaSwap_Withdraw_ConfirmOrder_GasFee = "MegaSwap_Withdraw_ConfirmOrder_GasFee";//  预估gas费
    String MegaSwap_Asset_chooseCoin_footViewText = "MegaSwap_Asset_chooseCoin_footViewText";// "没有找到相对应的代币/n可点击下方按钮添加"
    String MegaSwap_SpotWithdraw_ToSwap = "MegaSwap_SpotWithdraw_ToSwap";// 提币到MegaSwap地址
    String MegaSwap_Withdraw_ServiceChargeTipTitle = "MegaSwap_Withdraw_ServiceChargeTipTitle";// 手续费说明
    String MegaSwap_Withdraw_ServiceChargeTipContent = "MegaSwap_Withdraw_ServiceChargeTipContent";// 新版本手续费说明
    String MegaSwap_Exchange_NotEnoughForGas = "MegaSwap_Exchange_NotEnoughForGas";// 余额不足以支付gas费
    String MegaSwap_Exchange_HasMinusGas = "MegaSwap_Exchange_HasMinusGas";// 已为您扣除gas费
    String MegaSwap_Slippage_Title = "MegaSwap_Slippage_Title";// 滑点限制
    String MegaSwap_Slippage_Content = "MegaSwap_Slippage_Content";// 滑点是指交易的预期价格与交易执行价格之间的差额。如果交易的叫个变化超过滑点设置，您的交易将被取消。
    String MegaSwap_RiskWarning_Title = "MegaSwap_RiskWarning_Title";// 此币种尚未通过安全审核
    String MegaSwap_RiskWarning_Content = "MegaSwap_RiskWarning_Content";// 请留意风险性，该币种或为知名币种的仿品。
    String MegaSwap_Slippage_ChoiceTitle = "MegaSwap_Slippage_ChoiceTitle";// 手动输入
    String MegaSwap_SpotWithdraw_TextError = "MegaSwap_SpotWithdraw_TextError";// MegaSwap尚未支持您当前选定的链

    String MegaSwap_SpotWithdraw_NoChain = "MegaSwap_SpotWithdraw_NoChain";// 请先选择链


    String X221104_BIND_GOOGLE_GUIDE = "x221104_bind_google_dialog_tips"; //引导谷歌绑定
    String X221104_BIND_GOOGLE_GUIDE_BUTTON = "x221104_bind_google_dialog_btn"; //引导谷歌绑定按钮
    String X2201107_ASSETS_FINANCIAL_RATE_LEVEL = "x2201107_assets_financial_rate_level"; //等级{0}
    String X221110_CONTRACT_TIP = "x221110_contract_tip";
    String X221110_CONTRACT_TIP_CONFIRM = "x221110_contract_tip_confirm";
    String X221110_CONTRACT_TIP_CHECK = "x221110_contract_tip_check";

    //kyc 接入samsub新版本
    String Kyc_country_region = "Kyc_country_region";
    String Kyc_country_region_placeholder = "Kyc_country_region_placeholder";
    String Kyc_navigationTitle = "Kyc_navigationTitle";
    String KYC_BENEFIT = "Kyc_benefit";//benfit
    String User_KycVerify_Benefit_individual_des_1 = "User_KycVerify_Benefit_individual_des_1";
    String KYC_BENEFIT_INDIVIDUAL_DES_2 = "Kyc_benefit_individual_des_2";
    String KYC_REQUIREMENTS = "Kyc_requirements";
    String KYC_INDIVIDUAL_IDCARD = "Kyc_individual_IDCard";
    String KYC_INDIVIDUAL_SELFIE = "Kyc_individual_selfie";
    String KYC_INSTITUTION_VERIFICATION = "Kyc_institution_verification";
    String Kyc_individual_verification = "Kyc_individual_verification";
    String Kyc_daily = "Kyc_daily";
    String Kyc_withdraw_limit_des = "Kyc_withdraw_limit_des";
    String KYC_BENEFIT_INSTITUTION_DES_2 = "Kyc_benefit_institution_des_2";
    String KYC_BENEFIT_INSTITUTION_DES_3 = "Kyc_benefit_institution_des_3";
    String KYC_INSTITUTION_LICENSE = "Kyc_institution_license";
    String KYC_INSTITUTION_BUS_CER = "Kyc_institution_bus_cer";
    String KYC_INSTITUTION_CORPORATEID = "Kyc_institution_corporateID";
    String KYC_BTN_VERIFYNOW = "Kyc_btn_verifyNow";
    String Kyc_error_tips = "Kyc_error_tips";
    String Kyc_pending_verified = "Kyc_pending_verified";//verified
    String Kyc_pending_unverified = "Kyc_pending_unverified";//unverified
    String Kyc_pending_tips = "Kyc_pending_tips";
    String Kyc_pending_back_button = "Kyc_pending_back_button";//Back
    String Kyc_verified_account = "Kyc_verified_account";//
    String Safe_Kyc_Country_Region = "Safe_Kyc_Country_Region";
    String Kyc_passport = "Kyc_passport";
    String Kyc_name = "Kyc_name";
    String Kyc_Id_card = "Kyc_Id_card";
    String Kyc_driving_license = "Kyc_driving_license";
    String Kyc_registration_num = "Kyc_registration_num";
    String Kyc_notsupported_title = "Kyc_notsupported_title";
    String Kyc_notsupported_institution = "Kyc_notsupported_institution";

    //--------------------------       Kyc接入机构认证版本     ----------------------------//

    String Kyc_institution_title = "Kyc_institution_title";               //机构认证标题
    String Kyc_individual_title = "Kyc_individual_title";                //个人认证标题
    String Kyc_individual_country_residence = "Kyc_individual_country_residence";    //居住国家
    String Kyc_individual_no_verify = "Kyc_individual_no_verify";            //Level{0}认证
    String Kyc_individual_verified = "Kyc_individual_verified";             //Level{0}已认证
    String Kyc_individual_under_review = "Kyc_individual_under_review";         //Level{0}正在审核中
    String Kyc_individual_validation_failed = "Kyc_individual_validation_failed";    //Level{0}认证失败
    String Kyc_individual_review_time = "Kyc_individual_review_time";          //审核时间{0}分钟
    String Kyc_basic_information = "Kyc_basic_information";               //基本信息
    String Kyc_individual_Id_verification = "Kyc_individual_Id_verification";      //身份认证
    String Kyc_individual_liveness_check = "Kyc_individual_liveness_check";       //人机识别
    String Kyc_account_limits = "Kyc_account_limits";                  //帐户限制
    String Kyc_crypto_withdraw_limit = "Kyc_crypto_withdraw_limit";           //加密货币提现额{0}
    String Kyc_flat_withdraw_limit = "Kyc_flat_withdraw_limit";             //法币提现额{0}
    String Kyc_individual_proof_address = "Kyc_individual_proof_address";        //地址证明
    String Kyc_individual_hint = "Kyc_individual_hint";                 //*基本信息提交后不可修改，请谨慎填写
    String Kyc_individual_profile = "Kyc_individual_profile";              //简介
    String Kyc_individual_legal_name = "Kyc_individual_legal_name";           //法定名称
    String Kyc_individual_birthday = "Kyc_individual_birthday";             //生日
    String Kyc_individual_nationality = "Kyc_individual_nationality";          //国籍
    String Kyc_status_verified = "Kyc_status_verified";                 //已认证
    String Kyc_status_under_review = "Kyc_status_under_review";             //审核中
    String Kyc_status_failed = "Kyc_status_failed";                   //失败
    String Kyc_individual_try_manual = "Kyc_individual_try_manual";           //试试人工审核?
    String Kyc_institution_entity_verification = "Kyc_institution_entity_verification"; //机构认证
    String Kyc_institution_declaration = "Kyc_institution_declaration";         //声明
    String Kyc_institution_exclusive = "Kyc_institution_exclusive";           //专属客服
    String Kyc_verify_again = "Kyc_verify_again";                    //Verify again
    String Kyc_failed_reason = "Kyc_failed_reason";                   //Kyc_failed_reason

    //--------------------------       Kyc接入机构认证版本End     ------------------------//

    //--------------------------       首页                     ------------------------//
    String User_Register_First_Deposit = "User_Register_First_Deposit";//首次入金可得
    String User_Register_First_Trade = "User_Register_First_Trade";//首次交易可得
    String User_Register_Benefits = "User_Register_Benefits";//福利金
    String User_Register_GoAndSee = "User_Register_GoAndSee";//去看看

    //首页改版3.1

    String Home_Assets_Add = "Home_Assets_Add";//首页资产按钮
    String Home_Quick_Earn = "Home_Quick_Earn";//金刚区earn
    String HomeCustomize_Nav_Title = "HomeCustomize_Nav_Title";//customize homepage
    String HomeCustomize_Nav_Reset = "HomeCustomize_Nav_Reset";//reset
    String HomeCustomize_Desc = "HomeCustomize_Desc";//描述
    String HomeCustomize_Assets = "HomeCustomize_Assets";//资产

    String HomeCustomize_LogSign = "HomeCustomize_LogSign";//首页编辑-未登录时展示
    String HomeCustomize_QuickEntry = "HomeCustomize_QuickEntry";//金刚区
    String HomeCustomize_Banner = "HomeCustomize_Banner";//Banner
    String HomeCustomize_Notice = "HomeCustomize_Notice";//公告
    String HomeCustomize_Investment = "HomeCustomize_Investment";//投资机会
    String HomeCustomize_TopMovers = "HomeCustomize_TopMovers";//top movers
    String HomeCustomize_TopTrader = "HomeCustomize_TopTrader";//顶级带单专家
    String HomeCustomize_Rewards = "HomeCustomize_Rewards";//福利中心
    String HomeCustomize_AccountManager = "HomeCustomize_AccountManager";//vip客户经理
    String HomeCustomize_ToastWarn = "HomeCustomize_ToastWarn";//最少选择3个模块

    String Home_ActivityA_Title = "Home_ActivityA_Title";//首页营销活动A
    String Home_ActivityB_Title = "Home_ActivityB_Title";//首页营销活动B

    String Home_QuickEntry_EditTopNine = "Home_QuickEntry_EditTopNine";

    //--------------------------       首页 END                 ------------------------//


    String X221031_IDENTIFICATION_EN_FIRST_NAME = "x221031_identify_en_first_name";//英文名字
    String X221031_IDENTIFICATION_EN_LAST_NAME = "x221031_identify_en_last_name";//英文姓氏
    String X221031_IDENTIFICATION_EN_FIRST_NAME_HINT = "x221031_identify_en_first_name_hint";//请输入英文名字
    String X221031_IDENTIFICATION_EN_LAST_NAME_HINT = "x221031_identify_en_last_name_hint";//请输入英文姓氏
    String X221031_IDENTIFICATION_PERSON = "x221031_identify_person";//个人认证
    String X221031_IDENTIFICATION_ORGANIZATION = "x221031_identify_organization";//机构认证
    String X221031_IDENTIFY_EN_NAME = "x221031_identify_en_name";
    String X221031_IDENTIFY_EN_NAME_HINT = "x221031_identify_en_name_hint";
    String X221031_IDENTIFY_FULL_EN_NAME = "x221031_identify_full_en_name";
    String Safe_Kyc_Add_Information = "Safe_Kyc_Add_Information";
    String Safe_Kyc_Full_Person_Info_Hint = "Safe_Kyc_Full_Person_Info_Hint";
    String X221031_IDENTIFY_FULL_ORGANIZATION_INFO = "x221031_identify_full_organization_info";
    String X221031_IDENTIFY_FULL_ORGANIZATION_INFO_HINT = "x221031_identify_full_organization_info_hint";
    String X221031_IDENTIFY_ORGANIZATION_EN_NAME = "x221031_identify_organization_en_name";
    String X221031_IDENTIFY_ORGANIZATION_FIRST_NAME = "x221031_identify_organization_first_name";
    String X221031_IDENTIFY_ORGANIZATION_LAST_NAME = "x221031_identify_organization_last_name";
    String X221031_IDENTIFY_ORGANIZATION_FIRST_EN_NAME = "x221031_identify_organization_first_en_name";
    String X221031_IDENTIFY_ORGANIZATION_LAST_EN_NAME = "x221031_identify_organization_last_en_name";
    String X221031_IDENTIFY_ORGANIZATION_EN_NAME_HINT = "x221031_identify_organization_en_name_hint";
    String X221031_IDENTIFY_ORGANIZATION_FIRST_NAME_HINT = "x221031_identify_organization_first_name_hint";
    String X221031_IDENTIFY_ORGANIZATION_LAST_NAME_HINT = "x221031_identify_organization_last_name_hint";
    String X221031_IDENTIFY_ORGANIZATION_FIRST_EN_NAME_HINT = "x221031_identify_organization_first_en_name_hint";
    String X221031_IDENTIFY_ORGANIZATION_LAST_EN_NAME_HINT = "x221031_identify_organization_last_en_name_hint";
    String X221031_IDENTIFY_ORGANIZATION_FULL_NAME = "x221031_identify_organization_full_name";
    String X221031_IDENTIFY_ORGANIZATION_FULL_PERSON_NAME = "x221031_identify_organization_full_person_name";
    String X221031_IDENTIFY_ORGANIZATION_FULL_PERSON_EN_NAME = "x221031_identify_organization_full_person_en_name";
    String X221031_IDENTIFY_ORGANIZATION_PERSON_NAME = "x221031_identify_organization_person_name";
    String X221101_IDENTIFY_LEGAL_PERSON_ENGLISH_NAME = "x221101_identify_legal_person_english_name";
    String X221101_IDENTIFY_NOT_EMPTY = "x221101_identify_not_empty";
    String X221101_IDENTIFY_ORGANIZATION_SYMBOL = "x221101_identify_organization_symbol";
    String X221031_IDENTIFY_INPUT_OTHER_HINT = "x221031_identify_input_other_hint";

    String X221012_FOLLOW_CONTRACT_TITLE = "x221012_follow_contract_title";//合约跟单
    String X221012_FOLLOW_SPOT_TITLE = "x221012_follow_spot_title";//现货跟单
    String X221018_SPOT_FOLLOW_TOTAL_EQU = "x221018_spot_follow_total_equ";//总资金
    String X221018_SPOT_FOLLOW_TOTAL_EQU_HINT = "x221018_spot_follow_total_equ_hint";//展示数据为交易员在现货账户的总资金，已将所有币种统一折合为USDT

    String X221024_FOLLOW_SPOT_END = "x221024_follow_spot_end";//结束
    String X221024_FOLLOW_SPOT_BUY_DATE = "x221024_follow_spot_buy_date";//买入时间
    String X221024_FOLLOW_SPOT_ALL_SELL = "x221024_follow_spot_all_sell";//	全卖
    String X221024_FOLLOW_SPOT_SELL_DATE = "x221024_follow_spot_sell_date";//卖出时间
    String X221024_FOLLOW_SPOT_PROFILE_HINT_TITLE = "x221024_follow_spot_profile_hint_title";//止盈止损说明
    String X221024_FOLLOW_SPOT_PROFILE_HINT_CONTENT = "x221024_follow_spot_profile_hint_content";//当市场最新成交价达到您设置的价格时，将会以最优成交价形式把该笔订单对应的数量成交。
    String X221024_FOLLOW_SPOT_END_HINT_TITLE = "x221024_follow_spot_end_hint_title";//结束跟单说明
    String X221024_FOLLOW_SPOT_END_HINT_CONTENT = "x221024_follow_spot_end_hint_content";//结束跟单仅代表按照当前价计算该笔订单的盈亏情况，不会将该笔订单对应的数字货币卖出，仍然会保留在您的现货账户。
    String X221102_FOLLOW_HISTORY_CROSS_SELL_HINT = "x221102_follow_history_cross_sell_hint";//该订单为跨区卖币，已将{0}卖为{1}。
    String TEXT_HISTORT_FOLLOW_NET_PROFIT_TITLE = "text_histort_follow_net_profit_title";//净利润
    String X221201_FOLLOW_END_SPOT_TITLE = "x221201_follow_end_spot_title";//结束跟单
    String X221201_FOLLOW_END_SPOT_HINT = "x221201_follow_end_spot_hint";//	确定要结束跟单吗

    String X221018_TEXT_TRACE_SPOT_ASSET = "x221018_text_trace_spot_asset";//	资产构成
    String X221022_CONTRACT_AUTOCANCLE_FOLLOW = "x221022_contract_autocancle_follow";//合约自动取消跟随
    String X221022_SPOT_AUTOCANCLE_FOLLOW = "x221022_spot_autocancle_follow";//合约自动取消跟随
    String COPYTRADE_PERSONALSET_SPOT_CROSS_SELL_COIN_TIPS = "CopyTrade_PersonalSet_SpotCrossSellCoinTips";//开启后您将跟随交易员的跨区卖币行为，关闭时您将卖到原交易区

    String X221103_REWARDS_COUPON = "x221103_rewards_coupon";
    String X221019_COUPON_STATE_ISUSED = "x221019_coupon_state_isused";
    String X221021_VIP_TIP = "x221021_vip_tip";
    String X221019_Dca_Spot_Type_1 = "x221019_dca_spot_type_1";//正向DCA
    String X221019_Dca_Spot_Type_2 = "x221019_dca_spot_type_2";//反向DCA
    String X221019_Dca_Contract_Type_1 = "x221019_dca_contract_type_1";//多头DCA
    String X221019_Dca_Contract_Type_2 = "x221019_dca_contract_type_2";//空头DCA
    String X221019_Quick_Create = "x221019_quick_create";//快捷创建
    String X221020_First_Order = "x221020_first_order";//首单
    String X221019_Required_Funds = "x221019_required_funds";//所需资金

    String X221021_Stop_Loss_Refer = "x221021_stop_loss_refer";//止损参照
    String X221025_Must_More_Than_Unit = "x221025_must_more_than_unit";//需大于{0} {1}


    String X221121_Recharge_Contract_Tip = "x221121_recharge_contract_tip";//合约信息
    String X221121_Recharge_Contract_Suf_Tip = "x221121_recharge_contract_suf_tip";//合约信息 末尾字符{0}
    String X221121_Recharge_Contract_Content = "x221121_recharge_contract_content";//请确认您要充值的币种合约信息与该币种的合约信息是否相同。充值不同的币种会造成币种丢失。当前币种合约信息详情，请复制下面链接在浏览器中打开查看。
    String X221121_Withdraw_Uid_Tip = "x221121_withdraw_uid_tip";//这是一个Bitget账户，UID{0}，立即转账0手续费
    String Assets_Withdraw_Uid_Fee_Tip = "Assets_Withdraw_uid_fee_tip";//这是一个Bitget账户，UID{0},内部转账或提币都是0手续费
    String Assets_Withdraw_UsernameFeeTip = "Assets_Withdraw_username_fee_tip";//这是一个Bitget账户，用户名{0},内部转账或提币都是0手续费
    String X221114_INSTALL_TIP = "x221114_install_tip";//若安装失败，请开启飞行模式
    String X221114_INSTALL_TIP_TITLE = "x221114_install_tip_title";//是否立即安装？
    String X221009_DELIVERY_CONTRACT = "x221009_delivery_contract";//交割合约
    String X221009_PERPETUAL_CONTRACT = "x221009_perpetual_contract";//永续合约
    String X221009_DELIVERY_CONTRACT_TIME = "x221009_delivery_contract_time";//距离交割时间
    String X221009_DELIVERY_CONTRACT_PRO_PRICE = "x221009_delivery_contract_pro_price";//预计交割价
    String X221009_DELIVERY_CONTRACT_ING = "x221009_delivery_contract_ing";//交割中
    String TEXT_NET_SPEED_CHECK = "text_net_speed_check";//网络检测

    String X220726_HOLD_POSITION_TYPE = "x220726_hold_position_type";//持仓模式
    String X220726_HOLD_POSITION_TYPE_SINGLE = "x220726_hold_position_type_single";//单向持仓
    String Futures_FuturesSetting_PositionOneWayMode = "Futures_FuturesSetting_PositionOneWayMode";
    String X220726_HOLD_POSITION_TYPE_ALL = "x220726_hold_position_type_all";//双向持仓
    String Futures_FuturesSetting_PositionHedgeMode = "Futures_FuturesSetting_PositionHedgeMode"; //双向持仓
    String X220727_ONLY_CLOSE_POSITION = "x220727_only_close_position";//只减仓
    String X220727_HOLD_POSITION_TYPE_SET = "x220727_hold_position_type_set";//持仓模式设置
    String Futures_FuturesSetting_PositionModeSetting = "Futures_FuturesSetting_PositionModeSetting";
    String X220727_ONLY_CLOSE_POSITION_DETAIL = "x220727_only_close_position_detail";//只减仓委托订单只会减少您的仓位，不会增加您的持仓。
    String X220728_HOLD_MODE_USDT_INFO = "x220728_hold_mode_usdt_info";//注：若您U本位合约下存在持仓或者挂单，不允许调整持仓模式。持仓模式调整对U本位合约下所有的合约统一生效。
    String X220728_HOLD_MODE_USD_INFO = "x220728_hold_mode_usd_info";//注：若您币本位合约下存在持仓或者挂单，不允许调整持仓模式。持仓模式调整对币本位合约下所有的合约统一生效。
    String X220728_HOLD_MODE_USDC_INFO = "x220728_hold_mode_usdc_info";//注：若您USDC合约下存在持仓或者挂单，不允许调整持仓模式。持仓模式调整对USDC合约下所有的合约统一生效。
    String X220728_HOLD_MODE_SINGLE_DESC = "x220728_hold_mode_single_desc";//单向持仓模式下,一个合约只允许持有一个方向的仓位
    String X220728_HOLD_MODE_DOUBLE_DESC = "x220728_hold_mode_double_desc";//双向持仓模式下，一个合约可以同时持有多空两个方向的仓位，同一合约下两个方向仓位的风险可以对冲。

    //小号================
    String Home_Home_TopBtnStart = "Home_Home_TopBtnStart";//小号首页top跳转按钮->登录（start now）
    String Home_Home_TopBtnRecharge = "Home_Home_TopBtnRecharge";//小号首页top跳转按钮->充值 (Deposit Now)
    String Home_Home_AssetsTitle = "Home_Home_AssetsTitle";//小号资产标题
    String Home_Home_NoAssetsBannerTitle = "Home_Home_NoAssetsBannerTitle";//小号首页没有资产banner的标题
    String Home_Home_NoAssetsBannerDesc = "Home_Home_NoAssetsBannerDesc";//小号首页没有资产banner的描述
    String Home_Home_RankTitle = "Home_Home_RankTitle";//小号首页排行榜标题
    String Personal_Personal_HeaderWelcome = "Personal_Personal_HeaderWelcome";//小号个人中心，未登录 欢迎语
    String Personal_Personal_HeaderWelcomeSlogan = "Personal_Personal_HeaderWelcomeSlogan";//小号个人中心，未登录 欢迎语
    String Personal_Personal_ImproveSecurity = "Personal_Personal_ImproveSecurity";//小号个人中心，Improve Security
    String Personal_Personal_ImproveSecurityNow = "Personal_Personal_ImproveSecurityNow";//小号个人中心，Improve Security Now
    String Personal_Settings_ThemeSetting = "Personal_Settings_ThemeSetting";//小号设置>主题模式
    String Personal_ThemeSettings_PageTitle = "Personal_ThemeSettings_PageTitle";//小号主题设置：主题颜色
    String Personal_ThemeSettings_ModeLight = "Personal_ThemeSettings_ModeLight";//小号主题设置：Light
    String Personal_ThemeSettings_ModeDark = "Personal_ThemeSettings_ModeDark";//小号主题设置：Dark
    String Personal_ThemeSettings_ModeFollowSystem = "Personal_ThemeSettings_ModeFollowSystem";//小号主题设置：Dark
    String Splash_Splash_Slogan = "Splash_Splash_Slogan";//小号启动页底部slogan
    String Market_Coin_CategoryAll = "Market_Coin_CategoryAll";//币种分类[全部]
    String GRID_MARKET_24H_AMPLITUDE = "Grid_Market_24H_amplitude";//24H振幅
    String WEB_JUMP_NOT_SUCCESS_TIPS = "Web_jump_not_success_tips";//    当前您的手机似乎没有找到可以处理这个链接的应用。您可以复制这个链接，在浏览器中尝试访问。

    //新人首页入金新增
    String x221222_guide_title = "x221222_guide_title";//首页banner标题
    String x221222_guide_content = "x221222_guide_content";//首页banner content
    String x221222_deposit_tutoria_title = "x221222_deposit_tutoria_title";//充币页面教程链接
    String x221222_deposit_tutoria_url = "x221222_deposit_tutoria_url";//充币页面教程地址

    String PriceRemind_Personal_Alert_Management = "PriceRemind_Personal_Alert_Management";
    String PriceRemind_view_CreateAlert_Create = "PriceRemind_view_CreateAlert_Create";

    String X220611_EXPIRED = "x220611_expired";

    String ASSETS_WITHDRAW_RISK_TIP_TITLE = "Assets_Withdraw_RiskTipTitle"; //提币信息与风险提示
    String ASSETS_WITHDRAW_RISK_TIP1 = "Assets_Withdraw_RiskTip1";// 请仔细核对提币地址，提币完成后资产无法追回。
    String ASSETS_WITHDRAW_RISK_TIP2 = "Assets_Withdraw_RiskTip2";// 建议不要使用与其他网站相同的密码，并定期更换密码。
    String ASSETS_WITHDRAW_RISK_TIP3 = "Assets_Withdraw_RiskTip3";//警惕诈骗！Bitget工作人员不会以任何名义要求您提币。
    String ASSETS_WITHDRAW_RISK_TIP4 = "Assets_Withdraw_RiskTip4";//任何活动以「官网」公告为准。
    String ASSETS_WITHDRAW_RISK_TIP5 = "Assets_Withdraw_RiskTip5";//收到任何可疑电话/邮件/提醒等，请联系Bitget在线客服进行核实。
    String X221220_COUNTRY_UNSELECTED_ERROR = "x221220_country_unselected_error"; //国家地区必填
    String X220927_COUPON_HEAD_TITLE = "x220927_coupon_head_title"; //卡券中心
    String ASSETS_SELECTCOIN_TABTITLE_CRYPTO = "Assets_SelectCoin_TabTitle_Crypto";//Crypto
    String ASSETS_SELECTCOIN_TABTITLE_FIAT = "Assets_SelectCoin_TabTitle_Fiat";//Fiat

    //红包改版词条
    String X221209_SIX_CODE = "x221209_six_code";//6位数字+字母组合 (选填)
    String X221209_VALID_TIME = "x221209_valid_time";//有效期
    String X220611_GIVEAWAYS_H1 = "x220611_giveaways_h1";//发红包
    String X221212_REDPACKET_MULTI_CURRENCY = "x221212_redpacket_multi_currency";//请选择币种 (可多选）
    String X221212_REDPACKET_COIN_TYPE = "x221212_redpacket_coin_type";//红包币种
    String X221212_REDPACKET_RULE1 = "x221212_redpacket_rule1";//红包规则：新注册用户通过红包链接领取您红包，将绑定邀请关系
    String X221212_REDPACKET_RULE2 = "x221212_redpacket_rule2";//红包失效后，未被领取的金额将原路返回
    String X221209_DIGITAL_REDPACKET1 = "x221209_digital_redpacket1";//Bitget数字红包
    String X221209_DIGITAL_REDPACKET2 = "x221209_digital_redpacket2";//搭建Web3.0友谊的桥梁
    String X221213_REDPACKET_COMPLETE_TIP = "x221213_redpacket_complete_tip";//蓄势待发
    String X221213_DELAY_RETURN = "x221213_delay_return";//{0}未领取的红包将被退回
    String X221214_PASSWORD_TIP = "x221214_password_tip";//请输入红包口令
    String X221214_OPEN_REDPACKET = "x221214_open_redpacket";//拆红包
    String X221214_CONGRATION_GET = "x221214_congration_get";//恭喜！成功抢到
    String X221214_GET_AND_CHECK = "x221214_get_and_check";//太棒了！红包已领取
    String X221214_VIEW_RECEIVING_DETAILS = "x221214_view_receiving_details";//查看领取详情，看看大家的手气吧！
    String X221214_STILL_CAN_GRAB = "x221214_still_can_grab";//稍等！还有红包未被领取
    String X221214_WAIT_TIME = "x221214_wait_time";//还有人未领取红包，{0}分后可再抢
    String X221214_REDPACKET_INVALID = "x221214_redpacket_invalid"; //红包已失效
    String X221216_REDPACKET_TOO_LATE = "x221216_redpacket_too_late";//来晚了，红包已抢完
    String X221216_REDPACKET_EXPIRE = "x221216_redpacket_expire";//该红包已过期，可点击查看领取详情
    String X221216_CHECK_DETAILS = "x221216_check_details";//领取详情
    String X221216_RECEIVE_SINGLE_RED_ENVELOPE = "x221216_receive_single_red_envelope";
    String X221209_REDPACKET_DESCRIBE = "x221209_redpacket_describe";

    String WEB_RISK_STATEMENT_TIPS = "Web_risk_statement_tips"; //您的网络可能存在风险，请联系客服。

    String X221022_CROSS_SELL_COIN = "x221022_cross_sell_coin";//	跨区卖币
    String X221022_SPOT_CROSS_SELL_COIN_ALERT = "x221022_spot_cross_sell_coin_alert";//	例如交易员在BTC/USDT买入BTC，然后跨区卖币为ETH，如果您开启该选项则代表跟随卖为ETH，如果关闭该选项则跟随卖为USDT。
    String x221020_with_order_management = "x221020_with_order_management";//	带单管理。
    String TEXT_PRESET_PROFILE_LOSS_TITLE = "text_preset_profile_loss_title";//   止盈止损 标题
    String TEXT_BUYING_RATE = "text_buying_rate"; //买入价
    String TEXT_PRESET_PROFILE_TITLE = "text_preset_profile_title"; //止盈
    String TEXT_PRESET_LOSS_TITLE = "text_preset_loss_title";//止损
    String X221028_FOLLOW_SPOT_SURE_SELL_MARKET_PRICE = "x221028_follow_spot_sure_sell_market_price"; // 您将以市价卖出
    String X221102_FOLLOW_SPOT_BUY_AVG_PRICE = "x221102_follow_spot_buy_avg_price";// 买入均价
    String X221028_FOLLOW_SPOT_SELL_PRICE = "x221028_follow_spot_sell_price";// 卖出价格
    String TEXT_CHOOSE_MARKET = "text_choose_market";//市价
    String X220801_Grid_Strategy = "x220801_grid_strategy";//网格策略
    String X220902_SHARE_GET_NET_DATA_FAIL = "x220902_share_get_net_data_fail";//获取分享数据失败
    String X220905_SHARE_FOLLOW_COPYING = "x220905_share_follow_copying";//	我正在带单
    String X220905_SHARE_FOLLOW_ATTENTION = "x220905_share_follow_attention";//我正在关注
    String X221110_FOLLOW_SPOT_BUY_PRICE = "x221110_follow_spot_buy_price";//	买入价格
    String X221110_FOLLOW_SPOT_UNUSABLE_HINT = "x221110_follow_spot_unusable_hint";//	现货跟单当前不可用，已为您切换到合约跟单

    String X221108_SPOT_FOLLOW_SPOT_RATE_CHART_TIPS = "x221108_spot_follow_spot_rate_chart_tips";//收益率\n收益率将分为已实现收益率、未实现收益率。\n\n该图表展示的是现货带单已实现收益率。\n已实现收益率 = 所有已卖出或已结束带单的总收益/历史最大投入本金\n\n未实现收益率：{0}
    String X221107_SPOT_FOLLOW_INCOME_CHART_TIPS = "x221107_spot_follow_income_chart_tips";//现货所有历史带单的盈亏总和(全部折合为USDT)。
    String X221107_SPOT_COIN_LIKE_CHART_TIPS = "x221107_spot_coin_like_chart_tips";//历史带单各现货交易对笔数占总笔数的百分比
    String X221107_SPOT_HOLD_TIME_CHART_TIPS = "x221107_spot_hold_time_chart_tips";//每笔历史带单持有的时长及盈亏(全部折合为USDT)。
    String X221107_SPOT_TRADE_VOL_CHART_TIPS = "x221107_spot_trade_vol_chart_tips";//现货所有历史带单的交易量总和(全部折合为USDT)。
    String X221107_SPOT_FOLLOW_ASSET_TIPS = "x221107_spot_follow_asset_tips";//跟随者视角/n数据为现货跟单的累计跟随收益，未减去交易手续费。\n\n交易员视角\n\n数据为现货跟单的带单分润，仅包含累计已收到的分润。
    String X221025_FOLLOW_SPOT_ASSET = "x221025_follow_spot_asset";//账户资金
    String X221110_FOLLOW_SPOT_TOTAL_HINT = "x221110_follow_spot_total_hint";//	汇总是将相同交易对的多条记录进行了合并 其中的买入均价、收益率均为汇总后的数据。
    String X221018_TEXT_TRACE_SPOT = "x221018_text_trace_spot";//带单币对
    String X221114_TEXT_FOLLOW_SPOT_PROFILE_RATE_CONTENT = "x221114_text_follow_spot_profile_rate_content"; //	*您的分润比例受BFT锁仓数量的影响，详情请查看“了解更多”
    String X221114_URL_FOLLOW_SPOT_SHARE_RATE = "x221114_url_follow_spot_share_rate";//=https://bitget.zendesk.com/hc/zh-cn/articles/360042661391
    String X221114_FOLLOW_INCOMING_SPOT = "x221114_follow_incoming_spot"; // 将带单获得的分润统一折合为USDT进行展示；预计待分润为动态值，当用户平仓一笔亏损的跟随订单后，预计待分润可能会减少
    String X221114_TODAY_INCOME_TIP_URL_SPOT = "x221114_today_income_tip_url_spot"; // 现货-待分润了解更多点击URL
    String X221114_FOLLOW_INCOME_SPOT_TOTAL = "x221114_follow_income_spot_total"; // 累计已分润是将带单获得的分润统一折合为USDT进行展示，实际发放分润时是用户跟随时使用的保证金币种。
    String X221114_FOLLOW_SPOT_ASSETS_DESC = "x221114_follow_spot_assets_desc"; //  现货跟单-①跟随所有交易员的已平仓订单的跟随本金",
    String X221114_FOLLOW_SPOT_REAL_INCOME_DESC = "x221114_follow_spot_real_income_desc"; // 现货跟单-"②跟随所有交易员已平仓订单的净利润(已减去开仓、平仓手续费)"
    String X221108_SPOT_FOLLOW_SPOT_ASSET_CHART_TIPS = "x221108_spot_follow_spot_asset_chart_tips"; // 现货账户中各类资产占总资产的比例，已折合为USDT进行计算。
    String X221114_FOLLOW_SPOT_FOLLOW_BUY_COUNT_HINT = "x221114_follow_spot_follow_buy_count_hint"; //此数量为已减去买入时的手续费。例如您买入1BTC，手续费0.0006BTC，此时该数量将显示为0.9994BTC。
    String X221114_FOLLOW_SPOT_FOLLOW_COINS_TITLE = "x221114_follow_spot_follow_coins_title"; //现货交易对
    String X221114_FOLLOW_SPOT_FOLLOW_COINS_HINT = "x221114_follow_spot_follow_coins_hint"; //交易员目前正在进行带单交易的现货交易对
    String X221205_HISTORY_FOLLOW_END_PRICE = "x221205_history_follow_end_price";//	结束价
    String X221205_HISTORY_FOLLOW_END_TIME = "x221205_history_follow_end_time";//结束时间
    String TEXT_OTC_NICKNAME_SETTING_TIP2 = "text_otc_nickname_setting_tip2";//最大长度为15个字符

    String MARKETS_MIX_LASTEDPRICE = "Markets_Mix_LastedPrice"; //最新价格
    String MARKETS_MIX_ETFPRICE = "Markets_Mix_etfPrice"; //指数价格
    String MARKETS_MIX_PRICERECORD = "Markets_Mix_priceRecord"; //价格记录
    String MARKETS_MIX_COMPONENTINFO = "Markets_Mix_componentInfo"; //成分信息
    String MARKETS_MIX_INFO_WEIGHT = "Markets_Mix_info_weight"; //权重(%)
    String MARKETS_MIX_LASTED_ETF_PRICE = "Markets_Mix_lasted_etf_price"; //"最新指数价格(USDT)",
    String MARKETS_MIX_24H_CHANGE = "Markets_Mix_24h_change"; // "24h涨跌",,
    String MARKETS_MIX_24H_ETFINDEX_CONTRIBUTION = "Markets_Mix_24h_etfIndex_contribution"; // "24h指数贡献",
    String X230111_SPOT_FOLLOW_HISTOTORY_END_HINT = "x230111_spot_follow_histotory_end_hint";//	该订单为结束类型，对应数量的{0}仍保留在现货账户

    String X221213_Strategy_Dca_Positive_Hint = "x221213_strategy_dca_positive_hint";//通过分批买入降低成本，反转卖出，上涨赚U。
    String X221213_Strategy_Dca_Reverse_Hint = "x221213_strategy_dca_reverse_hint";//通过分批卖出增加获利，反转买回，下跌赚币。
    String X221213_Strategy_Cca_Long_Hint = "x221213_strategy_dca_long_hint";//加杠杆的DCA策略，有效提高资金利用率，上涨赚U。
    String X221213_Strategy_Dca_Short_Hint = "x221213_strategy_dca_short_hint";//加杠杆的DCA策略，有效提高资金利用率，下跌赚U。

    String Kyc_Authentication_Error_Hint = "Kyc_Authentication_Error_Hint";//您的图片正在上传，请稍等
    String MARGIN_MARGINTRADE_RISK_TIP = "Margin_MarginTrade_Risk_Tip"; //风险提示
    String MARGIN_MARGINTRADE_ALL_POSITION = "Margin_MarginTrade_All_Position"; //全仓
    String MARGIN_MARGINTRADE_CHASE_POSITION = "Margin_MarginTrade_Chase_Position"; //逐仓
    String MARGIN_MARGINTRADE_RISK_RATE = "Margin_MarginTrade_Risk_rate"; //风险率
    String MARGIN_MARGINTRADE_AUTO_REPAY = "Margin_MarginTrade_Auto_Repay"; //自动还款
    String MARGIN_MARGINTRADE_AUTO_BORROW = "Margin_MarginTrade_Auto_Borrow"; //自动借款
    String MARGIN_MARGINTRADE_BORROW_REPAY = "Margin_MarginTrade_Borrow_Repay"; //借/还
    String MARGIN_MARGINTRADE_BORROWING = "Margin_MarginTrade_Borrowing"; //借款
    String MARGIN_MARGINTRADE_REPAYING = "Margin_MarginTrade_Repaying"; //还款
    String MARGIN_MARGINTRADE_POSITIONS = "Margin_MarginTrade_Positions"; //仓位
    String MARGIN_MARGINTRADE_OPENORDERS = "Margin_MarginTrade_OpenOrders"; //开仓订单
    String MARGIN_MARGINTRADE_FUNDS = "Margin_MarginTrade_Funds"; //基金
    String MARGIN_MARGINTRADEDIALOG_AUTO_REPAY_DESC = "Margin_MarginTradeDialog_Auto_Repay_Desc"; //在您现货交易成功后，系统将会自动把现货交易所得的币种数量偿还对应币种的负债。
    String MARGIN_MARGINTRADEDIALOG_AUTO_BORROW_DESC = "Margin_MarginTradeDialog_Auto_Borrow_Desc"; //系统将自动以您的名义借贷来执行订单，自动借款订单一旦下单成功，无论现货交易是否成交，都将开始计算利息；如果现货订单撤销，系统不会自动帮您还款，需要您手动还款。
    String MARGIN_MARGINTRADE_NORMAL_MODE = "Margin_MarginTrade_Normal_Mode"; //普通模式
    String MARGIN_MARGINTRADEDIALOG_NORMAL_MODE_DESC = "Margin_MarginTradeDialog_Normal_Mode_Desc"; //所有交易规则都与现货交易规则相同。
    String MARGIN_MARGINTRADE_FAILS = "Margin_MarginTrade_Fails"; //交易失败
    String MARGIN_MARGINTRADE_FAILS_REASON = "Margin_MarginTrade_Fails_Reason"; //由于以下原因您无法提交订单，请重试：
    String MARGIN_MARGINTRADE_EMPTY_DESC = "Margin_MarginTrade_Empty_Desc"; //哎呀，保证金账户里没有余额了
    String MARGIN_MARGINTRADE_POSITION_LIST_COIN = "Margin_MarginTrade_Position_List_Coin"; //币
    String MARGIN_MARGINTRADE_POSITION_LIST_POSITION = "Margin_MarginTrade_Position_List_Position"; //仓位
    String Margin_MarginTrade_Hold_Positions = "Margin_MarginTrade_Hold_Positions"; // 持仓
    String MARGIN_MARGINTRADE_POSITION_LIST_LIQPRICE = "Margin_MarginTrade_Position_List_LiqPrice"; //预计强平价
    String MARGIN_MARGINTRADE_POSITION_LIST_INDEXPRICE = "Margin_MarginTrade_Position_List_IndexPrice"; //价格指数
    String MARGIN_MARGINTRADE_ORDERS_LIST_PRICE = "Margin_MarginTrade_Orders_List_Price"; //价格
    String MARGIN_MARGINTRADE_ORDERS_LIST_AMOUNT = "Margin_MarginTrade_Orders_List_Amount"; //数量
    String MARGIN_MARGINTRADE_ORDERS_LIST_TOTAL = "Margin_MarginTrade_Orders_List_Total"; //总数
    String MARGIN_MARGINTRADE_FUNDS_ALL_LIST_TOTAL = "Margin_MarginTrade_Funds_All_List_Total"; //总数
    String MARGIN_MARGINTRADE_FUNDS_ALL_LIST_AVAILABLE = "Margin_MarginTrade_Funds_All_List_Available"; //可用
    String MARGIN_MARGINTRADE_FUNDS_ALL_LIST_DEBT = "Margin_MarginTrade_Funds_All_List_Debt"; //欠款
    String MARGIN_MARGINTRADE_FUNDS_CHASE_LIST_COIN = "Margin_MarginTrade_Funds_Chase_List_Coin"; //币种
    String MARGIN_MARGINTRADE_FUNDS_CHASE_LIST_TOTAL = "Margin_MarginTrade_Funds_Chase_List_Total"; //总数
    String MARGIN_MARGINTRADE_FUNDS_CHASE_LIST_DEBT = "Margin_MarginTrade_Funds_Chase_List_Debt"; //欠款

    String MARGIN_MARGINTRADE_MENU_TRANSFER = "Margin_MarginTrade_Menu_Transfer"; // 资金划转
    String MARGIN_MARGINTRADE_MENU_BORROW = "Margin_MarginTrade_Menu_Borrow"; // 借款
    String MARGIN_MARGINTRADE_MENU_REPAY = "Margin_MarginTrade_Menu_Repay"; // 还款
    String MARGIN_MARGINTRADE_MENU_MARGIN_SET = "Margin_MarginTrade_Menu_Margin_Set"; // 杠杆设置
    String MARGIN_MARGINTRADE_MENU_CALCULATOR = "Margin_MarginTrade_Menu_Calculator"; // 计算器

    String MARGIN_MARGINTRADE_MENU_TUTORIAL = "Margin_MarginTrade_Menu_Tutorial"; // 使用说明


    String MARGIN_MARGINTRADE_LIST_SEARCH = "Margin_MarginTrade_List_Search"; //搜索
    String MARGIN_MARGINTRADE_LIST_FAVORITES = "Margin_MarginTrade_List_Favorites"; //自选
    String MARGIN_MARGINTRADE_LIST_COIN_VOLUME = "Margin_MarginTrade_List_Coin_Volume"; //币种/交易量
    String MARGIN_MARGINTRADE_LIST_LAST_PRICE = "Margin_MarginTrade_List_Last_Price"; //最新价
    String MARGIN_MARGINTRADE_LIST_CHANGE_RASE = "Margin_MarginTrade_List_Change_Rase"; //涨跌幅
    String MARGIN_BORROWPAGE_BORROW_TITLE = "Margin_BorrowPage_Borrow_Title"; //借款标题
    String MARGIN_BORROWPAGE_BORROWING_ACCOUNT = "Margin_BorrowPage_Borrowing_Account"; //借款账户
    String MARGIN_BORROWPAGE_HOURLY_INTEREST_RATE = "Margin_BorrowPage_Hourly_Interest_Rate"; //小时利率
    String MARGIN_BORROWPAGE_CROSS_MARGIN = "Margin_BorrowPage_Cross_Margin"; //全仓杠杆
    String MARGIN_BORROWPAGE_AVAILABLE_BALANCE = "Margin_BorrowPage_Available_Balance"; //可用余额
    String MARGIN_BORROWPAGE_BORROWED = "Margin_BorrowPage_Borrowed"; //借款
    String MARGIN_BORROWPAGE_MAXIMUM_BORROW_AMOUNT = "Margin_BorrowPage_Maximum_Borrow_Amount"; //最大借款金额
    String MARGIN_BORROWPAGE_WANT_BORROW = "Margin_BorrowPage_Want_Borrow"; //我想借款
    String MARGIN_BORROWPAGE_ENTER_AMOUNT = "Margin_BorrowPage_Enter_Amount"; //输入借款金额
    String MARGIN_BORROWPAGE_MAX_TITLE = "Margin_BorrowPage_Max_Title"; //最大值
    String MARGIN_BORROWPAGE_CONFIRM = "Margin_BorrowPage_Confirm"; //确认
    String MARGIN_REPAYPAGE_REPAY_TITLE = "Margin_RepayPage_Repay_Title"; //还款
    String MARGIN_REPAYPAGE_INTEREST = "Margin_RepayPage_Interest"; //还款利息
    String MARGIN_REPAYPAGE_TOTAL_DEBT = "Margin_RepayPage_Total_Debt"; //债务总额
    String MARGIN_REPAYPAGE_REPAY_AMOUNT = "Margin_RepayPage_Repay_Amount"; //还款金额
    String MARGIN_REPAYPAGE_ISOLATE_MARGIN = "Margin_RepayPage_Isolate_Margin";//逐仓杠杆
    String MARGIN_REPAYPAGE_BORROWING_ACCOUNT = "Margin_RepayPage_Borrowing_Account"; //借款账户
    String MARGIN_REPAYPAGE_AVAILABLE_BALANCE = "Margin_RepayPage_Available_Balance"; //可用余额
    String MARGIN_REPAYPAGE_BORROWED = "Margin_RepayPage_Borrowed"; //借款
    String MARGIN_REPAYPAGE_ENTER_AMOUNT = "Margin_RepayPage_Enter_Amount"; //输入金额
    String MARGIN_REPAYPAGE_MAX_TITLE = "Margin_RepayPage_Max_Title"; //最大值
    String MARGIN_REPAYPAGE_CONFIRM = "Margin_RepayPage_Confirm"; //确认
    String MARGIN_BORROWREPAYPAGE_CHOOSE_CURRENCY = "Margin_BorrowRepayPage_Choose_Currency"; //选择币对
    String MARGIN_BORROWREPAYPAGE_CHOOSE_CURRENCY_SEARCH = "Margin_BorrowRepayPage_Choose_Currency_Search"; //搜索
    String MARGIN_BORROWREPAYPAGE_CHOOSE_COIN = "Margin_BorrowRepayPage_Choose_Coin"; //选择币种
    String MARGIN_BORROWREPAYPAGE_CHOOSE_COIN_SEARCH = "Margin_BorrowRepayPage_Choose_Coin_search"; //搜索
    String MARGIN_MARGINDATA_PAGE_TITLE = "Margin_MarginData_Page_Title"; //杠杆数据
    String MARGIN_MARGINDATA_ALL_TITLE = "Margin_MarginData_All_Title"; //全仓利率
    String MARGIN_MARGINDATA_SEARCH = "Margin_MarginData_Search"; //搜索
    String MARGIN_MARGINDATA_ALL_ROLL_IN = "Margin_MarginData_All_Roll_In"; //可转入
    String MARGIN_MARGINDATA_ALL_BORROW = "Margin_MarginData_All_Borrow"; //可借贷
    String MARGIN_MARGINDATA_ALL_DAYORYEAR = "Margin_MarginData_All_DayOrYear"; //日利率/年利率
    String MARGIN_MARGINDATA_ALL_QUOTA = "Margin_MarginData_All_Quota"; //限额
    String MARGIN_MARGINDATA_CHASE_TITLE = "Margin_MarginData_Chase_Title"; //逐仓利率
    String MARGIN_MARGINDATA_CHASE_LEVER_MULRIPLE = "Margin_MarginData_Chase_Lever_Mulriple"; //杠杆倍数
    String MARGIN_MARGINDATA_CHASE_ROLL_IN = "Margin_MarginData_Chase_Roll_In"; //可转入
    String MARGIN_MARGINDATA_CHASE_BORROW = "Margin_MarginData_Chase_Borrow"; //可借贷
    String MARGIN_MARGINDATA_CHASE_DAYORYEAR = "Margin_MarginData_Chase_DayOrYear"; //日利率/年利率
    String MARGIN_MARGINDATA_CHASE_QUOTA = "Margin_MarginData_Chase_Quota"; //限额
    String MARGIN_MARGINDATA_GEARS_TITLE = "Margin_MarginData_Gears_Title"; //逐仓档位
    String MARGIN_MARGINDATA_GEARS_LEVEL = "Margin_MarginData_Gears_Level"; //档位
    String MARGIN_MARGINDATA_GEARS_VALID_LEVER = "Margin_MarginData_Gears_Valid_Lever"; //有效杠杆倍数
    String MARGIN_MARGINDATA_GEARS_DEPOSIT_RATE = "Margin_MarginData_Gears_Deposit_Rate"; //维持保证金率
    String MARGIN_MARGINDATA_GEARS_VALUATION_MAX_BORROW = "Margin_MarginData_Gears_Valuation_Max_Borrow"; //计价币最大可借
    String MARGIN_MARGINDATA_GEARS_BASIC_MAX_BORROW = "Margin_MarginData_Gears_Basic_Max_Borrow"; //基础币最大可借
    String MARGIN_MARGINASSETS_PANDECT_ALL_LEVER = "Margin_MarginAssets_Pandect_All_Lever"; //全仓杠杆
    String MARGIN_MARGINASSETS_PANDECT_CHASE_LEVER = "Margin_MarginAssets_Pandect_Chase_Lever"; //逐仓杠杆
    String MARGIN_MARGINASSETS_LEVER_TITLE = "Margin_MarginAssets_Lever_Title"; //杠杆
    String MARGIN_MARGINASSETS_LEVER_VALUATION = "Margin_MarginAssets_Lever_Valuation"; //杠杆总资产估值
    String MARGIN_MARGINASSETS_LEVER_ALL = "Margin_MarginAssets_Lever_All"; //全仓
    String MARGIN_MARGINASSETS_LEVER_CHASE = "Margin_MarginAssets_Lever_Chase"; //逐仓
    String MARGIN_MARGINASSETS_LEVER_ALL_ASSETS_TOTAL = "Margin_MarginAssets_Lever_All_Assets_Total"; //全仓资产大约总额
    String MARGIN_MARGINASSETS_LEVER_ALL_TRANSFER = "Margin_MarginAssets_Lever_All_Transfer"; //划转
    String MARGIN_MARGINASSETS_LEVER_ALL_BORROW = "Margin_MarginAssets_Lever_All_Borrow"; //借款
    String MARGIN_MARGINASSETS_LEVER_ALL_REPAY = "Margin_MarginAssets_Lever_All_Repay"; //还款
    String MARGIN_MARGINASSETS_CHASE_ALL_ASSETS_TOTAL = "Margin_MarginAssets_Chase_All_Assets_Total"; //逐仓资产大约总额
    String MARGIN_MARGINASSETSDETAILS_TOTAL = "Margin_MarginAssetsDetails_Total"; //总额
    String MARGIN_MARGINASSETSDETAILS_VALID_BALANCE = "Margin_MarginAssetsDetails_Valid_Balance"; //可用余额
    String MARGIN_MARGINASSETSDETAILS_FREEZE = "Margin_MarginAssetsDetails_Freeze"; //冻结
    String MARGIN_MARGINASSETSDETAILS_BORROWED = "Margin_MarginAssetsDetails_Borrowed"; //已借
    String MARGIN_MARGINASSETSDETAILS_INTEREST = "Margin_MarginAssetsDetails_Interest"; //利息
    String MARGIN_MARGINASSETSDETAILS_NET_ASSET = "Margin_MarginAssetsDetails_Net_Asset"; //净资产
    String MARGIN_MARGINASSETSDETAILS_TRADE = "Margin_MarginAssetsDetails_Trade"; //去交易
    String MARGIN_MARGINASSETSDETAILS_MORE = "Margin_MarginAssetsDetails_More"; //更多
    String MARGIN_MARGINASSETSDETAILS_TRANSFER = "Margin_MarginAssetsDetails_Transfer"; //划转
    String MARGIN_MARGINASSETSDETAILS_BORROW = "Margin_MarginAssetsDetails_Borrow"; //借款
    String MARGIN_MARGINASSETSDETAILS_REPAY = "Margin_MarginAssetsDetails_Repay"; //还款
    String MARGIN_MARGINTRADE_ALTER_TITLE = "Margin_MarginTrade_Alter_Title"; //订单确认
    String MARGIN_MARGINTRADE_ALTER_TYPE = "Margin_MarginTrade_Alter_Type"; //类型
    String MARGIN_MARGINTRADE_ALTER_BUYING_PRICE = "Margin_MarginTrade_Alter_Buying_Price"; //买入价格
    String MARGIN_MARGINTRADE_ALTER_AMOUNT = "Margin_MarginTrade_Alter_Amount"; //数量
    String MARGIN_MARGINTRADE_ALTER_BORROWED = "Margin_MarginTrade_Alter_Borrowed"; //借款
    String MARGIN_MARGINTRADE_ALTER_TOTAL = "Margin_MarginTrade_Alter_Total"; //总计
    String MARGIN_MARGINTRADE_ALTER_DESC = "Margin_MarginTrade_Alter_Desc"; //不再显示。稍后在“首选项设置”中更改
    String MARGIN_MARGINTRADE_ORDER_TYPE = "Margin_MarginTrade_Order_Type"; //订单类型
    String MARGIN_MARGINTRADE_TAB_TITLE = "Margin_MarginTrade_Tab_title"; //Margin
    String MARGIN_MARGINTRADE_ORDER_CANCEL_ALL = "Margin_MarginTrade_Order_Cancel_All"; //全部取消
    String MARGIN_MARGINTRADEDIALOG_MARGIN_MODE = "Margin_MarginTradeDialog_Margin_Mode"; //模式
    String MARGIN_MARGINTRADE_GUIDE_TRANSFER_TITLE = "Margin_MarginTrade_Guide_Transfer_Title"; //划转
    String MARGIN_MARGINTRADE_GUIDE_TRANSFER_CONTENT = "Margin_MarginTrade_Guide_Transfer_Content"; //请将资产转移到您的保证金帐户。
    String MARGIN_MARGINTRADE_GUIDE_TRANSFER_DESC = "Margin_MarginTrade_Guide_Transfer_Desc"; //我对此很熟悉，不需要任何指导。跳过
    String MARGIN_MARGINTRADE_GUIDE_BORROW_TITLE = "Margin_MarginTrade_Guide_Borrow_Title"; //借款
    String MARGIN_MARGINTRADE_GUIDE_BORROW_CONTENT = "Margin_MarginTrade_Guide_Borrow_Content"; //在你转移存款后，借款就开始了。
    String MARGIN_MARGINTRADE_GUIDE_BORROW_DESC = "Margin_MarginTrade_Guide_Borrow_Desc"; //我对此很熟悉，不需要任何指导。跳过
    String MARGIN_MARGINTRADE_GUIDE_AUTO_BORROW_TITLE = "Margin_MarginTrade_Guide_Auto_Borrow_Title"; //自动借款
    String MARGIN_MARGINTRADE_GUIDE_AUTO_BORROW_CONTENT = "Margin_MarginTrade_Guide_Auto_Borrow_Content"; //您可以在此处选择自动借款。
    String MARGIN_MARGINTRADE_GUIDE_AUTO_BORROW_DESC = "Margin_MarginTrade_Guide_Auto_Borrow_Desc"; //我对此很熟悉，不需要任何指导。跳过
    String MARGIN_MARGINTRADE_GUIDE_AUTO_REPAY_TITLE = "Margin_MarginTrade_Guide_Auto_Repay_Title"; //自动还款
    String MARGIN_MARGINTRADE_GUIDE_AUTO_REPAY_CONTENT = "Margin_MarginTrade_Guide_Auto_Repay_Content"; //您可以在此处选择自动还款。
    String MARGIN_MARGINTRADE_GUIDE_AUTO_REPAY_DESC = "Margin_MarginTrade_Guide_Auto_Repay_Desc"; //我对此很熟悉，不需要任何指导。跳过
    String MARGIN_MARGINTRADE_GUIDE_NEXT = "Margin_MarginTrade_Guide_Next"; //下一步
    String MARGIN_MARGINTRADE_GUIDE_TRADING = "Margin_MarginTrade_Guide_Trading"; //交易

    String MARGIN_MARGINTRADE_EXPLAINDIALOG_INDEX_PRICE = "Margin_MarginTrade_ExplainDialog_Index_Price"; //指数价格
    String MARGIN_MARGINTRADE_EXPLAINDIALOG_INDEX_PRICE_CONTENT = "Margin_MarginTrade_ExplainDialog_Index_Price_Content"; //保证金交易价格指数的计算方法与期货合约价格指数相同。价格指数是主要现货市场交易所的一桶价格，按其相对成交量加权。
    String MARGIN_MARGINTRADE_EXPLAINDIALOG_LIQUIDATION_PRICE = "Margin_MarginTrade_ExplainDialog_Liquidation_Price"; //强平价格
    String MARGIN_MARGINTRADE_EXPLAINDIALOG_LIQUIDATION_PRICE_CONTENT = "Margin_MarginTrade_ExplainDialog_Liquidation_Price_Content"; //每当价格波动导致头寸的风险率大于1时，我们的系统将自动为您清算一些资产以降低风险率。清算价格是我们预测的近似价格，供您参考。
    String MARGIN_BORROWREPAYPAGE_BORROW_SUCCESS_TOAST = "Margin_BorrowRepayPage_Borrow_success_toast"; //交易成功， 账户余额已更新
    String MARGIN_BORROWREPAYPAGE_BORROW_EXCEED_TOAST = "Margin_BorrowRepayPage_Borrow_exceed_toast"; //超过最大可借额度
    String MARGIN_BORROWREPAYPAGE_BORROW_NOT_BOND_TIP_TITLE = "Margin_BorrowRepayPage_Borrow_not_bond_tip_title"; //账户内无保证金
    String MARGIN_BORROWREPAYPAGE_BORROW_NOT_BOND_TIP_CONTENT = "Margin_BorrowRepayPage_Borrow_not_bond_tip_content"; //{0}杠杆账户内无保证金，请先划转。
    String MARGIN_BORROWREPAYPAGE_BORROW_NOT_BOND_TIP_TRANSFER = "Margin_BorrowRepayPage_Borrow_not_bond_tip_transfer"; //去划转
    String MARGIN_BORROWREPAYPAGE_REPAY_SUCCESS_TOAST = "Margin_BorrowRepayPage_Repay_success_toast"; //还款成功
    String MARGIN_BORROWREPAYPAGE_REPAY_SUSPENSION_TOAST = "Margin_BorrowRepayPage_Repay_suspension_toast"; //强平中禁止操作
    String MARGIN_BORROWREPAYPAGE_REPAY_NOT_BORROW_TOAST = "Margin_BorrowRepayPage_Repay_not_borrow_toast"; //尚无借款
    String MARGIN_MARGINTRADE_BORROW_DESC = "Margin_MarginTrade_Borrow_Desc"; //本次下单的金额中需要自动借款的金额
    String MARGIN_MARGINTRADE_REPAY_DESC = "Margin_MarginTrade_Repay_Desc"; //本次下单成交后自动还款的金额
    String MARGIN_MARGINTRADE_CANUSE_DESC = "Margin_MarginTrade_CanUse_Desc"; //本次下单的金额中可以直接使用的余额

    String MARGIN_MARGINASSET_PRIVACYDIALOG_TITLE = "Margin_MarginAsset_PrivacyDialog_Title"; //未开通
    String MARGIN_MARGINASSET_PRIVACYDIALOG_CONTENT = "Margin_MarginAsset_PrivacyDialog_Content"; //杠杆账户尚未开通。
    String MARGIN_MARGINASSET_PRIVACYDIALOG_OPEN_BTN = "Margin_MarginAsset_PrivacyDialog_Open_Btn"; //去开通
    String MARGIN_MARGINASSET_PRIVACYWEBVIEW_TITLE = "Margin_MarginAsset_PrivacyWebView_Title"; //风险提示
    String MARGIN_MARGINTRADE_TRANSLATE_TIP_1 = "Margin_MarginTrade_Translate_Tip_1";
    String MARGIN_MARGINTRADE_TRANSLATE_TIP_2 = "Margin_MarginTrade_Translate_Tip_2";
    String Assets_Transfer_Cross_Tip = "Assets_Transfer_Cross_Tip";
    String Assets_Transfer_Isolated_Tip = "Assets_Transfer_Isolated_Tip";

    String MARGIN_MARGINTRADE_NO_SUPPORT_CHILD_ACCOUNT = "Margin_MarginTrade_No_Support_Child_Account"; //暂不支持子账户使用杠杆服务。

    String MARGIN_SWITCHPOSITION_TITLE = "Margin_SwitchPosition_Title"; //{0}保证金模式。
    String MARGIN_SWITCHPOSITION_DESC = "Margin_SwitchPosition_Desc"; //调整后仅对当前仓位模式的订单生效
    String MARGIN_SWITCHPOSITION_POSITION_DESC = "Margin_SwitchPosition_Position_Desc"; //什么是全仓和逐仓模式？
    String MARGIN_SWITCHPOSITION_MODE_CROSS = "Margin_SwitchPosition_Mode_Cross"; //全仓模式：
    String MARGIN_SWITCHPOSITION_MODE_ISOLATE = "Margin_SwitchPosition_Mode_Isolate"; //全仓模式：
    String MARGIN_SWITCHPOSITION_MODE_CROSS_DESC = "Margin_SwitchPosition_Mode_Cross_Desc"; //所有币种的仓位共用全仓杠杆账户中的保证金来避免仓位被强平。在强平事件中，交易者可能会损失所有的保证金和仓位。
    String MARGIN_SWITCHPOSITION_MODE_ISOLATE_DESC = "Margin_SwitchPosition_Mode_Isolate_Desc"; //每个交易对为一个独立保证金账户，同一个交易对的左币和右币共用保证金。在强平事件中，交易者可能会损失爆仓交易对中的保证金和仓位，但不会对其他未爆仓交易对的仓位产生任何影响。

    String X230210_HOME_PAGE_INNOVATE_TITLE = "x230210_home_page_innovate_title";  //创新区

    String USER_SUBPARENT_MAIN_ACCOUNT = "User_Subparent_main_account";  //主账号
    String USER_SUBPARENT_SUB_ACCOUNT = "User_Subparent_sub_account";  //子账号
    String USER_SUBPARENT_CHANGE_ACCOUNT = "User_Subparent_change_account";  //切换账号
    String USER_SUBPARENT_SEARCH = "User_Subparent_search";  //搜索
    String USER_SUBPARENT_ACCOUNT_LOGIN_TIPS = "User_Subparent_Account_login_tips";  //虚拟邮箱子账户禁止直接登陆，可登陆主账户后通过“切换账户”功能登陆
    String USER_PERSONAL_CENTER_UID = "User_PersonalCenter_Uid";
    String USER_PERSONAL_CENTER_PREFERENCES = "User_PersonalCenter_Preferences";
    String USER_PERSONAL_CENTER_FEES = "User_PersonalCenter_Fees";
    String USER_PERSONAL_CENTER_SETTINGS = "User_PersonalCenter_Settings";
    String USER_PERSONAL_CENTER_REWARDS_CENTER = "User_PersonalCenter_RewardsCenter";
    String USER_PERSONAL_CENTER_VIP_PROGRAM = "User_PersonalCenter_VipProgram";
    String USER_PERSONAL_CENTER_BONUS_MISSIONS = "User_PersonalCenter_BonusMissions";

    String User_PersonalCenter_My_Events = "User_PersonalCenter_My_Events";
    String USER_PERSONAL_CENTER_MY_COMPETITIONS = "User_PersonalCenter_MyCompetitions";
    String USER_PERSONAL_CENTER_SHARE_APP = "User_PersonalCenter_ShareApp";
    String USER_PERSONAL_CENTER_HELP_CENTER = "User_PersonalCenter_HelpCenter";
    String USER_PERSONAL_CENTER_USE_SYSTEM_THEME = "User_PersonalCenter_UseSystemTheme";
    String User_PersonalCenter_HomeSetting = "User_PersonalCenter_HomeSetting";
    String User_PersonalCenter_SystemSetting = "User_PersonalCenter_SystemSetting";
    String User_PersonalCenter_Language = "User_PersonalCenter_Language";

    String CONVERT_TAB_TITLE = "Convert_Tab_Title";  // Convert(闪兑)
    String CONVERT_HOME_TITLE = "Convert_Home_Title";  // Spot Account
    String CONVERT_HOME_FROM = "Convert_Home_From";  // From
    String CONVERT_HOME_TO = "Convert_Home_To";  // To
    String CONVERT_HOME_ADD_FUNDS = "Convert_Home_AddFunds";  // Add Funds
    String CONVERT_HOME_PREVIEW_RATES = "Convert_Home_PreviewRates";  // preview Rates
    String CONVERT_HOME_TO_SWAP = "Convert_Home_ToSwap";  // Use on-chain instead
    String CONVERT_HOME_ADDFUNDS_TITLE = "Convert_Home_AddFunds_Title";  // Add Funds
    String CONVERT_HOME_ADDFUNDS_TRANSFER = "Convert_Home_AddFunds_Transfer";  // Transfer
    String CONVERT_HOME_ADDFUNDS_DEPOSIT = "Convert_Home_AddFunds_Deposit";  // Deposit
    String CONVERT_HOME_ADDFUNDS_BUY = "Convert_Home_AddFunds_Buy";  // Buy
    String CONVERT_HOME_MAX = "Convert_Home_Max";  // Max
    String CONVERT_HOME_TIPS_INSUFFICIENT = "Convert_Home_Tips_Insufficient";  // Insufficient account balance
    String CONVERT_HOME_BALANCE = "Convert_Home_Balance";  //  Balance: {0}


    String CONVERT_HOME_LOGIN = "Convert_Home_Login";  //  Login
    String CONVERT_HOME_INPUT_TOAST = "Convert_Home_Input_Toast";  //  请输入金额
    String CONVERT_HOME_PREVIEWRESULT = "Convert_Home_PreviewResult";  //  Preview the exchange result


    String ASSETS_ADDRESS_ADD_USER_ID = "Assets_AddressAdd_UserID";
    String VIEW_REMINDERS = "view_Reminders";
    String VIEW_DEEPLINK_HINT_TITLE = "view_DeeplinkHint_title";
    String VIEW_DEEPLINK_HINT = "view_DeeplinkHint";
    String Login_View_IP_Limit_Hint = "Login_View_IP_Limit_Hint";  //抱歉，您的IP可能受限

    String MARGIN_MARGINTRADE_NOTSUPPORT = "Margin_MarginTrade_NotSupport";   //{0}该币对暂时不支持杠杆交易
    String MARGIN_CHANGEPOSITION_NOTSUPPORT = "Margin_ChangePosition_NotSupport"; // 暂时不支持
    String TEXT_SELECT_SPOTMARGIN_LIST = "Text_Select_SpotMargin_List"; // 杠杆列表

    String ASSETS_ADDRESS_ADD_ORDINARY_TITLE_TEXT = "Assets_AddressAdd_Ordinary_Title_Text";//=普通地址
    String ASSETS_ADDRESS_ADD_INNER_TITLE_TEXT = "Assets_AddressAdd_Inner_Title_Text";//=内部地址
    String ASSETS_ADDRESS_ADD_NETWORK = "Assets_AddressAdd_Network";
    String ASSETS_ADDRESS_ADD_NETWORK_PLACEHOLDER = "Assets_AddressAdd_Network_PlaceHolder";
    String ASSETS_ADDRESS_ADD_ADDRESS = "Assets_AddressAdd_Address";
    String ASSETS_ADDRESS_ADD_ADDRESS_PLACEHOLDER = "Assets_AddressAdd_Address_PlaceHolder";
    String ASSETS_ADDRESS_ADD_ADD_NOTES_PLACEHOLDER = "Assets_AddressAdd_Add_Notes_PlaceHolder";
    String ASSETS_ADDRESS_ADD_ACCOUNT_TYPE = "Assets_AddressAdd_AccountType";
    String ASSETS_ADDRESS_ADD_MOBILE = "Assets_AddressAdd_Mobile";
    String ASSETS_ADDRESS_ADD_ACCOUNT = "Assets_AddressAdd_Account";
    String ASSETS_ADDRESS_ADD_EMAIL_PLACEHOLDER = "Assets_AddressAdd_EmailPlaceHolder";
    String Futures_Main_CurrentFollowOrder = "Futures_Main_CurrentFollowOrder";

    String COPYTRADE_TRADERPAGE_FUTURE_TOTAL_EQUITY = "CopyTrade_TraderPage_FutureTotalEquity"; //收益率
    String COPYTRADE_VIEW_TEXTJOINED = "CopyTrade_view_TextJoined"; //=已入驻 bg_param_1 天
    String COPYTRADE_TRADERPAGE_FUTURE_INCOME_TOTAL = "CopyTrade_TraderPage_FutureIncomeTotal"; // 累计收益
    String COPYTRADE_TRADERPAGE_TOTAL_DEAL_COUNT = "CopyTrade_TraderPage_TotalDealCount"; // 总笔数

    String COPYTRADE_TRADERPAGE_INFO_DATA = "CopyTrade_TraderPage_InfoData";//	数据
    String COPYTRADE_TRADERPAGE_COIN_ORDER_TITLE = "CopyTrade_TraderPage_CoinOrderTitle";//=订单
    String COPYTRADE_TRADERPAGE_DATA_TITLE = "CopyTrade_TraderPage_DataTitle";//=交易数据
    String CopyTrade_view_CurrentCarrayOrder = "CopyTrade_view_CurrentCarrayOrder";//当前带单
    String CopyTrade_TraderPage_OpenPrice = "CopyTrade_TraderPage_OpenPrice";//=开仓价
    String COPYTRADE_VIEW_INCOME_RATE = "CopyTrade_view_IncomeRate";//收益率
    String COPYTRADE_TRADERPAGE_FOLLOWER = "CopyTrade_TraderPage_Follower";//=跟随者
    String COPYTRADE_PERSONALSET_TEXT_AVATAR = "CopyTrade_PersonalSet_TextAvatar";//头像
    String COPYTRADE_PERSONALSET_TEXT_NICKNAME = "CopyTrade_PersonalSet_TextNickname";//带单个人设置昵称
    String COPYTRADE_PERSONALSET_TEXT_FOLLOW_INFO = "CopyTrade_PersonalSet_FollowInfo";//跟单信息
    String COPYTRADE_PERSONALSET_CONTRACT_AUTOCANCLE_FOLLOW = "CopyTrade_PersonalSet_ContractAutoCancleFollow";//合约自动取消跟随
    String COPYTRADE_PERSONALSET_SPOT_AUTOCANCLE_FOLLOW = "CopyTrade_PersonalSet_SpotAutoCancleFollow";//合约自动取消跟随
    String COPYTRADE_PERSONALSET_SPOT_CROSS_SELL_COIN = "CopyTrade_PersonalSet_SpotCrossSellCoin";//现货跨区卖币
    String COPYTRADE_TRADERPAGE_EMPTY_REMIND = "CopyTrade_TraderPage_EmptyRemind";//=空位提醒
    String COPYTRADE_PERSONALSET_PERMISSION_CAMERA_HINT = "CopyTrade_PersonalSet_PermissionCameraHint";//	无法获取摄像头数据，请在手机权限管理中打开Bitget的摄像头权限
    String COPYTRADE_PERSONALSET_SETNICKNAME = "CopyTrade_PersonalSet_SetNickName";//	昵称设置
    String COPYTRADE_VIEW_COPYTRADING = "CopyTrade_view_CopyTrading";//	跟单
    String COPYTRADE_VIEW_MYCOPY = "CopyTrade_view_MyCopy";//	我的跟单

    String COPYTRADE_COPYHOME_VIEWINCOME = "CopyTrade_CopyHome_ViewIncome";//=我的跟单
    String COPYTRADE_COPYHOME_BECOMETRADER = "CopyTrade_CopyHome_BecomeTrader";//=成为交易员
    String COPYTRADE_COPYHOME_STARTTRADING = "CopyTrade_CopyHome_StartTrading";//=即可开启带单
    String COPYTRADE_COPYHOME_TRADERLISTTITLE = "CopyTrade_CopyHome_TraderListTitle";//=即可开启带单
    String COPYTRADE_VIEW_USERSETTING = "CopyTrade_view_UserSetting";//设置
    String COPYTRADE_FOLLOWERPAGE_COPYDATA = "CopyTrade_FollowerPage_CopyData";//=跟单数据
    String COPYTRADE_VIEW_NETPROFITUNIT = "CopyTrade_view_NetProfitUnit";//=跟单净利润（{0}）
    String CopyTrade_view_NetProfit = "CopyTrade_view_NetProfit";//=跟单净利润
    String COPYTRADE_VIEW_CURRENTCOPYORDER = "CopyTrade_view_CurrentCopyOrder";//当前跟单
    String StrategicTrading_Share_Trader_Pnl = "StrategicTrading_Share_Trader_Pnl";//订阅者总收益
    String StrategicTrading_View_Days_Time = "StrategicTrading_View_Days_Time";//{0}d
    String StrategicTrading_View_Hours_Time = "StrategicTrading_View_Hours_Time";//{0}h
    String StrategicTrading_View_Minute_Time = "StrategicTrading_View_Minute_Time";//{0}m

    /**
     * ======Spot词条更新Start============
     */
    String SPOT_MORE_SETTING = "Spot_More_Setting";
    String SPOT_MORE_ADDFAVORITE = "Spot_More_AddFavorite";
    String SPOT_MORE_DELETEFAVORITE = "Spot_More_DeleteFavorite";
    String SPOT_MORE_PERCENTSETTING = "Spot_More_PercentSetting";
    String SPOT_MORE_TRADINGINFORMATION = "Spot_More_TradingInformation";
    String SPOT_ADVANCEDSETTING_GTC_TITLE = "Spot_AdvancedSetting_GTC_Title";
    String SPOT_TRADE_EXPIRATE = "Spot_Trade_Expirate";
    /**======Spot词条更新End============*/

    /**
     * ======公共词条 Start============
     */
    String view_EmptyNoData = "view_EmptyNoData"; //text_empty_no_data
    String view_LoadMoreNoData = "view_LoadMoreNoData"; //no_more_data
    String view_PermissionHint = "view_PermissionHint"; //high_permission_hint
    String view_GoToSetting = "view_GoToSetting"; //text_bund_pwd_go_to_set
    String view_SubmitSuccess = "view_SubmitSuccess";
    String view_OK = "view_OK";
    String view_Cancel = "view_Cancel";
    /**
     * ======公共词条 结束============
     */

    String Safe_Bund_Pwd_Title = "Safe_Bund_Pwd_Title";//资金密码
    String Safe_Bund_Pwd_Input_Hint = "Safe_Bund_Pwd_Input_Hint";//请输入资金密码
    String Safe_Bund_Pwd_Forget_Bond_Pwd = "Safe_Bund_Pwd_Forget_Bond_Pwd";//忘记资金密码
    /**
     * 资产相关
     */
    String ASSETS_SPOT_BILL = "Assets_Spot_Bill";
    String ASSETS_REWARD_TITLE = "Assets_Reward_Title";//reward
    String ASSETS_TRADING_BONUSES = "Assets_Trading_Bonuses";//Trading bonuses
    String ASSETS_COIN_SELECT_CHANGE = "Assets_CoinSelect_change"; // 切换货币偏好
    String ASSETS_COIN_SELECT_DIGIT = "Assets_CoinSelect_digit"; //数字货币
    String ASSETS_COIN_SELECT_RATE = "Assets_CoinSelect_rate"; //汇率
    String Assets_CoinSelect_SearchTip = "Assets_CoinSelect_SearchTip"; //搜索提示


    String Login_ThirdLogin_LinkAccount = "Login_ThirdLogin_LinkAccount";
    String Assets_Withdraw_InnerDialogContent = "Assets_Withdraw_InnerDialogContent";

    String MARGIN_ASSET_MINE_DISCOUNT_TIP = "Margin_Asset_mine_discount_tip"; //使用BGB支付交易手续费优惠{0}%,当BGB余额不足以支付本次交易手续费时，将按原有方式全额支付交易手续费。（仅支持现货交易）
    String MARGIN_ASSET_BOTTOM_DISCOUNT = "Margin_Asset_bottom_discount"; //优惠{0}%
    String MARGIN_ASSET_MINE_CLOSE_DISCOUNT = "Margin_Asset_mine_close_discount"; //关闭后，将不会继续自动使用BGB优惠抵扣手续费，确认关闭吗
    String MARGIN_ASSET_BOTTOM_DISCOUNT_SUBTITLE = "Margin_Asset_bottom_discount_subTitle"; //BGB抵扣现货和杠杆交易手续费
    String CopyTrade_MyFollower_AssetsZero = "CopyTrade_MyFollower_AssetsZero"; // 资产为0
    String CopyTrade_MyFollower_LessThanSomeMoney = "CopyTrade_MyFollower_LessThanSomeMoney"; //资产低于 100 USDT
    String CopyTrade_MyFollower_ClearSomeFollowerTip = "CopyTrade_MyFollower_ClearSomeFollowerTip"; // 确定要移除这{0}名跟单者
    String CopyTrade_MyFollower_SelectClearType = "CopyTrade_MyFollower_SelectClearType"; // 选择移除类型
    String CopyTrade_MyFollower_HaveSelected = "CopyTrade_MyFollower_HaveSelected"; //已选 ：{0}
    String CopyTrade_MyFollower_SelectAll = "CopyTrade_MyFollower_SelectAll"; //全选
    String CopyTrade_MyFollower_Clear = "CopyTrade_MyFollower_Clear"; //移除
    String CopyTrade_MyFollower_ClearAll = "CopyTrade_MyFollower_ClearAll"; //全部移除
    String CopyTrade_MyFollower_LessThanSomeMoneyTip = "CopyTrade_MyFollower_LessThanSomeMoneyTip"; //跟单者资产低于 100 USDT 时，系统会优先通知用户进行充值，在此期间(24小时内)将无法移除这部分用户。
    String CopyTrade_MyFollower_SpotAssets = "CopyTrade_MyFollower_SpotAssets"; //现货资产
    String CopyTrade_MyFollower_ContractAssets = "CopyTrade_MyFollower_ContractAssets"; //账户权益
    String CopyTrade_Search_EmptyViewTip = "CopyTrade_Search_EmptyViewTip"; //搜索不到您要找的交易员？
    String SPOT_SPOTTRADE_SELLLIMIT = "Spot_SpotTrade_sellLimit"; //起购限制：卖出须持有{0}BGB
    String SPOT_SPOTTRADE_BUYLIMIT = "Spot_SpotTrade_buyLimit"; //起购限制：买入须持有{0}BGB
    String SPOT_SPOTTRADE_BUYSELLLIMIT = "Spot_SpotTrade_buySellLimit"; //起购限制：买入须持有{0}BGB；卖出须持有{1}BGB

    //swap 2.2 新增
    String MegaSwap_Withdraw_PopContentV2 = "MegaSwap_Withdraw_PopContentV2";//
    String MegaSwap_Withdraw_PopContentWithoutExchangeV2 = "MegaSwap_Withdraw_PopContentWithoutExchangeV2";//
    String MEGASWAP_WITHDRAW_KNOWBUTTON = "MegaSwap_Withdraw_KnowButton";//知道了


    String AutoInvest_Order_Transaction_Record = "AutoInvest_Order_Transaction_Record";//成交记录
    String AutoInvest_Spot_Title = "AutoInvest_Spot_Title";//现货定投
    String FUTURES_FUTURESLIMITORDERMODIFY_DIALOG_TITLE = "Futures_FuturesLimitOrderModify_Dialog_Title"; //修改订单
    String FUTURES_FUTURESCURRENTDELEGATE_MODIFY_BUTTON = "Futures_FuturesCurrentDelegate_Modify_Button"; //修改
    String FUTURES_FUTURESLIMITORDERMODIFY_DIALOG_PRICE = "Futures_FuturesLimitOrderModify_Dialog_Price"; //当前价格
    String FUTURES_FUTURESLIMITORDERMODIFY_DIALOG_DELEGATE_PRICE = "Futures_FuturesLimitOrderModify_Dialog_Delegate_Price"; //委托价格
    String FUTURES_FUTURESLIMITORDERMODIFY_DIALOG_DELEGATE_NUMBER = "Futures_FuturesLimitOrderModify_Dialog_Delegate_Number"; //委托数量
    String FUTURES_FUTURESLIMITORDERMODIFY_DIALOG_HINT = "Futures_FuturesLimitOrderModify_Dialog_Hint"; //注意：受市场波动影响，一键撤单并重新挂单的修改功能，也存在无法成交或无法完全成功挂单的可能。
    String FUTURES_LIMITORDERMODIFY_DIALOG_OPEN_NUMBER_LIMIT = "Futures_LimitOrderModify_Dialog_Open_Number_Limit"; //开仓数量不可小于最少数量{0}
    String FUTURES_LIMITORDERMODIFY_DIALOG_INPUT_EMPTY = "Futures_LimitOrderModify_Dialog_Input_Empty"; //输入不能为空
    String FUTURES_LIMITORDERMODIFY_DIALOG_INPUT_PRICE_UPZERO = "Futures_LimitOrderModify_Dialog_Input_Price_UpZero"; //价格需大于0
    String FUTURES_LIMITORDERMODIFY_DIALOG_MODFIY_SUCCESS_TOAST = "Futures_LimitOrderModify_Dialog_Modfiy_Success_Toast"; //修改成功
    String FUTURES_FUTURESDEALNOTICE_MODIFY_SUCCESS = "Futures_FuturesDealNotice_Modify_Success"; //修改成功
    String FUTURES_FUTURESDEALNOTICE_MODIFY_FAIL = "Futures_FuturesDealNotice_Modify_Fail"; //修改失败
    String FUTURES_FUTURESDEALNOTICE_PRICE = "Futures_FuturesDealNotice_Price"; //价格
    String FUTURES_FUTURESDEALNOTICE_NUMBER = "Futures_FuturesDealNotice_Number"; //数量
    String FUTURES_FUTURESDEALNOTICE_REGISTRATION_SUCCESS = "Futures_FuturesDealNotice_Registration_Success"; //挂单成功
    String FUTURES_FUTURESDEALNOTICE_REGISTRATION_FAIL = "Futures_FuturesDealNotice_Registration_Fail"; //挂单失败

    String SPOT_LIMITEIDTNOTICE_TRADETOTAL = "Spot_LimitEidtNotice_TradeTotal"; //交易额
    String SPOT_SPOTCURRENTDELEGATE_MODIFY_BUTTON = "Spot_SpotCurrentDelegate_Modify_Button"; //修改
    String SPOT_SPOTLIMITORDERMODIFY_DIALOG_TITLE = "Spot_SpotLimitOrderModify_Dialog_Title"; //修改订单
    String SPOT_SPOTLIMITORDERMODIFY_DIALOG_PRICE = "Spot_SpotLimitOrderModify_Dialog_Price"; //当前价格
    String SPOT_SPOTLIMITORDERMODIFY_DIALOG_DELEGATE_PRICE = "Spot_SpotLimitOrderModify_Dialog_Delegate_Price"; //委托价格
    String SPOT_SPOTLIMITORDERMODIFY_DIALOG_DELEGATE_NUMBER = "Spot_SpotLimitOrderModify_Dialog_Delegate_Number"; //委托数量


    String MARGIN_MARGINCURRENTDELEGATE_MODIFY_BUTTON = "Margin_MarginCurrentDelegate_Modify_Button"; //修改
    String MARGIN_MARGINLIMITORDERMODIFY_DIALOG_TITLE = "Margin_MarginLimitOrderModify_Dialog_Title"; //修改订单
    String MARGIN_MARGINLIMITORDERMODIFY_DIALOG_PRICE = "Margin_MarginLimitOrderModify_Dialog_Price"; //当前价格
    String MARGIN_MARGINLIMITORDERMODIFY_DIALOG_DELEGATE_PRICE = "Margin_MarginLimitOrderModify_Dialog_Delegate_Price"; //委托价格
    String MARGIN_MARGINLIMITORDERMODIFY_DIALOG_DELEGATE_NUMBER = "Margin_MarginLimitOrderModify_Dialog_Delegate_Number"; //委托数量

    String FUTURES_FUTURESDEALNOTICE_CANCEL_SUCCESS = "Futures_FuturesDealNotice_Cancel_Success"; //撤单成功
    String FUTURES_FUTURESDEALNOTICE_CANCEL_FAIL = "Futures_FuturesDealNotice_Cancel_Fail"; //撤单失败
    String FUTURES_LIMITORDERMODIFY_APPLYSUBMIT = "Futures_LimitOrderModify_ApplySubmit";  //修改申请已提交
    String FUTURES_FUTURESTRACEDELEGATE_DIALOG_TITLE = "Futures_FuturesTraceDelegate_Dialog_Title"; //追踪委托说明
    String FUTURES_FUTURESDEALNOTICE_EXPLAIN = "Futures_FuturesDealNotice_Explain"; // * 请注意，如果您之前的委托有预设止盈止损将会被取消


    String X221230_SAFETY_TIP_TITLE = "x221230_safety_tip_title";//请先知晓
    String X221230_SAFETY_TIP_CONTENT = "x221230_safety_tip_content";//你即将开始重置安全项目。当时所有的信息提交之后，客服会在24小时内审核所有的内容，重置才可完成。重置密码后该账户将禁用提币和C2C卖币24小时。
    String X221230_GOT_IT = "x221230_got_it";
    String X221230_SAFETY_TITLE = "x221230_safety_title";//修改验证方式
    String X230131_LOGIN_ACCOUNT_HINT = "x230131_login_account_hint";//手机/邮箱
    String X230103_EMAIL_NOT_USED = "x230103_email_not_used";//bg_param_1 不可用，申请重置邮箱
    String X230103_PHONE_NOT_USED = "x230103_phone_not_used";//bg_param_1 不可用，申请重置邮箱
    String X230103_GOOGLE_NOT_USED = "x230103_google_not_used";//谷歌验证不可用，申请与谷歌验证取消关联
    String X230103_SAFETY_UPDATE = "x230103_safety_update";//需修改的安全项
    String X230103_SAFETY_NOT_USED_HINT = "x230103_safety_not_used_hint";//Once you request for a change in the security item, any withdrawals and P2P sales will be put on a temporary hold for 24 hours.
    String X230103_SAFETY_STEP2_WARNING1 = "x230103_safety_step2_warning1";//勾选两项或更多选项时，我们将会通过视频通话联系你并且确认，请注意这个流程会花费时间更长，请谨慎勾选。
    String X230103_SAFETY_STEP2_WARNING2 = "x230103_safety_step2_warning2";//勾选唯一的选项时，我们将会通过视频通话联系你并且确认，请注意这个流程会花费时间更长，请谨慎勾选。
    String X230201_SENT_TO_EMAIL = "x230201_sent_to_email";
    String X230201_SENT_TO_MOBILE = "x230201_sent_to_mobile";
    String X230106_VERIFY_WAIT_TIME = "x230106_verify_wait_time";
    String X230106_SAFETY_STEP3_TIPS = "x230106_safety_step3_tips";
    String X230116_SAFETY_ANSWER_TITLE = "x230116_safety_answer_title";//想了解您上次的登陆的情况
    String X230117_SAFE_LAST_LOGIN_COUNTRY = "x230117_safe_last_login_country";//最后登陆所在地
    String X230117_SAFE_LAST_LOGIN_EQUIP = "x230117_safe_last_login_equip";//最后登陆设备
    String X230117_SAFE_MOBILE = "x230117_safe_mobile";//手机
    String X230117_SAFE_DESKTOP = "x230117_safe_desktop";//电脑
    String X230117_SAFE_TRADING_HISTORY = "x230117_safe_trading_history";//目前你在平台的资产及交易历史
    String X230117_SAFE_CURRENTLY_LIMIT = "x230117_safe_currently_limit";//你目前拥有的资产（最多10个币种）
    String X230117_SAFE_CURRENTLY_LIKE = "x230117_safe_currently_like";//列出你最喜爱的资产（最多10个币种）
    String X230207_SAFE_ISEXIST_TRANSACTION = "x230207_safe_isexist_transaction";//你曾在我们的平台做过哪种交易？
    String X230130_SELECT_DEVICE = "x230130_select_device";//Didn’t quite catch that! Please select a device
    String X230201_SAFE_PROVIDE_INFORMATION_TITLE = "x230201_safe_provide_information_title";
    String X230201_SAFE_UPDATA_ID_TITLE = "x230201_safe_updata_id_title";//上传你的身份证件
    String X230202_SAFE_CERTIFICATES_RULE_1 = "x230202_safe_certificates_rule_1";//以上内容必须真实有效，不得做任何修改，及必须清晰可见。
    String X230202_SAFE_CERTIFICATES_RULE_2 = "x230202_safe_certificates_rule_2";//仅支持PNG/JPG/JPEG, 并尺寸小于2MB
    String X230201_SAFE_UPDATA_SELFIE = "x230201_safe_updata_selfie";//上传你的自拍
    String X230203_SAFE_SUBMIT = "x230203_safe_submit";//提交需求
    String X230203_SAFETY_MOBILE = "x230203_safety_mobile";//电话号码
    String X230203_SAFE_NEW_EMAIL = "x230203_safe_new_email";//新邮箱地址
    String X230203_SAFE_COUNTDOWN_TITLE = "x230203_safe_countdown_title";//24小时倒计时
    String X230203_SUBMIT_TIP1 = "x230203_submit_tip1";//修改安全项目24小时内将暂时无法提现，但是充值还是可以的。
    String X230203_SUBMIT_TIP2 = "x230203_submit_tip2";//申请将需要1个工作日去处理。
    String X230203_SUBMIT_TIP3 = "x230203_submit_tip3";//申请通过后，你会收到邮件或者短信的提示。
    String X230203_SAFETY_SUBMIT = "x230203_safety_submit";//申请已提交
    String X230203_SAFETY_SUBMIT_TIP = "x230203_safety_submit_tip";//你的申请已成功提交，我们将会给你的邮箱发送通知。
    String X221230_SAFETY_GO_HOME = "x221230_safety_go_home";//回到主页
    String X230131_WELCOME_BACK = "x230131_welcome_back";//Welcome back!
    String X230129_UNRECEIVED_CODE = "x230129_unreceived_code";//未收到验证码？
    String X230130_UNRECEIVED_CODE_TIP1 = "x230130_unreceived_code_tip1";//Haven't received your code?
    String X230130_UNRECEIVED_CODE_TIP2 = "x230130_unreceived_code_tip2";//That's frustrating. Have you tried these steps?
    String X230130_UNRECEIVED_CODE_TIP3 = "x230130_unreceived_code_tip3";//Make sure you've typed your email or mobile number correctly.
    String X230130_UNRECEIVED_CODE_TIP4 = "x230130_unreceived_code_tip4";//Check your spam folder. Sometimes even the good emails end up there.
    String X230130_UNRECEIVED_CODE_TIP5 = "x230130_unreceived_code_tip5";//Give it a few minutes. There might be a delay.
    String X230130_UNRECEIVED_CODE_TIP6 = "x230130_unreceived_code_tip6";//If you still haven’t received your code, contact our customer service.
    String X230131_LOGIN_ACCOUNT = "x230131_login_account";//Email or mobile number
    String X230131_FORGOT_PWD = "x230131_forgot_pwd";//Forgot your password?
    String X230201_BE_ALMOST_THERE = "x230201_be_almost_there";
    String X230207_SAFE_ITEM_CAN_NOT_USE = "x230207_safe_item_can_not_use";
    String X221228_GET_STARTED = "x221228_get_started";//Get started
    String X221228_CREATE_ACCOUNT = "x221228_create_account";//Create account
    String X230202_FORGOT_TIP = "x230202_forgot_tip";//Don't worry, it happens to the best of us.
    String X230202_FORGOT_ENTER_ACCOUNT = "x230202_forgot_enter_account";//Please enter your email or mobile number below
    String X230202_RESET_PWD = "x230202_reset_pwd";//请输入新的密码
    String X230202_RESET_PWD_TIP = "x230202_reset_pwd_tip";//重置密码后该账户将禁用提币和C2C卖币24小时。
    String X230202_RESET_CREATE_PWD = "x230202_reset_create_pwd";
    String X230202_RESET_CONFIRM_PWD = "x230202_reset_confirm_pwd";
    String X230202_RESET_PWD_MISMATCH = "x230202_reset_pwd_mismatch";
    String X230202_PLEASE_NOTE_INFO = "x230202_please_note_info";
    String X230202_PLEASE_NOTE = "x230202_please_note";
    String X221228_SIGNUP_WELCOME1 = "x221228_signup_welcome1";
    String X221228_SIGNUP_WELCOME2 = "x221228_signup_welcome2";
    String X230208_SAFE_STEP_SUBMIT_FRONT_IMG = "x230208_safe_step_submit_front_img";
    String X230208_SAFE_STEP_SUBMIT_BACK_IMG = "x230208_safe_step_submit_back_img";
    String X230208_SAFE_STEP_SUBMIT_HANDS_IMG = "x230208_safe_step_submit_hands_img";
    String X230202_RESET_SUCCESSFUL = "x230202_reset_successful";
    String X230202_RESET_SUCCESSFUL_DESCRIBE = "x230202_reset_successful_describe";
    String X230202_GO_TO_LOGIN = "x230202_go_to_login";
    String X230209_SAFE_EMAIL_VERIFY_CODE = "x230209_safe_email_verify_code";
    String X230209_SAFE_NEW_MOBILE_NUMBER = "x230209_safe_new_mobile_number";
    String X230209_SAFE_SMS_VERIFY_CODE = "x230209_safe_sms_verify_code";
    String X221228_SIGNUP_TERMS1 = "x221228_signup_terms1";
    String X221228_SIGNUP_TERMS2 = "x221228_signup_terms2";
    String X230211_AGREE_CONFIRM = "x230211_agree_confirm";
    String X230211_VERIFICATON_TIP1 = "x230211_verificaton_tip1";
    String X221227_PWD_TIP1 = "x221227_pwd_tip1";//8~32个字符
    String X221227_PWD_TIP2 = "x221227_pwd_tip2";//至少一个数字
    String X221227_PWD_TIP3 = "x221227_pwd_tip3";//至少一个小写字母
    String X221227_PWD_TIP4 = "x221227_pwd_tip4";//至少一个大写字母
    String X221227_PWD_TIP5 = "x221227_pwd_tip5";//至少一个特殊符号
    String X221228_REFERRAL_CODE = "x221228_referral_code";
    String X230131_SWITCH_ROBOT_VERIFY = "x230131_switch_robot_verify";//切换人机验证方法
    String X221228_MOBILE_NUMBER = "x221228_mobile_number";
    String X230202_FORGOT_CONTINUE = "x230202_forgot_continue";
    String X230207_SAFE_NO_TRANSACTION = "x230207_safe_no_transaction";

    /**
     * =========== 首页词条 ===========
     **/
    String Home_NoRecharge_Title = "Home_NoRecharge_Title";     //首页 新人入金  Title
    String Home_LegalCurrency_Title = "Home_LegalCurrency_Title";  //首页 充值/买币 Title
    String Home_WelfareCenter_Title = "Home_WelfareCenter_Title";  //首页 福利中心  title
    String Home_NewUser_Title = "Home_NewUser_Title";        //首页 福利中心  新人7天内展示的描述
    String Home_WelfareCenter_Conent = "Home_WelfareCenter_Conent"; //首页 福利中心  非7天内新人展示的描述
    String Active_Home_EventMarketing = "Active_Home_EventMarketing";//首页 活动营销  首页营销活动
    String Home_Banner_Title = "Home_Banner_Title";         //首页 Banner   Title
    String Home_NewUser_Time = "Home_NewUser_Time";         //首页 福利中心  时间

    String Home_WelfareCenter_Contract = "Home_WelfareCenter_Contract"; // 首页 福利中心 合约每周挑战
    String Home_WelfareCenter_Spot = "Home_WelfareCenter_Spot";// 首页 福利中心 现货每周挑战

    /**=========== 首页词条End ========**/

    /**
     * =========== kyc词条 =============
     **/

    String Kyc_verify_failed_try = "Kyc_verify_failed_try";//KYC 失败页面  认证失败提示
    String Kyc_manual_review = "Kyc_manual_review";        //Kyc 失败页面  认证失败，去重试 文案
    String Kyc_error_tips_new = "Kyc_error_tips_new";      //Kyc 失败页面  人工审核

    /**=========== kyc词条End =========**/

    /**
     * 交易页词条
     **/
    String Futures_Tabbar_Item_Trade_Title = "Futures_Tabbar_Item_Trade_Title";//交易Tab文字

    String StrategicTrading_TradeType_StrategyTitle = "StrategicTrading_TradeType_StrategyTitle";//策略交易
    String Futures_TradeType_ContractTitle = "Futures_TradeType_ContractTitle";//合约交易
    String Spot_TradeType_SpotTitle = "Spot_TradeType_SpotTitle";//现货交易
    String Margin_TradeType_SpotMarginTitle = "Margin_TradeType_SpotMarginTitle";//现货杠杆
    String MegaSwap_TradeType_MegaSwapTitle = "MegaSwap_TradeType_MegaSwapTitle";//MegaSwap

    String StrategicTrading_TradeType_StrategyDesc = "StrategicTrading_TradeType_StrategyDesc";//多种智能策略，自动捕捉收益
    String Futures_TradeType_ContractDesc = "Futures_TradeType_ContractDesc";//流动性充足，交易流畅
    String Spot_TradeType_SpotDesc = "Spot_TradeType_SpotDesc";//买币卖币，快捷便利
    String Margin_TradeType_SpotMarginDesc = "Margin_TradeType_SpotMarginDesc";//借贷交易，放大收益
    String MegaSwap_TradeType_MegaSwapDesc = "MegaSwap_TradeType_MegaSwapDesc";//无缝跨链，全新体验
    String Savings_Home_SharkFinTitle = "Savings_Home_SharkFinTitle"; //鲨鱼鳍
    String Savings_Home_DualCurrencyTitle = "Savings_Home_DualCurrencyTitle"; //双币投资
    String Savings_Home_ProductList_Icon = "Savings_Home_ProductList_Icon"; //赎回
    String Savings_Home_SavingType_Regular = "Savings_Home_SavingType_Regular"; //定期
    String Savings_Home_OrderTotalAmount = "Savings_Home_OrderTotalAmount"; //订单金额
    String Savings_Home_SavingPeriod = "Savings_Home_SavingPeriod"; //{0}天
    String Savings_Home_TotalProfitAlert_Title = "Savings_Home_TotalProfitAlert_Title"; //累计收益
    String Savings_Home_TotalProfitAlert_Content = "Savings_Home_TotalProfitAlert_Content"; //累计收益是包含活期和定期
    String Savings_Home_TotalProfitAlert_Btn = "Savings_Home_TotalProfitAlert_Btn"; //我知道了
    String Savings_Home_Applying = "Savings_Home_Applying"; //申购中
    String Savings_Home_Redeem = "Savings_Home_Redeem"; //赎回中
    String Savings_Home_SavingPeriod_Title = "Savings_Home_SavingPeriod_Title"; //期限
    String Savings_Home_Status_Title = "Savings_Home_Status_Title"; //状态
    String Savings_SharkFin_Type = "Savings_SharkFin_Type"; //类型
    String Savings_SharkFin_SubscriptionAmount = "Savings_SharkFin_SubscriptionAmount"; //申购金额
    String Savings_SharkFin_Duration = "Savings_SharkFin_Duration"; //期限
    String Savings_SharkFin_SettlementDate = "Savings_SharkFin_SettlementDate"; //结算日期
    String Savings_SharkFin_Interest = "Savings_SharkFin_Interest"; //收益
    String Savings_SharkFin_Status = "Savings_SharkFin_Status"; //状态
    String Savings_SharkFin_Bullish = "Savings_SharkFin_Bullish"; //看涨
    String Savings_SharkFin_Bearish = "Savings_SharkFin_Bearish"; //看跌
    String Savings_SharkFin_Subscribing = "Savings_SharkFin_Subscribing"; // 已申购
    String Savings_SharkFin_WaitSettled = "Savings_SharkFin_WaitSettled"; //待结算
    String Savings_SharkFin_WaitRedeem = "Savings_SharkFin_WaitRedeem"; //待赎回
    String Savings_SharkFin_Settled = "Savings_SharkFin_Settled"; //已结算
    String Savings_SharkFin_Period = "Savings_SharkFin_Period"; //{0}天
    String Savings_SharkFin_NamePeriod = "Savings_SharkFin_NamePeriod"; //{0}天
    String Savings_SharkFin_NameSharkFin = "Savings_SharkFin_NameSharkFin"; //鲨鱼鳍
    String Savings_SharkFin_NameTrendUp = "Savings_SharkFin_NameTrendUp"; //看涨
    String Savings_SharkFin_NameTrendDown = "Savings_SharkFin_NameTrendDown"; //看跌
    String Savings_SharkFin_NameActivity = "Savings_SharkFin_NameActivity"; //活动
    String Savings_DualCurrency_SubscriptionAmount = "Savings_DualCurrency_SubscriptionAmount"; //申购金额
    String Savings_DualCurrency_StrikePrice = "Savings_DualCurrency_StrikePrice"; //目标价格
    String Savings_DualCurrency_SettlementDate = "Savings_DualCurrency_SettlementDate"; //结算日期
    String Savings_DualCurrency_APR = "Savings_DualCurrency_APR"; //年利率
    String Savings_DualCurrency_SettlementAmountAlert = "Savings_DualCurrency_SettlementAmountAlert"; //结算金额
    String Savings_DualCurrency_Status = "Savings_DualCurrency_Status"; //状态
    String Savings_DualCurrency_Subscribing = "Savings_DualCurrency_Subscribing"; //申购中
    String Savings_DualCurrency_Settled = "Savings_DualCurrency_Settled"; //已结算
    String Savings_DualCurrency_SellHigh = "Savings_DualCurrency_SellHigh"; //{0} 高卖
    String Savings_DualCurrency_BuyLow = "Savings_DualCurrency_BuyLow"; //{0} 低买
    String Savings_SharkFin_IncomeAlert_Title = "Savings_SharkFin_IncomeAlert_Title"; //收益
    String Savings_SharkFin_IncomeAlert_Content = "Savings_SharkFin_IncomeAlert_Content"; //收益将在结算后显示
    String Savings_SharkFin_IncomeAlert_Btn = "Savings_SharkFin_IncomeAlert_Btn"; //我知道了
    String Savings_DualCurrency_SettlementAmountAlert_Title = "Savings_DualCurrency_SettlementAmountAlert_Title"; //结算金额
    String Savings_DualCurrency_SettlementAmountAlert_Content = "Savings_DualCurrency_SettlementAmountAlert_Content"; //结算金额将在到期后显示
    String Savings_DualCurrency_SettlementAmountAlert_Btn = "Savings_DualCurrency_SettlementAmountAlert_Btn"; //我知道了
    String Savings_SharkFin_AssetSubscribingAmount = "Savings_SharkFin_AssetSubscribingAmount"; //申购中
    String Savings_SharkFin_AssetHistorySubscribeAmount = "Savings_SharkFin_AssetHistorySubscribeAmount"; //历史申购
    String Savings_DualCurrency_AssetSubscribeAmount = "Savings_DualCurrency_AssetSubscribeAmount"; //申购金额
    String SPOT_SPOTTRADE_FUTURES_TIPS = "Spot_SpotTrade_Futures_Tips";// 从这里进入合约
    String TEXT_WITHDRAW_TIPS = "text_withdraw_tips";//温馨提示
    String Assets_P2p_QuickBuyCoin = "Assets_P2p_QuickBuyCoin";//快捷买币
    String Assets_P2p_ExchangeOnLine = "Assets_P2p_ExchangeOnLine";//链上划转
    String Assets_P2p_DetailBtnBuyCoin = "Assets_P2p_DetailBtnBuyCoin";//买币
    String Assets_P2p_DetailBtnExchange = "Assets_P2p_DetailBtnExchange";//划转
    String Assets_P2p_DetailP2pBuyTitle = "Assets_P2p_DetailP2pBuyTitle";//P2P买
    String Assets_P2p_DetailBuyFrom = "Assets_P2p_DetailBuyFrom";//从
    String Assets_P2p_DetailBuyPrice = "Assets_P2p_DetailBuyPrice";//买价
    String Assets_P2p_DetailBuyTo = "Assets_P2p_DetailBuyTo";//到
    String Assets_P2p_DetailP2pSellTitle = "Assets_P2p_DetailP2pSellTitle";//P2P卖
    String Assets_P2p_DetailSellFrom = "Assets_P2p_DetailSellFrom";//从
    String Assets_P2p_DetailSellPrice = "Assets_P2p_DetailSellPrice";//卖价
    String Assets_P2p_DetailSellTo = "Assets_P2p_DetailSellTo";//到


    String ASSETS_ASSETSSPOT_RECHARGE_BTN = "Assets_AssetsSpot_Recharge_Btn"; // 充值
    String ASSETS_ASSETSSPOT_WITHDRAW_BTN = "Assets_AssetsSpot_Withdraw_Btn"; // 提现
    String ASSETS_ASSETSSPOT_BUYCOINS_BTN = "Assets_AssetsSpot_BuyCoins_Btn"; // 买币
    String ASSETS_ASSETSSPOT_TRANSFER_BTN = "Assets_AssetsSpot_Transfer_Btn"; // 划转

    String Assets_Overview_BuyCoins = "Assets_Overview_BuyCoins"; // 买币
    String Assets_Overview_Recharge = "Assets_Overview_Recharge"; // 充值
    String Assets_Overview_Transfer = "Assets_Overview_Transfer"; // 划转


    String ASSETS_ASSETSCOINDETAILS_TRADE_BTN = "Assets_AssetsCoinDetails_Trade_Btn"; // 交易
    String ASSETS_ASSETSCOINDETAILS_FINANCIAL = "Assets_CoinDetails_Financial"; // 去理财
    String ASSETS_ASSETSCOINDETAILS_APY = "Assets_AssetsCoinDetails_Apy"; // 利率


    String Assets_Overview_copy_captal_replace = "Assets_Overview_copy_captal_replace"; // 累计跟单金额({0})
    String Assets_Overview_net_profit = "Assets_Overview_net_profit"; // 净利润({0})
    String Assets_Overview_dividend = "Assets_Overview_dividend"; // 累计已分润({0})
    String Assets_Overview_dividending = "Assets_Overview_dividending"; // 预计待分润({0})


    String Assets_Overview_follow_spot = "Assets_Overview_follow_spot"; // 现货跟单
    String Assets_Overview_follow_contract = "Assets_Overview_follow_contract"; // 合约跟单
    String Assets_Overview_follow_strategy = "Assets_Overview_follow_strategy"; // 策略跟单
    String Assets_Overview_copy_spot = "Assets_Overview_copy_spot"; // 现货带单
    String Assets_Overview_copy_contract = "Assets_Overview_copy_contract"; // 合约带单
    String Assets_Overview_copy_strategy = "Assets_Overview_copy_strategy"; // 策略带单
    String Assets_Overview_follow_profit = "Assets_Overview_follow_profit"; // 跟单收益
    String Assets_Overview_strategy_profit = "Assets_Overview_strategy_profit"; // $策略收益
    String Assets_Overview_strategy_pay = "Assets_Overview_strategy_pay"; // $策略支出
    String Assets_Overview_strategy_sell = "Assets_Overview_strategy_sell"; // $策略售卖收入
    String Assets_Overview_strategy_proficient = "Assets_Overview_strategy_proficient"; // $策略专家收入

    String Assets_Overview_red_packet = "Assets_Overview_red_packet"; // 红包

    String Assets_Overview_AssetsRatios = "Assets_Overview_AssetsRatios"; // 账户资产分布
    String Assets_Overview_AssetsChange = "Assets_Overview_AssetsChange"; // 资产收益变化
    String Assets_Overview_ChartSpotAsset = "Assets_Overview_ChartSpotAsset"; // 现货账户
    String Assets_Overview_ChartCrossAsset = "Assets_Overview_ChartCrossAsset"; // 杠杆账户(全仓)
    String Assets_Overview_ChartIsolateAsset = "Assets_Overview_ChartIsolateAsset"; // 杠杆账户(逐仓)
    String Assets_Overview_ChartContractAsset = "Assets_Overview_ChartContractAsset"; // 合约账户
    String Assets_Overview_ChartOtherAsset = "Assets_Overview_ChartOtherAsset"; // 其他
    String Assets_Overview_ChartAsset = "Assets_Overview_ChartAsset"; // 资产
    String Assets_Overview_AssetLineDay = "Assets_Overview_AssetLineDay"; // {}日
    //资产的福利banner
    String Assets_Overview_welfare_center = "Assets_Overview_welfare_center"; //福利中心
    String Assets_Overview_welfare_sub = "Assets_Overview_welfare_sub"; //7天新手任务
    String Assets_Overview_welfare_desc = "Assets_Overview_welfare_desc"; //完成新手任务最高可({0})
    //无资产
    String Assets_Overview_zero_desc = "Assets_Overview_zero_desc"; //您的账户尚无资产。立即充值或买币就有机会获得 {0} 奖励
    String Assets_Overview_zero_btn = "Assets_Overview_zero_btn"; //快捷充值


    public static final String Assets_AssetsFurures_BuyCoins_Btn = "Assets_AssetsFurures_BuyCoins_Btn";// 资产合约详情 -买币
    String HOME_REGISTER_GUIDE_REGISTER = "Home_Register_Guide_Register";//注册
    String HOME_REGISTER_GUIDE_LOGIN = "Home_Register_Guide_Login";//登录
    String HOME_REGISTER_GUIDE_REWARD = "Home_Register_Guide_reward";//Register to send{0}USDT reward

    String Futures_TradeEntrance_GuideText = "Futures_TradeEntrance_GuideText";//气泡：所有交易都在这里
    String Futures_TradeType_Exchange_GuideText = "Futures_TradeType_Exchange_GuideText";//气泡：再次点击进行切换
    String Futures_TradeEntrance_OKBtnTitle = "Futures_TradeEntrance_OKBtnTitle";//气泡：再次点击进行切换

    String VIEW_NOORDERSYET = "view_NoOrdersYet";
    String VIEW_COPY = "view_Copy";
    /************/
    String CopyTrade_Tabbar_Item_Copy_Title = "CopyTrade_Tabbar_Item_Copy_Title";//tabbar 跟单
    String Market_Change_FearGreedyIndex = "Market_Change_FearGreedyIndex";//恐慌与贪婪指数
    String Market_Change_HistoryIndex = "Market_Change_HistoryIndex";//历史指数
    String Market_Change_UpsAndDowns = "Market_Change_UpsAndDowns";//涨跌分布
    String Market_Change_ExtremePanic = "Market_Change_ExtremePanic";//极度恐慌
    String Market_Change_ExtremeGreedy = "Market_Change_ExtremeGreedy";//极度贪婪
    String Market_Change_Panic = "Market_Change_Panic";//恐慌
    String Market_Change_Greedy = "Market_Change_Greedy";//贪婪
    String Market_Change_Neutral = "Market_Change_Neutral";//中性
    String Market_Change_DataUps = "Market_Change_DataUps";//上涨：
    String Market_Change_DataDowns = "Market_Change_DataDowns";//下跌：
    String Market_Change_DataISee = "Market_Change_DataISee";//好的, 明白了
    String Market_Change_DataYesterday = "Market_Change_DataYesterday";//昨天
    String Market_Change_DataBefore = "Market_Change_DataBefore";//{0}天前
    String Market_Change_DataDates = "Market_Change_DataDates";//日期
    String Market_Kine_24hPriceHigh = "Market_Kine_24hPriceHigh";//24h最高价
    String Market_Kine_24hPriceLow = "Market_Kine_24hPriceLow";//24h最低价
    String Market_Kine_24hVolume = "Market_Kine_24hVolume";//24h成交量({0})
    String Market_Kine_24hTurnover = "Market_Kine_24hTurnover";//24h成交额({0})
    String MARKET_VIEW_COIN_TURNOVER = "Market_view_CoinTurnover";//币种/成交额
    String Home_Mainstream_Currency = "Home_Mainstream_Currency";//主流币
    String Home_Innovation_Zone = "Home_Innovation_Zone";//创新区
    String Home_Hot_Contract = "Home_Hot_Contract";//热门合约
    String Home_Market_24H_increase = "Home_Market_24H_increase";//24H涨幅
    String Home_Market_Latest_Price = "Home_Market_Latest_Price";//最新价
    String Home_Market_Decline = "Home_Market_Decline";//跌幅
    String Home_Market_Increase = "Home_Market_Increase";//涨幅


    String HOME_TOTAL_ASSET_VALUE = "Home_Total_Asset_Value";
    String Home_CopyTrade_Spot = "Home_CopyTrade_Spot";//现货
    String Home_CopyTrade_Contract = "Home_CopyTrade_Contract";//合约

    String view_SeeMore = "view_SeeMore";// 查看更多
    String Home_Quick_deposit = "Home_Quick_deposit";//Quick deposit
    String Home_Deposit_have_crypto = "Home_Deposit_have_crypto";//Quick deposit
    String Home_Deposit_crypto_title = "Home_Deposit_crypto_title";//Deposit crypto
    String Home_Deposit_crypto_describe = "Home_Deposit_crypto_describe";//Add crypto funds to your Bitget account
    String Home_Deposit_not_have_crypto = "Home_Deposit_not_have_crypto";//I don’t have crypto
    String User_PersonalCenter_HomeQuickEntry = "User_PersonalCenter_HomeQuickEntry";
    String User_PersonalCenter_HomeWelfareCenter = "User_PersonalCenter_HomeWelfareCenter";
    String User_PersonalCenter_HomeMarket = "User_PersonalCenter_HomeMarket";
    String User_PersonalCenter_Safe = "User_PersonalCenter_Safe";
    String User_PersonalCenter_Welfare = "User_PersonalCenter_Welfare";
    String User_PersonalCenter_Service = "User_PersonalCenter_Service";
    String Home_QuickEntry_More = "Home_QuickEntry_More";
    String Home_QuickEntry_EditOrder = "Home_QuickEntry_EditOrder";
    String Home_QuickEntry_Edit = "Home_QuickEntry_Edit";
    String Home_QuickEntry_Confirm = "Home_QuickEntry_Confirm";
    String Home_QuickEntry_HomeEdit = "Home_QuickEntry_HomeEdit";
    String Home_QuickEntry_UserCenterEdit = "Home_QuickEntry_UserCenterEdit";
    String Home_QuickEntry_EditTopHint = "Home_QuickEntry_EditTopHint";
    String Home_QuickEntry_DelLimitHint = "Home_QuickEntry_DelLimitHint";
    String Home_QuickEntry_Reset = "Home_QuickEntry_Reset";
    String Home_QuickEntry_CommonFunction = "Home_QuickEntry_CommonFunction";
    String Home_QuickEntry_Welfare = "Home_QuickEntry_Welfare";
    String Home_QuickEntry_Finance = "Home_QuickEntry_Finance";
    String Home_QuickEntry_Service = "Home_QuickEntry_Service";
    String Home_QuickEntry_Other = "Home_QuickEntry_Other";
    String Home_QuickEntry_Bot = "Home_QuickEntry_Bot";
    String Home_QuickEntry_MaxCoutHint = "Home_QuickEntry_MaxCoutHint";
    String Home_QuickEntry_MaxNine = "Home_QuickEntry_MaxNine";
    String Home_Position_Title = "Home_Position_Title";//Position
    String Home_Insight_Head_Tip = "Home_Insight_Head_Tip"; //首页第三方风险提示
    String Home_Insight_Head_Tip_Here = "Home_Insight_Head_Tip_Here"; //首页第三方风险提示

    String Home_Investment_Opportunity_Title = "Home_Investment_Opportunity_Title";//investment opportunity
    String Home_Investment_Opportunity_Favorites_Title = "Home_Investment_Opportunity_Favorites_Title";//自选
    String Home_Investment_Opportunity_Futures_title = "Home_Investment_Opportunity_Futures_title";//热门合约
    String Home_Investment_Opportunity_Gainers_title = "Home_Investment_Opportunity_Gainers_title";//涨幅榜
    String Home_Investment_Opportunity_Trending_title = "Home_Investment_Opportunity_Trending_title";//热搜榜
    String Home_Investment_Opportunity_New_title = "Home_Investment_Opportunity_New_title";//新币榜
    String Home_Deposit_digital = "Home_Deposit_digital";
    String CopyTrade_Overview_Top_Contract_Trader = "CopyTrade_Overview_Top_Contract_Trader";//顶级合约交易员
    String CopyTrade_Overview_Top_Spot_Trader = "CopyTrade_Overview_Top_Spot_Trader";//顶级现货交易员
    String CopyTrade_Overview_Top_strategist = "CopyTrade_Overview_Top_strategist";//顶级策略专家
    String CopyTrade_Overview_My_Trader = "CopyTrade_Overview_My_Trader";//我的交易员
    String CopyTrade_Overview_Recommand_Strategy = "CopyTrade_Overview_Recommand_Strategy";//推荐策略
    String CopyTrade_Overview_Strategy_Long = "CopyTrade_Overview_Strategy_Long";//多头
    String CopyTrade_Overview_Strategy_Short = "CopyTrade_Overview_Strategy_Short";//空头
    String CopyTrade_Overview_Strategy_Positive = "CopyTrade_Overview_Strategy_Positive";//正向
    String CopyTrade_Overview_Strategy_Reverse = "CopyTrade_Overview_Strategy_Reverse";//反向
    String CopyTrade_Overview_Strategy_Neutral = "CopyTrade_Overview_Strategy_Neutral";//中性
    String CopyTrade_Overview_Strategy_Sell_Count = "CopyTrade_Overview_Strategy_Sell_Count";//售出次数 {0}
    String CopyTrade_Overview_Check_More = "CopyTrade_Overview_Check_More";//查看更多

    String CopyTrade_Overview_Apply_Title1 = "CopyTrade_Overview_Apply_Title1";//成为交易专家
    String CopyTrade_Overview_Apply_Title2 = "CopyTrade_Overview_Apply_Title2";//成为策略专家
    String CopyTrade_Overview_Apply_Content1 = "CopyTrade_Overview_Apply_Content1";//赚10%分润
    String CopyTrade_Overview_Apply_Content2 = "CopyTrade_Overview_Apply_Content2";//让策略成功变现
    String CopyTrade_Overview_Apply_Button_Title = "CopyTrade_Overview_Apply_Button_Title";//立即申请
    String CopyTrade_My_Copytrde_CurrentPrice = "CopyTrade_My_Copytrde_CurrentPrice";   //	当前价
    String CopyTrade_Overview_Trader_Apply_Toast = "CopyTrade_Overview_Trader_Apply_Toast";//您已拥有该身份,无需重复申请
    String CopyTrade_Overview_HotInsights = "CopyTrade_Overview_HotInsights";//热门观点
    String CopyTrade_Overview_WaitProfit = "CopyTrade_Overview_WaitProfit";//预计待分润
    String CopyTrade_Overview_Trader_Yield = "CopyTrade_Overview_Trader_Yield";//收益率
    String User_PersonalCenter_Certified = "User_PersonalCenter_Certified";
    String X220616_COMMUNITY_PUBLISH_NEWS = "x220616_community_publish_news";

    String Markets_Home_Favourites = "Markets_Home_Favourites";// 自选
    String Markets_Home_Market = "Markets_Home_Market";//市场
    String Markets_Home_Rankings = "Markets_Home_Rankings";//实时榜单
    String Markets_Home_Insights = "Markets_Home_Insights";//观点
    String Markets_Home_MarketData = "Markets_Home_MarketData";//数据
    String Markets_Home_InnovationArea = "Markets_Home_InnovationArea";//创新区
    String Markets_Home_IncreaseList = "Markets_Home_IncreaseList";//涨幅榜
    String Markets_Home_DeclineList = "Markets_Home_DeclineList";//跌幅榜
    String Markets_Home_TopSearchList = "Markets_Home_TopSearchList";//热搜榜
    String Markets_Home_NewCoinList = "Markets_Home_NewCoinList";//新币榜
    String Markets_Home_SpotInFavourites = "Markets_Home_SpotInFavourites";//现货
    String Markets_Home_ContractInFavourites = "Markets_Home_ContractInFavourites";//合约
    String Markets_Home_MarginInFavourites = "Markets_Home_MarginInFavourites";//杠杆
    String Markets_Home_MegaSwapInFavourites = "Markets_Home_MegaSwapInFavourites";//MegaSwap
    String Markets_Home_SpotInMarket = "Markets_Home_SpotInMarket";// 现货
    String Markets_Home_ContractInMarket = "Markets_Home_ContractInMarket";//合约
    String Markets_Home_MarginInMarket = "Markets_Home_MarginInMarket";//杠杆
    String Markets_Home_MegaSwapInMarket = "Markets_Home_MegaSwapInMarket";//MegaSwap
    String view_RankingsTitle_Hot = "view_RankingsTitle_Hot";// 首页行情和行情实时榜单的热门
    String CopyTrade_Overview_Strategy_Sell_Count_Two = "CopyTrade_Overview_Strategy_Sell_Count_Two";//售出次数
    String CopyTrade_view_SearchTrader = "CopyTrade_view_SearchTrader";//搜索交易专家
    String CopyTrade_Overview_Subscriber_Total_Profit = "CopyTrade_Overview_Subscriber_Total_Profit";//订阅者收益
    String CopyTrade_Overview_Banner_Default_Title = "CopyTrade_Overview_Banner_Default_Title";//一键跟单，长久收益
    String CopyTrade_Overview_Banner_Default_Content = "CopyTrade_Overview_Banner_Default_Content";//交易专家带你实现资产增值
    String Savings_Home_SmartTrendTitle = "Savings_Home_SmartTrendTitle";// 趋势智盈
    String Savings_SmartTrend_IncomeAlert_Title = "Savings_SmartTrend_IncomeAlert_Title";// 收益
    String Savings_SmartTrend_IncomeAlert_Content = "Savings_SmartTrend_IncomeAlert_Content";// 收益将在结算后显示
    String Savings_SmartTrend_IncomeAlert_Btn = "Savings_SmartTrend_IncomeAlert_Btn";// 我知道了
    String Savings_SmartTrend_Type = "Savings_SmartTrend_Type";// 类型
    String Savings_SmartTrend_SubscriptionAmount = "Savings_SmartTrend_SubscriptionAmount";// 申购金额
    String Savings_SmartTrend_Duration = "Savings_SmartTrend_Duration";// 期限
    String Savings_SmartTrend_SettlementDate = "Savings_SmartTrend_SettlementDate";// 结算日期
    String Savings_SmartTrend_Interest = "Savings_SmartTrend_Interest";// 收益
    String Savings_SmartTrend_Status = "Savings_SmartTrend_Status";// 状态
    String Savings_SmartTrend_Bullish = "Savings_SmartTrend_Bullish";// 看涨
    String Savings_SmartTrend_Bearish = "Savings_SmartTrend_Bearish";// 看跌
    String Savings_SmartTrend_Period = "Savings_SmartTrend_Period";// {0}天
    String Savings_SmartTrend_Subscribing = "Savings_SmartTrend_Subscribing";// 已申购
    String Savings_SmartTrend_WaitSettled = "Savings_SmartTrend_WaitSettled";// 待结算
    String Savings_SmartTrend_WaitRedeem = "Savings_SmartTrend_WaitRedeem";// 待赎回
    String Savings_SmartTrend_Settled = "Savings_SmartTrend_Settled";// 已结算
    String Savings_SmartTrend_NameSmartTrend = "Savings_SmartTrend_NameSmartTrend";// 趋势智盈
    String Savings_Home_RangeSniperTitle = "Savings_Home_RangeSniperTitle";// 区间猎手
    String Savings_RangeSniper_SettlementAmountAlert_Title = "Savings_RangeSniper_SettlementAmountAlert_Title";// 结算金额
    String Savings_RangeSniper_SettlementAmountAlert_Content = "Savings_RangeSniper_SettlementAmountAlert_Content";// 结算金额将在到期后显示
    String Savings_RangeSniper_SettlementAmountAlert_Btn = "Savings_RangeSniper_SettlementAmountAlert_Btn";// 我知道了
    String Savings_RangeSniper_Type = "Savings_RangeSniper_Type";// 类型
    String Savings_RangeSniper_SubscriptionAmount = "Savings_RangeSniper_SubscriptionAmount";// 申购金额
    String Savings_RangeSniper_Duration = "Savings_RangeSniper_Duration";// 期限
    String Savings_RangeSniper_SettlementDate = "Savings_RangeSniper_SettlementDate";// 结算日期
    String Savings_RangeSniper_SettlementAmount = "Savings_RangeSniper_SettlementAmount";// 结算金额
    String Savings_RangeSniper_Status = "Savings_RangeSniper_Status";// 状态
    String Savings_RangeSniper_Bullish = "Savings_RangeSniper_Bullish";// 看涨
    String Savings_RangeSniper_Bearish = "Savings_RangeSniper_Bearish";// 看跌
    String Savings_RangeSniper_Period = "Savings_RangeSniper_Period";// {0}天
    String Savings_RangeSniper_Subscribing = "Savings_RangeSniper_Subscribing";// 已申购
    String Savings_RangeSniper_WaitSettled = "Savings_RangeSniper_WaitSettled";// 待结算
    String Savings_RangeSniper_WaitRedeem = "Savings_RangeSniper_WaitRedeem";// 待赎回
    String Savings_RangeSniper_Settled = "Savings_RangeSniper_Settled";// 已结算
    String Savings_RangeSniper_NameRangeSniper = "Savings_RangeSniper_NameRangeSniper";// 区间猎手
    String Savings_Regular_Redemption = "Savings_Regular_Redemption";// 提前赎回
    String Savings_Flexible_Redemption = "Savings_Flexible_Redemption";// 赎回
    String Savings_Launchpool_Redemption = "Savings_Launchpool_Redemption";// 赎回
    String Savings_BgbEarn_Redemption = "Savings_BgbEarn_Redemption";// 提前赎回
    String MARGIN_ORDERLIST_ORDER_DELEGATETYPE = "Margin_Orderlist_Order_delegatetype_"; //杠杆 当前委托 delegatetype
    String T_Market_Changes_Add_Favourite_Title = "t_market_changes_add_favourite_title";//添加到自选

    String Home_Overview_AssetLineDay = "Home_Overview_AssetLineDay";// {}日
    String Markets_Kline_DrawTools = "Markets_Kline_DrawTools"; // "画线工具",
    String Markets_Kline_drawTools_guide = "Markets_Kline_DrawToolsGuide"; // "画线工具在这里可以设置",
    String Markets_Kline_draw_text_know = "Markets_Kline_DrawTextKnow"; // "知道了",
    String Markets_Kline_draw_trendline = "Markets_Kline_DrawTrendline"; // "趋势线",
    String Markets_Kline_draw_extended_trendline = "Markets_Kline_DrawExtendedTrendline"; // "延伸趋势线",
    String Markets_Kline_draw_rays = "Markets_Kline_DrawRays"; // "射线",
    String Markets_Kline_draw_horizontal_line = "Markets_Kline_DrawHorizontalLine"; // "水平直线",
    String Markets_Kline_draw_vertical_line = "Markets_Kline_DrawVerticalLine"; // "垂直线段",
    String Markets_Kline_draw_parallel_lines = "Markets_Kline_DrawParallelLines";// "平行线",
    String Markets_Kline_draw_price_line = "Markets_Kline_DrawPriceLine"; // "价格线",
    String Markets_Kline_draw_waves_three = "Markets_Kline_DrawThreeWaves";// "三浪",
    String Markets_Kline_draw_waves_five = "Markets_Kline_DrawFiveWaves"; // "五浪",
    String Markets_Kline_DrawRectangle = "Markets_Kline_DrawRectangle"; // "矩形",
    String Markets_Kline_DrawFibonacoci = "Markets_Kline_DrawFibonacoci"; // "斐波那契回撤",
    String Markets_Kline_DrawBezier = "Markets_Kline_DrawBezier"; // "斐波那契回撤",
    String Markets_Kline_DrawHighlighter = "Markets_Kline_DrawHighlighter"; // "斐波那契回撤",
    String Markets_Kline_drawTools_master_tips = "Markets_Kline_DrawMasterTips"; // "请在主图范围内画线",
    String Markets_Kline_draw_line_already_selected = "Markets_Kline_DrawLineSelected"; // "{0} 已选",
    String Markets_Kline_draw_complate_tips = "Markets_Kline_DrawLineComplateTips"; // "点击{0}个锚点,完成画线 ({1}/{2})",
    String Markets_Kline_draw_already_complate = "Markets_Kline_DrawAlreadyComplate";// "已完成",
    String Markets_Kline_draw_confirm_clear = "Markets_Kline_DrawConfirmClear"; // "确定删除当前所有画线?",
    String Markets_Kline_draw_continue_on = "Markets_Kline_DrawContinueOn";// "已开启连续画线",
    String Markets_Kline_draw_continue_off = "Markets_Kline_DrawContinueOff"; // "已取消连续画线"

    String Markets_KlineTime_Title = "Markets_KlineTime_Title";//K线时间
    String Markets_KlineTime_Description = "Markets_KlineTime_Description";//"UTC+8 0:00\\K线开盘价以香港时间0点开始计算\\nUTC+0 0:00\\K线开盘价以国际时间0点开始计算"

    String Safe_Safe_Paste = "Safe_Safe_Paste";

    //加息券
    String COUPON_COUPON_LIST_HIKE_TYPE = "Coupon_CouponList_HikeType"; // 资产-加息券 tab标题
    String COUPON_COUPON_LIST_BUSINESS_MONEY_TREASURE = "Coupon_CouponList_BusinessMoneyTreasure"; //理财宝标签  资产-卡券-加息券
    String COUPON_COUPON_LIST_MAX_HIKE_AMOUNT = "Coupon_CouponList_MaxHikeAmount"; //最大加息金额
    String COUPON_COUPON_LIST_VALID_DATE = "Coupon_CouponList_ValidDate"; // {0}天
    // 合约 已实现净盈亏
    String FUTURES_HISTORY_NET_PROFITS = "Futures_History_NetProfits";//合约-历史委托/成交明细 净盈亏
    String FUTURES_HISTORY_NET_PROFITS_TIP = "Futures_History_NetProfitsTip"; //净盈亏 说明提示文案
    String FUTURES_HISTORY_CLOSE_COUNT ="Futures_History_CloseCount";// 平仓数量
    String Strategy_Details_Spot_Buy = "Strategy_Details_Spot_Buy";//买入
    String Strategy_Details_Spot_Sell = "Strategy_Details_Spot_Sell";//卖出
    String Strategy_Details_Contract_OrderType_ = "Strategy_Details_Contract_OrderType_";
    String Strategy_DCA_Spot_Forward_Remind = "Strategy_DCA_Spot_Forward_Remind";//定价定投买入
    String Strategy_DCA_Spot_Reverse_Remind = "Strategy_DCA_Spot_Reverse_Remind";//定价定投出货
    String Strategy_Details_Complete_Time = "Strategy_Details_Complete_Time";//完成时间
    String Strategy_DCA_Create_NumberOfTotalCycle = "Strategy_DCA_Create_NumberOfTotalCycle";//总循环次数
    String Strategy_DCA_Create_CurrentNumberOfCycle = "Strategy_DCA_Create_CurrentNumberOfCycle";//当前循环次数
    String X220314_Copy_Strategy_Dismiss_Tip = "x220314_copy_strategy_dismiss_tip";//复制策略中的参数将会丢失
    String Strategy_DCA_Create_Backtest_interval = "Strategy_DCA_Create_Backtest_interval";//回测区间
    String Strategy_DCA_Create_RangeHint = "Strategy_DCA_Create_RangeHint";//范围{0}~{1}
    String StrategicTrading_CreateStrategy_close = "StrategicTrading_CreateStrategy_close";//关闭
    String Strategy_DCA_Create_Manal_Gallon_Down = "Strategy_DCA_Create_Manal_Gallon_Down";//补单跌幅
    String Strategy_DCA_Create_Manal_Gallon_Up = "Strategy_DCA_Create_Manal_Gallon_Up";//补单涨幅
    String Strategy_DCA_Create_CyclesNumberHint = "Strategy_DCA_Create_CyclesNumberHint";//为策略运行止盈的次数，比如循环次数设置为5，就是策略在第5次止盈后终止；如果为无限循环，在资金满足的情况下，可以一直运行下去。
    String Strategy_DCA_Please_Input_valid_number = "Strategy_DCA_Please_Input_valid_number";//请输入合法的数字
    String Strategy_DCA_Create_Manal_Trigger_Price = "Strategy_DCA_Create_Manal_Trigger_Price";//触发价格
    String StrategicTrading_CreateStrategy_EstimateBurstPrice = "StrategicTrading_CreateStrategy_EstimateBurstPrice";//预估爆仓价:{0} {1}
    String StrategicTrading_Upgrade_Account_Alert_Tips = "StrategicTrading_Upgrade_Account_Alert_Tips";
    String StrategicTrading_UpgradeAlert_Title = "StrategicTrading_UpgradeAlert_Title";
    String StrategicTrading_UpgradeAlert_Btn = "StrategicTrading_UpgradeAlert_Btn";//我知道了
    String Strategy_SubAccount_Click_Change_Margin = "Strategy_SubAccount_Click_Change_Margin";//点击此处修改保证金
    String Strategy_SubAccount_Warning = "Strategy_SubAccount_Warning";//爆仓风险较高，请注意风险

    String strategy_grid_invest_coin_type = "strategy_grid_invest_coin_type";//投资币种
    String strategy_grid_hoard_coin_mode = "strategy_grid_hoard_coin_mode";//囤币模式
    String StrategicTrading_HoardCoinModeAlert_Message = "StrategicTrading_HoardCoinModeAlert_Message";//长期看好币的升值空间，用网格套利产生的利润，以市价买币并长期持有（需网格利润≥最低单次交易额时买入）

    String StrategicTrading_DetailInfo_HoardCoinNumber = "StrategicTrading_DetailInfo_HoardCoinNumber";//囤币数量
    String StrategicTrading_AIStrategyCreate_UsersNumber = "StrategicTrading_AIStrategyCreate_UsersNumber";//使用人数
    String StrategicTrading_AIStrategyCreate_PriceRange = "StrategicTrading_AIStrategyCreate_PriceRange";//价格区间 ({0})
    String StrategicTrading_AIStrategyCreate_GridCount = "StrategicTrading_AIStrategyCreate_GridCount";//网格数量
    String StrategicTrading_AIStrategyCreate_GridEveryProfit = "StrategicTrading_AIStrategyCreate_GridEveryProfit";//每格利润
    String StrategicTrading_AIStrategyCreate_UseStrategy = "StrategicTrading_AIStrategyCreate_UseStrategy";//使用此策略
    String StrategicTrading_StrategyCreate_AIStrategy = "StrategicTrading_StrategyCreate_AIStrategy";//AI策略
    String StrategicTrading_Grid_ManalStrategy = "StrategicTrading_Grid_ManalStrategy";//手动创建
    String StrategicTrading_AIType_Aggressive = "StrategicTrading_AIType_Aggressive";//进取型
    String StrategicTrading_AIType_Balanced = "StrategicTrading_AIType_Balanced";//稳健型
    String StrategicTrading_AIType_Conservative = "StrategicTrading_AIType_Conservative";//保守型



    String StrategicTrading_Grid_ContractTypePositive = "StrategicTrading_Grid_ContractTypePositive";//多头网格
    String StrategicTrading_Grid_ContractTypeReverse = "StrategicTrading_Grid_ContractTypeReverse";//空头网格
    String StrategicTrading_Grid_SpotTypePositive = "StrategicTrading_Grid_SpotTypePositive";//正向网格
    String StrategicTrading_Grid_SpotTypeReverse = "StrategicTrading_Grid_SpotTypeReverse";//反向网格
    String StrategicTrading_StrategyCreate_Parameters = "StrategicTrading_StrategyCreate_Parameters";//策略参数
    String Strategy_Grid_Neutral = "Strategy_Grid_Neutral";//中性网格
    String Strategy_Neutral_Terminate_Myself = "Strategy_Neutral_Terminate_Myself";//否，我自己操作
    String Strategy_Grid_Neutral_Explain = "Strategy_Grid_Neutral_Explain";//适合震荡行情，自动高抛低吸
    String Strategy_Grid_Neutral_Explain_Tag_Disk = "Strategy_Grid_Neutral_Explain_Tag_Disk";//横盘震荡
    String Strategy_Grid_Neutral_Explain_Tag_DoubleCoin = "Strategy_Grid_Neutral_Explain_Tag_DoubleCoin";//双币投资
    String Strategy_Grid_Neutral_Clear_text = "Strategy_Grid_Neutral_Clear_text";//终止时全部平仓
    String Strategy_Grid_Neutral_Can_Use_amount = "Strategy_Grid_Neutral_Can_Use_amount";//可用资金
    String Strategy_Grid_Neutral_Close_In = "Strategy_Grid_Neutral_Close_In";//终止时全部买入
    String X2205011_Strategy_Spot_Close_Tip = "x220511_strategy_spot_close_tip";//终止时全部卖出

    String StrategicCopy_Spot_Neutral_Grid = "StrategicCopy_Spot_Neutral_Grid";// 现货中性网格
    String StrategicCopy_Contract_Neutral_Grid = "StrategicCopy_Contract_Neutral_Grid";//合约中性网格

    String Strategy_Grid_Neutral_close_high_price = "Strategy_Grid_Neutral_close_high_price";//终止最高价
    String Strategy_Grid_Neutral_close_low_price = "Strategy_Grid_Neutral_close_low_price";//终止最低价
    String Strategy_Grid_Neutral_modify_text = "Strategy_Grid_Neutral_modify_text";//修改
    String Strategy_Grid_Neutral_price_range = "Strategy_Grid_Neutral_price_range";//价格区间
    String StrategicTrading_Neutral_BurstPrices_Long = "StrategicTrading_Neutral_BurstPrices_Long";//多头预估爆仓价
    String StrategicTrading_Neutral_BurstPrices_Empty = "StrategicTrading_Neutral_BurstPrices_Empty";//空头预估爆仓价
    String StrategicTrading_Neutral_FullPriceDesc = "StrategicTrading_Neutral_FullPriceDesc";
    String StrategicTrading_Neutral_StopPriceDesc = "StrategicTrading_Neutral_StopPriceDesc";
    String StrategicTrading_UpgradeAlert_CTATitle = "StrategicTrading_UpgradeAlert_CTATitle";
    String StrategicTrading_UpgradeAlert_CTAContent1 = "StrategicTrading_UpgradeAlert_CTAContent1";
    String StrategicTrading_UpgradeAlert_CTAContent2 = "StrategicTrading_UpgradeAlert_CTAContent2";
    String StrategicTrading_UpgradeAlert_CTAContent3 = "StrategicTrading_UpgradeAlert_CTAContent3";
    String StrategicTrading_EditInvestmentDialog_EditInvestment = "StrategicTrading_EditInvestmentDialog_EditInvestment";
    String StrategicTrading_EditInvestmentDialog_Amount = "StrategicTrading_EditInvestmentDialog_Amount";
    String StrategicTrading_EditInvestmentDialog_RevisedInvestmentAmount = "StrategicTrading_EditInvestmentDialog_RevisedInvestmentAmount";
    String StrategicTrading_EditInvestmentDialog_AddInvestment = "StrategicTrading_EditInvestmentDialog_AddInvestment";
    String StrategicTrading_EditInvestmentDialog_ReduceInvestment = "StrategicTrading_EditInvestmentDialog_ReduceInvestment";
    String StrategicTrading_EditInvestmentDialog_EmptyAmountHint = "StrategicTrading_EditInvestmentDialog_EmptyAmountHint";
    String StrategicTrading_EditInvestmentDialog_Deductible = "StrategicTrading_EditInvestmentDialog_Deductible";

    String SPOT_KLINEETF_CURRENCY_INFO_TOTAL_SUPPLY = "Spot_KLineEtf_Currency_Info_Total_Supply";// ETF总供应量
    String SPOT_KLINEETF_CURRENCY_INFO_CIRCULATE_SUPPLY = "Spot_KLineEtf_Currency_Info_Circulate_Supply";//ETF流通供应量
    String SPOT_KLINEETF_CURRENCY_INFO_PUBLIC_DATE = "Spot_KLineEtf_Currency_Info_Public_Date"; //发行日期
    String SPOT_KLINEETF_CURRENCY_INFO_TITLE = "Spot_KLineEtf_Currency_Info_Title"; //ETF描述
    String SPOT_KLINEETF_CURRENCY_INFO_DES = "Spot_KLineEtf_Currency_Info_Des"; //描述
    String SPOT_KLINEETF_CURRENCY_INFO_DES_TIPS = "Spot_KLineEtf_Currency_Info_Des_Tips"; //
    String SPOT_KLINEETF_BASKET_INFO = "Spot_KLineEtf_Basket_Info"; //ETF成分信息
    String SPOT_KLINEETF_BASKET_INFO_CURRENCY = "Spot_KLineEtf_Basket_Info_Currency"; //币种
    String SPOT_KLINEETF_BASKET_INFO_CURRENCY_WEIGHT = "Spot_KLineEtf_Basket_Info_Currency_Weight"; //权重
    String SPOT_KLINEETF_COUNTDOWN_TO_TAKEDOWN = "Spot_KLineEtf_Countdown_To_Takedown"; //距离下架时间
    String SPOT_KLINEETF_TAKEDOWN_SETTLEMENT_PRICE = "Spot_KLineEtf_Takedown_Settlement_Price"; //下架结算价

    String SPOT_SPOTTRADEETF_RISK_ALERT_TITLE = "Spot_SpotTradeEtf_Risk_Alert_Title"; // 风险提示
    String SPOT_SPOTTRADEETF_RISK_ALERT_CONTENT = "Spot_SpotTradeEtf_Risk_Alert_Content"; //注：ETF现货与平台其它一般现货不同，由Bitget平台发行，仅可在本平台进行交易，并不具有一般现货的充币，提币，划转，杠杆等功能。其价格变化可真实反映对应板块市场变化，用户可根据对板块行情的判断，进行交易。ETF现货的最终解释权归Bitget

    String SPOT_SPOTTRADEETF_RISK_ALERT_CONFRIM = "Spot_SpotTradeEtf_Risk_Alert_Confrim"; // 我已阅读并同意此协议
    String SPOT_SPOTTRADEETF_RISK_ALERT_BTN_TITLE = "Spot_SpotTradeEtf_Risk_Alert_Btn_Title"; // 确定

    String SPOT_KLINEETF_DAY = "Spot_KlineEtf_DownDay";
    String SPOT_KLINEETF_Hour = "Spot_KlineEtf_DownHour";

    /**
     * ======自主撤销交易员========
     */
    String text_withdraw_tips = "text_withdraw_tips";
    String app_common_next_step = "app_common_next_step";
    String text_reset_ensure1 = "text_reset_ensure1";
    String CopyTrade_view_RevokeTraderStatus = "CopyTrade_view_RevokeTraderStatus";
    String CopyTrade_RevokeTraderReason_IssuePrompt = "CopyTrade_RevokeTraderReason_IssuePrompt";
    String CopyTrade_RevokeTraderReason_RevokeReasonTitle = "CopyTrade_RevokeTraderReason_RevokeReasonTitle";
    String CopyTrade_RevokeTraderReason_RevokeReason1 = "CopyTrade_RevokeTraderReason_RevokeReason1";
    String CopyTrade_RevokeTraderReason_RevokeReason2 = "CopyTrade_RevokeTraderReason_RevokeReason2";
    String CopyTrade_RevokeTraderReason_RevokeReason3 = "CopyTrade_RevokeTraderReason_RevokeReason3";
    String CopyTrade_RevokeTraderReason_RevokeReason4 = "CopyTrade_RevokeTraderReason_RevokeReason4";
    String CopyTrade_RevokeTraderReason_RevokeReasonHint = "CopyTrade_RevokeTraderReason_RevokeReasonHint";
    String CopyTrade_RevokeTraderReason_RevokeContinue = "CopyTrade_RevokeTraderReason_RevokeContinue";
    String CopyTrade_RevokeTraderReason_FeedbackOnly = "CopyTrade_RevokeTraderReason_FeedbackOnly";
    String CopyTrade_RevokeTraderReason_InputNoPrompt = "CopyTrade_RevokeTraderReason_InputNoPrompt";
    String CopyTrade_RevokeTraderResultFeedback_SuccessHint = "CopyTrade_RevokeTraderResultFeedback_SuccessHint";
    String CopyTrade_RevokeTraderConfirm_RevokeAfterTitle = "CopyTrade_RevokeTraderConfirm_RevokeAfterTitle";
    String CopyTrade_RevokeTraderConfirm_RevokeAfterChange = "CopyTrade_RevokeTraderConfirm_RevokeAfterChange";
    String CopyTrade_RevokeTraderConfirm_LockPositionTitle = "CopyTrade_RevokeTraderConfirm_LockPositionTitle";
    String CopyTrade_RevokeTraderConfirm_Tips1 = "CopyTrade_RevokeTraderConfirm_Tips1";
    String CopyTrade_RevokeTraderConfirm_Tips2 = "CopyTrade_RevokeTraderConfirm_Tips2";
    String CopyTrade_RevokeTraderConfirm_AlertHint = "CopyTrade_RevokeTraderConfirm_AlertHint";
    String CopyTrade_RevokeTraderResultSuccess_Prompt = "CopyTrade_RevokeTraderResultSuccess_Prompt";
    String CopyTrade_RevokeTraderResultSuccess_Hint = "CopyTrade_RevokeTraderResultSuccess_Hint";
    String TRADE_ORDER_BY_DRAW_LINE = "trade_order_by_draw_line";//画线下单
    String TRADE_ORDER_QUICK = "trade_order_quick";//闪电下单
    String TRADE_CLOSE = "trade_close";//关闭

    //语音验证码功能

    String X220303_Grid_Mast_Above_Maximum_Price = "x220303_grid_mast_above_maximum_price";//必须大于最高价
    String X220303_Grid_Less_Minimum_Price = "x220303_grid_less_minimum_price";//必须小于最低价
    //语音验证码功能
    String SAFE_SECURITYVERIFY_VERIFYCODE_RETRY_SEND_CODE = "Safe_SecurityVerify_VerifyCode_retry_send_code";

    String SAFE_SECURITYVERIFY_VERIFYCODE_RETRY_SEND_CODE_PARAM = "Safe_SecurityVerify_VerifyCode_retry_send_code_param";//修改线上语音验证码问题
    String SAFE_SECURITYVERIFY_VERIFYCODE_SEND_CODE = "Safe_SecurityVerify_VerifyCode_send_code";
    String SAFE_SECURITYVERIFY_VERIFYCODE_VOICE_VERIFY_CODE = "Safe_SecurityVerify_VerifyCode_voice_verify_code";
    String SAFE_SECURITYVERIFY_SENDCODE_RETRY_WAIT_TIME = "Safe_SecurityVerify_SendCode_retry_wait_time";
    String SAFE_SECURITYVERIFY_UNRECEIVED_EMAIL = "Safe_SecurityVerify_Unreceived_Email";
    String SAFE_SECURITYVERIFY_UNRECEIVED_MOBILE = "Safe_SecurityVerify_Unreceived_Mobile";
    String SAFE_SECURITYVERIFY_UNRECEIVED_GOOGLE = "Safe_SecurityVerify_Unreceived_google";
    String SAFE_SECURITYVERIFY_UNRECEIVED_EMAIL_TIP1 = "Safe_SecurityVerify_Unreceived_email_tip1";
    String SAFE_SECURITYVERIFY_UNRECEIVED_EMAIL_TIP2 = "Safe_SecurityVerify_Unreceived_email_tip2";
    String SAFE_SECURITYVERIFY_UNRECEIVED_EMAIL_TIP3 = "Safe_SecurityVerify_Unreceived_email_tip3";
    String SAFE_SECURITYVERIFY_UNRECEIVED_CODE_TIP3 = "Safe_SecurityVerify_Unreceived_code_tip3";
    String SAFE_SECURITYVERIFY_UNRECEIVED_CODE_GOT_IT = "Safe_SecurityVerify_Unreceived_code_got_it";
    String SAFE_SECURITYVERIFY_UNRECEIVED_MOBILE_TIP1 = "Safe_SecurityVerify_Unreceived_mobile_tip1";
    String SAFE_SECURITYVERIFY_UNRECEIVED_MOBILE_TIP2 = "Safe_SecurityVerify_Unreceived_mobile_tip2";
    String SAFE_SECURITYVERIFY_UNRECEIVED_GOOGLE_TIP1 = "Safe_SecurityVerify_Unreceived_google_tip1";
    String SAFE_SECURITYVERIFY_UNRECEIVED_GOOGLE_TIP2 = "Safe_SecurityVerify_Unreceived_google_tip2";
    String SAFE_SECURITYVERIFY_UNRECEIVED_GOOGLE_TIP3 = "Safe_SecurityVerify_Unreceived_google_tip3";
    String SAFE_SECURITYVERIFY_UNRECEIVED_CODE = "Safe_SecurityVerify_unreceived_code";
    String SAFE_SECURITYVERIFY_SEND = "Safe_SecurityVerify_Send";
    String SAFE_SECURITYVERIFY_RESEND = "Safe_SecurityVerify_Resend";
    String SAFE_SECURITYVERIFY_SEND_VOICE_CODE_SENDING = "Safe_SecurityVerify_Send_voice_code_sending";
    String SAFE_SECURITYVERIFY_SEND_VOICE_CODE_LATER = "Safe_SecurityVerify_Send_voice_code_later";
    String SAFE_SECURITYVERIFY_SEND_VOICE_CODE = "Safe_SecurityVerify_Send_voice_code";
    String SAFE_SECURITYVERIFY_MOBILE_VERIFICATION = "Safe_SecurityVerify_Mobile_verification";
    String SAFE_SECURITYVERIFY_EMAIL_VERIFICATION = "Safe_SecurityVerify_Email_verification";
    String SAFE_SECURITYVERIFY_MOBILE_VERIFICATION_HINT = "Safe_SecurityVerify_Mobile_verification_hint";
    String SAFE_SECURITYVERIFY_CODE_FORMAT_ERROR = "Safe_SecurityVerify_Code_format_error";
    String SAFE_SECURITYVERIFY_UNRECEIVED_CODE_TIP1 = "Safe_SecurityVerify_Unreceived_code_tip1";//Haven't received your code?
    String SAFE_SECURITYVERIFY_UNRECEIVED_CODE_TIP2 = "Safe_SecurityVerify_Unreceived_code_tip2";
    //资产分析入口
    String Assets_Analysis_Today = "Assets_Analysis_Today";//今日盈亏
    String Assets_Analysis_ContractTypeU = "Assets_Analysis_ContractTypeU";//U本位合约
    String Assets_Analysis_ContractTypeCoin = "Assets_Analysis_ContractTypeCoin";//币本位合约
    String Assets_Analysis_ContractTypeC = "Assets_Analysis_ContractTypeC";//USDC合约
    //资产分析合约分析 平仓盈亏入口
    String Assets_Analysis_ContractTitle = "Assets_Analysis_ContractTitle";//平仓盈亏
    String Assets_AnalysisContract_RealizedProfit = "Assets_AnalysisContract_RealizedProfit";//已实现盈利
    String Assets_AnalysisContract_RealizedLoss = "Assets_AnalysisContract_RealizedLoss";//已实现亏损
    String Assets_AnalysisContract_TotalPurchase = "Assets_AnalysisContract_TotalPurchase";//买入总量
    String Assets_AnalysisContract_TotalVolumeSold = "Assets_AnalysisContract_TotalVolumeSold";//卖出总量
    String Assets_AnalysisContract_AverageBuyingPrice = "Assets_AnalysisContract_AverageBuyingPrice";//买入均价
    String Assets_AnalysisContract_AverageSellingPrice = "Assets_AnalysisContract_AverageSellingPrice";//卖出均价
    String Assets_AnalysisContract_TotalProfitLoss = "Assets_AnalysisContract_TotalProfitLoss";//总计实现盈亏
    String Assets_AnalysisContract_SellFlat = "Assets_AnalysisContract_SellFlat";//平多
    String Assets_AnalysisContract_BuyShort = "Assets_AnalysisContract_BuyShort";//平空
    String Assets_AnalysisContract_TransactionPrice = "Assets_AnalysisContract_TransactionPrice";//成交价格
    String Assets_AnalysisContract_MarginLiquidationPrice = "Assets_AnalysisContract_MarginLiquidationPrice";//爆仓价格
    String Assets_AnalysisContract_TurnoverAmount = "Assets_AnalysisContract_TurnoverAmount";//成交数量({0}
    String Assets_AnalysisContract_ServiceCharge = "Assets_AnalysisContract_ServiceCharge";//手续费({0})
    String Assets_AnalysisContract_RealizedProfitLoss = "Assets_AnalysisContract_RealizedProfitLoss";//已实现盈亏({0})
    String Assets_AnalysisContract_TradingVolume = "Assets_AnalysisContract_TradingVolume";//成交量
    String Assets_AnalysisContract_Turnover = "Assets_AnalysisContract_Turnover";//成交额
    //现货分析 交易分析词条
    String Assets_Analysis_SpotTitle = "Assets_Analysis_SpotTitle";//交易分析
    String Assets_AnalysisSpot_RealizedProfit = "Assets_AnalysisSpot_RealizedProfit";//已实现盈利
    String Assets_AnalysisSpot_RealizedLoss = "Assets_AnalysisSpot_RealizedLoss";//已实现亏损
    String Assets_AnalysisSpot_ClosingPosition = "Assets_AnalysisSpot_ClosingPosition";//期末持仓
    String Assets_AnalysisSpot_LatestPrice = "Assets_AnalysisSpot_LatestPrice";//最新价
    String Assets_AnalysisSpot_LatestPositionValue = "Assets_AnalysisSpot_LatestPositionValue";//最新仓位价值
    String Assets_AnalysisSpot_TransactionComparison = "Assets_AnalysisSpot_TransactionComparison";//成交对比
    String Assets_AnalysisSpot_TradingVolume = "Assets_AnalysisSpot_TradingVolume";//成交量
    String Assets_AnalysisSpot_Turnover = "Assets_AnalysisSpot_Turnover";//成交额
    String Assets_AnalysisSpot_TotalPurchase = "Assets_AnalysisSpot_TotalPurchase";//买入总额
    String Assets_AnalysisSpot_TotalVolumeSold = "Assets_AnalysisSpot_TotalVolumeSold";//卖出总额
    String Assets_AnalysisSpot_TotalPurchaseNum = "Assets_AnalysisSpot_TotalPurchaseNum";//买入总量
    String Assets_AnalysisSpot_TotalVolumeSoldNum = "Assets_AnalysisSpot_TotalVolumeSoldNum";//卖出总量
    String Assets_AnalysisSpot_AverageBuyingPrice = "Assets_AnalysisSpot_AverageBuyingPrice";//买入均价
    String Assets_AnalysisSpot_AverageSellingPrice = "Assets_AnalysisSpot_AverageSellingPrice";//卖出均价
    String Assets_AnalysisSpot_AverageCostOfPosition = "Assets_AnalysisSpot_AverageCostOfPosition";//持仓成本均价
    String Assets_AnalysisSpot_TotalCostOfPosition = "Assets_AnalysisSpot_TotalCostOfPosition";//持仓成本总额
    String Assets_AnalysisSpot_TotalTransaction = "Assets_AnalysisSpot_TotalTransaction";//成交总量
    String Assets_AnalysisSpot_TotalTransactionValue = "Assets_AnalysisSpot_TotalTransactionValue";//成交总额
    String Assets_AnalysisSpot_Tip = "Assets_AnalysisSpot_Tip";//*只统计同一个交易对在指定时间内的买,卖订单,不包括充值、提现、划转、分发。
    String Assets_AnalysisSpot_ProfitLossStatistics = "Assets_AnalysisSpot_ProfitLossStatistics";//盈亏统计
    String Assets_AnalysisSpot_RealizedProfitLoss = "Assets_AnalysisSpot_RealizedProfitLoss";//已实现盈亏
    String Assets_AnalysisSpot_UnRealizedProfitLoss = "Assets_AnalysisSpot_UnRealizedProfitLoss";//未实现盈亏


    String ASSETS_SPOT_ANALYSIS = "Assets_Spot_Analysis";// 现货分析
    String ASSETS_CONTRACT_ANALYSIS = "Assets_Contract_Analysis";//合约分析
    String ASSETS_SPOT_VALUATION = "Assets_Spot_Valuation";// 现货估值{0}
    String ASSETS_TODAY_PROFIT = "Assets_Today_Profit";// 今日盈亏
    String ASSETS_MONTH_PROFIT = "Assets_Month_Profit";// 30天盈亏
    String ASSETS_TOTAL_PROFIT = "Assets_Total_Profit";// 累计盈亏
    String ASSETS_RECENT_DAYS = "Assets_Recent_Days"; //最近{0}天
    String ASSETS_PROFIT = "Assets_Profit";//盈亏
    String ASSETS_TRADE_VOLUME = "Assets_Trade_Volume";//交易量
    String ASSETS_ORDER = "Assets_Order";//订单
    String ASSETS_HISTORY_PROFIT = "Assets_History_Profit";//历史盈亏
    String ASSETS_DAY_PROFIT = "Assets_Day_Profit";//日盈亏
    String ASSETS_WIN = "Assets_Win";//盈利
    String ASSETS_LOSS = "Assets_Loss";//亏损
    String ASSETS_ASSETS_DISTRIBUTE = "Assets_Assets_Distribute";//资产分布
    String ASSETS_COIN_TYPE = "Assets_Coin_Type";//币种
    String ASSETS_PERCENT = "Assets_Percent";//百分比
    String ASSETS_AMOUNT_OR_ASSETS = "Assets_Amount_Or_Assets";//数量/资产
    String ASSETS_ANALYSIS_CONTRACT_TYPE_U = "Assets_Analysis_ContractTypeU"; //U本位合约
    String ASSETS_ANALYSIS_CONTRACT_TYPE_COIN = "Assets_Analysis_ContractTypeCoin"; // 币本位合约
    String ASSETS_ANALYSIS_CONTRACT_TYPE_C = "Assets_Analysis_ContractTypeC"; // USDC合约
    String ASSETS_CONTRACT_VALUATION = "Assets_Contract_Valuation";// 合约估值({0})
    String ASSETS_PROFIT_LOSS_RATE = "Assets_Profit_Loss_Rate"; //盈亏率
    String ASSETS_WIN_RATE = "Assets_Win_Rate"; // 胜率
    String ASSETS_COIN_FAVORITE = "Assets_Coin_Favorite"; //币种偏好

    String ASSETS_SELECT_MARKET = "Assets_Select_Market"; //选择交易对
    String ASSETS_CLOSE = "Assets_Close"; // 关闭
    String ASSETS_RESET = "Assets_Reset"; //重置
    String ASSETS_CONFIRM = "Assets_Confirm"; //确定

    String ASSETS_SELECT_CONTRACT = "Assets_Select_Contract"; //选择合约
    String ASSETS_SEARCH = "Assets_Search"; // 搜索
    String Assets_AnalysisSpot_RealizedProfitLossTip = "Assets_AnalysisSpot_RealizedProfitLossTip"; // 已实现盈亏=卖出总量*（卖出均价-买入均价）
    String Assets_Analysis_7Day = "Assets_Analysis_7Day"; // 最近7天
    String Assets_Analysis_3Month = "Assets_Analysis_3Month"; // 最近3个月
    String Assets_Analysis_30Day = "Assets_Analysis_30Day"; // 最近30天

    String ASSETS_I_KNOW = "Assets_I_Know"; //我知道了

    String ASSETS_SPOT_TOTAL_PNL_TIP = "Assets_Spot_Total_Pnl_Tip";
    String ASSETS_SPOT_HISTORY_PNL_TIP = "Assets_Spot_History_Pnl_Tip";
    String ASSETS_SPOT_ASSETS_DISTRIBUTION_TIP = "Assets_Spot_Assets_Distribution_Tip";
    String ASSETS_CONTRACT_TOTAL_PNL_TIP = "Assets_Contract_Total_Pnl_Tip";
    String ASSETS_CONTRACT_HISTORY_PNL_TIP = "Assets_Contract_History_Pnl_Tip";
    String ASSETS_CONTRACT_COIN_FAVORITE_TIP = "Assets_Contract_Coin_Favorite_Tip";

    String Assets_AnalysisSpot_Profitability = "Assets_AnalysisSpot_Profitability"; // 盈亏率

    String Assets_AnalysisSpot_change_rase = "Assets_AnalysisSpot_change_rase"; // 涨跌幅
    String Assets_AnalysisSpot_ChangeRate = "Assets_AnalysisSpot_ChangeRate"; // 涨跌率

    String Assets_Analysis_MarginLiquidationTip = "Assets_Analysis_MarginLiquidationTip"; // 爆仓解释说明
    String Assets_Analysis_MarginLiquidationProfitTip = "Assets_Analysis_MarginLiquidationProfitTip"; // 爆仓盈利的解释说明

    String WEB_NAV_REFRESH = "Webview_Nav_Refresh";
    String WEB_NAV_COPY_LINK = "Webview_Nav_CopyLink";
    String WEB_NAV_CLOSE = "Webview_Nav_Close";
    String RedPacket_Send_PopGrabRule = "RedPacket_Send_PopGrabRule";//红包规则
    String RedPacket_Send_PopGrabRuleQ1 = "RedPacket_Send_PopGrabRuleQ1";//什么是红包？
    String RedPacket_Send_PopGrabRuleA1 = "RedPacket_Send_PopGrabRuleA1";//你可将加密货币以红包的形式送给好友，让好友免费收到加密货币。
    String RedPacket_Send_PopGrabRuleQ2 = "RedPacket_Send_PopGrabRuleQ2";//如何发送红包？
    String RedPacket_Send_PopGrabRuleA2 = "RedPacket_Send_PopGrabRuleA2";//点击APP首页的更多功能，点击红包功能，选择发红包，塞币进红包，并输入希望发送的金额以及红包个数，系统会自动生成红包链接和海报。好友通过你分享的链接或海报即可领取红包。
    String RedPacket_Send_PopGrabRuleQ3 = "RedPacket_Send_PopGrabRuleQ3";//如何领取红包？
    String RedPacket_Send_PopGrabRuleA3 = "RedPacket_Send_PopGrabRuleA3";//您可以通过他人分享的红包链接或海报，获得红包。
    String RedPacket_Send_PopGrabRuleTip = "RedPacket_Send_PopGrabRuleTip";//温馨提示：新注册用户通过红包链接领取您的红包后，将绑定邀请关系；红包失效后未被领取的金额将原路返回；红包口令仅限知道口令的用户领取，若不想红包被其他不认识的人领取，请妥善保管好口令。
    String RedPacket_Send_PopGrabRuleMore = "RedPacket_Send_PopGrabRuleMore";//查看更多
    String RedPacket_Guide_Step1 = "RedPacket_Guide_Step1";//选择红包类型
    String RedPacket_Guide_Step2 = "RedPacket_Guide_Step2";//设置红包个数
    String RedPacket_Guide_Step3 = "RedPacket_Guide_Step3";//选择红包币种
    String RedPacket_Guide_Step4 = "RedPacket_Guide_Step4";//设置红包金额
    String RedPacket_Guide_Step5 = "RedPacket_Guide_Step5";//点击塞币进红包
    String RedPacket_Guide_Step6 = "RedPacket_Guide_Step6";//选择红包币种并且设置红包金额
    String RedPacket_Guide_Next = "RedPacket_Guide_Next";//下一步
    String RedPacket_Guide_Finish = "RedPacket_Guide_Finish";//完成
    String RedPacket_Get_RandomAmount = "RedPacket_Get_RandomAmount";//用户将获得随机的红包金额
    String RedPacket_Get_FixedAmount = "RedPacket_Get_FixedAmount";//用户将获得固定的红包金额
    String RedPacket_Got_It = "RedPacket_Got_It";//知道了
    String RedPacket_Scan_Album = "RedPacket_Scan_Album";//相册
    String RedPacket_Scan_Album_Error = "RedPacket_Scan_Album_Error";//无法识别二维码\n请扫描Bitget官网二维码
    String RedPacket_Scan_Album_Error1 = "RedPacket_Scan_Album_Error1";//无法识别二维码
    String RedPacket_Scan_Album_Error2 = "RedPacket_Scan_Album_Error2";//请扫描Bitget官网二维码
    String RedPacket_Send_PopGrabRuleTip1 = "RedPacket_Send_PopGrabRuleTip1";//温馨提示
    String RedPacket_Send_PopGrabRuleTip2 = "RedPacket_Send_PopGrabRuleTip2";//1.新注册用户通过红包链接领取您的红包后，将绑定邀请关系
    String RedPacket_Send_PopGrabRuleTip3 = "RedPacket_Send_PopGrabRuleTip3";//2.红包失效后未被领取的金额将原路返回
    String RedPacket_Send_PopGrabRuleTip4 = "RedPacket_Send_PopGrabRuleTip4";//3.红包口令仅限知道口令的用户领取，若不想红包被其他不认识的人领取，请妥善保管好口令
    String HOME_VIP_TITLE = "Home_VIP_Title";
    String HOME_VIP_CONTENT = "Home_VIP_Content";
    String HOME_EDIT_MODULE = "Home_Edit_Module";
    String HOME_VIP_CONTACT = "Home_Vip_Contact";
    String HOME_VIP_QRCODE_SCREENSHOT = "Home_Vip_Qrcode_Screenshot";
    String HOME_QUICKENTRY_FIRST_CURRENT = "Home_QuickEntry_Current";
    String HOME_QUICKENTRY_SLIDER_CURRENT = "Home_QuickEntry_Slider_Current";

    String HOME_INVESTMENT_0PPORTUNITY_TITLE = "Home_Investment_0pportunity_Title";
    String Home_Invest_Launchpool = "Home_Invest_Launchpool";
    String Home_Invest_SharkFin = "Home_Invest_SharkFin";
    String Home_Invest_DownTime = "Home_Invest_DownTime";
    String Home_Invest_Periodical = "Home_Invest_Periodical";
    String Home_Invest_Current = "Home_Invest_Current";
    String Home_Invest_Finances = "Home_Invest_Finances";
    String Home_Invest_DualCurrency = "Home_Invest_DualCurrency";
    String Home_Invest_HotTab = "Home_Invest_HotTab";
    String Home_Invest_Innovation = "Home_Invest_Innovation";
    String Home_Invest_BgbEarn = "Home_Invest_BgbEarn";
    String Home_Invest_SmartTrend = "Home_Invest_SmartTrend";
    String Home_Invest_RangeSniper = "Home_Invest_RangeSniper";
    String Home_Invest_Pos = "Home_Invest_Pos";
    String Home_Invest_Bgb = "Home_Invest_Bgb";

    String Home_IpLimit_Tip_Title = "Home_IpLimit_Tip_Title";
    String Home_IpLimit_Tip_Content = "Home_IpLimit_Tip_Content";
    String Home_IpLimit_Tip_Acknowledge = "Home_IpLimit_Tip_Acknowledge";
    String Home_IpLimit_Tip_GoToVerify = "Home_IpLimit_Tip_GoToVerify";
    String Home_IpLimit_Tip_JumpHkUrl = "Home_IpLimit_Tip_JumpHkUrl";
    String MARKETS_KLINE_ORDERLISTRECORD = "Markets_Kline_orderListRecord";
    String MARKETS_KLINE_ATMOSTTHREEMONTHORDER = "Markets_Kline_atMostThreeMonthOrder";
    String Margin_MarginTrade_Risk_Rate_Desc = "Margin_MarginTrade_Risk_Rate_Desc"; //当风险比率>=1时，我们采取风控措施，一旦触发就会进行还款，如果币种数量不足，全仓模式下会对仓位进行平仓，逐仓模式下将触发减仓或爆仓。
    String Login_ChooseThirdAccount_Tip = "Login_ChooseThirdAccount_Tip";//您确定与该账户关联吗？
    String User_Input_Account = "User_Input_Account";//请输入邮箱/手机号
    String AutoInvest_Create_Confirm_Strategy = "AutoInvest_Create_Confirm_Strategy";//策略

    String Futures_Main_Depth_EntrustMode_Title = "Futures_Main_Depth_EntrustMode_Title"; //默认
    String Futures_Main_Depth_EntrustMode_Default = "Futures_Main_Depth_EntrustMode_Default"; //默认
    String Futures_Main_Depth_EntrustMode_BidOnly = "Futures_Main_Depth_EntrustMode_BidOnly"; //买入
    String Futures_Main_Depth_EntrustMode_AskOnly = "Futures_Main_Depth_EntrustMode_AskOnly"; //卖出
    String Futures_Main_DepthScale_Title = "Futures_Main_DepthScale_Title";

    String Markets_Kline_CurrentSellAndBuy = "Markets_Kline_CurrentSellAndBuy";//即时买卖价
    String Markets_Kline_Countdown = "Markets_Kline_Countdown";//倒数计时
    String Markets_Kline_CurrentDealPrice = "Markets_Kline_CurrentDealPrice";//最新成交价
    String Markets_Kline_HighAndLowestPrice = "Markets_Kline_HighAndLowestPrice";//区间最低最高价
    String Markets_Kline_ChartHeight = "Markets_Kline_ChartHeight";//图表高度
    String Markets_Kline_OperationFunc = "Markets_Kline_OperationFunc";//操作功能
    String Markets_Kline_ChartAppear = "Markets_Kline_ChartAppear";//图表显示
    String Markets_Kline_OrderAppear = "Markets_Kline_OrderAppear";//订单显示
    String Markets_Kline_PostionInfo = "Markets_Kline_PostionInfo";//仓位信息
    String Markets_Kline_CurrentEntrust = "Markets_Kline_CurrentEntrust";//当前委托
    String Markets_Kline_ChartIndex = "Markets_Kline_ChartIndex";//技术指标
    String Markets_Kline_ChartStyle = "Markets_Kline_ChartStyle";//样式
    String Markets_Kline_ChartDraw = "Markets_Kline_ChartDraw";//绘图
    String Markets_Kline_CurrentEntrustStyle = "Markets_Kline_CurrentEntrustStyle";//当前委托类型
    String Markets_Kline_SPSL = "Markets_Kline_SPSL";//止盈止损
    String Markets_Kline_PlanEntrust = "Markets_Kline_PlanEntrust";//计划委托
    String Markets_Kline_MoreSet = "Markets_Kline_MoreSet";//更多
    String Markets_Kline_CountdownDes = "Markets_Kline_CountdownDes";//倒数计时显示最后一根蜡烛的剩余时间。启用此功能后，您可以在图表上看到最后一个价格旁边的倒计时。
    String Markets_Kline_CurrentSellAndBuyDes = "Markets_Kline_CurrentSellAndBuyDes";//启用后，您就可以在图表上看到买价和卖价。这是追踪订单薄价格变化的便利方法。
    String Markets_Kline_BuyText = "Markets_Kline_BuyText";//买
    String Markets_Kline_SellText = "Markets_Kline_SellText";//卖

    String Markets_Kline_MarketStopProfit = "Markets_Kline_MarketStopProfit";//市价止盈
    String Markets_Kline_MarketStopLoss = "Markets_Kline_MarketStopLoss";//市价止损
    String Markets_Kline_ChartViewSetting = "Markets_Kline_ChartViewSetting";//K线设置
    String Markets_Kline_OpenLong = "Markets_Kline_OpenLong";//开多
    String Markets_Kline_OpenShort = "Markets_Kline_OpenShort";//开空
    String Markets_Kline_CloseLong = "Markets_Kline_CloseLong";//平多
    String Markets_Kline_CloseShort = "Markets_Kline_CloseShort";//平空
    String Markets_Kline_Amplitude = "Markets_Kline_Amplitude";//振幅
    String Markets_Kline_ScorllviewChangeCoin = "Markets_Kline_ScorllviewChangeCoin";//上滑下滑可切换其他币种

    String MegaSwap_CoinInfo_Title = "MegaSwap_CoinInfo_Title";// 币种信息
    String MegaSwap_CoinInfo_CoinOverview = "MegaSwap_CoinInfo_CoinOverview";// 币种概况
    String MegaSwap_CoinInfo_CurrentPrice = "MegaSwap_CoinInfo_CurrentPrice";// 当前价格
    String MegaSwap_CoinInfo_24HighestPrice = "MegaSwap_CoinInfo_24HighestPrice";// 24小时最高价
    String MegaSwap_CoinInfo_24LowestPrice = "MegaSwap_CoinInfo_24LowestPrice";// 24小时最低价
    String MegaSwap_CoinInfo_MainChain = "MegaSwap_CoinInfo_MainChain";// 主链
    String MegaSwap_CoinInfo_CMCRanking = "MegaSwap_CoinInfo_CMCRanking";// CMC排行
    String MegaSwap_CoinInfo_MarketValue = "MegaSwap_CoinInfo_MarketValue";// 市值
    String MegaSwap_CoinInfo_TotalSupply = "MegaSwap_CoinInfo_TotalSupply";// 总供应量
    String MegaSwap_CoinInfo_TotalCirculation = "MegaSwap_CoinInfo_TotalCirculation";// 总流通量
    String MegaSwap_CoinInfo_Address = "MegaSwap_CoinInfo_Address";// 合约地址
    String MegaSwap_CoinInfo_Resource = "MegaSwap_CoinInfo_Resource";// 官方资源
    String MegaSwap_CoinInfo_Website = "MegaSwap_CoinInfo_Website";// 官网
    String MegaSwap_CoinInfo_WhitePaper = "MegaSwap_CoinInfo_WhitePaper";// 白皮书
    String Spot_SpotTrade_asset = "Spot_SpotTrade_asset";//资产
    String Spot_SpotTrade_asset_show_all = "Spot_SpotTrade_asset_show_all";//显示全部资产
    String Spot_SpotTrade_asset_coins = "Spot_SpotTrade_asset_coins";//币种
    String Spot_SpotTrade_asset_total = "Spot_SpotTrade_asset_total";//总额
    String Spot_SpotTrade_asset_ratio = "Spot_SpotTrade_asset_ratio";//资产比率
    String Spot_SpotTrade_asset_ratio_info = "Spot_SpotTrade_asset_ratio_info";//所选资产占现货账户总资产的百分比。

    String Margin_MarginTrade_Menu_More = "Margin_MarginTrade_Menu_More"; // 更多咨询
    String Margin_MarginTrade_Menu_ProductNews = "Margin_MarginTrade_Menu_ProductNews"; //产品动态

    String Assets_Spot_Coin_Detail_Occupancy = "Assets_Spot_Coin_Detail_Occupancy"; //占用
    String Assets_Spot_Coin_Detail_Follow = "Assets_Spot_Coin_Detail_Follow"; //跟单
    String Assets_Spot_Coin_Detail_Occupancy_Tips = "Assets_Spot_Coin_Detail_Occupancy_Tips"; //账户中某个币种当前已被占用的数量，包括下单时冻结，法币商家等锁仓和跟单占用


    String Futures_view_InnovateTitle = "Futures_view_InnovateTitle";// 创新区
    String view_RiskTipTitle = "view_RiskTipTitle";//风险提示
    String TEXT_QUICK_BUY_COIN_SURE_DIALOG_AGREE_HINT = "text_quick_buy_coin_sure_dialog_agree_hint";
    String Markets_Futures_TitleAll = "Markets_Futures_TitleAll";// 合约板块-全部
    //闪兑
    String Convert_Select_Currency_Title = "Convert_Select_Currency_Title";//    Select Currency
    String Convert_Select_Currency_From = "Convert_Select_Currency_From";//    From
    String Convert_Select_Currency_To = "Convert_Select_Currency_To";//            To
    String Convert_Select_Currency_Consume = "Convert_Select_Currency_Consume";//    You will consume

    String Convert_Select_Currency_Obtain = "Convert_Select_Currency_Obtain";//    You will obtain
    String Convert_ConfirmView_Title = "Convert_ConfirmView_Title";//    Center Title
    String Convert_ConfirmView_From = "Convert_ConfirmView_From";//    From
    String Convert_ConfirmView_To = "Convert_ConfirmView_To";//            To
    String Convert_ConfirmView_Exchange_Rate = "Convert_ConfirmView_Exchange_Rate";//    Exchange Rate
    String Convert_ConfirmView_Payment_Method = "Convert_ConfirmView_Payment_Method";//    Payment Method
    String Convert_ConfirmView_Transaction_Fees = "Convert_ConfirmView_Transaction_Fees";//    Transaction Fees
    String Convert_ConfirmView_Receive = "Convert_ConfirmView_Receive";//    You will Receive
    String Convert_ConfirmView_Button_Confirm = "Convert_ConfirmView_Button_Confirm";//            Confirm
    String Convert_ConfirmView_Button_Refresh = "Convert_ConfirmView_Button_Refresh";//刷新
    String Convert_ConfirmView_SpotAccount = "Convert_ConfirmView_SpotAccount";//  现货账户
    String Convert_ConfirmView_Zerofees = "Convert_ConfirmView_Zerofees";//    Fees
    String Convert_Successful_Convert = "Convert_Successful_Convert";//    Successful Convert
    String Convert_Successful_Received = "Convert_Successful_Received";//    You have Received
    String Convert_Successful_Rate = "Convert_Successful_Rate";//    Exchange Rate
    String Convert_Successful_Button_Stock = "Convert_Successful_Button_Stock";//    View Stock
    String Convert_Successful_Button_Confirm = "Convert_Successful_Button_Confirm";//    Confirm

    String Convert_FAQ_Title = "Convert_FAQ_Title";
    String Convert_FAQ_Content_Question1 = "Convert_FAQ_Content_Question1";
    String Convert_FAQ_Content_Answer1 = "Convert_FAQ_Content_Answer1";
    String Convert_FAQ_Content_Question2 = "Convert_FAQ_Content_Question2";
    String Convert_FAQ_Content_Answer2 = "Convert_FAQ_Content_Answer2";
    String Convert_FAQ_Content_Question3 = "Convert_FAQ_Content_Question3";
    String Convert_FAQ_Content_Answer3 = "Convert_FAQ_Content_Answer3";

    String Convert_Convert_Time = "Convert_Convert_Time";
    String Convert_Convert_Fail = "Convert_Convert_Fail";
    String Convert_Error_ = "Convert_Error_";
    String Convert_Error_50127 = "Convert_Error_50127";
    String Convert_Error_50128 = "Convert_Error_50128";
    String Convert_Error_50129 = "Convert_Error_50129";
    String Convert_Error_02014 = "Convert_Error_02014";


    String KChart_Market_Countdown = "KChart_Market_Countdown";//K线倒计时

    String KChart_Market_Onlinetime = "KChart_Market_Onlinetime";//K线上线时间
    String KChart_Market_CountDownTitle = "KChart_Market_CountDownTitle";// 马上就来
    String KChart_Market_Maintained = "KChart_Market_Maintained";// 维护中
    String KChart_Market_LearnMore = "KChart_Market_LearnMore";// 了解更多

    String KCHART_KCHART_FUNCTION_INTRODUCE = "KChart_KChart_Function_Introduce";// K线功能介绍
    String KCHART_COMPLETE = "KChart_Complete"; // 完成
    String KCHART_POSITION_INFO = "KChart_Position_Info"; // 仓位信息
    String KCHART_POSITION_INFO_INTRODUCE = "KChart_Position_Info_Introduce"; //您可以使用此按鈕切換柱狀圖與折線圖，當你想觀察長時間趨勢時，推薦你使用折線圖
    String KCHART_DEAL_RECORD = "KChart_Deal_Record"; //成交记录
    String KCHART_DEAL_RECORD_INTRODUCE = "KChart_Deal_Record_Introduce"; // 開啟後，您可以在K棒下方看到買單紀錄（B)與K棒上方看到賣單紀錄（S）的標示
    String KCHART_CURRENT_ENTRUST = "KChart_Current_Entrust"; // 当前委托
    String KCHART_CURRENT_ENTRUST_INTRODUCE = "KChart_Current_Entrust_Introduce"; // 您可以自定義不同類型訂單是否顯示在K線上
    String KCHART_COUNT_DOWN = "KChart_Count_Down"; // 倒数计时
    String KCHART_COUNT_DOWN_INTRODUCE = "KChart_Count_Down_Introduce";//開啟後，您可以看到距離當前Ｋ線收線還有多少時間
    String KCHART_MARKET_PRICE = "KChart_Market_Price"; //即时买卖价
    String KCHART_MARKET_PRICE_INTRODUCE = "KChart_Market_Price_Introduce"; // 開啟後，您可以在圖表上即時看到盤口買一跟賣一的價格
    String KCHART_NEW_DEAL = "KChart_New_Deal"; // 最新成交价
    String KCHART_NEW_DEAL_INTRODUCE = "KChart_New_Deal_Introduce"; // 開啟後，您可以在圖表上看到最新成交價
    String KCHART_SCOPE_HIGHEST_LOWEST = "KChart_Scope_Highest_Lowest"; // 区间最低最高价
    String KCHART_SCOPE_HIGHEST_LOWEST_INTRODUCE = "KChart_Scope_Highest_Lowest_Introduce"; // 開啟後，會顯示這個K線範圍裡最高與最低價

    String KCHART_FUNC_MORE_SPOT = "KChart_Func_More_Spot"; // Spot

    String KCHART_FUNC_MORE_CONTRACT = "KChart_Func_More_Contract"; // Contract
    String KCHART_FUNC_MORE_MARGIN = "KChart_Func_More_Margin"; // Margin
    String KCHART_FUNC_MORE_SWAP = "KChart_Func_More_Swap"; // Swap
    String KCHART_FUNC_MORE_STRATEGY = "KChart_Func_More_Strategy"; // Strategy
    String KChart_view_PopularSearches = "KChart_view_PopularSearches"; // 热搜榜
    String KChart_Kline_StepTwoDay = "KChart_Kline_StepTwoDay"; // 2D
    String KChart_Kline_StepFiveDay = "KChart_Kline_StepFiveDay"; // 5D

    String KChart_Market_DepthPriceSpread = "KChart_Market_DepthPriceSpread";

    String KChart_Market_DepthDelegatePrice = "KChart_Market_DepthDelegatePrice";

    String KChart_Market_TotalPendingOrders = "KChart_Market_TotalPendingOrders";

    String KChart_Kline_DepthNoData = "KChart_Kline_DepthNoData";

    String KChart_Kline_OrderBook = "KChart_Kline_OrderBook";

    String KChart_Kline_DepthChart = "KChart_Kline_DepthChart";

    String kChart_News_NewsAboutCoin = "kChart_News_NewsAboutCoin"; // 币种相关新闻

    String kChart_News_NewsAboutOther = "kChart_News_NewsAboutOther"; // In Other News

    String kChart_News_NewsAboutCoinEmpty="kChart_News_NewsAboutCoinEmpty"; //当前币对没有相关新闻
    String kChart_News_TopTip="kChart_News_TopTip";// 风险提示

    String Assets_Analysis_Time_Customize = "Assets_Analysis_Time_Customize"; // 自定义
    String Spot_Trade_Minimum_Order_Value_Toast = "Spot_Trade_Minimum_Order_Value_Toast"; //不满足最小下单价值
    String Spot_Trade_Operation_Market_Tip = "Spot_Trade_Operation_Market_Tip"; //


    String Futures_Simulate_Sheet_Title = "Futures_Simulate_Sheet_Title";
    String Futures_Simulate_Sheet_Toast_Sucess = "Futures_Simulate_Sheet_Toast_Sucess";
    String Futures_Simulate_Sheet_Toast_Fail_TimeLimit = "Futures_Simulate_Sheet_Toast_Fail_TimeLimit";
    String Futures_Simulate_Sheet_Toast_Fail_Account = "Futures_Simulate_Sheet_Toast_Fail_Account";
    String Futures_Simulate_Sheet_Alert = "Futures_Simulate_Sheet_Alert";
    String Futures_Simulate_Sheet_Alert_Highlight = "Futures_Simulate_Sheet_Alert_Highlight";
    String Futures_Simulate_Sheet_SelectIcon = "Futures_Simulate_Sheet_SelectIcon";
    String Futures_Simulate_Recharge = "Futures_Simulate_Recharge";

    String User_Kyc_Update_File = "User_Kyc_Update_File";//上传文件
    String TEXT_KLINE_ORDER_EDIT_GUIDE_TITLE = "text_kline_order_edit_guide_title";//画线下单
    String TEXT_GUIDE_SKIP = "text_guide_skip";//跳过


    //*********************************** 首页排行榜 ***********************************************//

    String HomeMarket_List_AllPrice = "HomeMarket_List_AllPrice";//全部价格
    String HomeMarket_List_AllValue = "HomeMarket_List_AllValue";//全部市值

    String HomeMarket_List_24Hours_Volume = "HomeMarket_List_24Hours_Volume";//24H成交额
    String HomeMarket_List_FiveMinutes_Volume = "HomeMarket_List_FiveMinutes_Volume";//5分钟成交额
    String HomeMarket_List_OneHour_Volume = "HomeMarket_List_OneHour_Volume";//1小时成交额
    String HomeMarket_List_Week_Volume = "HomeMarket_List_Week_Volume";//当周成交额
    String HomeMarket_List_Month_Volume = "HomeMarket_List_Month_Volume";//当月成交额

    String HomeMarket_List_Increase_ToDay = "HomeMarket_List_Increase_ToDay";//今日涨幅
    String HomeMarket_List_Increase_FiveMinutes = "HomeMarket_List_Increase_FiveMinutes";//5分钟涨幅
    String HomeMarket_List_Increase_OneHour = "HomeMarket_List_Increase_OneHour";//1小时涨幅
    String HomeMarket_List_Increase_Week = "HomeMarket_List_Increase_Week";//当周涨幅
    String HomeMarket_List_Increase_Month = "HomeMarket_List_Increase_Month";//当月涨幅

    String HomeMarket_List_Decline_ToDay = "HomeMarket_List_Decline_ToDay";//今日跌幅
    String HomeMarket_List_Decline_FiveMinutes = "HomeMarket_List_Decline_FiveMinutes";//5分钟跌幅
    String HomeMarket_List_Decline_OneHour = "HomeMarket_List_Decline_OneHour";//1小时跌幅
    String HomeMarket_List_Decline_Week = "HomeMarket_List_Decline_Week";//当周跌幅
    String HomeMarket_List_Decline_Month = "HomeMarket_List_Decline_Month";//当月跌幅

    String HomeMarket_FavouriteTypeTitle = "HomeMarket_FavouriteTypeTitle";// 展示类型
    String HomeMarket_view_ItemTypeKLineTitle = "HomeMarket_view_ItemTypeKLineTitle";// 展示模式
    String HomeMarket_view_ItemTypeKLine = "HomeMarket_view_ItemTypeKLine";// K线
    String HomeMarket_view_ItemTypeData = "HomeMarket_view_ItemTypeData";// 数据
    String HomeMarket_view_Last24Hour = "HomeMarket_view_Last24Hour";// 过去 24h

    String HomeMarket_view_ChangeOrLastPrice = "HomeMarket_view_ChangeOrLastPrice";// 涨跌幅/最新价
    String HomeMarket_TransactionVolumeList = "HomeMarket_TransactionVolumeList";// 成交额榜
    String HomeMarket_24HourTransactionVolume = "HomeMarket_24HourTransactionVolume";// 24H成交额
    String HomeMarket_OnlineTime = "HomeMarket_OnlineTime";// 上线时间
    String HomeMarket_OpenMarketCountDown = "HomeMarket_OpenMarketCountDown";// 开盘倒计时
    String HomeMarket_view_Recharge = "HomeMarket_view_Recharge";// 充值
    String HomeMarket_view_Withdrawal = "HomeMarket_view_Withdrawal";// 提现
    String HomeMarket_view_Transfer = "HomeMarket_view_Transfer";// 划转
    String HomeMarket_view_Favourite = "HomeMarket_view_Favourite";// 收藏
    String HomeMarket_view_Exchange = "HomeMarket_view_Exchange";// 交易
    String HomeMarket_view_Coin = "HomeMarket_view_Coin";// 币种
    String HomeMarket_view_Borrow = "HomeMarket_view_Borrow";// 借款
    String HomeMarket_view_Repay = "HomeMarket_view_Repay";// 还款
    String HomeMarket_List_Page_Title = "HomeMarket_List_Page_Title";// 实时排行榜
    String HomeMarket_Increase_SubTitle = "HomeMarket_Increase_SubTitle";// 涨幅最大的币种排名
    String HomeMarket_Favorite_SubTitle = "HomeMarket_Favorite_SubTitle";// 自定义的币种列表
    String HomeMarket_Decline_SubTitle = "HomeMarket_Decline_SubTitle";// 跌幅最大的币种排名
    String HomeMarket_NewCoin_SubTitle = "HomeMarket_NewCoin_SubTitle";// 发掘最新币种，及时捕捉市场机会
    String HomeMarket_Volume_SubTitle = "HomeMarket_Volume_SubTitle";// 加密货币成交额排名
    String HomeMarket_Title_RealTimeRank = "HomeMarket_Title_RealTimeRank";// 实时排行榜

    //*********************************** 首页排行榜 ***********************************************//


    String view_Duration_Minute = "view_Duration_Minute";//{0}分钟
    String view_Duration_Hour = "view_Duration_Hour";//{0}小时
    String view_Duration_Day = "view_Duration_Day";//{0}天
    String X220610_Contract_What_is_init_margin = "x220610_contract_what_is_init_margin";//策略将会使用您合约账户的资产。创建策略时实际使用的资金取决于市场，可能不等于您输入的数量。

    String view_Cooper_UnsupportTip = "view_Cooper_UnsupportTip";// Cooper用户提示：不支持

    String Spot_SpotTrade_OCO_Entrust = "Spot_SpotTrade_OCO_Entrust"; //OCO
    String StrategyTrading_Grid_RunMinAmountTip = "StrategyTrading_Grid_RunMinAmountTip"; //运行此网格最低需要{0}
    String Margin_MarginTrade_Auto_Borrow_Repay = "Margin_MarginTrade_Auto_Borrow_Repay"; //自动借还

    String kChart_Setting_Abstract = "kChart_Setting_Abstract"; //K线十字摘要
    String kChart_Setting_Floating_Window = "kChart_Setting_Floating_Window"; //悬浮窗口
    String kChart_Setting_None = "kChart_Setting_None"; //无
    String kChart_Info_Change_Rase = "kChart_Info_Change_Rase"; //涨幅
    String kChart_Info_Change_Amount = "kChart_Info_Change_Amount"; //涨额
    String kChart_Info_Amount = "kChart_Info_Amount"; //量

    String Spot_SpotTrade_TakeProfitStopLoss = "Spot_SpotTrade_TakeProfitStopLoss"; //止盈止损

    String Spot_SpotTrade_ModifyOrder_MarketPriceBuy_Placeholder = "Spot_SpotTrade_ModifyOrder_MarketPriceBuy_Placeholder";  //以市场最优价格买入

    String Spot_SpotTrade_ModifyOrder_MarketPriceSell_Placeholder = "Spot_SpotTrade_ModifyOrder_MarketPriceSell_Placeholder";  //以市场最优价格卖出

    String Spot_SpotOrderList_EditOrderDelegatePrice_HintText = "Spot_SpotOrderList_EditOrderDelegatePrice_HintText"; //请输入委托价格

    String Spot_SpotTrade_OCOEntrust_DelegateCount = "Spot_SpotTrade_OCOEntrust_DelegateCount"; //数量

    String Spot_Track_Title = "Spot_Track_Title";//追踪委托

    String Spot_Track_SourceName = "Spot_Track_SourceName";//追踪委托
    String Spot_Track_Status_Prefix = "Spot_Track_Status_";//追踪委托状态名称，前缀
    String Spot_Track_MarketPrice = "Spot_Track_MarketPrice";//市价
    //理财原生分享
    String Earn_LaunchPool_ShareTitle = "Earn_LaunchPool_ShareTitle";
    String Earn_LaunchPool_ShareDesc = "Earn_LaunchPool_ShareDesc";


    String Savings_Flexible_CounponAddding = "Savings_Flexible_CounponAddding"; // 加息中
    String Savings_Regular_CounponAddding = "Savings_Regular_CounponAddding"; // 加息中
    String Savings_CounponAddding_Title = "Savings_CounponAddding_Title"; // {0}加息中
    String Savings_CounponAddding_Tips = "Savings_CounponAddding_Tips"; // 加息券
    String Savings_CounponAddding_Period = "Savings_CounponAddding_Period"; // {0}天
    String Savings_CounponAddding_StardDate = "Savings_CounponAddding_StardDate"; // 启动日期：{0}
    String Savings_CounponAddding_EndDate = "Savings_CounponAddding_EndDate"; // 有效期至：{0}
    String StrategyTrading_Common_Terminated = "StrategyTrading_Common_Terminated";//已终止

    String WebView_Dialog_ContinueVisiting = "WebView_Dialog_ContinueVisiting"; // 继续访问
    String kChart_Market_Price_Change = "kChart_Market_Price_Change"; //价格变化
    String kChart_Market_Price_Range = "kChart_Market_Price_Range"; //价格区间
    String kChart_Market_Lowest_price = "kChart_Market_Lowest_price"; //最低价
    String kChart_Market_Highest_price = "kChart_Market_Highest_price"; //最高价
    String kChart_Market_Today = "kChart_Market_Today"; //今日
    String kChart_Market_7_Days = "kChart_Market_7_Days"; //7天

    String kChart_Market_TodayPriceChange = "kChart_Market_TodayPriceChange"; //今日
    String kChart_Market_30_DaysPriceChange = "kChart_Market_30_DaysPriceChange"; //30天

    String kChart_Market_30_Days = "kChart_Market_30_Days"; //30天
    String kChart_Market_90_Days = "kChart_Market_90_Days"; //90天
    String kChart_Market_180_Days = "kChart_Market_180_Days"; //180天
    String kChart_Maket_1_Year = "kChart_Maket_1_Year"; //1年
    String kChart_Market_60_mins = "kChart_Market_60_mins"; //60分钟
    String kChart_Market_All_time = "kChart_Market_All_time"; //全部

    String kChart_Market_About_KeyStats = "kChart_Market_About_KeyStats"; //Key Stats
    String kChart_Market_Quotes = "kChart_Market_Quotes"; //Quotes
    String kChart_Market_News = "kChart_Market_News"; //News
    String kChart_Swap_Analysis = "kChart_Swap_Analysis"; //Analysis
    String kChart_Market_About = "kChart_Market_About"; //About
    String kChart_Market_About_About = "kChart_Market_About_About"; //About {0}
    String kChart_Swap_About_Tags = "kChart_Swap_About_Tags"; //Tags
    String kChart_Market_About_Community = "kChart_Market_About_Community"; //Community
    String kChart_Market_About_Resources = "kChart_Market_About_Resources"; //Resources
    String kChart_Market_About_Investment = "kChart_Market_About_Investment"; //投资机构
    String kChart_Market_About_KeyStats_Volume_24 = "kChart_Market_About_KeyStats_Volume_24"; //Volume 24h
    String Spot_KLineEtf_search_history = "Spot_KLineEtf_search_history"; //侧边栏搜索历史

    String Spot_s_usdt_mix_contract = "Spot_s_usdt_mix_contract"; //模拟U本位
    String Spot_s_usdc_mix_contract = "Spot_s_usdc_mix_contract"; //模拟USDC本位
    String Spot_s_usd_mix_contract = "Spot_s_usd_mix_contract"; //模拟币本位

    String Spot_KLineEtf_key_data = "Spot_KLineEtf_key_data"; //ETF详情
    String Spot_KLineEtf_asset_allocation = "Spot_KLineEtf_asset_allocation";
    String Spot_KLineEtf_exponent_symbol = "Spot_KLineEtf_exponent_symbol";
    String Spot_klineEtf_settlement_unit = "spot_klineEtf_settlement_unit";
    String Spot_KLineEtf_index_basis = "Spot_KLineEtf_index_basis";
    String Spot_KLineEtf_leverage_ratio = "Spot_KLineEtf_leverage_ratio";
    String Spot_KLineEtf_issuing_date = "Spot_KLineEtf_issuing_date";


    String kChart_Martet_About_About = "kChart_Martet_About_About";


    String KChart_More_Spot_Fix = "KChart_More_Spot_Fix"; //现货定投
    String KChart_More_Spot_Grid = "KChart_More_Spot_Grid"; //现货网格
    String KChart_More_Contract_Grid = "KChart_More_Contract_Grid"; //合约网格
    String KChart_More_Spot_Marting = "KChart_More_Spot_Marting"; //现货马丁格尔
    String KChart_More_Contract_Marting = "KChart_More_Contract_Marting"; //合约马丁格尔
    String KChart_More_Spot_Cta = "KChart_More_Spot_Cta"; //现货CTA
    String KChart_More_Contract_Cta = "KChart_More_Contract_Cta"; //合约CTA
    String KChart_More_Spot = "KChart_More_Spot"; //现货
    String KChart_More_Margin = "KChart_More_Margin"; //杠杆
    String KChart_More_Bitget_Swap = "KChart_More_Bitget_Swap"; //Bitget闪兑
    String KChart_More_Contract = "KChart_More_Contract"; //合约

    String KChart_More_ComingSoon = "KChart_More_ComingSoon"; //即将推出
    String KChart_Price_RangeTitleTips = "KChart_Price_RangeTitleTips"; //价格变动
    String KChart_Price_RangeTipsDes = "KChart_Price_RangeTipsDes"; //1. “价格变动”的涨跌幅按照设置的K线时间，取日线开盘价进行计算得出\n2. 当前设置的K线时间为：{0}
    String KChart_Price_ToChangedTips = "KChart_Price_ToChangedTips"; //去修改
    String CopyTrade_MyFollow_Current_Follow_Trader = "CopyTrade_MyFollow_Current_Follow_Trader";//	交易员

    String CopyTrade_MyFollow_Current_Follow_Profile_loss = "CopyTrade_MyFollow_Current_Follow_Profile_loss";//	止盈止损

    String HOME_SCREEN_QUICK_ACTION_TRADE = "home_screen_quick_action_trade";//立即交易
    String HOME_SCREEN_QUICK_ACTION_COPYTRADE = "home_screen_quick_action_Copytrade";//跟单
    String HOME_SCREEN_QUICK_ACTION_ORDERS = "home_screen_quick_action_Orders";//当前委托
    String HOME_SCREEN_QUICK_ACTION_NOTIFICATIONS = "home_screen_quick_action_Notifications";//通知


    String Earn_RechargeGuide_Content = "Earn_RechargeGuide_Content";    // 您的账户尚无理财记录。请先补充现货账户中的余额，Bitget将依持仓币种为您推荐最优产品
    String Earn_RechargeGuide_Buy = "Earn_RechargeGuide_Buy";    // 立即买币
    String Earn_RechargeGuide_Recharge = "Earn_RechargeGuide_Recharge";    // 充值
    String Earn_Recommend_Content = "Earn_Recommend_Content";    // 您的账户尚无理财记录。起步並不困難，我們推薦您從以下保本型產品開始Bitget理財之旅，低門檻、低風險、賺入穩定收益！
    String Earn_Recommend_Apy = "Earn_Recommend_Apy";    // 最高年化
    String Earn_Recommend_ToHome = "Earn_Recommend_ToHome";    // 前往理财首页
    String Earn_Home_LastIncome = "Earn_Home_LastIncome";    // 昨日收益
    String Earn_Home_TotalIncome = "Earn_Home_TotalIncome";    // 累计收益
    String Earn_Home_EarnNow = "Earn_Home_EarnNow";    // 立即赚币
    String Earn_Home_MyPositions = "Earn_Home_MyPositions";    // 我的持仓理财
    String Earn_Flexible_ProductName = "Earn_Flexible_ProductName";    // 活期理财宝
    String Earn_Savings_TotalIncome = "Earn_Savings_TotalIncome";    // 累计利息
    String Earn_Regular_ProductName = "Earn_Regular_ProductName";    // 定期理财宝
    String Earn_Savings_Period = "Earn_Savings_Period";    // {0}天
    String Earn_Savings_DailySettlement = "Earn_Savings_DailySettlement";    // 日息日结
    String Earn_Savings_ExpireSettlement = "Earn_Savings_ExpireSettlement";    // 到期后派息
    String Earn_Launchpool_ProductName = "Earn_Launchpool_ProductName";    // 新币挖矿
    String Earn_Launchpool_EndDate = "Earn_Launchpool_EndDate";    // {0} 结束
    String Earn_Launchpool_ProjectName = "Earn_Launchpool_ProjectName";    // 项目名称
    String Earn_Launchpool_TotalIncome = "Earn_Launchpool_TotalIncome";    // 累计收益
    String Earn_SharkFin_ProductName = "Earn_SharkFin_ProductName";    // 鲨鱼鳍
    String Earn_SharkFin_EndDate = "Earn_SharkFin_EndDate";    // {0} 到期
    String Earn_SharkFin_Trend = "Earn_SharkFin_Trend";    // 预测走向
    String Earn_SharkFin_Bearish = "Earn_SharkFin_Bearish";    // 看跌{0}
    String Earn_SharkFin_Bullish = "Earn_SharkFin_Bullish";    // 看涨{0}
    String Earn_SharkFin_PriceRange = "Earn_SharkFin_PriceRange";    // 价格区间
    String Earn_SmartTrend_ProductName = "Earn_SmartTrend_ProductName";    // 趋势智赢
    String Earn_RangeSniper_ProductName = "Earn_RangeSniper_ProductName";    // 区间猎手
    String Earn_DualInvestment_ProductName = "Earn_DualInvestment_ProductName";    // 双币投资
    String Earn_DualInvestment_EndDate = "Earn_DualInvestment_EndDate";    // {0} 到期
    String Earn_DualInvestment_Trend = "Earn_DualInvestment_Trend";    // 预测走向
    String Earn_DualInvestment_Bearish = "Earn_DualInvestment_Bearish";    // 看跌{0}
    String Earn_DualInvestment_Bullish = "Earn_DualInvestment_Bullish";    // 看涨{0}
    String Earn_DualInvestment_TargetPrice = "Earn_DualInvestment_TargetPrice";    // 目标价格
    String Earn_PoS_ProductName = "Earn_PoS_ProductName";    // PoS质押
    String Earn_BGB_ProductName = "Earn_BGB_ProductName";    // 质押BGB
    String Earn_BGB_TotalIncome = "Earn_BGB_TotalIncome";    // 累计收益
    String Earn_APR_Title = "Earn_APR_Title";    // 阶梯等级
    String Earn_APR_SubTitle = "Earn_APR_SubTitle";    // 产品收益根据以下阶梯规则计算：
    String Earn_APR_Level = "Earn_APR_Level";    // 等级{0}
    String Earn_Savings_Interest = "Earn_Savings_Interest";  //理财宝 apy
    String Earn_DualInvestment_Interest = "Earn_DualInvestment_Interest";  //双币理财 apy
    String Earn_BGB_Interest = "Earn_BGB_Interest"; // -bgb质押  apy
    String Text_Share_Desc_Unlogin = "x220628_community_app_desc"; // share desc（unlogin）

    String Text_Share_more = "share_panel_more";// 更多（分享）
    String Text_Contract_Trade_Report = "share_contract_trade_report";// 合约交易报告
    String Text_Spot_Trade_Report = "share_spot_trade_report";// 现货交易报告

    String CopyTrade_ClosePosition_AllOperation = "CopyTrade_ClosePosition_AllOperation"; //一键操作


    String Futures_Position_History = "Futures_Position_History";//历史仓位
    String Futures_Position_Profit = "Futures_Position_Profit";//仓位盈亏
    String Futures_Position_Open_CloseTime = "Futures_Position_Open_CloseTime";//开平仓次数
    String Futures_Position_AllCloseTime = "Futures_Position_AllCloseTime";//全部平仓时间
    String Futures_Position_CloseAccount = "Futures_Position_CloseAccount";//平仓量
    String Futures_Position_CloseValue = "Futures_Position_CloseValue";//平仓价值

    String Futures_Position_OpenPriceAvg = "Futures_Position_OpenPriceAvg"; // 开仓均价

    String Futures_Position_ClosePriceAvg = "Futures_Position_ClosePriceAvg"; // 平仓均价

    String Share_Image_To_Insights_Fail = "share_image_to_insights_fail";//分享至insights失败

    String Kyc_AccountRegisterSuccess_Title = "Kyc_AccountRegisterSuccess_Title";
    String Kyc_view_UseBitgetService_Tip = "Kyc_view_UseBitgetService_Tip";
    String Kyc_view_UploadIdCard_Tip = "Kyc_view_UploadIdCard_Tip";
    String Kyc_view_FaceVerification_Tip = "Kyc_view_FaceVerification_Tip";
    String Kyc_view_AuthenticateNow_Button = "Kyc_view_AuthenticateNow_Button";
    String Kyc_view_JumpOver_Button = "Kyc_view_JumpOver_Button";
    String Kyc_NeedIdCerification_Title = "Kyc_NeedIdCerification_Title";
    String Kyc_InAuthentication_Title = "Kyc_InAuthentication_Title";
    String Kyc_InAuthentication_Content = "Kyc_InAuthentication_Content";
    String Kyc_view_CertificationStatus_Button = "Kyc_view_CertificationStatus_Button";
    String Kyc_CompleteIdVerification_Tip = "Kyc_CompleteIdVerification_Tip";

    String CopyTrade_MyFollow_Current_Cur_Symbol = "CopyTrade_MyFollow_Current_Cur_Symbol"; //
    String CopyTrade_MyFollow_Current_Cur_Contract = "CopyTrade_MyFollow_Current_Cur_Contract"; //
    String CopyTrade_MyFollow_Current_FollowFilterTrader = "CopyTrade_MyFollow_Current_FollowFilterTrader"; //交易员
    String CopyTrade_MyFollow_Current_FollowFilterMix = "CopyTrade_MyFollow_Current_FollowFilterMix";//合约
    String CopyTrade_MyFollow_Current_FollowFilterCoin = "CopyTrade_MyFollow_Current_FollowFilterCoin";//交易对
    String User_Subparent_AccountSwitchSuccess = "User_Subparent_AccountSwitchSuccess";//账户切换成功

    String Login_User_Protocol = "Login_User_Protocol";//我已阅读并同意bitget的{0}与{1}
    String Login_User_Terms = "Login_User_Terms";//用户协议
    String Login_User_Policy = "Login_User_Policy";//个人隐私政策

    String Insights_HelpCenter_Title = "Insights_HelpCenter_Title"; // 帮助中心


    String Spot_ChargingNotification_safeing = "Spot_ChargingNotification_safeing"; // 保障中
    String Spot_ChargingNotification_safeTip = "Spot_ChargingNotification_safeTip"; // 您充值的资产将由Bitget 3亿用户保护基金保障
    String Spot_Charge_Confirming = "Spot_Charge_Confirming";
    String Spot_Charge_Accept = "Spot_Charge_Accept";
    String Spot_Charge_Fail = "Spot_Charge_Fail";
    String Spot_Charge_Fail_Confirming = "Spot_Charge_Fail_Confirming";
    String Spot_Charge_Fail_Accept = "Spot_Charge_Fail_Accept";
    String Spot_Charge_Confirming_Accept = "Spot_Charge_Confirming_Accept";
    String Spot_Charge_Fail_Confirming_Accept = "Spot_Charge_Fail_Confirming_Accept";
    String Spot_Withdraw_Confirming = "Spot_Withdraw_Confirming";
    String Spot_Withdraw_Send = "Spot_Withdraw_Send";
    String Spot_Withdraw_Fail = "Spot_Withdraw_Fail";
    String Spot_Withdraw_Fail_Confirming = "Spot_Withdraw_Fail_Confirming";
    String Spot_Withdraw_Fail_Send = "Spot_Withdraw_Fail_Send";
    String Spot_Withdraw_Confirming_Send = "Spot_Withdraw_Confirming_Send";
    String Spot_Withdraw_Fail_Confirming_Send = "Spot_Withdraw_Fail_Confirming_Send";
    String Login_HKRestricted_Content="Login_HKRestricted_Content";// Your IP address shows that you are trying to access our services from a restricted jurisdiction, we are unable to provide any services in your area, we apologise for the inconvenience caused.
    String Login_HKRestricted_Title="Login_HKRestricted_Title";// Restricted IP
    String Login_HKRestricted_GotiT="Login_HKRestricted_GotiT";// Got it
    String Assets_Transfer_ExperienceTranserOutTip = "Assets_Transfer_ExperienceTranserOutTip"; //转出资产，该币种的体验金余额将回收，币种可用资产减少，请注意资产变化。
    String Assets_Margin_DetailExperienceMoney = "Assets_Margin_DetailExperienceMoney"; //体验金
    String Safe_Center_Verification_Switch = "Safe_Center_Verification_Switch";//切换验证方式

    String Safe_Register_Email_Hint = "Safe_Register_Email_Hint";//Enter your email
    String Safe_Register_Mobile_Number_Hint = "Safe_Register_Mobile_Number_Hint";//Enter your phone number
    String Safe_Security_Switch_Confirm = "Safe_Security_Switch_Confirm";//Confirm

    String Safe_Center_Risk_Title = "Safe_Center_Risk_Title";//安全风险提示
    String Safe_Center_Risk_Desc = "Safe_Center_Risk_Desc";//为了您的资金安全，请在安全中心完成2FA中任意两项安全项的绑定，并设置资金密码后可继续操作。
    String Safe_Center_Set = "Safe_Center_Set";//开始
    String Safe_Center_Cancel = "Safe_Center_Cancel";//取消
    String Safe_Center_Risk_Desc_2 = "Safe_Center_Risk_Desc_2";//为了您的账户安全，请完成双重验证中至少两项。
    String Safe_Center_Verification_Switch_Desc = "Safe_Center_Verification_Switch_Desc";//{0} methods are required
    String Safe_Center_Verification_Switch_toast = "Safe_Center_Verification_Switch_toast";//Already selected {0} methods

    String Spot_TrackEntrust_track_Title ="Spot_TrackEntrust_track_Title";// 追踪委托

    String Futures_TransactionDetail_Fee = "Futures_TransactionDetail_Fee"; // 手续费
    String Futures_TransactionDetail_LiquidityDirection = "Futures_TransactionDetail_LiquidityDirection"; // 流动性方向
    String Futures_HistoryEntrust_LiquidationPrice = "Futures_HistoryEntrust_LiquidationPrice"; // 强平价格
    String Futures_TransactionDetail_Taker = "Futures_TransactionDetail_Taker"; // 吃单
    String Futures_TransactionDetail_Maker = "Futures_TransactionDetail_Maker"; // 挂单


    String KChart_Kline_TextSustainable = "KChart_Kline_TextSustainable"; //永续
    String KChart_Kline_TextDelivery = "KChart_Kline_TextDelivery"; //交割
    String KChart_Kline_TipsMarkPrice = "KChart_Kline_TipsMarkPrice"; //标记价格
    String KChart_Kline_PriceVolume = "KChart_Kline_PriceVolume"; //额

    String Login_User_Table1 = "Login_User_Table1";// Email / Mobile
    String Login_User_Table2 = "Login_User_Table2";//Sub-account
    String Login_User_Next = "Login_User_Next";//Next

    String Login_User_Virtual_Email_Unsupport = "Login_User_Virtual_Email_Unsupport";//虚拟邮箱类型的子账号不支持此处登录，请登录主账号使用切换账号功能登录
    String Login_User_Login_Unsupport_Pwd_Tip ="Login_User_Login_Unsupport_Pwd_Tip";//您的密码包含不支持的零宽字符，请检查后重新手动输入
    String Login_User_SignUp_Unsupport_Pwd_Tip = "Login_User_SignUp_Unsupport_Pwd_Tip";//密码格式错误或包含不支持的特殊字符，请重新输入。
    String Login_User_SignUp_Support_Character ="Login_User_SignUp_Support_Character";//仅支持: ~`!@#$%^&*()_-+={}[]|;:,<>.?/
    String Login_User_Sub_Account_Hint = "Login_User_Sub_Account_Hint";
    String Login_User_Scan_Confirm_Login = "Login_User_Scan_Confirm_Login";
    String Login_User_Scan_Confirm_Login_Des = "Login_User_Scan_Confirm_Login_Des";
    String Login_User_Scan_Address = "Login_User_Scan_Address";
    String Login_User_Scan_Location = "Login_User_Scan_Location";
    String Login_User_Scan_Device = "Login_User_Scan_Device";
    String Login_User_Scan_AuthLogin = "Login_User_Scan_AuthLogin";
    String Login_User_Scan_Cancel = "Login_User_Scan_Cancel";
    String Login_User_Scan_Expired = "Login_User_Scan_Expired";
    String Login_User_Scan_Rescan = "Login_User_Scan_Rescan";
    String Login_User_Scan_QR_Code = "Login_User_Scan_QR_Code";
}