//package com.upex.common.utils
//
//import android.annotation.SuppressLint
//import android.content.Context
//import android.content.res.Configuration
//import android.content.res.Resources
//import android.util.TypedValue
//import androidx.annotation.AttrRes
//import androidx.annotation.StyleableRes
//import androidx.appcompat.view.ContextThemeWrapper
//import com.example.myapplication.R
//import com.upex.biz_service_interface.widget.HomeNavBar
//import com.upex.common.R
//import com.upex.common.utils.app.ApplicationUtil
//import kotlin.properties.Delegates
//
///**
// *  author : wanghui
// *  date : 2021/3/2 10:17 AM
// *  description :
// */
//object ThemeUtils {
//    enum class ThemeEnum(
//        @StyleableRes private val themeStyleId: Int,
//        val showName: String,
//        @StyleableRes private val themeTransparentStyleId: Int
//    ) {
//        Light(R.style.BaseAppTheme_Light, "standard", R.style.BaseAppTheme_Light_Transparent),
//        Dark(R.style.BaseAppTheme_Dark, "dark", R.style.BaseAppTheme_Dark_Transparent);
//
//        fun getThemeStyleId():Int{
//            if (XiaoMiUtils.isXiaoMiV125){
//                return XiaoMiUtils.getTheme(this)
//            }
//            return themeStyleId
//        }
//
//        fun getThemeTransparentStyleId():Int{
//            if (XiaoMiUtils.isXiaoMiV125){
//                return XiaoMiUtils.getThemeTransparent(this)
//            }
//            return themeTransparentStyleId
//        }
//    }
//
//    var themeEnum: ThemeEnum = ThemeEnum.Light
//    lateinit var contextThemeWrapper: ContextThemeWrapper
//    var isFollowSystem by Delegates.notNull<Boolean>()
//
//    val themeChangeListeners = mutableListOf<OnThemeChangeListener>()
//
//    fun addThemeChangeListener(listener: OnThemeChangeListener) {
//        themeChangeListeners.add(listener)
//    }
//
//    var isCurrentLight: Boolean = true
//        private set
//
//    init {
//        initThemeEnum()
//
//        changeLightVar()
//    }
//
//    @SuppressLint("ResourceType")
//    private fun initThemeEnum() {
//        isFollowSystem = CommonBridgeUtils.isThemeFollowSystem()//SPUtilHelper.isThemeFollowSystem
//        var theme: ThemeEnum? = null
//        try {
//            theme = ThemeEnum.valueOf(CommonBridgeUtils.getThemeName())//SPUtilHelper.themeName
//            if (isFollowSystem) {
//                val temp = getCurrentSystemTheme()
//                if (theme != temp) {
//                    theme = temp
//                    CommonBridgeUtils.setThemeName(theme.name)//SPUtilHelper.themeName = theme.name
//                }
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//        themeEnum = theme ?: ThemeEnum.Light
//
//        ThemeUtils.themeEnum =
//            ThemeEnum.values().find { it.showName == theme?.showName }
//                ?: ThemeEnum.Light
//
//        contextThemeWrapper = ContextThemeWrapper(ApplicationUtil.app, themeEnum.getThemeStyleId())
//
//        ThemeUtils.init(contextThemeWrapper)
//    }
//
//    fun init(themWrapper: ContextThemeWrapper) {
//        contextThemeWrapper = themWrapper
//    }
//
//    /**
//     * 获取当前手机系统主题
//     *  true 暗黑主题
//     *  false 亮色主题
//     */
//    fun getCurrentSystemTheme(): ThemeEnum {
//        return if ((ApplicationUtil.app.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES) ThemeEnum.Dark else ThemeEnum.Light
//    }
//
//    @SuppressLint("ResourceType")
//    fun changeTheme(themeEnum: ThemeEnum) {
//        ThemeUtils.themeEnum =
//            ThemeEnum.values().find { it.showName == themeEnum.showName }
//                ?: ThemeEnum.Light
//        ThemeUtils.themeEnum = themeEnum
//
//        contextThemeWrapper =
//            ContextThemeWrapper(ApplicationUtil.app, ThemeUtils.themeEnum.getThemeStyleId())
//        ThemeUtils.init(contextThemeWrapper)
//
//        CommonBridgeUtils.setThemeName(themeEnum.name)//SPUtilHelper.themeName = themeEnum.name
//
//        ToolResUtil.onSkinChanged()
//        changeLightVar()
//        //修改吐司的背景
//        ToastUtil.onThemeChanged()
//        HomeNavBar.onSkinChanged()
//
//        CommonBridgeUtils.setNewInitRequestTime(0)//SPUtil.setNewInitReqeustTime(0)
//
//        CommonBridgeUtils.commonInfoReqUtilForceReqeustAll()//CommonInfoReqUtil.forceReqeustAll()
//        themeChangeListeners.forEach { it.onThemeChanged() }
//    }
//
//    fun changeFollowSystem(isFollowSystem: Boolean) {
//        ThemeUtils.isFollowSystem = isFollowSystem
//        CommonBridgeUtils.setThemeFollowSystem(isFollowSystem)//SPUtilHelper.isThemeFollowSystem = isFollowSystem
//
//    }
//
//    // 使用代码获取主题属性颜色值的方法
//    fun getThemeColor(context: Context? = null, @AttrRes attrIdForColor: Int): Int {
//        val typedValue = TypedValue()
//        val theme = context?.theme ?: getTheme()
//        theme.resolveAttribute(attrIdForColor, typedValue, true);
//        return typedValue.data
//    }
//
//    fun getThemeString(context: Context? = null, @AttrRes attrIdForString: Int): CharSequence {
//        val typedValue = TypedValue()
//        val theme = context?.theme ?: getTheme()
//        theme.resolveAttribute(attrIdForString, typedValue, true);
//        return typedValue.string
//    }
//
//    fun getThemeBoolean(context: Context? = null, @AttrRes attrIdForBoolean: Int): Boolean {
//        val typedValue = TypedValue()
//        val theme = context?.theme ?: getTheme()
//        theme.resolveAttribute(attrIdForBoolean, typedValue, true);
//        return typedValue.data != 0
//    }
//
//    // 使用代码获取主题属性颜色值的方法
//    fun getThemeId(context: Context? = null, @AttrRes attrId: Int): Int {
//        val typedValue = TypedValue()
//        val theme = context?.theme ?: getTheme()
//        theme.resolveAttribute(attrId, typedValue, true);
//        return typedValue.resourceId
//    }
//
//    private fun changeLightVar() {
//        isCurrentLight = themeEnum == ThemeEnum.Light
//    }
//
//    interface OnThemeChangeListener {
//        fun onThemeChanged();
//    }
//
//    fun getTheme(): Resources.Theme {
//        return contextThemeWrapper.theme
//    }
//
//    /**
//     * 获取当前context的主题枚举
//     */
//    fun getThemeEnum(context: Context): ThemeEnum {
//        val themeName = getThemeString(context, R.attr.string_theme_name)
//        return ThemeEnum.values().find { it.showName == themeName } ?: themeEnum
//    }
//}