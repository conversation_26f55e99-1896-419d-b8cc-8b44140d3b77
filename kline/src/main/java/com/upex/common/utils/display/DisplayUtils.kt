package com.upex.common.utils.display

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.graphics.Point
import android.graphics.Rect
import android.os.Build
import android.util.DisplayMetrics
import android.view.Display
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import com.upex.common.utils.app.ApplicationUtil
import java.lang.reflect.Method


/**
 * Created by lion on 2017/4/11.
 */
object DisplayUtils {
    /**
     * dp2px
     */
    @JvmStatic
    fun dp2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    @JvmStatic
    fun dp2px(dpValue: Float): Int {
        val scale = ApplicationUtil.app.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    /**
     * px2dp
     */
    @JvmStatic
    fun px2dp(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    /**
     * sp to px
     *
     * @param context activity context
     * @param sp      the font size sp
     * @return pixel
     */
    @JvmStatic
    fun sp2px(context: Context, sp: Float): Int {
        val fontScale = context.resources.displayMetrics.scaledDensity
        return (sp * fontScale + 0.5f).toInt()
    }

    @JvmStatic
    fun sp2px(sp: Float): Int {
        val fontScale = ApplicationUtil.app.resources.displayMetrics.scaledDensity
        return (sp * fontScale + 0.5f).toInt()
    }

    // 将px值转换为sp值，保证文字大小不变
    @JvmStatic
    fun px2sp(context: Context, pxValue: Float): Int {
        val fontScale = context.resources.displayMetrics.scaledDensity
        return (pxValue / fontScale + 0.5f).toInt()
    }

    @JvmStatic
    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    @JvmStatic
    fun getScreenSize(context: Context): Rect {
        val wm = context
                .getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val width = wm.defaultDisplay.width
        val height = wm.defaultDisplay.height
        val size = Rect()
        size[0, 0, width] = height
        return size
    }

    private var wm: WindowManager? = null

    /**
     * 获取真实屏幕高度
     *
     * @return
     */
    @JvmStatic
    val realHeight: Int
        get() {
            if (null == wm) {
                wm = ApplicationUtil.app.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            }
            val point = Point()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                wm!!.defaultDisplay.getRealSize(point)
            } else {
                wm!!.defaultDisplay.getSize(point)
            }
            return point.y
        }

    /**
     * 判断是否显示了导航栏
     * (说明这里的context 一定要是activity的context 否则类型转换失败)
     *
     * @param context
     * @return
     */
    @JvmStatic
    fun isShowNavBar(context: Activity?): Boolean {
        if (null == context) {
            return false
        }
        /**
         * 获取应用区域高度
         */
        val outRect1 = Rect()
        try {
            context.window.decorView.getWindowVisibleDisplayFrame(outRect1)
        } catch (e: ClassCastException) {
            e.printStackTrace()
            return false
        }
        val activityHeight = outRect1.height()

        /**
         * 获取状态栏高度
         */
        val statuBarHeight = getStatusBarHeight(context)

        /**
         * 屏幕物理高度 减去 状态栏高度
         */
        val remainHeight = realHeight - statuBarHeight
        /**
         * 剩余高度跟应用区域高度相等 说明导航栏没有显示 否则相反
         */
        return activityHeight != remainHeight
    }

    @JvmStatic
    fun getHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        display.getMetrics(dm)
        return dm.heightPixels
    }

    @JvmStatic
    fun getRealHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        display.getRealMetrics(dm)
        return dm.heightPixels
    }

    @JvmStatic
    fun getVirtualBarHeight(context: Context): Int {
        var vh = 0
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        try {
            val c = Class.forName("android.view.Display")
            val method = c.getMethod("getRealMetrics", DisplayMetrics::class.java)
            method.invoke(display, dm)
            vh = dm.heightPixels - display.height
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return vh
    }

    fun getNavigationBarHeight(context: Context): Int {
        var result = 0
        if (hasNavBar(context)) {
            val res: Resources = context.resources
            val resourceId: Int = res.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                result = res.getDimensionPixelSize(resourceId)
            }
        }
        return result
    }

    /**
     * 检查是否存在虚拟按键栏
     *
     * @param context
     * @return
     */
    fun hasNavBar(context: Context): Boolean {
        val res: Resources = context.resources
        val resourceId: Int = res.getIdentifier("config_showNavigationBar", "bool", "android")
        return if (resourceId != 0) {
            var hasNav: Boolean = res.getBoolean(resourceId)
            // check override flag
            val sNavBarOverride = getNavBarOverride()
            if ("1" == sNavBarOverride) {
                hasNav = false
            } else if ("0" == sNavBarOverride) {
                hasNav = true
            }
            hasNav
        } else { // fallback
            !ViewConfiguration.get(context).hasPermanentMenuKey()
        }
    }

    /**
     * 判断虚拟按键栏是否重写
     *
     * @return
     */
    private fun getNavBarOverride(): String? {
        var sNavBarOverride: String? = null
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            try {
                val c = Class.forName("android.os.SystemProperties")
                val m: Method = c.getDeclaredMethod("get", String::class.java)
                m.setAccessible(true)
                sNavBarOverride = m.invoke(null, "qemu.hw.mainkeys").toString()
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        return sNavBarOverride
    }

    /**
     *
     * @param iv
     * @param rate
     * @param baseX
     * @param lp
     */
    @JvmStatic
    fun fitXYImageView(iv: ImageView, rate: Double, baseX: Boolean, lp: ViewGroup.LayoutParams) {
        try {
            if (baseX) {
                val height = (getScreenSize(ApplicationUtil.app).width() * rate).toInt()
                lp.height = height
            } else {
                val wid = (getScreenSize(ApplicationUtil.app).height() * rate).toInt()
                lp.width = wid
            }
            iv.layoutParams = lp
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * Get Display
     *
     * @param context Context for get WindowManager
     * @return Display
     */
    @JvmStatic
    fun getDisplay(context: Context): Display? {
        val wm: WindowManager?
        wm = if (context is Activity) {
            context.windowManager
        } else {
            context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }
        return wm?.defaultDisplay
    }

    @JvmStatic
    fun getScreenWidth(context: Context?): Int {
        var tempContext = context
        if (tempContext == null) {
            tempContext = ApplicationUtil.app
        }
        val display = getDisplay(tempContext!!) ?: return 0
        val point = Point()
        display.getSize(point)
        return point.x
    }

    @JvmStatic
    fun getScreenHeight(context: Context): Int {
        val display = getDisplay(context) ?: return 0
        val point = Point()
        display.getSize(point)
        return point.y
    }

    @JvmStatic
    fun getScreenRealWidth(context: Context?): Int {
        var tempContext = context
        if (tempContext == null) {
            tempContext = ApplicationUtil.app
        }
        val display = getDisplay(tempContext) ?: return 0
        val outSize = Point()
        display.getRealSize(outSize)
        return outSize.x
    }

    @JvmStatic
    fun getScreenRealHeight(context: Context): Int {
        val display = getDisplay(context) ?: return 0
        val outSize = Point()
        display.getRealSize(outSize)
        return outSize.y
    }
}