<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#151924">


    <RelativeLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:clipChildren="false">


        <FrameLayout
            android:id="@+id/fvalue_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false">


<!--            <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--                android:id="@+id/vol_chart"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="@dimen/bottom_chart_height"-->
<!--                android:layout_marginRight="@dimen/right_chart_margin_label" />-->

            <RelativeLayout
                android:id="@+id/volue_frame"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="3px"

                android:layout_marginRight="@dimen/right_chart_margin_label">


                <TextView
                    android:id="@+id/volue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:textColor="#C9D3E6"
                    android:textSize="9sp"
                    tools:text="sdfsdf" />

                <TextView
                    android:id="@+id/volue_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10px"
                    android:layout_toRightOf="@+id/volue"
                    android:textColor="#EAD291"
                    android:textSize="9sp"
                    tools:text="sdfsdf" />

                <TextView
                    android:id="@+id/volue_10"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10px"
                    android:layout_toRightOf="@+id/volue_5"
                    android:textColor="#D175EB"
                    android:textSize="9sp"
                    tools:text="sdfsdf" />


                <TextView
                    android:id="@+id/volue_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:textColor="#636680"
                    android:textSize="9sp"
                    tools:text="sdfsdf" />

            </RelativeLayout>

        </FrameLayout>

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/macd_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="@dimen/bottom_chart_height"-->
<!--            android:layout_below="@+id/fvalue_layout"-->
<!--            android:layout_marginRight="@dimen/right_chart_margin_label" />-->

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/kdj_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="@dimen/bottom_chart_height"-->
<!--            android:layout_below="@+id/fvalue_layout"-->
<!--            android:layout_marginRight="@dimen/right_chart_margin_label"-->

<!--            />-->

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/rsi_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="@dimen/bottom_chart_height"-->
<!--            android:layout_below="@+id/fvalue_layout"-->
<!--            android:layout_marginRight="@dimen/right_chart_margin_label" />-->


        <RelativeLayout
            android:id="@+id/right_text_count"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/bottom_chart_height_100"
            android:layout_below="@+id/fvalue_layout"
            android:layout_alignParentRight="true"
            android:layout_marginRight="4dp"
            android:paddingBottom="20px"
            tools:visibility="visible"
            android:visibility="gone">


            <TextView
                android:id="@+id/order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="3px"
                android:textColor="#636680"
                android:textSize="9sp"
                tools:text="sdfsdf" />

            <TextView
                android:id="@+id/order_text_midum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/order_text"
                android:layout_alignParentRight="true"
                android:layout_marginTop="18px"
                android:textColor="#636680"
                tools:text="sdfssssdf"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/order_text_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:textColor="#636680"
                tools:text="sdff"
                android:textSize="9sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/order_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/fvalue_layout"
            android:layout_marginTop="3px"
            android:layout_marginRight="20dp"
            tools:visibility="visible"
            android:visibility="gone">


            <TextView

                android:id="@+id/order"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:textColor="#636680"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/order_one"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/order"
                android:textColor="#C9D3E6"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/order_two"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/order_one"
                android:textColor="#EAD291"
                android:textSize="9sp" />


            <TextView
                android:id="@+id/order_three"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/order_two"
                android:textColor="#D175EB"
                android:textSize="9sp" />


        </RelativeLayout>


    </RelativeLayout>


    <FrameLayout
        android:id="@+id/flayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        android:clipChildren="false">

<!--        <com.guoziwei.klinelib.chart.CustomCombinedChart-->
<!--            android:id="@+id/price_chart"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:layout_marginRight="@dimen/right_chart_margin_label" />-->

<!--        <com.guoziwei.klinelib.chart.KLineChartInfoView-->
<!--            android:id="@+id/k_info"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginRight="@dimen/right_chart_margin_label"-->
<!--            android:visibility="gone" />-->


        <RelativeLayout
            android:id="@+id/ma30_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginRight="20dp">


            <TextView
                android:id="@+id/value_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:textColor="#FFE69D"
                android:visibility="gone"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/value_20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/value_5"
                android:visibility="gone"
                android:textColor="#A76DFF"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/value_30"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:visibility="gone"
                android:layout_marginLeft="10px"
                android:layout_toRightOf="@+id/value_20"
                android:textColor="@color/color_commen_23BA82"
                android:textSize="9sp" />


        </RelativeLayout>


    </FrameLayout>


</FrameLayout>
