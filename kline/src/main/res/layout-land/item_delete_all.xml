<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">
<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        android:layout_width="300dp"-->
<!--        android:minHeight="150dp"-->
<!--        android:layout_height="wrap_content">-->
<!--        <TextView-->
<!--            android:layout_marginStart="15dp"-->
<!--            android:layout_marginEnd="15dp"-->
<!--            android:textSize="14sp"-->
<!--            android:id="@+id/title"-->
<!--            android:layout_marginTop="30dp"-->
<!--            tools:text="确定删除当前所有画线？确定删除当前所有画线确定删除当前所有画线"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:gravity="center"-->
<!--            android:id="@+id/cancle"-->
<!--            android:textSize="16sp"-->
<!--            android:textColor="?attr/colorTitle"-->
<!--            app:corner="3dp"-->
<!--            app:strokeColor="?attr/colorLine"-->
<!--            app:strokeWidth="2dp"-->
<!--            android:layout_marginBottom="15dp"-->
<!--            android:layout_marginStart="15dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            android:layout_width="128dp"-->
<!--            android:layout_height="40dp" />-->

<!--        <com.upex.common.widget.view.BaseTextView-->
<!--            android:gravity="center"-->
<!--            android:id="@+id/confirm"-->
<!--            android:textSize="16sp"-->
<!--            android:textColor="@color/white"-->
<!--            app:corner="3dp"-->
<!--            app:normalColor="?attr/color_b_00"-->
<!--            android:layout_marginBottom="15dp"-->
<!--            android:layout_marginEnd="15dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            android:layout_width="128dp"-->
<!--            android:layout_height="40dp" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>